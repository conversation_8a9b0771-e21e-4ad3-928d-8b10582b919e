#!/bin/bash

# COFL Health Check Script
# Checks the status of all COFL services and infrastructure

echo "🔍 COFL System Health Check"
echo "=========================="
echo "Timestamp: $(date)"
echo ""

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to check if a service is running
check_service() {
    local service_name=$1
    local container_name=$2
    
    if docker ps --format "table {{.Names}}\t{{.Status}}" | grep -q "$container_name.*Up"; then
        echo -e "✅ ${GREEN}$service_name${NC} is running"
        return 0
    else
        echo -e "❌ ${RED}$service_name${NC} is not running"
        return 1
    fi
}

# Function to check HTTP endpoint
check_endpoint() {
    local service_name=$1
    local url=$2
    local timeout=${3:-5}
    
    if curl -s --max-time $timeout "$url" > /dev/null 2>&1; then
        echo -e "✅ ${GREEN}$service_name${NC} endpoint is responding"
        return 0
    else
        echo -e "❌ ${RED}$service_name${NC} endpoint is not responding"
        return 1
    fi
}

echo "📊 Infrastructure Services Status:"
echo "--------------------------------"

# Check infrastructure services
check_service "MariaDB" "cofl_mariadb"
check_service "Redis" "cofl_redis"
check_service "Kafka" "cofl_kafka"
check_service "Zookeeper" "cofl_zookeeper"
check_service "Jaeger" "cofl_jaeger"
check_service "MinIO" "cofl_minio"
check_service "phpMyAdmin" "cofl_phpmyadmin"
check_service "ScyllaDB" "cofl_scylla"
check_service "MongoDB" "cofl_mongo"
check_service "CockroachDB" "cofl_cockroachdb"

echo ""
echo "🚀 Application Services Status:"
echo "------------------------------"

# Check application services
check_service "Items Service" "cofl_items"
check_service "API Service" "cofl_api"
check_service "Frontend" "cofl_frontend"

echo ""
echo "🌐 Web Interface Accessibility:"
echo "------------------------------"

# Check web interfaces
check_endpoint "Frontend" "http://localhost:3000"
check_endpoint "API Health" "http://localhost:1234"
check_endpoint "Items Service" "http://localhost:5014"
check_endpoint "Jaeger UI" "http://localhost:16686"
check_endpoint "MinIO Console" "http://localhost:9001"
check_endpoint "phpMyAdmin" "http://localhost:8038"

echo ""
echo "🔗 Service URLs:"
echo "---------------"
echo "🎯 Main Application:"
echo "• Frontend: http://localhost:3000"
echo "• API Gateway: http://localhost:1234"
echo "• Items Service: http://localhost:5014"
echo ""
echo "🛠️ Management Interfaces:"
echo "• Jaeger UI: http://localhost:16686"
echo "• MinIO Console: http://localhost:9001 (admin/cofl_minio_password_2024)"
echo "• phpMyAdmin: http://localhost:8038 (root/cofl_secure_password_2024)"
echo "• CockroachDB Admin: http://localhost:8080"
echo ""
echo "🔌 Database Connections:"
echo "• MariaDB: localhost:3307"
echo "• Redis: localhost:6380"
echo "• Kafka: localhost:9092"
echo "• MongoDB: localhost:27018"
echo "• ScyllaDB: localhost:9043"

echo ""
echo "📈 Resource Usage:"
echo "-----------------"
docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}"

echo ""
echo "Health check completed at $(date)"
