{"DB_CONNECTION": "server=mariadb;user=root;password=takenfrombitnami;database=mcconnect", "MARIADB_VERSION": "10.5.5", "KAFKA_HOST": "kafka:9092", "TOPICS": {"NEW_AUCTION": "sky-newauction", "NEW_BID": "sky-newbid", "SOLD_AUCTION": "sky-soldauction", "VERIFIED": "sky-verified"}, "KAFKA": {"BROKERS": "kafka:9092", "USERNAME": "", "PASSWORD": "", "TLS": {"CERTIFICATE_LOCATION": "", "CA_LOCATION": "", "KEY_LOCATION": ""}, "REPLICATION_FACTOR": "1"}, "OTEL_EXPORTER_OTLP_TRACES_ENDPOINT": "http://jaeger", "JAEGER_SAMPLER_TYPE": "ratelimiting", "JAEGER_SAMPLER_PARAM": "2", "JAEGER_SERVICE_NAME": "sky-mcconnect", "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "AllowedHosts": "*"}