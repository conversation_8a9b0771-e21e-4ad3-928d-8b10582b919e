This service is in charge of handling user to minecraft account connections.
It verifies account ownership by requiring the user to create an auction or bid with a specific starting coin amount. 

## Setup
This service should be executed inside as a container. Configuration is handled via [asp.net core configuration](https://docs.microsoft.com/en-us/aspnet/core/fundamentals/configuration/?view=aspnetcore-6.0#environment-variables)  
Default variable names and values are documented via appsettings.json.

