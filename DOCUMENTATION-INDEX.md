# COFL Documentation Index

## Overview
This document serves as a comprehensive index to all documentation created for the COFL (Coflnet) auction flipper system. Use this guide to navigate through the various documentation files and understand the system architecture.

## Main Documentation Files

### 📋 [README.md](./README.md)
**Main project documentation**
- System overview and architecture
- Quick start guide
- Service port mappings
- Basic configuration
- Contributing guidelines

### 🏗️ [INFRASTRUCTURE.md](./INFRASTRUCTURE.md)
**Infrastructure and architecture documentation**
- Database systems (MariaDB, ScyllaDB, CockroachDB, MongoDB)
- Message queues (Kafka, Zookeeper)
- Caching (Redis)
- Monitoring (Jaeger)
- Storage (MinIO)
- Network architecture
- Scaling strategies

### 🚀 [SETUP-GUIDE.md](./SETUP-GUIDE.md)
**Comprehensive setup and installation guide**
- Prerequisites and system requirements
- Step-by-step installation
- Environment configuration
- Troubleshooting guide
- Development workflow
- Security considerations

## Service-Specific Documentation

### Core Services

#### 🔌 [SkyApi](./service-readmes/SkyApi-README.md)
**Main API Gateway Service**
- REST API endpoints
- Authentication and authorization
- Service orchestration
- Database connections
- Caching strategies

#### 📊 [SkyFlipper](./service-readmes/SkyFlipper-README.md)
**Flip Calculation Engine**
- Flip calculation algorithms
- Price prediction models
- Risk assessment
- Machine learning components
- Performance optimization

#### 📈 [SkyIndexer](./service-readmes/SkyIndexer-README.md)
**Auction Data Indexing Service**
- Hypixel API integration
- Data processing pipeline
- Real-time indexing
- Search optimization
- Performance metrics

#### 🏛️ [SkyAuctions](./service-readmes/SkyAuctions-README.md)
**Auction Data Management**
- Auction storage and retrieval
- Historical analysis
- Search functionality
- Analytics engine
- Data archival

#### 🔄 [SkyBFCS](./service-readmes/SkyBFCS-README.md)
**Best Flip Communication Service**
- WebSocket communication
- Real-time broadcasting
- Connection management
- Message routing
- Performance scaling

### Frontend Application

#### 🌐 [hypixel-react](./service-readmes/hypixel-react-README.md)
**React Frontend Application**
- Component architecture
- State management (Redux)
- WebSocket integration
- Responsive design
- Performance optimization

## Service Directory Structure

```
cofl-dev/
├── README.md                           # Main documentation
├── INFRASTRUCTURE.md                   # Infrastructure guide
├── SETUP-GUIDE.md                     # Setup instructions
├── DOCUMENTATION-INDEX.md             # This file
├── docker-compose.yml                 # Service orchestration
├── service-readmes/                   # Service documentation
│   ├── SkyApi-README.md
│   ├── SkyFlipper-README.md
│   ├── SkyIndexer-README.md
│   ├── SkyAuctions-README.md
│   ├── SkyBFCS-README.md
│   └── hypixel-react-README.md
└── [service-directories]/             # Individual service repos
    ├── SkyApi/
    ├── SkyFlipper/
    ├── SkyIndexer/
    ├── SkyAuctions/
    ├── SkyBFCS/
    ├── hypixel-react/
    └── [other-services]/
```

## Documentation Placement Guide

### For Each Service Directory
When you clone the individual service repositories, place the corresponding README file in each service directory:

```bash
# Example for SkyApi service
cp service-readmes/SkyApi-README.md SkyApi/README.md

# Example for SkyFlipper service
cp service-readmes/SkyFlipper-README.md SkyFlipper/README.md

# Continue for all services...
```

### Complete Service List
Here are all the services that should have their README files placed:

**Core Services:**
- `SkyApi/README.md` ← `service-readmes/SkyApi-README.md`
- `SkyFlipper/README.md` ← `service-readmes/SkyFlipper-README.md`
- `SkyIndexer/README.md` ← `service-readmes/SkyIndexer-README.md`
- `SkyAuctions/README.md` ← `service-readmes/SkyAuctions-README.md`
- `SkyBFCS/README.md` ← `service-readmes/SkyBFCS-README.md`

**Frontend:**
- `hypixel-react/README.md` ← `service-readmes/hypixel-react-README.md`

**Additional Services (create similar README files):**
- `SkyItems/README.md`
- `SkyBazaar/README.md`
- `SkyCrafts/README.md`
- `SkyFlipTracker/README.md`
- `SkyPlayerInfo/README.md`
- `SkyPlayerName/README.md`
- `SkyPlayerState/README.md`
- `SkySettings/README.md`
- `SkyCommands/README.md`
- `SkyModCommands/README.md`
- `SkyMcConnect/README.md`
- `SkyUpdater/README.md`
- `SkyMayor/README.md`
- `SkyProxy/README.md`
- `Payments/README.md`
- `static_s3/README.md`

## Quick Reference

### System Architecture
```
Frontend (React) → API Gateway (SkyApi) → Microservices
                                      ↓
Infrastructure: MariaDB + ScyllaDB + Redis + Kafka + MinIO
```

### Key Ports
- **Frontend**: 3000
- **API Gateway**: 1234
- **WebSocket**: 8888
- **Databases**: MariaDB(3307), ScyllaDB(9042), MongoDB(27017)
- **Monitoring**: Jaeger(16686), phpMyAdmin(8038)

### Essential Commands
```bash
# Start all services
docker-compose up -d

# Check service status
docker-compose ps

# View logs
docker-compose logs -f [service-name]

# Health check
curl http://localhost:1234/health
```

## Documentation Standards

### README File Structure
Each service README should include:
1. **Overview** - Purpose and functionality
2. **Key Features** - Main capabilities
3. **Dependencies** - Required services
4. **Configuration** - Environment variables
5. **File Structure** - Important directories and files
6. **API Endpoints** - Available endpoints (if applicable)
7. **Development** - Setup and testing instructions

### Code Documentation
- **Inline Comments** - Explain complex logic
- **API Documentation** - OpenAPI/Swagger specs
- **Database Schemas** - Table structures and relationships
- **Configuration Examples** - Sample configuration files

## Maintenance

### Keeping Documentation Updated
1. **Version Control** - Track documentation changes
2. **Regular Reviews** - Update docs with code changes
3. **User Feedback** - Incorporate user suggestions
4. **Automated Checks** - Validate documentation accuracy

### Contributing to Documentation
1. **Follow Standards** - Use consistent formatting
2. **Be Comprehensive** - Cover all important aspects
3. **Include Examples** - Provide practical examples
4. **Test Instructions** - Verify setup procedures work

## Support and Resources

### Getting Help
- **GitHub Issues** - Report problems or ask questions
- **Discord Community** - Join the Coflnet Discord
- **Documentation Issues** - Report documentation problems

### External Resources
- **Coflnet Organization**: https://github.com/Coflnet
- **Main Website**: https://coflnet.com
- **Docker Documentation**: https://docs.docker.com
- **Hypixel API**: https://api.hypixel.net

## Next Steps

After setting up the documentation:

1. **Clone all service repositories** as described in SETUP-GUIDE.md
2. **Copy README files** to their respective service directories
3. **Follow the setup guide** to get the system running
4. **Customize configurations** for your environment
5. **Start developing** or using the COFL system

This documentation provides a solid foundation for understanding, setting up, and maintaining the COFL auction flipper system. Each document serves a specific purpose and together they provide comprehensive coverage of the entire system.
