# COFL Minimal Infrastructure Setup
# Start with essential infrastructure services only
version: '3.8'

services:
  mariadb:
    image: 'docker.io/bitnami/mariadb:10.11-debian-11'
    container_name: cofl_mariadb
    volumes:
      - 'skyblockdb_data:/bitnami/mariadb'
    ports:
      - '3307:3306'
    environment:
      - MARIADB_ROOT_PASSWORD=${MARIADB_ROOT_PASSWORD:-cofl_secure_password_2024}
      - MARIADB_EXTRA_FLAGS=--innodb-buffer-pool-size=500M --key-buffer-size=1G --connect-timeout=101
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p${MARIADB_ROOT_PASSWORD:-cofl_secure_password_2024}"]
      timeout: 20s
      retries: 10
    restart: unless-stopped

  redis:
    image: "redis:alpine"
    container_name: cofl_redis
    environment:
      - REDIS_REPLICATION_MODE=master
    ports:
      - "6380:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      timeout: 10s
      retries: 5
    restart: unless-stopped

  phpmyadmin:
    image: 'docker.io/bitnami/phpmyadmin:5-debian-10'
    container_name: cofl_phpmyadmin
    ports:
      - '8038:8080'
      - '4438:8443'
    depends_on:
      mariadb:
        condition: service_healthy
    environment:
      - PMA_HOST=mariadb
      - PMA_PORT=3306
      - PMA_USER=root
      - PMA_PASSWORD=${MARIADB_ROOT_PASSWORD:-cofl_secure_password_2024}
    restart: unless-stopped

  zookeeper:
    image: docker.io/bitnami/zookeeper:3.7
    container_name: cofl_zookeeper
    ports:
      - "2181:2181"
    volumes:
      - "zookeeper_data:/bitnami"
    environment:
      - ALLOW_ANONYMOUS_LOGIN=yes
    healthcheck:
      test: ["CMD", "zkServer.sh", "status"]
      timeout: 10s
      retries: 5
    restart: unless-stopped

  kafka:
    image: docker.io/bitnami/kafka:2
    container_name: cofl_kafka
    ports:
      - "9092:9092"
    volumes:
      - "kafka_data:/bitnami"
    environment:
      - KAFKA_CFG_ZOOKEEPER_CONNECT=zookeeper:2181
      - ALLOW_PLAINTEXT_LISTENER=yes
      - KAFKA_CFG_LISTENERS=PLAINTEXT://:9092
      - KAFKA_CFG_ADVERTISED_LISTENERS=PLAINTEXT://localhost:9092
    depends_on:
      zookeeper:
        condition: service_healthy
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2500M
    healthcheck:
      test: ["CMD", "kafka-topics.sh", "--bootstrap-server", "localhost:9092", "--list"]
      timeout: 10s
      retries: 5
    restart: unless-stopped

  jaeger:
    image: "jaegertracing/all-in-one:1.35"
    container_name: cofl_jaeger
    ports:
      - "16686:16686"
      - "14268:14268"
      - "14250:14250"
      - "5775:5775"
      - "6831:6831"
      - "6832:6832"
      - "5778:5778"
      - "14269:14269"
      - "9411:9411"
    environment:
      - COLLECTOR_ZIPKIN_HOST_PORT=:9411
    restart: unless-stopped

  minio:
    image: minio/minio
    container_name: cofl_minio
    command: server --console-address ":9001" /data
    ports:
      - '9000:9000'
      - '9001:9001'
    environment:
      MINIO_ROOT_USER: ${MINIO_ROOT_USER:-cofl_admin}
      MINIO_ROOT_PASSWORD: ${MINIO_ROOT_PASSWORD:-cofl_minio_password_2024}
    volumes:
      - 'minio_data:/data'
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      timeout: 10s
      retries: 5
    restart: unless-stopped

volumes:
  zookeeper_data:
    driver: local
  kafka_data:
    driver: local
  skyblockdb_data:
    driver: local
  minio_data:
    driver: local

networks:
  default:
    name: cofl_network
