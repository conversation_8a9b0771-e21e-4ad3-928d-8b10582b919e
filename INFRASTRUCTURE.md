# COFL Infrastructure Documentation

## Overview
This document describes the infrastructure components that support the COFL auction flipper system, including databases, message queues, caching, monitoring, and storage solutions.

## Architecture Diagram
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   API Gateway   │    │  Microservices  │
│  (React App)    │◄──►│   (SkyApi)      │◄──►│   (Various)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │                       │
                                ▼                       ▼
┌─────────────────────────────────────────────────────────────────┐
│                    Infrastructure Layer                         │
├─────────────────┬─────────────────┬─────────────────┬───────────┤
│   Databases     │  Message Queue  │    Caching      │ Monitoring│
│                 │                 │                 │           │
│ • MariaDB       │ • Kafka         │ • Redis         │ • <PERSON><PERSON><PERSON>  │
│ • ScyllaDB      │ • Zookeeper     │                 │           │
│ • CockroachDB   │                 │                 │           │
│ • MongoDB       │                 │                 │           │
└─────────────────┴─────────────────┴─────────────────┴───────────┘
```

## Database Systems

### MariaDB (Primary Database)
**Purpose**: Main relational database for structured data
**Port**: 3307 (Host) → 3306 (Container)
**Image**: `bitnami/mariadb:10.11-debian-11`

#### Configuration
```yaml
environment:
  - MARIADB_ROOT_PASSWORD=takenfrombitnami
  - MARIADB_EXTRA_FLAGS=--innodb-buffer-pool-size=500M --key-buffer-size=1G --connect-timeout=101
volumes:
  - skyblockdb_data:/bitnami/mariadb
```

#### Key Features
- **InnoDB Storage Engine**: ACID compliance and row-level locking
- **Connection Pooling**: Optimized for high concurrent connections
- **Buffer Pool**: 500MB allocated for frequently accessed data
- **Backup Strategy**: Regular automated backups
- **Replication**: Master-slave setup for read scaling

#### Database Schema
- **Auctions**: Active and historical auction data
- **Players**: Player profiles and statistics
- **Items**: Item metadata and pricing information
- **Bids**: Bidding history and tracking
- **Users**: User accounts and preferences

### ScyllaDB (High-Performance NoSQL)
**Purpose**: Time-series data and high-throughput operations
**Port**: 9042 (CQL), 7000 (Inter-node)
**Image**: `scylladb/scylla`

#### Configuration
```yaml
command: --smp 1
deploy:
  resources:
    limits:
      cpus: '2.0'
      memory: 8000M
volumes:
  - scylla_data:/var/lib/scylla
```

#### Key Features
- **High Throughput**: Millions of operations per second
- **Low Latency**: Sub-millisecond response times
- **Horizontal Scaling**: Easy cluster expansion
- **Cassandra Compatible**: CQL query language
- **Time-Series Optimization**: Perfect for price history data

#### Data Models
```cql
-- Price history tracking
CREATE TABLE price_history (
    item_tag TEXT,
    timestamp TIMESTAMP,
    price BIGINT,
    volume INT,
    PRIMARY KEY (item_tag, timestamp)
) WITH CLUSTERING ORDER BY (timestamp DESC);

-- Flip tracking
CREATE TABLE flip_events (
    user_id UUID,
    timestamp TIMESTAMP,
    item_tag TEXT,
    profit BIGINT,
    success BOOLEAN,
    PRIMARY KEY (user_id, timestamp)
) WITH CLUSTERING ORDER BY (timestamp DESC);
```

### CockroachDB (Distributed SQL)
**Purpose**: Proxy service data requiring strong consistency
**Port**: 26257 (SQL), 8080 (Admin UI)
**Image**: `cockroachdb/cockroach:v21.2.8`

#### Configuration
```yaml
command: start-single-node --insecure
environment:
  - COCKROACH_DATABASE=proxy
  - COCKROACH_USER=root
volumes:
  - cockroachdb_data:/cockroach/cockroach-data
```

#### Key Features
- **ACID Transactions**: Strong consistency guarantees
- **Horizontal Scaling**: Automatic data distribution
- **Fault Tolerance**: Automatic failover and recovery
- **SQL Compatibility**: Standard SQL interface
- **Geo-Distribution**: Multi-region deployment support

### MongoDB (Document Database)
**Purpose**: Flexible document storage for complex data structures
**Port**: 27017
**Image**: `bitnami/mongodb:7.0`

#### Configuration
```yaml
environment:
  - MONGODB_USERNAME=my_user
  - MONGODB_PASSWORD=password123
  - MONGODB_ROOT_PASSWORD=password123
  - MONGODB_DATABASE=my_database
volumes:
  - mongodb_data:/bitnami
```

#### Key Features
- **Document Storage**: JSON-like document structure
- **Flexible Schema**: Dynamic schema evolution
- **Indexing**: Rich indexing capabilities
- **Aggregation**: Powerful data processing pipeline
- **Replication**: Replica sets for high availability

## Message Queue System

### Apache Kafka
**Purpose**: Event streaming and message processing
**Port**: 9092
**Image**: `bitnami/kafka:2`

#### Configuration
```yaml
environment:
  - KAFKA_CFG_ZOOKEEPER_CONNECT=zookeeper:2181
  - ALLOW_PLAINTEXT_LISTENER=yes
deploy:
  resources:
    limits:
      cpus: '2.0'
      memory: 2500M
volumes:
  - kafka_data:/bitnami
```

#### Key Topics
- **auction-updates**: Real-time auction changes
- **flip-opportunities**: New flip opportunities
- **price-changes**: Item price updates
- **user-events**: User activity tracking
- **system-events**: System-wide notifications

#### Producer Services
- **SkyIndexer**: Publishes auction updates
- **SkyFlipper**: Publishes flip opportunities
- **SkyAuctions**: Publishes price changes

#### Consumer Services
- **SkyBFCS**: Consumes for real-time broadcasting
- **SkyFlipTracker**: Consumes for flip tracking
- **Analytics Services**: Consume for data analysis

### Apache Zookeeper
**Purpose**: Kafka cluster coordination
**Port**: 2181
**Image**: `bitnami/zookeeper:3.7`

#### Configuration
```yaml
environment:
  - ALLOW_ANONYMOUS_LOGIN=yes
volumes:
  - zookeeper_data:/bitnami
```

## Caching Layer

### Redis
**Purpose**: High-performance caching and session storage
**Port**: 6380 (Host) → 6379 (Container)
**Image**: `redis:alpine`

#### Configuration
```yaml
environment:
  - REDIS_REPLICATION_MODE=master
```

#### Cache Strategies
- **Auction Data**: Cache frequently accessed auctions (TTL: 5 minutes)
- **Price Data**: Cache item prices (TTL: 1 minute)
- **User Sessions**: Store user authentication sessions
- **Search Results**: Cache search query results (TTL: 2 minutes)
- **Analytics**: Cache computed analytics (TTL: 15 minutes)

#### Cache Keys Structure
```
cofl:auction:{auction_id}           # Individual auction data
cofl:prices:{item_tag}              # Item price information
cofl:search:{query_hash}            # Search result cache
cofl:user:{user_id}:session         # User session data
cofl:analytics:{type}:{period}      # Analytics cache
```

## Storage Solutions

### MinIO (S3-Compatible Storage)
**Purpose**: Object storage for files and static assets
**Port**: 9000 (API), 9001 (Console)
**Image**: `minio/minio`

#### Configuration
```yaml
command: server --console-address ":9001" /data
environment:
  MINIO_ROOT_USER: minio
  MINIO_ROOT_PASSWORD: minio123
volumes:
  - minio_data:/data
```

#### Buckets
- **sky-sniper**: Sniper service data and configurations
- **static**: Static files and assets
- **backups**: Database and configuration backups
- **logs**: Application logs and audit trails

## Monitoring & Observability

### Jaeger (Distributed Tracing)
**Purpose**: Distributed tracing and performance monitoring
**Port**: 16686 (UI), 14268 (Collector)
**Image**: `jaegertracing/all-in-one:1.35`

#### Configuration
```yaml
environment:
  - COLLECTOR_ZIPKIN_HOST_PORT=:9411
ports:
  - "16686:16686"  # Jaeger UI
  - "14268:14268"  # Jaeger collector
  - "6831:6831"    # Jaeger agent
```

#### Tracing Features
- **Request Tracing**: End-to-end request tracking
- **Performance Analysis**: Identify bottlenecks
- **Error Tracking**: Monitor error rates and patterns
- **Service Dependencies**: Visualize service interactions

### phpMyAdmin (Database Administration)
**Purpose**: Web-based MariaDB administration
**Port**: 8038 (HTTP), 4438 (HTTPS)
**Image**: `bitnami/phpmyadmin:5-debian-10`

#### Features
- **Database Management**: Create, modify, and delete databases
- **Query Interface**: Execute SQL queries
- **Data Import/Export**: Backup and restore data
- **User Management**: Manage database users and permissions

## Image Processing

### ImgProxy
**Purpose**: On-the-fly image processing and optimization
**Port**: 8234
**Image**: `darthsim/imgproxy`

#### Configuration
```yaml
environment:
  - IMGPROXY_ALLOWED_SOURCES=https://sky.shiiyu.moe/,https://skycrypt.coflnet.com,https://mc-heads.net/,https://crafatar.com/,https://static.coflnet.com/
  - IMGPROXY_BIND=:80
  - IMGPROXY_MAX_ANIMATION_FRAMES=100
```

#### Features
- **Image Resizing**: Dynamic image resizing
- **Format Conversion**: Convert between image formats
- **Optimization**: Automatic image optimization
- **Caching**: Built-in image caching

## Network Architecture

### Service Communication
- **Internal Network**: All services communicate via Docker network
- **Service Discovery**: Services discover each other by container name
- **Load Balancing**: Built-in Docker load balancing
- **Health Checks**: Regular health check endpoints

### Security
- **Network Isolation**: Services isolated in Docker network
- **Authentication**: JWT-based authentication
- **API Keys**: Service-to-service authentication
- **Rate Limiting**: Request rate limiting and throttling

## Backup & Recovery

### Backup Strategy
- **Database Backups**: Daily automated backups to MinIO
- **Configuration Backups**: Version-controlled configurations
- **Volume Snapshots**: Regular Docker volume snapshots
- **Cross-Region Replication**: Backup replication to different regions

### Recovery Procedures
- **Point-in-Time Recovery**: Restore to specific timestamps
- **Service Recovery**: Individual service restoration
- **Full System Recovery**: Complete system restoration
- **Data Validation**: Post-recovery data integrity checks

## Performance Tuning

### Database Optimization
- **Connection Pooling**: Optimized connection pool sizes
- **Query Optimization**: Regular query performance analysis
- **Index Optimization**: Proper indexing strategies
- **Partitioning**: Table partitioning for large datasets

### Caching Optimization
- **Cache Hit Rates**: Monitor and optimize cache effectiveness
- **TTL Tuning**: Optimize time-to-live values
- **Memory Management**: Efficient memory usage
- **Eviction Policies**: Appropriate cache eviction strategies

### Message Queue Optimization
- **Partition Strategy**: Optimal topic partitioning
- **Consumer Groups**: Efficient consumer group configuration
- **Batch Processing**: Batch message processing
- **Retention Policies**: Appropriate message retention

## Scaling Strategies

### Horizontal Scaling
- **Database Sharding**: Distribute data across multiple databases
- **Service Replication**: Multiple instances of services
- **Load Balancing**: Distribute traffic across instances
- **Auto-scaling**: Automatic scaling based on metrics

### Vertical Scaling
- **Resource Allocation**: Increase CPU and memory
- **Storage Expansion**: Expand storage capacity
- **Network Bandwidth**: Increase network capacity
- **Performance Tuning**: Optimize existing resources
