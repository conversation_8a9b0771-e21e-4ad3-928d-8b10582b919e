# SkyFlipper - Auction Flip Calculation Engine

## Overview
SkyFlipper is the core flip calculation and recommendation engine of the COFL system. It analyzes auction data, calculates potential profits, and provides intelligent flip recommendations to users.

## Purpose
- **Flip Analysis**: Calculate potential profit margins for auction items
- **Price Prediction**: Predict future item prices based on historical data
- **Recommendation Engine**: Suggest the most profitable flips
- **Risk Assessment**: Evaluate flip risk factors and success probability
- **Market Analysis**: Analyze market trends and patterns

## Key Features
- Real-time flip calculation algorithms
- Machine learning-based price predictions
- Risk-adjusted profit calculations
- Market trend analysis
- Flip success rate tracking
- Customizable filtering criteria

## Dependencies
- **Items Service**: Item metadata and current prices
- **Auctions Service**: Live auction data
- **FlipTracker**: Historical flip performance data
- **Bazaar Service**: Bazaar price comparisons

## Environment Variables
```bash
ITEMS_BASE_URL=http://items:8000
AUCTIONS_BASE_URL=http://auctions:8000
FLIPTRACKER_BASE_URL=http://fliptracker:8000
BAZAAR_BASE_URL=http://bazaar:8000
```

## Important Files & Directories
```
SkyFlipper/
├── Algorithms/           # Core flip calculation algorithms
│   ├── FlipCalculator.cs       # Main flip calculation logic
│   ├── PricePredictor.cs       # Price prediction algorithms
│   ├── RiskAnalyzer.cs         # Risk assessment logic
│   └── TrendAnalyzer.cs        # Market trend analysis
├── Models/               # Data models
│   ├── FlipOpportunity.cs      # Flip opportunity model
│   ├── PricePrediction.cs      # Price prediction model
│   ├── RiskFactor.cs           # Risk assessment model
│   └── MarketTrend.cs          # Market trend model
├── Services/             # Business services
│   ├── FlipService.cs          # Main flip service
│   ├── DataAggregator.cs       # Data collection service
│   ├── FilterService.cs        # Flip filtering logic
│   └── RecommendationEngine.cs # Recommendation algorithms
├── Filters/              # Flip filtering logic
│   ├── ProfitFilter.cs         # Minimum profit filtering
│   ├── VolumeFilter.cs         # Trading volume filtering
│   ├── RiskFilter.cs           # Risk level filtering
│   └── CategoryFilter.cs       # Item category filtering
├── Utils/                # Utility classes
│   ├── MathUtils.cs            # Mathematical calculations
│   ├── StatisticsUtils.cs      # Statistical analysis
│   └── TimeUtils.cs            # Time-based calculations
├── Configuration/        # Configuration classes
│   └── FlipperConfig.cs        # Flipper settings
├── Program.cs           # Application entry point
└── appsettings.json     # Configuration settings
```

## Flip Calculation Algorithm

### Basic Flip Calculation
```csharp
public class FlipCalculator
{
    public FlipResult CalculateFlip(Auction auction, ItemPrice currentPrice)
    {
        var buyPrice = auction.StartingBid;
        var sellPrice = currentPrice.LowestBin * 0.98; // Account for fees
        var profit = sellPrice - buyPrice;
        var profitMargin = (profit / buyPrice) * 100;
        
        return new FlipResult
        {
            Profit = profit,
            ProfitMargin = profitMargin,
            RiskScore = CalculateRisk(auction, currentPrice),
            Confidence = CalculateConfidence(auction, currentPrice)
        };
    }
}
```

### Risk Assessment Factors
- **Price Volatility**: How much the item price fluctuates
- **Trading Volume**: How frequently the item is traded
- **Market Trend**: Whether prices are rising or falling
- **Competition**: Number of similar auctions available
- **Historical Success**: Past flip success rate for this item

### Recommendation Scoring
```csharp
public double CalculateRecommendationScore(FlipOpportunity flip)
{
    var profitWeight = 0.4;
    var riskWeight = 0.3;
    var volumeWeight = 0.2;
    var trendWeight = 0.1;
    
    return (flip.ProfitScore * profitWeight) +
           ((1 - flip.RiskScore) * riskWeight) +
           (flip.VolumeScore * volumeWeight) +
           (flip.TrendScore * trendWeight);
}
```

## API Endpoints
- `GET /api/flips/recommendations` - Get top flip recommendations
- `POST /api/flips/calculate` - Calculate flip for specific auction
- `GET /api/flips/analyze/{itemTag}` - Analyze flip potential for item
- `GET /api/flips/trends` - Get market trend analysis
- `POST /api/flips/filter` - Apply custom filters to flips

## Configuration Parameters

### Flip Thresholds
```json
{
  "FlipSettings": {
    "MinimumProfit": 100000,
    "MinimumProfitMargin": 5.0,
    "MaximumRiskScore": 0.7,
    "MinimumVolume": 10,
    "MaximumCompetition": 5
  }
}
```

### Algorithm Weights
```json
{
  "AlgorithmWeights": {
    "ProfitWeight": 0.4,
    "RiskWeight": 0.3,
    "VolumeWeight": 0.2,
    "TrendWeight": 0.1
  }
}
```

## Data Sources
- **Live Auctions**: Real-time auction house data
- **Historical Prices**: Price history from completed auctions
- **Bazaar Data**: Instant buy/sell prices from bazaar
- **Flip History**: Past flip performance and outcomes

## Performance Optimizations
- **Caching**: Frequently accessed calculations cached
- **Batch Processing**: Multiple flips calculated in batches
- **Parallel Processing**: Concurrent calculation threads
- **Data Preprocessing**: Pre-calculated market indicators

## Machine Learning Components
- **Price Prediction Models**: LSTM networks for price forecasting
- **Risk Classification**: Random forest for risk assessment
- **Trend Detection**: Time series analysis for market trends
- **Success Prediction**: Logistic regression for flip success

## Monitoring & Metrics
- **Calculation Performance**: Processing time per flip
- **Prediction Accuracy**: How accurate price predictions are
- **Recommendation Success**: Success rate of recommended flips
- **System Load**: CPU and memory usage during calculations

## Testing Strategy
- **Unit Tests**: Individual algorithm testing
- **Integration Tests**: End-to-end flip calculation
- **Performance Tests**: Load testing with large datasets
- **Accuracy Tests**: Validation against historical data

## Development Guidelines
- **Algorithm Updates**: Version control for algorithm changes
- **A/B Testing**: Test new algorithms against current ones
- **Data Validation**: Ensure input data quality
- **Error Handling**: Graceful handling of invalid data

## Deployment Considerations
- **Scaling**: Horizontal scaling for high load
- **Monitoring**: Real-time performance monitoring
- **Rollback**: Quick rollback for algorithm issues
- **Configuration**: Runtime configuration updates
