# SkyAuctions - Auction Data Management Service

## Overview
SkyAuctions is responsible for managing all auction-related data in the COFL system. It handles auction storage, retrieval, analysis, and provides comprehensive auction management capabilities.

## Purpose
- **Auction Storage**: Persistent storage of auction data
- **Data Retrieval**: Fast auction queries and searches
- **Historical Analysis**: Track auction trends and patterns
- **Price Tracking**: Monitor price changes over time
- **Auction Lifecycle**: Manage auction states and transitions

## Key Features
- High-performance auction data storage
- Complex auction queries and filtering
- Real-time auction updates
- Historical price analysis
- Auction performance metrics
- Data archival and cleanup

## Port Configuration
- **Container Port**: 8000
- **Host Port**: 5031
- **Protocol**: HTTP

## Dependencies
- **MariaDB**: Primary auction data storage
- **ScyllaDB**: High-performance time-series data
- **Redis**: Caching layer
- **Kafka**: Event streaming
- **Jaeger**: Distributed tracing

## Environment Variables
```bash
ASPNETCORE_URLS=http://+:8000
DBCONNECTION=server=mariadb;user=root;password=takenfrombitnami;database=test
REDIS_HOST=redis
JAEGER_AGENT_HOST=jaeger
JAEGER_SERVICE_NAME=sky-auctions
KAFKA__BROKERS=kafka:9092
CASSANDRA__HOSTS=scylla
CASSANDRA__KEYSPACE=sky_auctions
```

## Important Files & Directories
```
SkyAuctions/
├── Controllers/          # API controllers
│   ├── AuctionController.cs    # Main auction endpoints
│   ├── SearchController.cs     # Search functionality
│   ├── AnalyticsController.cs  # Analytics endpoints
│   └── HistoryController.cs    # Historical data
├── Services/             # Business services
│   ├── AuctionService.cs       # Core auction logic
│   ├── SearchService.cs        # Search implementation
│   ├── AnalyticsService.cs     # Analytics calculations
│   ├── CacheService.cs         # Redis caching
│   └── ArchivalService.cs      # Data archival
├── Models/               # Data models
│   ├── Auction.cs              # Main auction model
│   ├── AuctionBid.cs           # Bid information
│   ├── AuctionHistory.cs       # Historical data
│   ├── PricePoint.cs           # Price tracking
│   └── SearchFilter.cs         # Search filters
├── Repositories/         # Data access layer
│   ├── AuctionRepository.cs    # Auction data access
│   ├── BidRepository.cs        # Bid data access
│   ├── HistoryRepository.cs    # Historical data access
│   └── CacheRepository.cs      # Cache operations
├── Analytics/            # Analytics components
│   ├── PriceAnalyzer.cs        # Price trend analysis
│   ├── VolumeAnalyzer.cs       # Trading volume analysis
│   ├── TrendCalculator.cs      # Trend calculations
│   └── StatisticsEngine.cs     # Statistical analysis
├── Workers/              # Background workers
│   ├── DataProcessor.cs        # Process auction updates
│   ├── ArchivalWorker.cs       # Archive old data
│   └── AnalyticsWorker.cs      # Generate analytics
├── Configuration/        # Configuration
│   └── AuctionConfig.cs        # Service configuration
├── Program.cs           # Application entry point
└── appsettings.json     # Configuration settings
```

## Database Schema

### Primary Tables (MariaDB)
```sql
-- Main auction table
CREATE TABLE Auctions (
    Id VARCHAR(36) PRIMARY KEY,
    ItemName VARCHAR(255) NOT NULL,
    ItemTag VARCHAR(100),
    StartingBid BIGINT NOT NULL,
    HighestBid BIGINT,
    AuctioneerUuid VARCHAR(36),
    StartTime DATETIME NOT NULL,
    EndTime DATETIME NOT NULL,
    Category VARCHAR(50),
    Tier VARCHAR(20),
    ItemBytes LONGTEXT,
    Claimed BOOLEAN DEFAULT FALSE,
    INDEX idx_item_tag (ItemTag),
    INDEX idx_end_time (EndTime),
    INDEX idx_starting_bid (StartingBid)
);

-- Bid history table
CREATE TABLE Bids (
    Id BIGINT AUTO_INCREMENT PRIMARY KEY,
    AuctionId VARCHAR(36) NOT NULL,
    BidderUuid VARCHAR(36) NOT NULL,
    Amount BIGINT NOT NULL,
    Timestamp DATETIME NOT NULL,
    FOREIGN KEY (AuctionId) REFERENCES Auctions(Id),
    INDEX idx_auction_id (AuctionId),
    INDEX idx_bidder (BidderUuid)
);
```

### Time-Series Data (ScyllaDB)
```cql
-- Price history table
CREATE TABLE price_history (
    item_tag TEXT,
    timestamp TIMESTAMP,
    price BIGINT,
    volume INT,
    auction_count INT,
    PRIMARY KEY (item_tag, timestamp)
) WITH CLUSTERING ORDER BY (timestamp DESC);

-- Auction events table
CREATE TABLE auction_events (
    auction_id TEXT,
    event_type TEXT,
    timestamp TIMESTAMP,
    data TEXT,
    PRIMARY KEY (auction_id, timestamp)
) WITH CLUSTERING ORDER BY (timestamp DESC);
```

## API Endpoints

### Auction Management
- `GET /api/auctions` - Get paginated auctions
- `GET /api/auctions/{id}` - Get specific auction
- `POST /api/auctions` - Create new auction (admin)
- `PUT /api/auctions/{id}` - Update auction
- `DELETE /api/auctions/{id}` - Delete auction (admin)

### Search & Filtering
- `POST /api/auctions/search` - Advanced auction search
- `GET /api/auctions/filter` - Filter auctions by criteria
- `GET /api/auctions/category/{category}` - Get by category
- `GET /api/auctions/item/{itemTag}` - Get by item

### Analytics & History
- `GET /api/auctions/analytics/prices/{itemTag}` - Price history
- `GET /api/auctions/analytics/volume/{itemTag}` - Volume analysis
- `GET /api/auctions/analytics/trends` - Market trends
- `GET /api/auctions/history/{id}` - Auction history

### Bidding
- `GET /api/auctions/{id}/bids` - Get auction bids
- `POST /api/auctions/{id}/bids` - Place bid
- `GET /api/auctions/bids/user/{uuid}` - Get user bids

## Search Implementation

### Advanced Search
```csharp
public class SearchService
{
    public async Task<SearchResult> SearchAuctions(SearchFilter filter)
    {
        var query = BuildQuery(filter);
        var auctions = await repository.SearchAsync(query);
        
        return new SearchResult
        {
            Auctions = auctions,
            TotalCount = await repository.CountAsync(query),
            Filters = filter,
            ExecutionTime = stopwatch.ElapsedMilliseconds
        };
    }
    
    private IQueryable<Auction> BuildQuery(SearchFilter filter)
    {
        var query = context.Auctions.AsQueryable();
        
        if (!string.IsNullOrEmpty(filter.ItemName))
            query = query.Where(a => a.ItemName.Contains(filter.ItemName));
            
        if (filter.MinPrice.HasValue)
            query = query.Where(a => a.StartingBid >= filter.MinPrice);
            
        if (filter.MaxPrice.HasValue)
            query = query.Where(a => a.StartingBid <= filter.MaxPrice);
            
        return query;
    }
}
```

### Search Filters
```csharp
public class SearchFilter
{
    public string ItemName { get; set; }
    public string ItemTag { get; set; }
    public string Category { get; set; }
    public string Tier { get; set; }
    public long? MinPrice { get; set; }
    public long? MaxPrice { get; set; }
    public DateTime? StartTime { get; set; }
    public DateTime? EndTime { get; set; }
    public bool? HasBids { get; set; }
    public string SellerUuid { get; set; }
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 50;
    public string SortBy { get; set; } = "EndTime";
    public string SortOrder { get; set; } = "ASC";
}
```

## Analytics Engine

### Price Analysis
```csharp
public class PriceAnalyzer
{
    public async Task<PriceAnalysis> AnalyzePrices(string itemTag, TimeSpan period)
    {
        var prices = await GetPriceHistory(itemTag, period);
        
        return new PriceAnalysis
        {
            AveragePrice = prices.Average(p => p.Price),
            MedianPrice = CalculateMedian(prices.Select(p => p.Price)),
            MinPrice = prices.Min(p => p.Price),
            MaxPrice = prices.Max(p => p.Price),
            PriceChange = CalculatePriceChange(prices),
            Volatility = CalculateVolatility(prices),
            TrendDirection = CalculateTrend(prices)
        };
    }
}
```

### Volume Analysis
```csharp
public class VolumeAnalyzer
{
    public async Task<VolumeAnalysis> AnalyzeVolume(string itemTag, TimeSpan period)
    {
        var data = await GetVolumeData(itemTag, period);
        
        return new VolumeAnalysis
        {
            TotalVolume = data.Sum(d => d.Volume),
            AverageVolume = data.Average(d => d.Volume),
            PeakVolume = data.Max(d => d.Volume),
            VolumeGrowth = CalculateVolumeGrowth(data),
            TradingFrequency = CalculateTradingFrequency(data)
        };
    }
}
```

## Caching Strategy

### Cache Keys
```csharp
public static class CacheKeys
{
    public const string AUCTION_PREFIX = "auction:";
    public const string SEARCH_PREFIX = "search:";
    public const string ANALYTICS_PREFIX = "analytics:";
    public const string PRICE_HISTORY_PREFIX = "prices:";
    
    public static string AuctionKey(string id) => $"{AUCTION_PREFIX}{id}";
    public static string SearchKey(string hash) => $"{SEARCH_PREFIX}{hash}";
    public static string PriceHistoryKey(string itemTag) => $"{PRICE_HISTORY_PREFIX}{itemTag}";
}
```

### Cache Implementation
```csharp
public class CacheService
{
    public async Task<T> GetOrSetAsync<T>(string key, Func<Task<T>> factory, TimeSpan expiry)
    {
        var cached = await redis.GetAsync<T>(key);
        if (cached != null) return cached;
        
        var value = await factory();
        await redis.SetAsync(key, value, expiry);
        return value;
    }
}
```

## Performance Optimization

### Database Indexing
- **Composite Indexes**: Multi-column indexes for common queries
- **Partial Indexes**: Indexes on filtered data
- **Covering Indexes**: Include all required columns
- **Index Maintenance**: Regular index optimization

### Query Optimization
- **Query Planning**: Analyze execution plans
- **Batch Operations**: Bulk inserts and updates
- **Connection Pooling**: Efficient connection management
- **Read Replicas**: Separate read and write operations

## Data Archival

### Archival Strategy
```csharp
public class ArchivalService
{
    public async Task ArchiveOldAuctions()
    {
        var cutoffDate = DateTime.UtcNow.AddMonths(-6);
        var oldAuctions = await repository.GetAuctionsOlderThan(cutoffDate);
        
        await MoveToArchiveStorage(oldAuctions);
        await repository.DeleteAuctions(oldAuctions.Select(a => a.Id));
    }
}
```

### Archive Storage
- **Cold Storage**: Move old data to cheaper storage
- **Compression**: Compress archived data
- **Backup**: Regular backups of archived data
- **Retrieval**: On-demand retrieval of archived data

## Monitoring & Metrics
- **Query Performance**: Track slow queries
- **Cache Hit Rates**: Monitor cache effectiveness
- **Data Growth**: Track database size growth
- **API Performance**: Monitor endpoint response times

## Development & Testing
- **Unit Tests**: Repository and service testing
- **Integration Tests**: Database integration testing
- **Performance Tests**: Load testing with large datasets
- **Data Migration**: Scripts for schema changes
