# hypixel-react - COFL Frontend Application

## Overview
The hypixel-react service is the main frontend web application for the COFL auction flipper system. Built with React, it provides a modern, responsive user interface for auction browsing, flip tracking, and user management.

## Purpose
- **User Interface**: Modern web interface for COFL features
- **Auction Browsing**: Browse and search auction listings
- **Flip Tracking**: Monitor flip opportunities and performance
- **User Dashboard**: Personal statistics and preferences
- **Real-time Updates**: Live auction and flip updates

## Key Features
- Responsive React-based web application
- Real-time WebSocket integration
- Advanced auction search and filtering
- Interactive charts and analytics
- User authentication and profiles
- Mobile-friendly design

## Port Configuration
- **Container Port**: 3000
- **Host Port**: 3000
- **Protocol**: HTTP

## Dependencies
- **SkyApi**: Main API for data retrieval
- **SkyBFCS**: WebSocket connection for real-time updates
- **Authentication Service**: User login and session management

## Technology Stack
- **React 18**: Modern React with hooks
- **TypeScript**: Type-safe JavaScript
- **Material-UI**: Component library
- **React Router**: Client-side routing
- **Axios**: HTTP client for API calls
- **Socket.IO**: WebSocket client
- **Chart.js**: Data visualization
- **Redux Toolkit**: State management

## Important Files & Directories
```
hypixel-react/
├── public/               # Static assets
│   ├── index.html              # Main HTML template
│   ├── favicon.ico             # Site favicon
│   └── manifest.json           # PWA manifest
├── src/                  # Source code
│   ├── components/             # Reusable components
│   │   ├── AuctionCard.tsx         # Auction display component
│   │   ├── FlipCard.tsx            # Flip opportunity component
│   │   ├── SearchBar.tsx           # Search functionality
│   │   ├── PriceChart.tsx          # Price history chart
│   │   └── UserProfile.tsx         # User profile component
│   ├── pages/                  # Page components
│   │   ├── HomePage.tsx            # Landing page
│   │   ├── AuctionsPage.tsx        # Auction listings
│   │   ├── FlipsPage.tsx           # Flip opportunities
│   │   ├── ProfilePage.tsx         # User profile
│   │   └── AnalyticsPage.tsx       # Analytics dashboard
│   ├── services/               # API and service layer
│   │   ├── api.ts                  # API client configuration
│   │   ├── auctionService.ts       # Auction API calls
│   │   ├── flipService.ts          # Flip API calls
│   │   ├── userService.ts          # User API calls
│   │   └── websocketService.ts     # WebSocket connection
│   ├── store/                  # Redux store
│   │   ├── index.ts                # Store configuration
│   │   ├── auctionSlice.ts         # Auction state
│   │   ├── flipSlice.ts            # Flip state
│   │   ├── userSlice.ts            # User state
│   │   └── uiSlice.ts              # UI state
│   ├── hooks/                  # Custom React hooks
│   │   ├── useAuctions.ts          # Auction data hook
│   │   ├── useFlips.ts             # Flip data hook
│   │   ├── useWebSocket.ts         # WebSocket hook
│   │   └── useAuth.ts              # Authentication hook
│   ├── utils/                  # Utility functions
│   │   ├── formatters.ts           # Data formatting
│   │   ├── validators.ts           # Input validation
│   │   ├── constants.ts            # App constants
│   │   └── helpers.ts              # Helper functions
│   ├── styles/                 # Styling
│   │   ├── globals.css             # Global styles
│   │   ├── components.css          # Component styles
│   │   └── themes.ts               # Material-UI themes
│   ├── types/                  # TypeScript types
│   │   ├── auction.ts              # Auction types
│   │   ├── flip.ts                 # Flip types
│   │   ├── user.ts                 # User types
│   │   └── api.ts                  # API response types
│   ├── App.tsx                 # Main app component
│   ├── index.tsx               # App entry point
│   └── setupTests.ts           # Test configuration
├── package.json            # Dependencies and scripts
├── tsconfig.json          # TypeScript configuration
├── webpack.config.js      # Webpack configuration
└── Dockerfile             # Container configuration
```

## Key Components

### Auction Card Component
```tsx
interface AuctionCardProps {
  auction: Auction;
  onFlipCalculate?: (auction: Auction) => void;
}

export const AuctionCard: React.FC<AuctionCardProps> = ({ auction, onFlipCalculate }) => {
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat().format(price);
  };

  return (
    <Card className="auction-card">
      <CardContent>
        <Typography variant="h6">{auction.itemName}</Typography>
        <Typography variant="body2">
          Starting Bid: {formatPrice(auction.startingBid)} coins
        </Typography>
        <Typography variant="body2">
          Time Left: {formatTimeLeft(auction.endTime)}
        </Typography>
        {onFlipCalculate && (
          <Button onClick={() => onFlipCalculate(auction)}>
            Calculate Flip
          </Button>
        )}
      </CardContent>
    </Card>
  );
};
```

### WebSocket Integration
```tsx
export const useWebSocket = () => {
  const dispatch = useAppDispatch();
  const [socket, setSocket] = useState<Socket | null>(null);

  useEffect(() => {
    const newSocket = io('ws://localhost:8888');
    
    newSocket.on('FlipUpdate', (flip: FlipOpportunity) => {
      dispatch(addFlipOpportunity(flip));
    });
    
    newSocket.on('AuctionUpdate', (auction: Auction) => {
      dispatch(updateAuction(auction));
    });
    
    setSocket(newSocket);
    
    return () => {
      newSocket.close();
    };
  }, [dispatch]);

  return socket;
};
```

### API Service Layer
```tsx
class ApiService {
  private baseURL = process.env.REACT_APP_API_URL || 'http://localhost:1234';
  
  async getAuctions(filters?: AuctionFilters): Promise<AuctionResponse> {
    const response = await axios.get(`${this.baseURL}/api/auctions`, {
      params: filters
    });
    return response.data;
  }
  
  async getFlipOpportunities(): Promise<FlipOpportunity[]> {
    const response = await axios.get(`${this.baseURL}/api/flips/recommendations`);
    return response.data;
  }
  
  async getUserProfile(userId: string): Promise<UserProfile> {
    const response = await axios.get(`${this.baseURL}/api/users/${userId}`);
    return response.data;
  }
}

export const apiService = new ApiService();
```

## State Management

### Redux Store Structure
```tsx
export interface RootState {
  auctions: AuctionState;
  flips: FlipState;
  user: UserState;
  ui: UIState;
}

interface AuctionState {
  items: Auction[];
  loading: boolean;
  error: string | null;
  filters: AuctionFilters;
  totalCount: number;
  currentPage: number;
}

interface FlipState {
  opportunities: FlipOpportunity[];
  loading: boolean;
  error: string | null;
  preferences: FlipPreferences;
}
```

### Auction Slice
```tsx
const auctionSlice = createSlice({
  name: 'auctions',
  initialState,
  reducers: {
    setAuctions: (state, action) => {
      state.items = action.payload.auctions;
      state.totalCount = action.payload.totalCount;
      state.loading = false;
    },
    updateAuction: (state, action) => {
      const index = state.items.findIndex(a => a.id === action.payload.id);
      if (index !== -1) {
        state.items[index] = action.payload;
      }
    },
    setFilters: (state, action) => {
      state.filters = action.payload;
    }
  }
});
```

## User Interface Features

### Advanced Search
- **Text Search**: Search by item name or description
- **Price Filters**: Min/max price range filtering
- **Category Filters**: Filter by item categories
- **Time Filters**: Filter by auction end time
- **Seller Filters**: Filter by specific sellers
- **Sort Options**: Multiple sorting criteria

### Real-time Updates
- **Live Auctions**: Real-time auction updates
- **Flip Notifications**: Instant flip opportunity alerts
- **Price Changes**: Live price change notifications
- **Bid Updates**: Real-time bidding updates

### Analytics Dashboard
- **Profit Tracking**: Track flip profits over time
- **Success Rates**: Monitor flip success rates
- **Market Trends**: Visualize market trends
- **Performance Metrics**: Personal performance analytics

## Responsive Design

### Mobile Optimization
```css
/* Mobile-first responsive design */
.auction-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

@media (min-width: 768px) {
  .auction-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .auction-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}
```

### Theme Configuration
```tsx
const theme = createTheme({
  palette: {
    mode: 'dark',
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
    background: {
      default: '#121212',
      paper: '#1e1e1e',
    },
  },
  typography: {
    fontFamily: 'Roboto, Arial, sans-serif',
  },
});
```

## Performance Optimization

### Code Splitting
```tsx
// Lazy load pages for better performance
const AuctionsPage = lazy(() => import('./pages/AuctionsPage'));
const FlipsPage = lazy(() => import('./pages/FlipsPage'));
const ProfilePage = lazy(() => import('./pages/ProfilePage'));

function App() {
  return (
    <Suspense fallback={<Loading />}>
      <Routes>
        <Route path="/auctions" element={<AuctionsPage />} />
        <Route path="/flips" element={<FlipsPage />} />
        <Route path="/profile" element={<ProfilePage />} />
      </Routes>
    </Suspense>
  );
}
```

### Memoization
```tsx
const AuctionList = memo(({ auctions, onAuctionClick }) => {
  return (
    <div className="auction-list">
      {auctions.map(auction => (
        <AuctionCard
          key={auction.id}
          auction={auction}
          onClick={onAuctionClick}
        />
      ))}
    </div>
  );
});
```

## Build & Deployment

### Development
```bash
npm install          # Install dependencies
npm start           # Start development server
npm test            # Run tests
npm run build       # Build for production
```

### Docker Build
```dockerfile
FROM node:18-alpine as build
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=build /app/build /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf
EXPOSE 3000
CMD ["nginx", "-g", "daemon off;"]
```

## Environment Configuration
```bash
REACT_APP_API_URL=http://localhost:1234
REACT_APP_WS_URL=ws://localhost:8888
REACT_APP_VERSION=1.0.0
REACT_APP_ENVIRONMENT=development
```

## Testing Strategy
- **Unit Tests**: Component and utility testing
- **Integration Tests**: API integration testing
- **E2E Tests**: End-to-end user flow testing
- **Visual Tests**: Component visual regression testing

## Accessibility
- **ARIA Labels**: Proper accessibility labels
- **Keyboard Navigation**: Full keyboard support
- **Screen Reader**: Screen reader compatibility
- **Color Contrast**: WCAG compliant color contrast
