# SkyApi - Main API Gateway

## Overview
SkyApi serves as the main API gateway for the COFL auction flipper system. It provides the primary REST API endpoints for client applications and coordinates communication between various microservices.

## Purpose
- **API Gateway**: Central entry point for all client requests
- **Service Orchestration**: Coordinates calls to multiple backend services
- **Authentication**: Handles user authentication and authorization
- **Rate Limiting**: Implements request throttling and abuse prevention
- **Response Aggregation**: Combines data from multiple services

## Key Features
- RESTful API endpoints for auction data
- Player statistics and profile information
- Item price history and trends
- Flip recommendations and analysis
- Real-time auction updates
- User preference management

## Port Configuration
- **Container Port**: 8000
- **Host Port**: 1234
- **Protocol**: HTTP

## Dependencies
- **Items Service**: Item metadata and information
- **MariaDB**: Primary database connection
- **Redis**: Caching layer
- **Kafka**: Event streaming

## Environment Variables
```bash
ITEMS_BASE_URL=http://items:8000
DBCONNECTION=server=mariadb;database=test;user=root;password=takenfrombitnami;convert zero datetime=True;Charset=utf8;Connect Timeout=30
```

## Important Files & Directories
```
SkyApi/
├── Controllers/           # API endpoint controllers
│   ├── AuctionController.cs    # Auction-related endpoints
│   ├── PlayerController.cs     # Player data endpoints
│   ├── ItemController.cs       # Item information endpoints
│   └── FlipController.cs       # Flip recommendation endpoints
├── Services/             # Business logic services
│   ├── AuctionService.cs       # Auction data processing
│   ├── PlayerService.cs        # Player data management
│   └── CacheService.cs         # Redis caching logic
├── Models/               # Data models and DTOs
│   ├── Auction.cs              # Auction data model
│   ├── Player.cs               # Player information model
│   └── FlipResult.cs           # Flip recommendation model
├── Middleware/           # Custom middleware
│   ├── AuthenticationMiddleware.cs  # Auth handling
│   └── RateLimitingMiddleware.cs    # Rate limiting
├── Configuration/        # App configuration
│   └── DatabaseConfig.cs       # Database connection setup
├── Program.cs           # Application entry point
├── Startup.cs           # Service configuration
└── appsettings.json     # Configuration settings
```

## API Endpoints

### Auction Endpoints
- `GET /api/auctions` - Get active auctions
- `GET /api/auctions/{id}` - Get specific auction
- `GET /api/auctions/search` - Search auctions
- `GET /api/auctions/ended` - Get ended auctions

### Player Endpoints
- `GET /api/player/{uuid}` - Get player information
- `GET /api/player/{uuid}/auctions` - Get player's auctions
- `GET /api/player/{uuid}/bids` - Get player's bids
- `GET /api/player/{uuid}/stats` - Get player statistics

### Item Endpoints
- `GET /api/items` - Get all items
- `GET /api/items/{tag}` - Get specific item
- `GET /api/items/{tag}/prices` - Get item price history
- `GET /api/items/{tag}/auctions` - Get item auctions

### Flip Endpoints
- `GET /api/flips` - Get flip recommendations
- `GET /api/flips/profitable` - Get most profitable flips
- `POST /api/flips/calculate` - Calculate flip profit

## Database Schema
The service primarily uses the following tables:
- `Auctions` - Active and historical auction data
- `Players` - Player information and statistics
- `Items` - Item metadata and current prices
- `Bids` - Bid history and tracking
- `FlipEvents` - Flip tracking and analytics

## Caching Strategy
- **Redis**: Used for frequently accessed data
- **Cache Keys**: Structured as `skyapi:{type}:{id}`
- **TTL**: Varies by data type (5min-1hour)
- **Invalidation**: Event-driven cache updates

## Error Handling
- Standardized error responses with HTTP status codes
- Detailed error logging with correlation IDs
- Graceful degradation when services are unavailable
- Circuit breaker pattern for external service calls

## Performance Considerations
- Connection pooling for database connections
- Async/await patterns for non-blocking operations
- Response compression for large payloads
- Pagination for large result sets

## Development Setup
1. Ensure MariaDB and Redis are running
2. Update connection strings in appsettings.json
3. Run database migrations if needed
4. Start the service: `dotnet run`

## Testing
- Unit tests in `SkyApi.Tests/`
- Integration tests for API endpoints
- Load testing configurations
- Mock services for isolated testing

## Monitoring & Logging
- Structured logging with Serilog
- Application metrics and health checks
- Jaeger tracing integration
- Performance counters and diagnostics

## Security
- JWT token authentication
- API key validation for service-to-service calls
- Input validation and sanitization
- CORS configuration for web clients

## Deployment
- Docker containerization
- Health check endpoints
- Graceful shutdown handling
- Environment-specific configurations
