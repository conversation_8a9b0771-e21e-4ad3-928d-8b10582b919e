# SkyIndexer - Auction Data Indexing Service

## Overview
SkyIndexer is responsible for continuously monitoring the Hypixel Skyblock auction house, collecting auction data, and indexing it for fast retrieval and analysis by other services.

## Purpose
- **Data Collection**: Fetch auction data from Hypixel API
- **Data Processing**: Clean and normalize auction information
- **Indexing**: Create searchable indexes for fast queries
- **Real-time Updates**: Provide live auction updates
- **Data Validation**: Ensure data quality and consistency

## Key Features
- Continuous auction house monitoring
- Real-time auction updates via WebSocket
- Efficient data indexing and storage
- Duplicate detection and handling
- Rate limiting and API management
- Data quality validation

## Port Configuration
- **Container Port**: 8008
- **Host Port**: 8007
- **Protocol**: HTTP/WebSocket

## Dependencies
- **MariaDB**: Primary auction data storage
- **Redis**: Caching and real-time data
- **Kafka**: Event streaming for updates
- **Items Service**: Item metadata validation

## Environment Variables
```bash
FRONTEND_PROD=frontend
JAEGER_SERVICE_NAME=hypixel-skyblock-core
JAEGER_AGENT_HOST=jaeger
KAFKA_HOST=kafka:9092
SKYCOMMANDS_HOST=commands:8008
HYPIXEL_API_KEY=your_api_key_here
```

## Important Files & Directories
```
SkyIndexer/
├── Services/             # Core indexing services
│   ├── AuctionIndexer.cs       # Main auction indexing logic
│   ├── HypixelApiClient.cs     # Hypixel API communication
│   ├── DataProcessor.cs        # Data cleaning and processing
│   ├── IndexManager.cs         # Index management
│   └── UpdateBroadcaster.cs    # Real-time update broadcasting
├── Models/               # Data models
│   ├── RawAuction.cs           # Raw auction data from API
│   ├── ProcessedAuction.cs     # Cleaned auction data
│   ├── AuctionIndex.cs         # Index structure
│   └── UpdateEvent.cs          # Update event model
├── Processors/           # Data processing pipeline
│   ├── DuplicateDetector.cs    # Duplicate auction detection
│   ├── DataValidator.cs        # Data quality validation
│   ├── PriceCalculator.cs      # Price calculation logic
│   └── CategoryClassifier.cs   # Item category classification
├── Indexing/             # Indexing components
│   ├── ElasticSearchIndex.cs   # Elasticsearch integration
│   ├── DatabaseIndex.cs        # Database indexing
│   ├── CacheIndex.cs           # Redis cache indexing
│   └── SearchOptimizer.cs      # Search optimization
├── Api/                  # API endpoints
│   ├── AuctionController.cs    # Auction data endpoints
│   ├── SearchController.cs     # Search functionality
│   └── StatusController.cs     # Service status
├── Workers/              # Background workers
│   ├── IndexingWorker.cs       # Main indexing worker
│   ├── UpdateWorker.cs         # Update processing worker
│   └── CleanupWorker.cs        # Data cleanup worker
├── Configuration/        # Configuration
│   ├── IndexerConfig.cs        # Indexer settings
│   └── ApiConfig.cs            # API configuration
├── Program.cs           # Application entry point
└── appsettings.json     # Configuration settings
```

## Data Flow Architecture

### 1. Data Collection
```csharp
public class HypixelApiClient
{
    public async Task<List<RawAuction>> FetchAuctions()
    {
        var response = await httpClient.GetAsync("https://api.hypixel.net/skyblock/auctions");
        var data = await response.Content.ReadAsStringAsync();
        return JsonSerializer.Deserialize<AuctionResponse>(data).Auctions;
    }
}
```

### 2. Data Processing Pipeline
```csharp
public class DataProcessor
{
    public ProcessedAuction ProcessAuction(RawAuction raw)
    {
        return new ProcessedAuction
        {
            Id = raw.Uuid,
            ItemName = CleanItemName(raw.ItemName),
            StartingBid = raw.StartingBid,
            HighestBid = raw.HighestBidAmount,
            EndTime = DateTimeOffset.FromUnixTimeMilliseconds(raw.End),
            Seller = raw.Auctioneer,
            Category = ClassifyItem(raw.ItemName),
            Rarity = ExtractRarity(raw.Tier),
            Enchantments = ParseEnchantments(raw.ItemLore)
        };
    }
}
```

### 3. Indexing Strategy
- **Primary Index**: Database storage for persistence
- **Search Index**: Elasticsearch for complex queries
- **Cache Index**: Redis for fast access
- **Real-time Index**: In-memory for live updates

## API Endpoints

### Data Retrieval
- `GET /api/auctions` - Get paginated auction list
- `GET /api/auctions/{id}` - Get specific auction
- `GET /api/auctions/search` - Search auctions with filters
- `GET /api/auctions/active` - Get currently active auctions

### Real-time Updates
- `WebSocket /ws/auctions` - Live auction updates
- `GET /api/auctions/recent` - Recently updated auctions
- `GET /api/auctions/ended` - Recently ended auctions

### Statistics
- `GET /api/stats/indexing` - Indexing performance stats
- `GET /api/stats/auctions` - Auction statistics
- `GET /api/health` - Service health check

## Indexing Performance

### Batch Processing
```csharp
public class IndexingWorker
{
    private async Task ProcessBatch(List<RawAuction> auctions)
    {
        var processed = auctions.Select(ProcessAuction).ToList();
        
        await Task.WhenAll(
            SaveToDatabase(processed),
            UpdateSearchIndex(processed),
            UpdateCache(processed),
            BroadcastUpdates(processed)
        );
    }
}
```

### Performance Metrics
- **Processing Rate**: ~1000 auctions/second
- **Index Update Time**: <100ms for batch updates
- **Search Response Time**: <50ms for complex queries
- **Memory Usage**: ~2GB for full auction index

## Data Quality Assurance

### Validation Rules
- Auction ID uniqueness
- Price value validation (positive numbers)
- Time validation (end time > start time)
- Item name consistency
- Seller UUID format validation

### Duplicate Detection
```csharp
public class DuplicateDetector
{
    public bool IsDuplicate(ProcessedAuction auction)
    {
        var key = $"{auction.Id}:{auction.StartingBid}:{auction.EndTime}";
        return !seenAuctions.Add(key);
    }
}
```

## Error Handling & Recovery
- **API Rate Limiting**: Exponential backoff for API calls
- **Connection Failures**: Automatic retry with circuit breaker
- **Data Corruption**: Validation and rollback mechanisms
- **Index Corruption**: Automatic index rebuilding

## Monitoring & Alerting
- **Processing Lag**: Alert if indexing falls behind
- **Error Rates**: Monitor API and processing errors
- **Performance Metrics**: Track throughput and latency
- **Data Quality**: Monitor validation failure rates

## Configuration

### Indexing Settings
```json
{
  "IndexingSettings": {
    "BatchSize": 1000,
    "ProcessingInterval": 5000,
    "MaxRetries": 3,
    "TimeoutMs": 30000
  }
}
```

### API Settings
```json
{
  "HypixelApi": {
    "BaseUrl": "https://api.hypixel.net",
    "RateLimit": 120,
    "TimeoutMs": 10000
  }
}
```

## Scaling Considerations
- **Horizontal Scaling**: Multiple indexer instances
- **Load Balancing**: Distribute API calls across instances
- **Database Sharding**: Partition data by time or category
- **Cache Clustering**: Redis cluster for high availability

## Development & Testing
- **Unit Tests**: Individual component testing
- **Integration Tests**: End-to-end indexing pipeline
- **Load Tests**: High-volume data processing
- **Mock Services**: Hypixel API mocking for testing
