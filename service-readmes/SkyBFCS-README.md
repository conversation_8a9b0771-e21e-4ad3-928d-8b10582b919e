# SkyBFCS - Best Flip Communication Service

## Overview
SkyBFCS (Best Flip Communication Service) is the real-time communication hub for the COFL system. It provides WebSocket connections for live flip updates, handles client connections, and manages real-time data distribution to connected users.

## Purpose
- **Real-time Communication**: WebSocket-based live updates
- **Flip Broadcasting**: Distribute flip opportunities to clients
- **Connection Management**: Handle client connections and sessions
- **Message Routing**: Route messages between services and clients
- **Authentication**: Secure WebSocket connections

## Key Features
- High-performance WebSocket server
- Real-time flip opportunity broadcasting
- Client session management
- Message queuing and delivery
- Connection scaling and load balancing
- Secure authentication and authorization

## Port Configuration
- **HTTP API Port**: 8021 (Host) → 8000 (Container)
- **WebSocket Port**: 8888 (Host) → 8888 (Container)
- **Protocol**: HTTP/WebSocket

## Dependencies
- **MariaDB**: User and session data
- **Redis**: Session caching and message queuing
- **Kafka**: Event streaming from other services
- **Jaeger**: Distributed tracing
- **MinIO**: Static file storage

## Environment Variables
```bash
DB_CONNECTION=server=mariadb;user=root;password=takenfrombitnami;database=base
JAEGER_SAMPLER_TYPE=ratelimiting
FLIP_REDIS_OPTIONS=redis
JAEGER_SAMPLER_PARAM=2
JAEGER_AGENT_HOST=jaeger
JAEGER_SERVICE_NAME=sky-bfcs
SOCKET_BASE_URL=ws://***************:8888
SNIPER_BASE_URL=http://sniper:8000
ITEMS_BASE_URL=http://items:8000
UPDATER_BASE_URL=http://updater:8000
```

## Important Files & Directories
```
SkyBFCS/
├── Hubs/                 # SignalR WebSocket hubs
│   ├── FlipHub.cs              # Main flip communication hub
│   ├── NotificationHub.cs      # General notifications
│   └── AdminHub.cs             # Admin-only communications
├── Services/             # Core services
│   ├── FlipBroadcaster.cs      # Flip broadcasting logic
│   ├── ConnectionManager.cs    # Client connection management
│   ├── MessageQueue.cs         # Message queuing service
│   ├── AuthenticationService.cs # WebSocket authentication
│   └── SessionManager.cs       # User session management
├── Models/               # Data models
│   ├── FlipMessage.cs          # Flip update message
│   ├── ClientConnection.cs     # Client connection info
│   ├── UserSession.cs          # User session data
│   └── BroadcastEvent.cs       # Broadcast event model
├── Controllers/          # HTTP API controllers
│   ├── ConnectionController.cs # Connection management API
│   ├── BroadcastController.cs  # Broadcasting API
│   └── StatusController.cs     # Service status
├── Middleware/           # Custom middleware
│   ├── WebSocketMiddleware.cs  # WebSocket handling
│   ├── AuthMiddleware.cs       # Authentication
│   └── RateLimitMiddleware.cs  # Rate limiting
├── Filters/              # Message filters
│   ├── UserPreferenceFilter.cs # User-specific filtering
│   ├── ProfitFilter.cs         # Profit-based filtering
│   └── CategoryFilter.cs       # Category filtering
├── Workers/              # Background workers
│   ├── MessageProcessor.cs     # Process incoming messages
│   ├── ConnectionCleaner.cs    # Clean up dead connections
│   └── MetricsCollector.cs     # Collect performance metrics
├── Configuration/        # Configuration
│   ├── SignalRConfig.cs        # SignalR configuration
│   └── WebSocketConfig.cs      # WebSocket settings
├── Program.cs           # Application entry point
└── appsettings.json     # Configuration settings
```

## WebSocket Communication

### Connection Flow
```csharp
public class FlipHub : Hub
{
    public async Task JoinFlipUpdates(string userId, FlipPreferences preferences)
    {
        await Groups.AddToGroupAsync(Context.ConnectionId, $"user_{userId}");
        await connectionManager.RegisterConnection(userId, Context.ConnectionId, preferences);
        await Clients.Caller.SendAsync("Connected", "Successfully connected to flip updates");
    }
    
    public async Task UpdatePreferences(FlipPreferences preferences)
    {
        await connectionManager.UpdatePreferences(Context.ConnectionId, preferences);
    }
}
```

### Message Broadcasting
```csharp
public class FlipBroadcaster
{
    public async Task BroadcastFlip(FlipOpportunity flip)
    {
        var connections = await connectionManager.GetEligibleConnections(flip);
        
        foreach (var connection in connections)
        {
            var filteredFlip = ApplyUserFilters(flip, connection.Preferences);
            if (filteredFlip != null)
            {
                await hubContext.Clients.Client(connection.ConnectionId)
                    .SendAsync("FlipUpdate", filteredFlip);
            }
        }
    }
}
```

## Message Types

### Flip Updates
```json
{
  "type": "flip_update",
  "data": {
    "auctionId": "uuid",
    "itemName": "Hyperion",
    "buyPrice": 500000000,
    "sellPrice": 600000000,
    "profit": 100000000,
    "profitMargin": 20.0,
    "riskScore": 0.3,
    "timeLeft": 3600,
    "seller": "player_uuid"
  }
}
```

### Notifications
```json
{
  "type": "notification",
  "data": {
    "title": "High Profit Flip",
    "message": "Found a flip with 100M profit!",
    "priority": "high",
    "timestamp": "2023-07-19T10:30:00Z"
  }
}
```

### Status Updates
```json
{
  "type": "status_update",
  "data": {
    "service": "flipper",
    "status": "online",
    "lastUpdate": "2023-07-19T10:30:00Z",
    "activeFlips": 1250
  }
}
```

## Client Connection Management

### Connection Tracking
```csharp
public class ConnectionManager
{
    private readonly ConcurrentDictionary<string, ClientConnection> connections;
    
    public async Task RegisterConnection(string userId, string connectionId, FlipPreferences preferences)
    {
        var connection = new ClientConnection
        {
            UserId = userId,
            ConnectionId = connectionId,
            ConnectedAt = DateTime.UtcNow,
            Preferences = preferences,
            IsActive = true
        };
        
        connections.TryAdd(connectionId, connection);
        await UpdateConnectionInRedis(connection);
    }
}
```

### Session Management
- **Session Persistence**: Store sessions in Redis
- **Reconnection Handling**: Automatic reconnection support
- **Timeout Management**: Clean up inactive connections
- **Load Balancing**: Distribute connections across instances

## Performance Optimization

### Connection Scaling
- **Connection Pooling**: Efficient connection management
- **Message Batching**: Batch multiple updates together
- **Compression**: Compress large messages
- **Selective Broadcasting**: Only send relevant updates

### Memory Management
```csharp
public class MessageQueue
{
    private readonly LimitedConcurrentQueue<FlipMessage> messageQueue;
    
    public void EnqueueMessage(FlipMessage message)
    {
        if (messageQueue.Count > MaxQueueSize)
        {
            messageQueue.TryDequeue(out _); // Remove oldest message
        }
        messageQueue.Enqueue(message);
    }
}
```

## Authentication & Security

### WebSocket Authentication
```csharp
public class AuthenticationService
{
    public async Task<bool> ValidateConnection(string token)
    {
        var tokenData = await jwtService.ValidateToken(token);
        return tokenData != null && !tokenData.IsExpired;
    }
}
```

### Rate Limiting
- **Connection Limits**: Max connections per user
- **Message Limits**: Max messages per second
- **Bandwidth Limits**: Data transfer limits
- **Abuse Prevention**: Detect and block malicious clients

## Monitoring & Metrics

### Performance Metrics
- **Active Connections**: Number of connected clients
- **Message Throughput**: Messages per second
- **Response Time**: Message delivery latency
- **Error Rates**: Connection and message errors

### Health Checks
```csharp
public class HealthController : ControllerBase
{
    [HttpGet("health")]
    public async Task<IActionResult> GetHealth()
    {
        var health = new
        {
            Status = "Healthy",
            ActiveConnections = connectionManager.GetActiveConnectionCount(),
            MessageQueueSize = messageQueue.Count,
            LastUpdate = DateTime.UtcNow
        };
        
        return Ok(health);
    }
}
```

## Configuration

### SignalR Settings
```json
{
  "SignalR": {
    "MaximumReceiveMessageSize": 32768,
    "StreamBufferCapacity": 10,
    "MaximumParallelInvocationsPerClient": 1,
    "EnableDetailedErrors": false
  }
}
```

### WebSocket Settings
```json
{
  "WebSocket": {
    "KeepAliveInterval": 30,
    "ReceiveBufferSize": 4096,
    "SendBufferSize": 4096,
    "MaxConnections": 10000
  }
}
```

## Deployment & Scaling
- **Horizontal Scaling**: Multiple BFCS instances
- **Load Balancing**: Distribute WebSocket connections
- **Session Affinity**: Sticky sessions for WebSocket
- **Health Monitoring**: Continuous health checks

## Development & Testing
- **Unit Tests**: Individual component testing
- **Integration Tests**: End-to-end WebSocket testing
- **Load Tests**: High-concurrency connection testing
- **Mock Clients**: Automated client simulation
