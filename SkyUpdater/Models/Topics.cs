using System.Text.Json.Serialization;

namespace Coflnet.Sky.Updater.Models;
public class Topics
{
    [<PERSON><PERSON><PERSON>ropertyName("MISSING_AUCTION")]
    public string Missing_Auction { get; set; }
    [JsonPropertyName("SOLD_AUCTION")]
    public string Sold_Auction { get; set; }
    [<PERSON><PERSON><PERSON><PERSON>tyName("NEW_AUCTION")]
    public string New_Auction { get; set; }
    [<PERSON><PERSON><PERSON><PERSON>tyName("AUCTION_ENDED")]
    public string Auction_Ended { get; set; }
    [<PERSON>son<PERSON>ropertyName("NEW_BID")]
    public string New_Bid { get; set; }
    [JsonPropertyName("BAZAAR")]
    public string Bazaar { get; set; }
    [<PERSON><PERSON><PERSON><PERSON><PERSON>Name("AH_SUMARY")]
    public string Ah_Sumary { get; set; }
    [<PERSON><PERSON><PERSON>ropertyName("AUCTION_CHECK")]
    public string Auction_Check { get; set; }
}