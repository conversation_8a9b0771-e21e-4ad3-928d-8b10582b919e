{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "AllowedHosts": "*", "FRONTEND_PROD": "frontend", "REDIS_HOST": "redis", "JAEGER_SERVICE_NAME": "sky-updater", "OTEL_EXPORTER_OTLP_TRACES_ENDPOINT": "http://jaeger", "JAEGER_SAMPLER_TYPE": "ratelimiting", "JAEGER_SAMPLER_PARAM": "2", "ITEMS_BASE_URL": "http://localhost:5014", "TOPICS": {"MISSING_AUCTION": "sky-canceledauction", "SOLD_AUCTION": "sky-soldauction", "NEW_AUCTION": "sky-newauction", "AUCTION_ENDED": "sky-endedauction", "NEW_BID": "sky-newbid", "BAZAAR": "sky-bazaar", "AH_SUMARY": "sky-<PERSON><PERSON><PERSON><PERSON>", "AUCTION_CHECK": "sky-checkauction"}, "KAFKA": {"BROKERS": "kafka:9092", "USERNAME": null, "PASSWORD": "", "TLS": {"CERTIFICATE_LOCATION": "", "CA_LOCATION": "", "KEY_LOCATION": ""}, "REPLICATION_FACTOR": "1"}, "IpRateLimiting": {"EnableEndpointRateLimiting": false, "StackBlockedRequests": false, "RealIpHeader": "X-Real-IP", "ClientIdHeader": "X-ClientId", "HttpStatusCode": 429, "EndpointWhitelist": [], "GeneralRules": [{"Endpoint": "*", "Period": "10s", "Limit": 20}]}, "SLOWDOWN_MS": 0, "OverwriteUpdaterIndex": null, "DBConnection": "server=mariadb;database=test;user=root;password=takenfrombitnami; convert zero datetime=True;Charset=utf8; Connect Timeout=30"}