{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "AllowedHosts": "*", "DB_CONNECTION": "server=mariadb;user=root;password=takenfrombitnami;database=test", "DBCONNECTION": "server=localhost;user=root;password=takenfrombitnami;database=test", "JAEGER_SAMPLER_TYPE": "ratelimiting", "KAFKA_HOST": "kafka", "REDIS_HOST": "redis", "SETTINGS_REDIS_HOST": "redis", "SETTINGS_BASE_URL": "http://localhost:5004", "API_BASE_URL": "https://sky.coflnet.com", "ITEMS_BASE_URL": "http://localhost:5014", "CRAFTS_BASE_URL": "http://localhost:5009", "MCCONNECT_BASE_URL": "http://localhost:5021", "UPDATER_BASE_URL": "http://localhost:5002", "SKYCOMMANDS_BASE_URL": "http://commands:8008", "PAYMENTS_BASE_URL": "http://localhost:5020", "INDEXER_BASE_URL": "http://indexer:8000", "FLIPTRACKER_BASE_URL": "http://localhost:5017", "SKYFLIPPER_BASE_URL": "http://flipper", "PLAYERNAME_BASE_URL": "http://localhost:5018", "PROXY_BASE_URL": "http://localhost:5029", "PLAYERSTATE_BASE_URL": "http://localhost:5025", "PROFILE_BASE_URL": "http://localhost:5013", "SNIPER_BASE_URL": "http://localhost:5022", "MOD_REDIS_HOST": "redis", "EVENTS_BASE_URL": "http://localhost:5024", "BAZAARFLIPPER_BASE_URL": "http://localhost:5033", "LOKI_BASE_URL": "http://loki-scalable-read.loki-scalable:3100", "EVENTS_REDIS_HOST": "redis", "REDIS_FLIP_INSTANCES": ["redis"], "TOPICS": {"MISSING_AUCTION": "sky-canceledauction", "SOLD_AUCTION": "sky-soldauction", "AUCTION_ENDED": "sky-endedauction", "FLIP": "sky-flip", "SETTINGS_CHANGE": "sky-settings", "LOW_PRICED": "sky-lowpriced", "FLIP_EVENT": "sky-flipevent", "NEW_AUCTION": "sky-newauction", "STATE_UPDATE": "sky-state-update", "LOAD_FLIPS": "sky-loadflips"}, "KAFKA": {"BROKERS": "kafka:9092", "USERNAME": "", "PASSWORD": "", "TLS": {"CERTIFICATE_LOCATION": "", "CA_LOCATION": "", "KEY_LOCATION": ""}, "REPLICATION_FACTOR": "1"}, "CHAT_BASE_URL": "http://localhost:5010", "CHAT_API_KEY": "sHV+SwbQLqkMJF5gmjcr4HN/nd5o36Xa", "PRODUCTS": {"PREMIUM": "premium", "TEST_PREMIUM": "test-premium", "PREMIUM_PLUS": "premium_plus", "STARTER_PREMIUM": "starter_premium", "PRE_API": "pre_api"}, "CASSANDRA": {"HOSTS": "localhost", "USER": "cassandra", "PASSWORD": "cassandra", "KEYSPACE": "sky_mod", "REPLICATION_CLASS": "SimpleStrategy", "REPLICATION_FACTOR": 1, "X509Certificate_PATHS": "", "X509Certificate_PASSWORD": null, "X509Certificate_VALIDATION_PATH": null}, "VPS": {"LOG_TOKEN": "9a0e37b13532890df46b36e70fd19ddeccdcf9af8ef2813c30b1a278f55ec306"}, "JAEGER_SAMPLER_PARAM": "2", "MARIADB_VERSION": "10.5.5", "OTEL_EXPORTER_OTLP_TRACES_ENDPOINT": "http://jaeger", "JAEGER_SERVICE_NAME": "sky-commands-mod"}