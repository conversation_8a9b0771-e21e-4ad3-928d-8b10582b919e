<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <NoWarn>1591,RS0016;RS0037</NoWarn>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <ErrorOnDuplicatePublishOutputFiles>false</ErrorOnDuplicatePublishOutputFiles>
    <GenerateProgramFile>false</GenerateProgramFile>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="../dev/hypixel.csproj" />
    <ProjectReference Include="../SkyFilter/SkyFilter.csproj" />
    <ProjectReference Include="../SkyBackendForFrontend/SkyBackendForFrontend.csproj" />
    <ProjectReference Include="../websocket-sharp/websocket-sharp/websocket-sharp.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="CassandraCSharpDriver" Version="3.22.0" />
    <PackageReference Include="Figgle.Fonts" Version="0.6.5" />
    <PackageReference Include="Coflnet.Sky.Chat.Client" Version="1.0.1" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.18">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Pomelo.EntityFrameworkCore.MySql" Version="8.0.3" />
    <PackageReference Include="Coflnet.Sky.Proxy.Client" Version="1.0.0" />
  </ItemGroup>

  <PropertyGroup>
    <DefaultItemExcludes>$(DefaultItemExcludes);Client\**\*</DefaultItemExcludes>
  </PropertyGroup>
  <ItemGroup Condition="'$(Configuration)' == 'Release'">
    <Compile Remove="**\*.Tests.cs" />
  </ItemGroup>
  <ItemGroup Condition="'$(Configuration)' != 'Release'">
    <PackageReference Include="Moq" Version="4.20.72" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.14.1" />
  </ItemGroup>
</Project>