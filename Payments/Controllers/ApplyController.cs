using System.Threading.Tasks;
using Coflnet.Payments.Models;
using Coflnet.Payments.Services;
using Microsoft.AspNetCore.Mvc;

namespace Payments.Controllers
{
    [ApiController]
    [Route("[controller]")]
    public class ApplyController : ControllerBase
    {
        private readonly PaymentContext db;
        private readonly ProductService productService;
        private readonly GroupService groupService;
        private readonly IRuleEngine ruleEngine;

        public ApplyController(PaymentContext db, ProductService productService, GroupService groupService, IRuleEngine ruleEngine)
        {
            this.db = db;
            this.productService = productService;
            this.groupService = groupService;
            this.ruleEngine = ruleEngine;
        }

        /// <summary>
        /// Brings all products, groups and roles into the given state
        /// will disable/delete anything not present so use carefully
        /// </summary>
        /// <param name="state"></param>
        /// <returns></returns>
        /// <exception cref="ApiException"></exception>
        [HttpPost]
        public async Task ApplyState(SystemState state)
        {
            if (state.Products != null && state.Products.Count > 0)
                await productService.ApplyProductList(state.Products);
            if (state.TopUps != null)
                await productService.ApplyTopupList(state.TopUps);
            if (state.Groups != null)
                await groupService.ApplyGroupList(state.Groups);
            if (state.Rules != null)
                await ruleEngine.ApplyRuleList(state.Rules);
        }
    }
}
