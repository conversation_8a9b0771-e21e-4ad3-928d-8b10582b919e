# COFL Setup Guide

## Prerequisites

### System Requirements
- **Operating System**: Linux, macOS, or Windows with WSL2
- **RAM**: Minimum 16GB, Recommended 32GB
- **Storage**: 50GB+ free disk space
- **CPU**: 4+ cores recommended
- **Network**: Stable internet connection for API access

### Required Software
- **Docker**: Version 20.10 or higher
- **Docker Compose**: Version 2.0 or higher
- **Git**: For cloning repositories
- **Node.js**: Version 18+ (for frontend development)
- **.NET SDK**: Version 6.0+ (for backend development)

### Installation Commands
```bash
# Install Docker (Ubuntu/Debian)
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Verify installations
docker --version
docker-compose --version
```

## Project Setup

### 1. Create Project Directory
```bash
mkdir cofl-dev
cd cofl-dev
```

### 2. Clone All Required Repositories
```bash
# Core services
git clone https://github.com/Coflnet/SkyApi.git
git clone https://github.com/Coflnet/SkyIndexer.git
git clone https://github.com/Coflnet/SkyAuctions.git
git clone https://github.com/Coflnet/SkyFlipper.git
git clone https://github.com/Coflnet/SkySniper.git
git clone https://github.com/Coflnet/SkyFilter.git

# Data services
git clone https://github.com/Coflnet/SkyItems.git
git clone https://github.com/Coflnet/SkyBazaar.git
git clone https://github.com/Coflnet/SkyCrafts.git
git clone https://github.com/Coflnet/SkyFlipTracker.git

# User services
git clone https://github.com/Coflnet/SkyPlayerInfo.git
git clone https://github.com/Coflnet/SkyPlayerName.git
git clone https://github.com/Coflnet/SkyPlayerState.git
git clone https://github.com/Coflnet/SkySettings.git

# Communication services
git clone https://github.com/Coflnet/SkyCommands.git
git clone https://github.com/Coflnet/SkyModCommands.git
git clone https://github.com/Coflnet/SkyMcConnect.git
git clone https://github.com/Coflnet/SkyBFCS.git

# Utility services
git clone https://github.com/Coflnet/SkyUpdater.git
git clone https://github.com/Coflnet/SkyMayor.git
git clone https://github.com/Coflnet/SkyProxy.git
git clone https://github.com/Coflnet/Payments.git
git clone https://github.com/Coflnet/static_s3.git

# Frontend
git clone https://github.com/Coflnet/hypixel-react.git

# Copy the docker-compose.yml file to this directory
```

### 3. Environment Configuration

#### Create Environment Files
```bash
# Create .env file for sensitive data
cat > .env << EOF
MARIADB_ROOT_PASSWORD=your_secure_password
REDIS_PASSWORD=your_redis_password
MINIO_ROOT_USER=admin
MINIO_ROOT_PASSWORD=your_minio_password
HYPIXEL_API_KEY=your_hypixel_api_key
JWT_SECRET=your_jwt_secret
EOF
```

#### Update Docker Compose Configuration
```bash
# Edit docker-compose.yml to use environment variables
sed -i 's/takenfrombitnami/${MARIADB_ROOT_PASSWORD}/g' docker-compose.yml
```

## Service Configuration

### Database Initialization
```bash
# Start only database services first
docker-compose up -d mariadb scylla mongo cockroachdb redis

# Wait for databases to initialize (check logs)
docker-compose logs -f mariadb

# Run database migrations (if available)
# This step varies by service - check individual service documentation
```

### API Keys and External Services

#### Hypixel API Key
1. Visit https://developer.hypixel.net/
2. Create an account and generate an API key
3. Add the key to your environment configuration

#### Optional Services
- **Jaeger**: Distributed tracing (optional but recommended)
- **MinIO**: Object storage (required for some features)
- **Kafka**: Message streaming (required for real-time features)

## Starting the System

### 1. Start Infrastructure Services
```bash
# Start databases and infrastructure
docker-compose up -d mariadb scylla mongo cockroachdb redis kafka zookeeper jaeger minio
```

### 2. Wait for Services to Initialize
```bash
# Check service health
docker-compose ps
docker-compose logs mariadb | grep "ready for connections"
docker-compose logs kafka | grep "started"
```

### 3. Start Application Services
```bash
# Start core services
docker-compose up -d indexer api auctions items flipper

# Start additional services
docker-compose up -d commands bfcs frontend

# Start remaining services
docker-compose up -d
```

### 4. Verify System Health
```bash
# Check all services are running
docker-compose ps

# Check service logs for errors
docker-compose logs --tail=50 api
docker-compose logs --tail=50 indexer
```

## Access Points

### Web Interfaces
- **Frontend Application**: http://localhost:3000
- **Jaeger UI**: http://localhost:16686
- **MinIO Console**: http://localhost:9001
- **phpMyAdmin**: http://localhost:8038
- **CockroachDB Admin**: http://localhost:8080

### API Endpoints
- **Main API**: http://localhost:1234
- **Auctions API**: http://localhost:5031
- **Items API**: http://localhost:5014
- **Flipper API**: http://localhost:8086

### Database Connections
- **MariaDB**: localhost:3307 (user: root, password: from .env)
- **ScyllaDB**: localhost:9042
- **MongoDB**: localhost:27017
- **CockroachDB**: localhost:26257
- **Redis**: localhost:6380

## Initial Data Setup

### 1. Item Database Population
```bash
# The items service should automatically populate item data
# Check the items service logs
docker-compose logs items

# Manually trigger item update if needed
curl -X POST http://localhost:5014/api/items/update
```

### 2. Auction Data Indexing
```bash
# The indexer should start collecting auction data automatically
# Monitor indexing progress
docker-compose logs -f indexer

# Check indexed auction count
curl http://localhost:1234/api/auctions/count
```

### 3. User Account Setup
```bash
# Create admin user (if user management is implemented)
curl -X POST http://localhost:1234/api/users/admin \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123","role":"admin"}'
```

## Troubleshooting

### Common Issues

#### Services Not Starting
```bash
# Check Docker resources
docker system df
docker system prune -f

# Check service dependencies
docker-compose logs [service-name]

# Restart specific service
docker-compose restart [service-name]
```

#### Database Connection Issues
```bash
# Check database status
docker-compose exec mariadb mysql -u root -p -e "SHOW DATABASES;"

# Reset database if needed
docker-compose down
docker volume rm cofl-dev_skyblockdb_data
docker-compose up -d mariadb
```

#### Memory Issues
```bash
# Check memory usage
docker stats

# Reduce service resource limits in docker-compose.yml
# Or increase system memory
```

#### Port Conflicts
```bash
# Check port usage
netstat -tulpn | grep [port]

# Change ports in docker-compose.yml if needed
```

### Performance Optimization

#### For Development
```bash
# Reduce resource limits for development
# Edit docker-compose.yml and reduce memory/CPU limits
# Disable non-essential services
```

#### For Production
```bash
# Increase resource limits
# Enable all monitoring services
# Set up proper backup strategies
# Configure log rotation
```

## Development Workflow

### Making Changes
1. **Stop affected services**: `docker-compose stop [service-name]`
2. **Make code changes** in the service directory
3. **Rebuild service**: `docker-compose build [service-name]`
4. **Start service**: `docker-compose up -d [service-name]`
5. **Check logs**: `docker-compose logs -f [service-name]`

### Database Changes
1. **Create migration scripts** in the service directory
2. **Apply migrations** using service-specific tools
3. **Update docker-compose.yml** if schema changes affect other services

### Frontend Development
```bash
# For faster frontend development
cd hypixel-react
npm install
npm start  # Runs on localhost:3001 with hot reload

# Update API endpoints to point to Docker services
# Edit .env.local in hypixel-react directory
```

## Monitoring and Maintenance

### Health Checks
```bash
# Create health check script
cat > health-check.sh << 'EOF'
#!/bin/bash
echo "Checking COFL system health..."

# Check critical services
services=("mariadb" "redis" "kafka" "api" "indexer" "frontend")
for service in "${services[@]}"; do
    if docker-compose ps $service | grep -q "Up"; then
        echo "✓ $service is running"
    else
        echo "✗ $service is not running"
    fi
done

# Check API endpoints
if curl -s http://localhost:1234/health > /dev/null; then
    echo "✓ API is responding"
else
    echo "✗ API is not responding"
fi

if curl -s http://localhost:3000 > /dev/null; then
    echo "✓ Frontend is responding"
else
    echo "✗ Frontend is not responding"
fi
EOF

chmod +x health-check.sh
./health-check.sh
```

### Log Management
```bash
# View logs for all services
docker-compose logs

# Follow logs for specific service
docker-compose logs -f [service-name]

# Save logs to file
docker-compose logs > cofl-logs.txt
```

### Backup Procedures
```bash
# Backup databases
docker-compose exec mariadb mysqldump -u root -p --all-databases > backup.sql

# Backup volumes
docker run --rm -v cofl-dev_skyblockdb_data:/data -v $(pwd):/backup alpine tar czf /backup/db-backup.tar.gz /data
```

## Security Considerations

### Production Deployment
1. **Change default passwords** in all services
2. **Use environment variables** for sensitive data
3. **Enable SSL/TLS** for external connections
4. **Set up firewall rules** to restrict access
5. **Regular security updates** for all components
6. **Monitor access logs** for suspicious activity

### Network Security
```bash
# Create custom Docker network for isolation
docker network create cofl-network

# Update docker-compose.yml to use custom network
# Add network configuration to all services
```

This setup guide provides a comprehensive walkthrough for getting the COFL system up and running. Follow the steps in order and refer to the troubleshooting section if you encounter any issues.
