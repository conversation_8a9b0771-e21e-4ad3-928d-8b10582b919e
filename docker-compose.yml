# COFL - Hypixel Skyblock Auction Flipper
# Docker Compose configuration for development environment
version: '3.8'

services:
  mariadb:
    image: 'docker.io/bitnami/mariadb:10.11-debian-11'
    volumes:
      - 'skyblockdb_data:/bitnami/mariadb'
    ports:
      - '3307:3306'
    environment:
      - MARIADB_ROOT_PASSWORD=${MARIADB_ROOT_PASSWORD:-cofl_secure_password_2024}
      - MARIADB_EXTRA_FLAGS=--innodb-buffer-pool-size=500M --key-buffer-size=1G --connect-timeout=101
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p${MARIADB_ROOT_PASSWORD:-cofl_secure_password_2024}"]
      timeout: 20s
      retries: 10
  phpmyadmin:
    image: 'docker.io/bitnami/phpmyadmin:5-debian-10'
    ports:
      - '8038:8080'
      - '4438:8443'
    depends_on:
      mariadb:
        condition: service_healthy
    environment:
      - PMA_HOST=mariadb
      - PMA_PORT=3306
      - PMA_USER=root
      - PMA_PASSWORD=${MARIADB_ROOT_PASSWORD:-cofl_secure_password_2024}
  indexer:
    build: SkyIndexer
    ports:
    - '8007:8008'
    restart: always
    depends_on:
      - mariadb
      - kafka
      - redis
      - items
    environment: 
      FRONTEND_PROD: "frontend"
      JAEGER_SERVICE_NAME: "hypixel-skyblock-core"
      JAEGER_AGENT_HOST: "jaeger"
      KAFKA_HOST: "kafka:9092"
      SKYCOMMANDS_HOST: "commands:8008"
  api:
    build: SkyApi
    restart: always 
    ports:
    - '1234:8000'
    environment:
      ITEMS_BASE_URL: http://items:8003
      DBCONNECTION: server=mariadb;database=test;user=root;password=takenfrombitnami; convert zero datetime=True;Charset=utf8; Connect Timeout=30
  commands:
    build: SkyCommands
    environment: 
      - JAEGER_AGENT_HOST=jaeger
      - KAFKA_HOST=kafka:9092
    ports:
    - "8008:8008"
    restart: always
    depends_on:
      - mariadb
      - kafka
      - redis
      - frontend
  redis:
    image: "redis:alpine"
    environment:
      - REDIS_REPLICATION_MODE=master
    ports:
      - "6380:6379"
  imgproxy:
    image: darthsim/imgproxy
    environment:
      - IMGPROXY_ALLOWED_SOURCES=https://sky.shiiyu.moe/,https://skycrypt.coflnet.com,https://mc-heads.net/,https://crafatar.com/,https://static.coflnet.com/
      - IMGPROXY_BIND=:80
      - IMGPROXY_MAX_ANIMATION_FRAMES=100
    ports:
      - 8234:80
  frontend:
    image: node:20.10.0-bullseye
    working_dir: /app
    command: bash -c "npm install && npm run dev"
    restart: always
    depends_on:
      - api
    environment:
      - NODE_ENV=development
      - REACT_APP_API_ENDPOINT=http://localhost:1234/api
      - REACT_APP_WEBSOCKET_ENDPOINT=ws://localhost:8021/skyblock
    volumes:
      - ./hypixel-react:/app
    ports:
      - "3000:3000"
  static:
    image: nginx:alpine
    ports:
      - "8086:80"
    volumes:
      - ./static:/usr/share/nginx/html
    restart: always
  mayor:
    build:
      context: SkyMayor
      dockerfile: Dockerfile
    ports:
      - '5026:8000'
    restart: always
    depends_on:
      - mariadb
      - kafka
      - redis
      - jaeger
      - mongo
      # Add any other dependencies if required
    environment:
      # Define any environment variables your `mayor` service needs
      - MARIADB_HOST=mariadb
      - MARIADB_USER=root
      - MARIADB_PASSWORD=takenfrombitnami
      - KAFKA_HOST=kafka:9092
      - REDIS_HOST=redis
      - JAEGER_AGENT_HOST=jaeger
      - MONGO_URL=*****************************************/my_database
      # Add other necessary environment variables here
  playername:
    build: SkyPlayerName
    restart: always
    depends_on:
      - mariadb
      - jaeger
    environment:
      - ASPNETCORE_URLS=http://+:8000
      - DBCONNECTION=server=mariadb;user=root;password=takenfrombitnami;database=test
      - MARIADB_VERSION=10.5.5
      - JAEGER_AGENT_HOST=jaeger
      - JAEGER_SERVICE_NAME=sky-playername
    ports:
      - '5018:8000'
  jaeger:
    image: "jaegertracing/all-in-one:1.35"
    ports:
      - "16686:16686"
      - "14268:14268"
      - "14250:14250"
      - "5775:5775"
      - "6831:6831"
      - "6832:6832"
      - "5778:5778"
      - "14269:14269"
      - "9411:9411"
    environment:
      - COLLECTOR_ZIPKIN_HOST_PORT=:9411
  zookeeper:
    image: docker.io/bitnami/zookeeper:3.7
    ports:
      - "2181:2181"
    volumes:
      - "zookeeper_data:/bitnami"
    environment:
      - ALLOW_ANONYMOUS_LOGIN=yes
  kafka:
    image: docker.io/bitnami/kafka:2
    ports:
      - "9092:9092"
    volumes:
      - "kafka_data:/bitnami"
    environment:
      - KAFKA_CFG_ZOOKEEPER_CONNECT=zookeeper:2181
      - ALLOW_PLAINTEXT_LISTENER=yes
    depends_on:
      - zookeeper
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2500M
  mcconnect:
    build: SkyMcConnect
    ports:
    - "8913:8000"
  modcommands:
    build: SkyModCommands
    environment: 
      - JAEGER_AGENT_HOST=jaeger
      - KAFKA_HOST=kafka:9092
      - DBConnection=server=mariadb;database=test;user=read;password=read; convert zero datetime=True;Charset=utf8; Connect Timeout=30; SslMode=none
      - CASSANDRA__HOSTS=scylla
    ports:
    - "8009:8008"
    restart: always
    depends_on:
     - commands
     - mcconnect
     - fliptracker
     - kafka
     - items
     - scylla
  items:
    build: SkyItems
    restart: always
    depends_on:
      - mariadb
      - redis
      - jaeger
    environment:
      - ASPNETCORE_URLS=http://+:8003
      - DBCONNECTION=server=mariadb;user=root;password=cofl_secure_password_2024;database=test;convert zero datetime=True;Charset=utf8;Connect Timeout=30
      - REDIS_HOST=redis
      - JAEGER_AGENT_HOST=jaeger
      - JAEGER_SERVICE_NAME=sky-items
    ports:
     - '5014:8003'
  auctions:
    build: SkyAuctions
    restart: always
    depends_on:
      - mariadb
      - redis
      - kafka
      - jaeger
      - scylla
    environment:
      - ASPNETCORE_URLS=http://+:8000
      - DBCONNECTION=server=mariadb;user=root;password=takenfrombitnami;database=test
      - REDIS_HOST=redis
      - JAEGER_AGENT_HOST=jaeger
      - JAEGER_SERVICE_NAME=sky-auctions
      - KAFKA__BROKERS=kafka:9092
      - KAFKA__REPLICATION_FACTOR=1
      - CASSANDRA__HOSTS=scylla
      - CASSANDRA__USER=cassandra
      - CASSANDRA__PASSWORD=cassandra
      - CASSANDRA__KEYSPACE=sky_auctions
      - CASSANDRA__REPLICATION_CLASS=SimpleStrategy
      - CASSANDRA__REPLICATION_FACTOR=1
      - CASSANDRA__X509Certificate_PATHS=
      - CASSANDRA__X509Certificate_PASSWORD=
      - CASSANDRA__X509Certificate_VALIDATION_PATH=
    ports:
      - '5031:8000'
  static_s3:
    build: static_s3
    environment:
      - S3_HOST=minio:9000
      - ACCESS_KEY=sniper
      - SECRET_KEY=snipersniper
      - BUCKET_NAME=static
    ports:
      - '5090:8000'  # Expose your .NET API on port 5000
    depends_on:
      - minio
  fliptracker:
    build: SkyFlipTracker
    restart: always
    environment:
      - CASSANDRA__HOSTS=scylla
      - ASPNETCORE_URLS=http://+:8001
    ports:
      - "5017:8001"
    depends_on:
      - scylla
      - kafka
      - crafts
  cockroachdb:
    image: cockroachdb/cockroach:v21.2.8
    command: start-single-node --insecure
    ports:
      - '26257:26257' # Port for SQL connections
      - '8080:8080'   # Port for the Admin UI
    volumes:
      - cockroachdb_data:/cockroach/cockroach-data
    environment:
      - COCKROACH_DATABASE=proxy
      - COCKROACH_USER=root
    restart: unless-stopped
  playerstate:
    build: SkyPlayerState
    restart: always
    ports:
      - '5015:8000'  # Host port 5015 mapped to container port 8000 (optional)
    environment:
      - ASPNETCORE_URLS=http://+:8000
      - JAEGER_AGENT_HOST=jaeger
      - CASSANDRA__HOSTS=scylla
      - KAFKA__BROKERS=kafka:9092
      - SCYLLA__HOSTS=scylla
      - MONGO__CONNECTIONSTRING=*****************************************
    depends_on:
      - scylla
      - kafka
      - mongo
      - jaeger
  proxy:
    build: SkyProxy
    depends_on:
      - cockroachdb
      - redis
      - kafka
      - jaeger
    environment:
      - ASPNETCORE_URLS=http://+:8000
      - DB_CONNECTION=host=cockroachdb;database=proxy;user id=root;password=;port=26257
      - REDIS_HOST=redis
      - JAEGER_AGENT_HOST=jaeger
      - KAFKA__BROKERS=kafka:9092
    ports:
      - '5029:8000'
  crafts:
    build: SkyCrafts
    ports:
      - '5009:8000'
  profile:
    build: SkyPlayerInfo
    ports:
      - "8087:8000"
  sniper:
    build: SkySniper
    restart: always 
    ports:
      - "8086:8000"
    depends_on:
      - minio
    environment:
      - S3_HOST=minio:9000
      - ACCESS_KEY=sniper
      - SECRET_KEY=snipersniper
      - BUCKET_NAME=sky-sniper
  # subscription:
  #  build: SkySubscriptions
  updater:
    build: SkyUpdater
    depends_on:
      - jaeger
    environment: 
      SLOWDOWN_MS: 800
    ports:
      - "5002:8000"
  filter:
    build: SkyFilter
    ports:
      - '5027:8000'  # Host Port : Container Port
    environment:
      - ASPNETCORE_ENVIRONMENT=Production  # Set the environment as needed
      - S3_HOST=minio:9000  # MinIO service hostname and port
      - ACCESS_KEY=sniper  # MinIO Access Key
      - SECRET_KEY=snipersniper  # MinIO Secret Key
      - BUCKET_NAME=sky-sniper  # MinIO Bucket Name
      - DATABASE_CONNECTION=server=mariadb;database=test;user=root;password=takenfrombitnami;convert zero datetime=True;Charset=utf8;Connect Timeout=30
      - REDIS_HOST=redis  # Redis service hostname
      - KAFKA_HOST=kafka:9092  # Kafka service hostname and port
      - OTHER_ENV_VARS=values  # Add other necessary environment variables here
    volumes:
      - filter_data:/data  # Mount the /data volume for persistence
      - ./ah/files:/app/ah/files  # Mount local directory to container
    depends_on:
      - mariadb
      - minio
      - redis
      - kafka
      # Add other dependencies if `SkyFilter` interacts with other services
    restart: always  # Ensure the service restarts on failure
    networks:
      - default  # Use the default network or specify a custom one
  bfcs:
    build: SkyBFCS  # Ensure this path points to your BFCS Dockerfile
    restart: always
    ports:
      - "8021:8000"   # HTTP API port
      - "8888:8888"   # WebSocket port (as per Program.cs)
    environment:
      DB_CONNECTION: "server=mariadb;user=root;password=takenfrombitnami;database=base"
      JAEGER_SAMPLER_TYPE: "ratelimiting"
      FLIP_REDIS_OPTIONS: "redis"
      JAEGER_SAMPLER_PARAM: "2"
      MARIADB_VERSION: "10.5.5"
      JAEGER_AGENT_HOST: "jaeger"
      JAEGER_SERVICE_NAME: "sky-bfcs"
      SOCKET_BASE_URL: "ws://***************:8888"
      SNIPER_TRANSFER_TOKEN: "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.-"
      SNIPER_BASE_URL: "http://sniper:8000"
      ITEMS_BASE_URL: "http://items:8003"
      UPDATER_BASE_URL: "http://updater:8000"
      MINIO_SECRET: "snipersniper"
      MINIO_KEY: "sniper"
      MINIO_HOST: "minio:9000"
      SNIPER_CONNECT_TOKEN: ""
      TLS__CERTIFICATE_LOCATION: ""
      TLS__KEY_LOCATION: ""
      TLS__CA_LOCATION: ""
      # Add any additional environment variables if required
    depends_on:
      - mariadb
      - kafka
      - redis
      - jaeger
      - minio
    volumes:
      - "C:/Users/<USER>/Desktop/Skyblock/HypixelSkyblock-master/SkyBFCS:/certs"
    networks:
      - default  # Ensure it joins the same network as other services
  flipper:
    build: SkyFlipper
    restart: always
    environment:
      - ITEMS_BASE_URL=http://items:8003
      - ASPNETCORE_URLS=http://+:8002
    ports:
      - "5018:8002"
    depends_on:
      - items
      - fliptracker
  payment:
    build: Payments
    depends_on:
      - mariadb
    ports:
      - "8089:8000"
  settings:
    build: SkySettings
    restart: always 
    depends_on:
      - scylla
    ports:
     - '5004:8000'
    environment:
      - CASSANDRA__HOSTS=scylla
  bazaar:
    build: SkyBazaar
    restart: always 
    ports:
    - '5011:8000'
    environment:
      CASSANDRA__HOSTS: scylla
  scylla:
    image: scylladb/scylla
    container_name: scylla
    volumes:
      - 'scylla_data:/var/lib/scylla'
    ports:
      - '7000:7000'
      - '9042:9042'
    command: --smp 1
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 8000M
  mongo:
    image: bitnami/mongodb:7.0
    environment:
      - MONGODB_USERNAME=my_user
      - MONGODB_PASSWORD=password123
      - MONGODB_ROOT_PASSWORD=password123
      - MONGODB_DATABASE=my_database
    volumes:
      - 'mongodb_data:/bitnami'
    ports:
      - '27017:27017'
  minio:
    image: minio/minio
    command: server --console-address ":9001" /data
    depends_on:
      - "kafka"
    expose:
      - 9000
      - 9001
    ports:
      - '9000:9000'
      - '9001:9001'
    environment:
      MINIO_ROOT_USER: minio
      MINIO_ROOT_PASSWORD: minio123
    volumes:
      - 'minio_data:/data'
volumes:
  zookeeper_data:
    driver: local
  kafka_data:
    driver: local
  skyblockdb_data:
    driver: local
  mongodb_data:
    driver: local
  scylla_data:
    driver: local
  cockroachdb_data:
    driver: local
  minio_data:
    driver: local
  filter_data:
    driver: local
