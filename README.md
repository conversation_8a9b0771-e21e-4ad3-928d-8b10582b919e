# COFL - Hypixel Skyblock Auction Flipper

COFL (Coflnet) is a comprehensive microservices-based auction flipper system for Hypixel Skyblock. It provides real-time auction tracking, price analysis, flip recommendations, and automated trading capabilities to help players maximize their profits in the Skyblock auction house.

## 🏗️ Architecture Overview

COFL is built using a microservices architecture with the following key components:

### Core Services
- **SkyApi** - Main API gateway and core functionality
- **SkyIndexer** - Auction data indexing and processing
- **SkyAuctions** - Auction house data management
- **SkyFlipper** - Flip calculation and recommendation engine
- **SkySniper** - Automated auction sniping service
- **SkyFilter** - Advanced filtering and search capabilities

### Data Services
- **SkyItems** - Item database and metadata management
- **SkyBazaar** - Bazaar price tracking and analysis
- **SkyCrafts** - Crafting recipes and profit calculations
- **SkyFlipTracker** - Flip history and performance tracking

### User Services
- **SkyPlayerInfo** - Player profile and statistics
- **SkyPlayerName** - Player name resolution service
- **SkyPlayerState** - Real-time player state tracking
- **SkySettings** - User preferences and configuration

### Communication Services
- **SkyCommands** - Command processing and execution
- **SkyModCommands** - Mod-specific command handling
- **SkyMcConnect** - Minecraft client connection management
- **SkyBFCS** - Best Flip Communication Service (WebSocket)

### Utility Services
- **SkyUpdater** - Data update coordination
- **SkyMayor** - Mayor election tracking
- **SkyProxy** - Request proxying and load balancing
- **Payments** - Payment processing and premium features
- **static_s3** - Static file serving

### Infrastructure Components
- **MariaDB** - Primary relational database
- **ScyllaDB** - High-performance NoSQL database
- **CockroachDB** - Distributed SQL database for proxy service
- **MongoDB** - Document database for flexible data
- **Redis** - Caching and session management
- **Kafka** - Message streaming and event processing
- **MinIO** - S3-compatible object storage
- **Jaeger** - Distributed tracing and monitoring

## 🚀 Quick Start

### Prerequisites
- Docker and Docker Compose
- Git
- At least 16GB RAM (recommended)
- 50GB+ free disk space

### Setup Instructions

1. **Clone the main repository structure:**
   ```bash
   # Create a parent directory
   mkdir cofl-dev && cd cofl-dev
   
   # Copy the docker-compose.yml to this directory
   # Then clone all required repositories:
   ```

2. **Clone all microservices:**
   ```bash
   git clone https://github.com/Coflnet/SkyApi.git
   git clone https://github.com/Coflnet/SkyIndexer.git
   git clone https://github.com/Coflnet/SkyAuctions.git
   git clone https://github.com/Coflnet/SkyFlipper.git
   git clone https://github.com/Coflnet/SkySniper.git
   git clone https://github.com/Coflnet/SkyFilter.git
   git clone https://github.com/Coflnet/SkyItems.git
   git clone https://github.com/Coflnet/SkyBazaar.git
   git clone https://github.com/Coflnet/SkyCrafts.git
   git clone https://github.com/Coflnet/SkyFlipTracker.git
   git clone https://github.com/Coflnet/SkyPlayerInfo.git
   git clone https://github.com/Coflnet/SkyPlayerName.git
   git clone https://github.com/Coflnet/SkyPlayerState.git
   git clone https://github.com/Coflnet/SkySettings.git
   git clone https://github.com/Coflnet/SkyCommands.git
   git clone https://github.com/Coflnet/SkyModCommands.git
   git clone https://github.com/Coflnet/SkyMcConnect.git
   git clone https://github.com/Coflnet/SkyBFCS.git
   git clone https://github.com/Coflnet/SkyUpdater.git
   git clone https://github.com/Coflnet/SkyMayor.git
   git clone https://github.com/Coflnet/SkyProxy.git
   git clone https://github.com/Coflnet/Payments.git
   git clone https://github.com/Coflnet/static_s3.git
   git clone https://github.com/Coflnet/hypixel-react.git
   ```

3. **Start the services:**
   ```bash
   docker-compose up -d
   ```

4. **Access the services:**
   - Frontend: http://localhost:3000
   - API: http://localhost:1234
   - Jaeger UI: http://localhost:16686
   - MinIO Console: http://localhost:9001
   - phpMyAdmin: http://localhost:8038

## 📊 Service Ports

| Service | Port | Description |
|---------|------|-------------|
| Frontend | 3000 | React web interface |
| API | 1234 | Main API gateway |
| Indexer | 8007 | Auction indexing service |
| Commands | 8008 | Command processing |
| ModCommands | 8009 | Mod command handling |
| Items | 5014 | Item database API |
| PlayerState | 5015 | Player state tracking |
| FlipTracker | 5017 | Flip tracking API |
| PlayerName | 5018 | Player name resolution |
| BFCS | 8021/8888 | WebSocket communication |
| Mayor | 5026 | Mayor tracking API |
| Filter | 5027 | Filtering service |
| Proxy | 5029 | Request proxy |
| Auctions | 5031 | Auction data API |
| Settings | 5004 | User settings API |
| Crafts | 5009 | Crafting API |
| Bazaar | 5011 | Bazaar data API |
| Sniper | 8086 | Auction sniping |
| Profile | 8087 | Player profiles |
| Payment | 8089 | Payment processing |
| Static S3 | 5090 | Static file serving |
| Updater | 5002 | Data updates |
| McConnect | 8913 | MC client connection |

## 🔧 Configuration

### Environment Variables
Key environment variables are configured in the docker-compose.yml file:

- **Database connections** - MariaDB, ScyllaDB, CockroachDB, MongoDB
- **Message queues** - Kafka brokers and topics
- **Caching** - Redis configuration
- **Storage** - MinIO S3 credentials
- **Monitoring** - Jaeger tracing endpoints
- **Service discovery** - Internal service URLs

### Database Setup
The system uses multiple databases:
- **MariaDB**: Primary relational data (auctions, players, items)
- **ScyllaDB**: High-performance time-series data (prices, flip history)
- **CockroachDB**: Distributed proxy data
- **MongoDB**: Flexible document storage
- **Redis**: Caching and real-time data

## 🔍 Monitoring & Debugging

- **Jaeger**: Distributed tracing at http://localhost:16686
- **Database Admin**: phpMyAdmin at http://localhost:8038
- **Storage Admin**: MinIO console at http://localhost:9001
- **Logs**: Use `docker-compose logs [service-name]` for service logs

## 🤝 Contributing

1. Fork the relevant repository from https://github.com/Coflnet
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📚 Documentation

Each service directory contains its own README.md with specific documentation:
- Service-specific setup instructions
- API endpoints and usage
- Database schemas
- Important code locations
- Development guidelines

## 🔗 Useful Links

- **Organization**: https://github.com/Coflnet
- **Main Website**: https://coflnet.com
- **Discord**: Join the community for support and updates

## ⚠️ Important Notes

- Ensure adequate system resources (16GB+ RAM recommended)
- Services have complex interdependencies - start all at once
- Initial startup may take several minutes for database initialization
- Some services require external API keys for full functionality

## 📄 License

This project is part of the Coflnet ecosystem. Check individual repositories for specific license information.
