{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "AllowedHosts": "*", "DB_CONNECTION": "host=localhost;database=proxy;user id=root;password=;port=26257", "JAEGER_SAMPLER_TYPE": "ratelimiting", "REDIS_HOST": "redis", "TOPICS": {"NAME_UPDATE_REQUEST": "sky-name-update-request", "USER_AH_UPDATE": "sky-user-ah-update", "SOLD_AUCTION": "sky-soldauction", "NEW_AUCTION": "sky-newauction"}, "KAFKA": {"BROKERS": "kafka:9092", "USERNAME": "", "PASSWORD": "", "TLS": {"CERTIFICATE_LOCATION": "", "CA_LOCATION": "", "KEY_LOCATION": ""}, "REPLICATION_FACTOR": "3"}, "JAEGER_SAMPLER_PARAM": "2", "MARIADB_VERSION": "10.5.5", "JAEGER_AGENT_HOST": "jaeger", "JAEGER_SERVICE_NAME": "sky-proxy"}