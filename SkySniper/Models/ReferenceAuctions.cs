using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using Coflnet.Sky.Sniper.Services;
using MessagePack;
using Newtonsoft.Json;

namespace Coflnet.Sky.Sniper.Models
{
    [MessagePackObject]
    public class ReferenceAuctions
    {
        [Key(0)]
        public long Price;
        [Key(1)]
        public ConcurrentQueue<ReferencePrice> References = new ConcurrentQueue<ReferencePrice>();
        [Key(2)]
        [Obsolete("replaed by Lbins ")]
        [JsonIgnore]
        public ReferencePrice LastLbin;
        /// <summary>
        /// Second lowest bin, used if the lowest bin got sold
        /// </summary>
        [IgnoreMember]
        [Obsolete("replaed by <PERSON><PERSON><PERSON> ", true)]
        [JsonIgnore]
        public ReferencePrice SecondLbin;
        /// <summary>
        /// The day of the oldest used reference for <see cref="Price"/>
        /// </summary>
        [Key(4)]
        public short OldestRef;
        [Key(5)]
        public List<ReferencePrice> Lbins = new();
        [Key(6)]
        public short HitsSinceCalculating = 0;
        [IgnoreMember]
        public short StonksHits = 0;
        [Key(7)]
        public byte Volatility = 0;
        [IgnoreMember]
        [JsonIgnore]
        public ReferencePrice Lbin => Lbins?.FirstOrDefault() ?? default;

        [IgnoreMember]
        [JsonIgnore]
        public short DeduplicatedReferenceCount;
        [IgnoreMember]
        public long RiskyEstimate;
        [IgnoreMember]
        public long TimeToSell;

        private float _volume;
        [IgnoreMember]
        public float Volume
        {
            get
            {
                if (_volume != 0)
                    return _volume;
                return (float)(References.TryPeek(out ReferencePrice price)
                    ? (float)References.Count / (SniperService.GetDay() - price.Day + 1)
                    : 0);
            }
            set => _volume = value;
        }
    }

}