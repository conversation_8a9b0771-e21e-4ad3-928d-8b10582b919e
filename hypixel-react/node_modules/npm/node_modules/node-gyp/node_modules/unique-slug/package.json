{"name": "unique-slug", "version": "3.0.0", "description": "Generate a unique character string suitible for use in files and URLs.", "main": "lib/index.js", "scripts": {"test": "tap", "lint": "eslint \"**/*.js\"", "postlint": "template-oss-check", "template-oss-apply": "template-oss-apply --force", "lintfix": "npm run lint -- --fix", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "snap": "tap", "posttest": "npm run lint"}, "keywords": [], "author": "GitHub Inc.", "license": "ISC", "devDependencies": {"@npmcli/eslint-config": "^3.1.0", "@npmcli/template-oss": "3.5.0", "tap": "^16.3.0"}, "repository": {"type": "git", "url": "https://github.com/npm/unique-slug.git"}, "dependencies": {"imurmurhash": "^0.1.4"}, "files": ["bin/", "lib/"], "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "3.5.0"}}