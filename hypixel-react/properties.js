let properties = {}
let isSSR = typeof window === 'undefined'

// Force use of local services only - no external dependencies
const useLocalServices = true; // Using local APIs only

properties = {
    commandEndpoint:
        isSSR || window.location.host.startsWith('localhost') || window.location.hostname.includes('pr-env-sky-')
            ? (useLocalServices ? 'http://localhost:1234/command' : 'https://sky.coflnet.com/command')
            : '/command',
    apiEndpoint:
        isSSR || window.location.host.startsWith('localhost') || window.location.hostname.includes('pr-env-sky-')
            ? (useLocalServices ? 'http://localhost:1234/api' : 'https://sky.coflnet.com/api')
            : '/api',
    websocketEndpoint: isSSR
            ? 'ws://localhost:1234/skyblock'
            : `ws://${window.location.hostname}:1234/skyblock`,
    refLink: useLocalServices ? 'http://localhost:3000/refed' : 'https://sky.coflnet.com/refed',
    websocketOldEndpoint: isSSR
            ? 'ws://localhost:1234/skyblock'
            : `ws://${window.location.hostname}:1234/skyblock`,
    feedbackEndpoint: 'http://localhost:1234/api/feedback/',
    isTestRunner: process.env.TEST_RUNNER === 'true' || false
}

export default properties
