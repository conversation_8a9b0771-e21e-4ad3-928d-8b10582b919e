let properties = {}
let isSSR = typeof window === 'undefined'

// Temporary fallback to production for working demo while local services are being set up
// TODO: Switch back to local once all services are properly configured
const useLocalServices = false; // Set to true once local APIs are working

properties = {
    commandEndpoint:
        isSSR || window.location.host.startsWith('localhost') || window.location.hostname.includes('pr-env-sky-')
            ? (useLocalServices ? 'http://localhost:1234/command' : 'https://sky.coflnet.com/command')
            : '/command',
    apiEndpoint:
        isSSR || window.location.host.startsWith('localhost') || window.location.hostname.includes('pr-env-sky-')
            ? (useLocalServices ? 'http://localhost:1234/api' : 'https://sky.coflnet.com/api')
            : '/api',
    websocketEndpoint: isSSR || window.location.host === 'localhost:8008'
            ? 'ws://localhost:8008/skyblock'
            : 'wss://sky.coflnet.com/skyblock',
    refLink: useLocalServices ? 'http://localhost:3000/refed' : 'https://sky.coflnet.com/refed',
    websocketOldEndpoint: 'wss://skyblock-backend.coflnet.com/skyblock',
    feedbackEndpoint: useLocalServices ? 'http://localhost:1234/api/feedback/' : 'https://feedback.coflnet.com/api/',
    isTestRunner: process.env.TEST_RUNNER === 'true' || false
}

export default properties
