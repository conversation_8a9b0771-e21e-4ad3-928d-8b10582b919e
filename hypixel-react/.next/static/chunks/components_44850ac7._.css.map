{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/FlipTracking/FlipTracking.module.css"], "sourcesContent": [".list {\n    display: flex;\n    flex-wrap: wrap;\n    justify-content: space-between;\n    flex-direction: initial;\n    width: 100%;\n}\n\n.listGroupItem {\n    border-radius: 20px !important;\n    width: 100%;\n    margin-bottom: 15px;\n}\n\n.noAuctionFound {\n    margin-right: auto;\n    margin-left: auto;\n    width: max-content;\n    text-align: center;\n    margin-top: 5vh;\n}\n\n.profitNumberCard {\n    width: 45%;\n    text-align: center;\n    border-color: transparent !important;\n    cursor: pointer;\n}\n\n.profitNumberHeader {\n    border-radius: 20px !important;\n}\n\n.profitNumberHeader:hover {\n    background-color: #3a3a3a;\n}\n\n@media all and (min-width: 1200px) {\n    .listGroupItem {\n        width: 49%;\n    }\n}\n\n.datePicker {\n    margin-right: 15px\n}\n\n.filterContainer{\n    display: flex;\n    align-items: center;\n}\n\n.rangeFilter {\n    width: 200px;\n}\n\n.itemFilterContainer:has(:global(.show.dropdown-menu)) {\n    z-index: 12;\n    position: relative;\n    height: 38px;\n}\n\n.multiSearch {\n    flex: 1;\n}\n\n.noPremiumInfoText{\n    font-size: small;\n}\n\n.topContainer {\n    display: flex;\n    justify-content: space-between;\n    margin-bottom: 20px;\n}\n\n.filterLabel {\n    min-width: 100px;\n}\n\n.filterValueField {\n    max-width: 200px;\n}\n\n@media all and (max-width: 768px) {\n    .noPremiumInfoText {\n        float: none;\n        text-align: center;\n    }\n    .topContainer {\n        display: flex;\n        flex-direction: column-reverse;\n        margin-bottom: 20px;\n        gap: 10px;\n    }\n}"], "names": [], "mappings": "AAAA;;;;;;;;AAQA;;;;;;AAMA;;;;;;;;AAQA;;;;;;;AAOA;;;;AAIA;;;;AAIA;EACI;;;;;AAKJ;;;;AAIA;;;;;AAKA;;;;AAIA;;;;;;AAMA;;;;AAIA;;;;AAIA;;;;;;AAMA;;;;AAIA;;;;AAIA;EACI;;;;;EAIA", "debugId": null}}, {"offset": {"line": 101, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/GoogleSignIn/GoogleSignIn.module.css"], "sourcesContent": ["/* Google shows a white box if the color-scheme is black for some reason */\n.googleButton{\n    width: 250px;\n    color-scheme: light;\n}"], "names": [], "mappings": "AACA", "debugId": null}}, {"offset": {"line": 108, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/ShowMoreText/ShowMoreText.module.css"], "sourcesContent": [".textContainer {\n    position: relative;\n    overflow: hidden;\n}\n  \n.textContainer::after {\n    content: '';\n    position: absolute;\n    bottom: 0;\n    left: 0;\n    width: 100%;\n    height: 30px;\n    background: linear-gradient(to bottom, transparent, #252525);\n}\n\n.textContainer.expanded {\n    max-height: none;\n    overflow: visible;\n}\n\n.textContainer.expanded::after {\n    display: none;\n}\n\n.showMoreContainer {\n    width: 100%;\n    position: absolute;\n    bottom: -4px;\n    cursor: pointer;\n    z-index: 2;\n}\n\n.showMoreText {\n    display: flex;\n    justify-content: center;\n    color: lightgrey;\n}"], "names": [], "mappings": "AAAA;;;;;AAKA;;;;;;;;;;AAUA;;;;;AAKA;;;;AAIA;;;;;;;;AAQA", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/Search/Search.module.css"], "sourcesContent": [".searchResultIcon {\n    margin-right: 20px;\n}\n\n.bar {\n    display: flex;\n}\n\n.current {\n    flex-grow: 100;\n    font-size: 1rem;\n}\n\n.search :global(.form-control) {\n    background-color: #303030;\n    color: white;\n    border-color: #222;\n    box-shadow: none;\n}\n\n.search :global(.form-control:focus) {\n    background-color: #303030;\n    color: white;\n    border-color: #222;\n    box-shadow: none;\n}\n\n.searchFormGroup {\n    display: flex;\n    justify-content: center;\n    align-content: center;\n    margin-bottom: 0;\n    border-bottom-width: 0;\n}\n\n.previousSearch {\n    color: #c389f6;\n}\n\n.multiInputfield {\n    color: white;\n    color-scheme: dark;\n}\n\n.multiSearch :global(.rbt-token){\n    background-color: #444;\n    color: white;\n}"], "names": [], "mappings": "AAAA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;;AAQA;;;;AAIA;;;;;AAKA", "debugId": null}}, {"offset": {"line": 199, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/NavBar/NavBar.module.css"], "sourcesContent": [".navClosing {\n    left: -270px;\n    transition: all 0.5s;\n}\n\n.navOpen {\n    left: 0px !important;\n    transition: left 0.5s;\n}\n\n.hamburgerIcon {\n    display: inline;\n    width: 36px;\n    height: 36px;\n    cursor: pointer;\n    margin-right: 12px;\n}\n\n.navBar {\n    position: fixed;\n    left: 0px;\n    top: 0px;\n    bottom: 0px;\n    z-index: 20;\n    font-size: 1rem;\n}\n\n.logo {\n    padding: 24px;\n    font-weight: bold;\n    font-size: 20px;\n    letter-spacing: 1px;\n    overflow: hidden;\n    white-space: nowrap;\n    display: flex;\n    align-items: center;\n}\n\n.navBar :global(#pro-sidebar) {\n    position: absolute;\n    bottom: 0;\n    z-index: 100;\n    left: 0;\n    top: 0;\n    min-height: 100vh;\n    border: none;\n}\n\n.navBar :global(.ps-menu-button):hover {\n    background-color: #505050 !important;\n}\n\n.menuItem {\n    display: block !important\n}"], "names": [], "mappings": "AAAA;;;;;AAKA;;;;;AAKA;;;;;;;;AAQA;;;;;;;;;AASA;;;;;;;;;;;AAWA;;;;;;;;;;AAUA;;;;AAIA", "debugId": null}}, {"offset": {"line": 257, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/OptionsMenu/OptionsMenu.module.css"], "sourcesContent": [".optionsMenu :global(a) {\n    text-decoration: none !important;\n    color: #fff;\n    margin-left: 5px;\n}\n\n.optionsMenu{\n    display: flex;\n    justify-content: flex-end;\n}\n\n.buttonsWrapper {\n    display: inline-block;\n}\n\n@media all and (max-width: 768px) {\n    .dropdown {\n        display: block;\n    }\n\n    .buttonsWrapper {\n        display: none;\n    }\n}\n\n@media all and (min-width: 768px) {\n    .dropdown {\n        display: none;\n    }\n\n    .buttonsWrapper {\n        display: block;\n    }\n}\n"], "names": [], "mappings": "AAAA;;;;;;AAMA;;;;;AAKA;;;;AAIA;EACI;;;;EAIA;;;;;AAKJ;EACI;;;;EAIA", "debugId": null}}]}