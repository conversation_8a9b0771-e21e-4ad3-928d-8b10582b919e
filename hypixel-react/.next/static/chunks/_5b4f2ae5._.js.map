{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///app/utils/Hooks.tsx"], "sourcesContent": ["import { Dispatch, SetStateAction, useCallback, useEffect, useRef, useState } from 'react'\nimport api from '../api/ApiHelper'\nimport { CUSTOM_EVENTS } from '../api/ApiTypes.d'\nimport { getCurrentCoflCoins, subscribeToCoflcoinChange } from './CoflCoinsUtils'\nimport { isClientSideRendering } from './SSRUtils'\nimport { getURLSearchParam } from './Parser/URLParser'\nimport { useRouter } from 'next/navigation'\n\nexport function useForceUpdate() {\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    const [update, setUpdate] = useState(0)\n    return () => setUpdate(update => update + 1)\n}\n\nexport function useSwipe(onSwipeUp?: Function, onSwipeRight?: Function, onSwipeDown?: Function, onSwipeLeft?: Function) {\n    if (!isClientSideRendering()) {\n        return\n    }\n\n    document.addEventListener('touchstart', handleTouchStart, false)\n    document.addEventListener('touchmove', handleTouchMove, false)\n\n    var xDown = null\n    var yDown = null\n\n    function getTouches(evt) {\n        return (\n            evt.touches || // browser API\n            evt.originalEvent.touches\n        ) // jQuery\n    }\n\n    function handleTouchStart(evt) {\n        const firstTouch = getTouches(evt)[0]\n        xDown = firstTouch.clientX\n        yDown = firstTouch.clientY\n    }\n\n    function handleTouchMove(evt) {\n        if (xDown === null || yDown === null) {\n            return\n        }\n\n        var xUp = evt.touches[0].clientX\n        var yUp = evt.touches[0].clientY\n\n        var xDiff = xDown! - xUp\n        var yDiff = yDown! - yUp\n\n        if (Math.abs(xDiff) > Math.abs(yDiff)) {\n            /*most significant*/\n            if (xDiff > 0) {\n                if (onSwipeLeft) {\n                    onSwipeLeft()\n                }\n            } else {\n                if (onSwipeRight) {\n                    onSwipeRight()\n                }\n            }\n        } else {\n            if (yDiff > 0) {\n                if (onSwipeUp) {\n                    onSwipeUp()\n                }\n            } else {\n                if (onSwipeDown) {\n                    onSwipeDown()\n                }\n            }\n        }\n        /* reset values */\n        xDown = null\n        yDown = null\n    }\n\n    return () => {\n        document.removeEventListener('touchstart', handleTouchStart, false)\n        document.removeEventListener('touchmove', handleTouchMove, false)\n    }\n}\n\nexport function useCoflCoins() {\n    const [coflCoins, setCoflCoins] = useState(getCurrentCoflCoins())\n\n    useEffect(() => {\n        let unsubscribe = subscribeToCoflcoinChange(setCoflCoins)\n\n        return () => {\n            unsubscribe()\n        }\n    }, [])\n\n    return coflCoins\n}\n\nexport function useWasAlreadyLoggedIn() {\n    const [wasAlreadyLoggedIn, setWasAlreadyLoggedIn] = useState(false)\n    useEffect(() => {\n        setWasAlreadyLoggedIn(localStorage.getItem('googleId') !== null)\n    }, [])\n\n    return wasAlreadyLoggedIn\n}\n\nexport function useDebounce(value, delay) {\n    const [debouncedValue, setDebouncedValue] = useState(value)\n    useEffect(() => {\n        const handler = setTimeout(() => {\n            setDebouncedValue(value)\n        }, delay)\n        return () => {\n            clearTimeout(handler)\n        }\n    }, [value, delay])\n    return debouncedValue\n}\n\ntype ReadonlyRef<T> = {\n    readonly current: T\n}\n\nexport function useStateWithRef<T>(defaultValue: T): [T, Dispatch<SetStateAction<T>>, ReadonlyRef<T>] {\n    const [state, _setState] = useState(defaultValue)\n    let stateRef = useRef(state)\n\n    const setState: typeof _setState = useCallback((newState: T) => {\n        stateRef.current = newState\n        _setState(newState)\n    }, [])\n\n    return [state, setState, stateRef]\n}\n\nexport function useQueryParamState<T>(key: string, defaultValue: T): [T, Dispatch<SetStateAction<T>>] {\n    const [state, setState] = useState<T>(getDefaultValue() || defaultValue)\n    const router = useRouter()\n\n    function getDefaultValue(): T | undefined {\n        let param = getURLSearchParam(key)\n        if (!param) {\n            return undefined\n        }\n        return JSON.parse(decodeURIComponent(param)) as T\n    }\n\n    function _setState(newState: T) {\n        setState(newState)\n        let urlparams = new URLSearchParams(window.location.search)\n        if (!newState) {\n            urlparams.delete(key)\n        } else {\n            urlparams.set(key, encodeURIComponent(JSON.stringify(newState)))\n        }\n        router.replace(`${window.location.pathname}?${urlparams.toString()}`)\n    }\n\n    return [state, _setState]\n}\n\nexport function useIsMobile() {\n    let [isMobile, setIsMobile] = useState(false)\n\n    useEffect(() => {\n        setIsMobile(isMobileCheck())\n    }, [])\n\n    function isMobileCheck() {\n        let check = false\n        // eslint-disable-next-line no-useless-escape\n        ;(function (a) {\n            if (\n                /(android|bb\\d+|meego).+mobile|avantgo|bada\\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i.test(\n                    a\n                ) ||\n                /1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\\-(n|u)|c55\\/|capi|ccwa|cdm\\-|cell|chtm|cldc|cmd\\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\\-s|devi|dica|dmob|do(c|p)o|ds(12|\\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\\-|_)|g1 u|g560|gene|gf\\-5|g\\-mo|go(\\.w|od)|gr(ad|un)|haie|hcit|hd\\-(m|p|t)|hei\\-|hi(pt|ta)|hp( i|ip)|hs\\-c|ht(c(\\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\\-(20|go|ma)|i230|iac( |\\-|\\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\\/)|klon|kpt |kwc\\-|kyo(c|k)|le(no|xi)|lg( g|\\/(k|l|u)|50|54|\\-[a-w])|libw|lynx|m1\\-w|m3ga|m50\\/|ma(te|ui|xo)|mc(01|21|ca)|m\\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\\-2|po(ck|rt|se)|prox|psio|pt\\-g|qa\\-a|qc(07|12|21|32|60|\\-[2-7]|i\\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\\-|oo|p\\-)|sdk\\/|se(c(\\-|0|1)|47|mc|nd|ri)|sgh\\-|shar|sie(\\-|m)|sk\\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\\-|v\\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\\-|tdg\\-|tel(i|m)|tim\\-|t\\-mo|to(pl|sh)|ts(70|m\\-|m3|m5)|tx\\-9|up(\\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\\-|your|zeto|zte\\-/i.test(\n                    a.substr(0, 4)\n                )\n            )\n                check = true\n        })(navigator.userAgent || navigator.vendor || (window as any).opera)\n        return check\n    }\n\n    return isMobile\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AAGA;AACA;AACA;AACA;;;;;;;AAEO,SAAS;;IACZ,6DAA6D;IAC7D,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,OAAO,IAAM,UAAU,CAAA,SAAU,SAAS;AAC9C;GAJgB;AAMT,SAAS,SAAS,SAAoB,EAAE,YAAuB,EAAE,WAAsB,EAAE,WAAsB;IAClH,IAAI,CAAC,CAAA,GAAA,qHAAA,CAAA,wBAAqB,AAAD,KAAK;QAC1B;IACJ;IAEA,SAAS,gBAAgB,CAAC,cAAc,kBAAkB;IAC1D,SAAS,gBAAgB,CAAC,aAAa,iBAAiB;IAExD,IAAI,QAAQ;IACZ,IAAI,QAAQ;IAEZ,SAAS,WAAW,GAAG;QACnB,OACI,IAAI,OAAO,IAAI,cAAc;QAC7B,IAAI,aAAa,CAAC,OAAO,EAC3B,SAAS;IACf;IAEA,SAAS,iBAAiB,GAAG;QACzB,MAAM,aAAa,WAAW,IAAI,CAAC,EAAE;QACrC,QAAQ,WAAW,OAAO;QAC1B,QAAQ,WAAW,OAAO;IAC9B;IAEA,SAAS,gBAAgB,GAAG;QACxB,IAAI,UAAU,QAAQ,UAAU,MAAM;YAClC;QACJ;QAEA,IAAI,MAAM,IAAI,OAAO,CAAC,EAAE,CAAC,OAAO;QAChC,IAAI,MAAM,IAAI,OAAO,CAAC,EAAE,CAAC,OAAO;QAEhC,IAAI,QAAQ,QAAS;QACrB,IAAI,QAAQ,QAAS;QAErB,IAAI,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC,QAAQ;YACnC,kBAAkB,GAClB,IAAI,QAAQ,GAAG;gBACX,IAAI,aAAa;oBACb;gBACJ;YACJ,OAAO;gBACH,IAAI,cAAc;oBACd;gBACJ;YACJ;QACJ,OAAO;YACH,IAAI,QAAQ,GAAG;gBACX,IAAI,WAAW;oBACX;gBACJ;YACJ,OAAO;gBACH,IAAI,aAAa;oBACb;gBACJ;YACJ;QACJ;QACA,gBAAgB,GAChB,QAAQ;QACR,QAAQ;IACZ;IAEA,OAAO;QACH,SAAS,mBAAmB,CAAC,cAAc,kBAAkB;QAC7D,SAAS,mBAAmB,CAAC,aAAa,iBAAiB;IAC/D;AACJ;AAEO,SAAS;;IACZ,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,GAAA,2HAAA,CAAA,sBAAmB,AAAD;IAE7D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACN,IAAI,cAAc,CAAA,GAAA,2HAAA,CAAA,4BAAyB,AAAD,EAAE;YAE5C;0CAAO;oBACH;gBACJ;;QACJ;iCAAG,EAAE;IAEL,OAAO;AACX;IAZgB;AAcT,SAAS;;IACZ,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2CAAE;YACN,sBAAsB,aAAa,OAAO,CAAC,gBAAgB;QAC/D;0CAAG,EAAE;IAEL,OAAO;AACX;IAPgB;AAST,SAAS,YAAY,KAAK,EAAE,KAAK;;IACpC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACN,MAAM,UAAU;iDAAW;oBACvB,kBAAkB;gBACtB;gDAAG;YACH;yCAAO;oBACH,aAAa;gBACjB;;QACJ;gCAAG;QAAC;QAAO;KAAM;IACjB,OAAO;AACX;IAXgB;AAiBT,SAAS,gBAAmB,YAAe;;IAC9C,MAAM,CAAC,OAAO,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACpC,IAAI,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAEtB,MAAM,WAA6B,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iDAAE,CAAC;YAC5C,SAAS,OAAO,GAAG;YACnB,UAAU;QACd;gDAAG,EAAE;IAEL,OAAO;QAAC;QAAO;QAAU;KAAS;AACtC;IAVgB;AAYT,SAAS,mBAAsB,GAAW,EAAE,YAAe;;IAC9D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAK,qBAAqB;IAC3D,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,SAAS;QACL,IAAI,QAAQ,CAAA,GAAA,gIAAA,CAAA,oBAAiB,AAAD,EAAE;QAC9B,IAAI,CAAC,OAAO;YACR,OAAO;QACX;QACA,OAAO,KAAK,KAAK,CAAC,mBAAmB;IACzC;IAEA,SAAS,UAAU,QAAW;QAC1B,SAAS;QACT,IAAI,YAAY,IAAI,gBAAgB,OAAO,QAAQ,CAAC,MAAM;QAC1D,IAAI,CAAC,UAAU;YACX,UAAU,MAAM,CAAC;QACrB,OAAO;YACH,UAAU,GAAG,CAAC,KAAK,mBAAmB,KAAK,SAAS,CAAC;QACzD;QACA,OAAO,OAAO,CAAC,GAAG,OAAO,QAAQ,CAAC,QAAQ,CAAC,CAAC,EAAE,UAAU,QAAQ,IAAI;IACxE;IAEA,OAAO;QAAC;QAAO;KAAU;AAC7B;IAxBgB;;QAEG,qIAAA,CAAA,YAAS;;;AAwBrB,SAAS;;IACZ,IAAI,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACN,YAAY;QAChB;gCAAG,EAAE;IAEL,SAAS;QACL,IAAI,QAAQ;QAEX,CAAC,SAAU,CAAC;YACT,IACI,2TAA2T,IAAI,CAC3T,MAEJ,0kDAA0kD,IAAI,CAC1kD,EAAE,MAAM,CAAC,GAAG,KAGhB,QAAQ;QAChB,CAAC,EAAE,UAAU,SAAS,IAAI,UAAU,MAAM,IAAI,AAAC,OAAe,KAAK;QACnE,OAAO;IACX;IAEA,OAAO;AACX;IAzBgB", "debugId": null}}, {"offset": {"line": 213, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/components/FlipTracking/FlipTracking.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"datePicker\": \"FlipTracking-module__IBfYNG__datePicker\",\n  \"filterContainer\": \"FlipTracking-module__IBfYNG__filterContainer\",\n  \"filterLabel\": \"FlipTracking-module__IBfYNG__filterLabel\",\n  \"filterValueField\": \"FlipTracking-module__IBfYNG__filterValueField\",\n  \"itemFilterContainer\": \"FlipTracking-module__IBfYNG__itemFilterContainer\",\n  \"list\": \"FlipTracking-module__IBfYNG__list\",\n  \"listGroupItem\": \"FlipTracking-module__IBfYNG__listGroupItem\",\n  \"multiSearch\": \"FlipTracking-module__IBfYNG__multiSearch\",\n  \"noAuctionFound\": \"FlipTracking-module__IBfYNG__noAuctionFound\",\n  \"noPremiumInfoText\": \"FlipTracking-module__IBfYNG__noPremiumInfoText\",\n  \"profitNumberCard\": \"FlipTracking-module__IBfYNG__profitNumberCard\",\n  \"profitNumberHeader\": \"FlipTracking-module__IBfYNG__profitNumberHeader\",\n  \"rangeFilter\": \"FlipTracking-module__IBfYNG__rangeFilter\",\n  \"topContainer\": \"FlipTracking-module__IBfYNG__topContainer\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 234, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/components/GoogleSignIn/GoogleSignIn.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"googleButton\": \"GoogleSignIn-module__lTSYOa__googleButton\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA"}}, {"offset": {"line": 243, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/GoogleSignIn/GoogleSignIn.tsx"], "sourcesContent": ["'use client'\nimport React, { useEffect, useState } from 'react'\nimport { toast } from 'react-toastify'\nimport api from '../../api/ApiHelper'\nimport { useMatomo } from '@jonkoops/matomo-tracker-react'\nimport { useForceUpdate } from '../../utils/Hooks'\nimport { isClientSideRendering } from '../../utils/SSRUtils'\nimport { CUSTOM_EVENTS } from '../../api/ApiTypes.d'\nimport { GoogleLogin } from '@react-oauth/google'\nimport styles from './GoogleSignIn.module.css'\nimport { GOOGLE_EMAIL, GOOGLE_NAME, GOOGLE_PROFILE_PICTURE_URL, setSetting } from '../../utils/SettingsUtils'\nimport { atobUnicode } from '../../utils/Base64Utils'\nimport { Modal } from 'react-bootstrap'\n\ninterface Props {\n    onAfterLogin?(): void\n    onLoginFail?(): void\n    onManualLoginClick?(): void\n    rerenderFlip?: number\n}\n\nfunction GoogleSignIn(props: Props) {\n    let [wasAlreadyLoggedInThisSession, setWasAlreadyLoggedInThisSession] = useState(\n        isClientSideRendering() ? isValidTokenAvailable(localStorage.getItem('googleId')) : false\n    )\n\n    let [isLoggedIn, setIsLoggedIn] = useState(false)\n    let [isSSR, setIsSSR] = useState(true)\n    let [isLoginNotShowing, setIsLoginNotShowing] = useState(false)\n    let [showButtonNotRenderingModal, setShowButtonNotRenderingModal] = useState(false)\n    let { trackEvent } = useMatomo()\n    let forceUpdate = useForceUpdate()\n\n    useEffect(() => {\n        setIsSSR(false)\n        if (wasAlreadyLoggedInThisSession) {\n            let token = localStorage.getItem('googleId')!\n            let userObject = JSON.parse(atobUnicode(token.split('.')[1]))\n            setSetting(GOOGLE_EMAIL, userObject.email)\n            onLoginSucces(token)\n        } else {\n            setTimeout(() => {\n                let isShown = false\n                document.querySelectorAll('iframe').forEach(e => {\n                    if (e.src && e.src.includes('accounts.google.com')) {\n                        isShown = true\n                    }\n                })\n                if (!isShown) {\n                    setIsLoggedIn(false)\n                    setIsLoginNotShowing(true)\n                    sessionStorage.removeItem('googleId')\n                    localStorage.removeItem('googleId')\n                }\n            }, 5000)\n        }\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [])\n\n    useEffect(() => {\n        if (wasAlreadyLoggedInThisSession) {\n            setIsLoggedIn(true)\n        }\n    }, [wasAlreadyLoggedInThisSession])\n\n    useEffect(() => {\n        forceUpdate()\n        setIsLoggedIn(sessionStorage.getItem('googleId') !== null)\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [props.rerenderFlip])\n\n    function onLoginSucces(token: string) {\n        setIsLoggedIn(true)\n        api.loginWithToken(token)\n            .then(token => {\n                localStorage.setItem('googleId', token)\n                sessionStorage.setItem('googleId', token)\n                let refId = (window as any).refId\n                if (refId) {\n                    api.setRef(refId)\n                }\n                document.dispatchEvent(new CustomEvent(CUSTOM_EVENTS.GOOGLE_LOGIN))\n                if (props.onAfterLogin) {\n                    props.onAfterLogin()\n                }\n            })\n            .catch(error => {\n                // dont show the error message for the invalid token error\n                // the google sign component sometimes sends an outdated token, causing this error\n                if (error.slug !== 'invalid_token') {\n                    toast.error(`An error occoured while trying to sign in with Google. ${error ? error.slug || JSON.stringify(error) : null}`)\n                } else {\n                    console.warn('setGoogle: Invalid token error', error)\n                    sessionStorage.removeItem('googleId')\n                    localStorage.removeItem('googleId')\n                }\n                setIsLoggedIn(false)\n                setWasAlreadyLoggedInThisSession(false)\n                sessionStorage.removeItem('googleId')\n                localStorage.removeItem('googleId')\n            })\n    }\n\n    function onLoginFail() {\n        toast.error('Something went wrong, please try again.', { autoClose: 20000 })\n    }\n\n    function onLoginClick() {\n        if (props.onManualLoginClick) {\n            props.onManualLoginClick()\n        }\n        trackEvent({\n            category: 'login',\n            action: 'click'\n        })\n    }\n\n    let style: React.CSSProperties = isLoggedIn\n        ? {\n              visibility: 'collapse',\n              height: 0\n          }\n        : {}\n\n    if (isSSR) {\n        return null\n    }\n\n    let buttonNotRenderingModal = (\n        <Modal\n            show={showButtonNotRenderingModal}\n            onHide={() => {\n                setShowButtonNotRenderingModal(false)\n            }}\n        >\n            <Modal.Header>\n                <Modal.Title>Google Login button not showing up?</Modal.Title>\n            </Modal.Header>\n            <Modal.Body>\n                <p>This is most likely caused by either an external software like an anti virus or your browser/extension blocking it.</p>\n                <hr />\n                <p>Known issues:</p>\n                <ul>\n                    <li>Kaspersky's \"Secure Browse\" feature seems to block the Google login.</li>\n                    <li>Opera GX seems to sometimes blocks the login button. The specific setting or reason on when it blocks it is unknown.</li>\n                </ul>\n            </Modal.Body>\n        </Modal>\n    )\n\n    return (\n        <div style={style} onClickCapture={onLoginClick}>\n            {!wasAlreadyLoggedInThisSession ? (\n                <>\n                    <div className={styles.googleButton}>\n                        {!isSSR ? (\n                            <GoogleLogin\n                                onSuccess={response => {\n                                    try {\n                                        let userObject = JSON.parse(atobUnicode(response.credential!.split('.')[1]))\n                                        setSetting(GOOGLE_PROFILE_PICTURE_URL, userObject.picture)\n                                        setSetting(GOOGLE_EMAIL, userObject.email)\n                                        setSetting(GOOGLE_NAME, userObject.name)\n                                    } catch {\n                                        toast.warn('Parsing issue with the google token. There might be issues when displaying details on the account page!')\n                                    }\n                                    onLoginSucces(response.credential!)\n                                }}\n                                onError={onLoginFail}\n                                theme={'filled_blue'}\n                                size={'large'}\n                                useOneTap\n                                auto_select\n                            />\n                        ) : null}\n                    </div>\n                    <p>\n                        I have read and agree to the <a href=\"https://coflnet.com/privacy\">Privacy Policy</a>\n                    </p>\n                    {isLoginNotShowing ? (\n                        <p>\n                            Login button not showing? Click{' '}\n                            <span\n                                style={{ color: '#007bff', cursor: 'pointer' }}\n                                onClick={() => {\n                                    setShowButtonNotRenderingModal(true)\n                                }}\n                            >\n                                here\n                            </span>\n                            .\n                        </p>\n                    ) : null}\n                </>\n            ) : null}\n            {buttonNotRenderingModal}\n        </div>\n    )\n}\n\nexport default GoogleSignIn\n\nexport function isValidTokenAvailable(token?: string | null) {\n    if (!token || token === 'null') {\n        return\n    }\n    try {\n        let details = JSON.parse(atobUnicode(token.split('.')[1]))\n        let expirationDate = new Date(parseInt(details.exp) * 1000)\n        return expirationDate.getTime() - 10000 > new Date().getTime()\n    } catch (e) {\n        toast.warn(\"Parsing issue with the google token. Can't automatically login!\")\n        return false\n    }\n}\n"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAZA;;;;;;;;;;;;;AAqBA,SAAS,aAAa,KAAY;;IAC9B,IAAI,CAAC,+BAA+B,iCAAiC,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAC3E,CAAA,GAAA,qHAAA,CAAA,wBAAqB,AAAD,MAAM,sBAAsB,aAAa,OAAO,CAAC,eAAe;IAGxF,IAAI,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,IAAI,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,IAAI,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,IAAI,CAAC,6BAA6B,+BAA+B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7E,IAAI,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,sNAAA,CAAA,YAAS,AAAD;IAC7B,IAAI,cAAc,CAAA,GAAA,kHAAA,CAAA,iBAAc,AAAD;IAE/B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACN,SAAS;YACT,IAAI,+BAA+B;gBAC/B,IAAI,QAAQ,aAAa,OAAO,CAAC;gBACjC,IAAI,aAAa,KAAK,KAAK,CAAC,CAAA,GAAA,wHAAA,CAAA,cAAW,AAAD,EAAE,MAAM,KAAK,CAAC,IAAI,CAAC,EAAE;gBAC3D,CAAA,GAAA,0HAAA,CAAA,aAAU,AAAD,EAAE,0HAAA,CAAA,eAAY,EAAE,WAAW,KAAK;gBACzC,cAAc;YAClB,OAAO;gBACH;8CAAW;wBACP,IAAI,UAAU;wBACd,SAAS,gBAAgB,CAAC,UAAU,OAAO;sDAAC,CAAA;gCACxC,IAAI,EAAE,GAAG,IAAI,EAAE,GAAG,CAAC,QAAQ,CAAC,wBAAwB;oCAChD,UAAU;gCACd;4BACJ;;wBACA,IAAI,CAAC,SAAS;4BACV,cAAc;4BACd,qBAAqB;4BACrB,eAAe,UAAU,CAAC;4BAC1B,aAAa,UAAU,CAAC;wBAC5B;oBACJ;6CAAG;YACP;QACA,uDAAuD;QAC3D;iCAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACN,IAAI,+BAA+B;gBAC/B,cAAc;YAClB;QACJ;iCAAG;QAAC;KAA8B;IAElC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACN;YACA,cAAc,eAAe,OAAO,CAAC,gBAAgB;QACrD,uDAAuD;QAC3D;iCAAG;QAAC,MAAM,YAAY;KAAC;IAEvB,SAAS,cAAc,KAAa;QAChC,cAAc;QACd,oHAAA,CAAA,UAAG,CAAC,cAAc,CAAC,OACd,IAAI,CAAC,CAAA;YACF,aAAa,OAAO,CAAC,YAAY;YACjC,eAAe,OAAO,CAAC,YAAY;YACnC,IAAI,QAAQ,AAAC,OAAe,KAAK;YACjC,IAAI,OAAO;gBACP,oHAAA,CAAA,UAAG,CAAC,MAAM,CAAC;YACf;YACA,SAAS,aAAa,CAAC,IAAI,YAAY,wHAAA,CAAA,gBAAa,CAAC,YAAY;YACjE,IAAI,MAAM,YAAY,EAAE;gBACpB,MAAM,YAAY;YACtB;QACJ,GACC,KAAK,CAAC,CAAA;YACH,0DAA0D;YAC1D,kFAAkF;YAClF,IAAI,MAAM,IAAI,KAAK,iBAAiB;gBAChC,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,uDAAuD,EAAE,QAAQ,MAAM,IAAI,IAAI,KAAK,SAAS,CAAC,SAAS,MAAM;YAC9H,OAAO;gBACH,QAAQ,IAAI,CAAC,kCAAkC;gBAC/C,eAAe,UAAU,CAAC;gBAC1B,aAAa,UAAU,CAAC;YAC5B;YACA,cAAc;YACd,iCAAiC;YACjC,eAAe,UAAU,CAAC;YAC1B,aAAa,UAAU,CAAC;QAC5B;IACR;IAEA,SAAS;QACL,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,2CAA2C;YAAE,WAAW;QAAM;IAC9E;IAEA,SAAS;QACL,IAAI,MAAM,kBAAkB,EAAE;YAC1B,MAAM,kBAAkB;QAC5B;QACA,WAAW;YACP,UAAU;YACV,QAAQ;QACZ;IACJ;IAEA,IAAI,QAA6B,aAC3B;QACI,YAAY;QACZ,QAAQ;IACZ,IACA,CAAC;IAEP,IAAI,OAAO;QACP,OAAO;IACX;IAEA,IAAI,wCACA,6LAAC,yLAAA,CAAA,QAAK;QACF,MAAM;QACN,QAAQ;YACJ,+BAA+B;QACnC;;0BAEA,6LAAC,yLAAA,CAAA,QAAK,CAAC,MAAM;0BACT,cAAA,6LAAC,yLAAA,CAAA,QAAK,CAAC,KAAK;8BAAC;;;;;;;;;;;0BAEjB,6LAAC,yLAAA,CAAA,QAAK,CAAC,IAAI;;kCACP,6LAAC;kCAAE;;;;;;kCACH,6LAAC;;;;;kCACD,6LAAC;kCAAE;;;;;;kCACH,6LAAC;;0CACG,6LAAC;0CAAG;;;;;;0CACJ,6LAAC;0CAAG;;;;;;;;;;;;;;;;;;;;;;;;IAMpB,qBACI,6LAAC;QAAI,OAAO;QAAO,gBAAgB;;YAC9B,CAAC,8CACE;;kCACI,6LAAC;wBAAI,WAAW,yJAAA,CAAA,UAAM,CAAC,YAAY;kCAC9B,CAAC,sBACE,6LAAC,qKAAA,CAAA,cAAW;4BACR,WAAW,CAAA;gCACP,IAAI;oCACA,IAAI,aAAa,KAAK,KAAK,CAAC,CAAA,GAAA,wHAAA,CAAA,cAAW,AAAD,EAAE,SAAS,UAAU,CAAE,KAAK,CAAC,IAAI,CAAC,EAAE;oCAC1E,CAAA,GAAA,0HAAA,CAAA,aAAU,AAAD,EAAE,0HAAA,CAAA,6BAA0B,EAAE,WAAW,OAAO;oCACzD,CAAA,GAAA,0HAAA,CAAA,aAAU,AAAD,EAAE,0HAAA,CAAA,eAAY,EAAE,WAAW,KAAK;oCACzC,CAAA,GAAA,0HAAA,CAAA,aAAU,AAAD,EAAE,0HAAA,CAAA,cAAW,EAAE,WAAW,IAAI;gCAC3C,EAAE,OAAM;oCACJ,sJAAA,CAAA,QAAK,CAAC,IAAI,CAAC;gCACf;gCACA,cAAc,SAAS,UAAU;4BACrC;4BACA,SAAS;4BACT,OAAO;4BACP,MAAM;4BACN,SAAS;4BACT,WAAW;;;;;mCAEf;;;;;;kCAER,6LAAC;;4BAAE;0CAC8B,6LAAC;gCAAE,MAAK;0CAA8B;;;;;;;;;;;;oBAEtE,kCACG,6LAAC;;4BAAE;4BACiC;0CAChC,6LAAC;gCACG,OAAO;oCAAE,OAAO;oCAAW,QAAQ;gCAAU;gCAC7C,SAAS;oCACL,+BAA+B;gCACnC;0CACH;;;;;;4BAEM;;;;;;+BAGX;;+BAER;YACH;;;;;;;AAGb;GAjLS;;QASgB,sNAAA,CAAA,YAAS;QACZ,kHAAA,CAAA,iBAAc;;;KAV3B;uCAmLM;AAER,SAAS,sBAAsB,KAAqB;IACvD,IAAI,CAAC,SAAS,UAAU,QAAQ;QAC5B;IACJ;IACA,IAAI;QACA,IAAI,UAAU,KAAK,KAAK,CAAC,CAAA,GAAA,wHAAA,CAAA,cAAW,AAAD,EAAE,MAAM,KAAK,CAAC,IAAI,CAAC,EAAE;QACxD,IAAI,iBAAiB,IAAI,KAAK,SAAS,QAAQ,GAAG,IAAI;QACtD,OAAO,eAAe,OAAO,KAAK,QAAQ,IAAI,OAAO,OAAO;IAChE,EAAE,OAAO,GAAG;QACR,sJAAA,CAAA,QAAK,CAAC,IAAI,CAAC;QACX,OAAO;IACX;AACJ", "debugId": null}}, {"offset": {"line": 576, "column": 0}, "map": {"version": 3, "sources": ["file:///app/utils/LoadingUtils.tsx"], "sourcesContent": ["import Image from 'next/image'\nimport React, { type JSX } from 'react';\nimport { Spinner } from 'react-bootstrap'\n\nexport function getLoadingElement(text?: JSX.Element): JSX.Element {\n    return (\n        <div style={{ textAlign: 'center' }}>\n            <span>\n                <Spinner animation=\"grow\" variant=\"primary\"></Spinner>\n                <Spinner animation=\"grow\" variant=\"primary\"></Spinner>\n                <Spinner animation=\"grow\" variant=\"primary\"></Spinner>\n            </span>\n            {text ? text : <p>Loading Data...</p>}\n        </div>\n    )\n}\n\nexport function getInitialLoadingElement(): JSX.Element {\n    return (\n        <div className=\"main-loading\" style={{ height: '500px' }}>\n            <div>\n                <Image src=\"/logo192.png\" height=\"192\" width=\"192\" alt=\"auction house logo\" />\n                <div className=\"main-loading\">\n                    <span>Loading App...</span>\n                </div>\n            </div>\n        </div>\n    )\n}\n"], "names": [], "mappings": ";;;;;AAAA;AAEA;;;;AAEO,SAAS,kBAAkB,IAAkB;IAChD,qBACI,6LAAC;QAAI,OAAO;YAAE,WAAW;QAAS;;0BAC9B,6LAAC;;kCACG,6LAAC,6LAAA,CAAA,UAAO;wBAAC,WAAU;wBAAO,SAAQ;;;;;;kCAClC,6LAAC,6LAAA,CAAA,UAAO;wBAAC,WAAU;wBAAO,SAAQ;;;;;;kCAClC,6LAAC,6LAAA,CAAA,UAAO;wBAAC,WAAU;wBAAO,SAAQ;;;;;;;;;;;;YAErC,OAAO,qBAAO,6LAAC;0BAAE;;;;;;;;;;;;AAG9B;AAEO,SAAS;IACZ,qBACI,6LAAC;QAAI,WAAU;QAAe,OAAO;YAAE,QAAQ;QAAQ;kBACnD,cAAA,6LAAC;;8BACG,6LAAC,gIAAA,CAAA,UAAK;oBAAC,KAAI;oBAAe,QAAO;oBAAM,OAAM;oBAAM,KAAI;;;;;;8BACvD,6LAAC;oBAAI,WAAU;8BACX,cAAA,6LAAC;kCAAK;;;;;;;;;;;;;;;;;;;;;;AAK1B", "debugId": null}}, {"offset": {"line": 691, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/Number/Number.tsx"], "sourcesContent": ["'use client'\nimport React, { useEffect, useState } from 'react'\nimport { numberWithThousandsSeparators } from '../../utils/Formatter'\n\ninterface Props {\n    number: number | string\n}\n\nexport default function NumberElement(props: Props) {\n    let [isSSR, setIsSSR] = useState(true)\n\n    let value = Number(props.number)\n\n    useEffect(() => {\n        setIsSSR(false)\n    }, [])\n\n    return <>{isSSR ? numberWithThousandsSeparators(value, ',', '.') : numberWithThousandsSeparators(value)}</>\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;;;AAFA;;;AAQe,SAAS,cAAc,KAAY;;IAC9C,IAAI,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjC,IAAI,QAAQ,OAAO,MAAM,MAAM;IAE/B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACN,SAAS;QACb;kCAAG,EAAE;IAEL,qBAAO;kBAAG,QAAQ,CAAA,GAAA,sHAAA,CAAA,gCAA6B,AAAD,EAAE,OAAO,KAAK,OAAO,CAAA,GAAA,sHAAA,CAAA,gCAA6B,AAAD,EAAE;;AACrG;GAVwB;KAAA", "debugId": null}}, {"offset": {"line": 728, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/FlipTracking/FlipTrackingTotalProfitCalculation.tsx"], "sourcesContent": ["import { useState } from 'react'\nimport { Form } from 'react-bootstrap'\nimport Tooltip from '../Tooltip/Tooltip'\nimport Number from '../Number/Number'\nimport SettingsIcon from '@mui/icons-material/Settings'\n\ninterface Props {\n    flips: FlipTrackingFlip[]\n    ignoreProfitMap: {}\n}\n\nenum TotalProfitType {\n    ALL,\n    POSITIVE,\n    NEGATIVE\n}\n\nexport function FlipTrackingTotalProfitCalculation(props: Props) {\n    let [totalProfitCalculationType, setTotalProfitCalculationType] = useState(TotalProfitType.ALL)                \n    let totalProfit = 0\n    props.flips.forEach(flip => {\n        if (props.ignoreProfitMap[flip.uId.toString(16)]) {\n            return\n        }\n        if (totalProfitCalculationType === TotalProfitType.NEGATIVE && flip.profit >= 0) {\n            return\n        }\n        if (totalProfitCalculationType === TotalProfitType.POSITIVE && flip.profit <= 0) {\n            return\n        }\n\n        totalProfit += flip.profit\n    })\n\n    return (\n        <b>\n            <p style={{ fontSize: 'x-large', display: 'flex', justifyContent: 'start', alignItems: 'center', gap: 10 }}>\n                <Tooltip\n                    content={<SettingsIcon style={{ display: 'flex' }} />}\n                    type=\"click\"\n                    tooltipTitle={<span>Calculation Type:</span>}\n                    tooltipContent={\n                        <div>\n                            <Form.Check>\n                                <Form.Check.Input\n                                    id=\"calculationTypeAll\"\n                                    type=\"radio\"\n                                    checked={totalProfitCalculationType === TotalProfitType.ALL}\n                                    onChange={e => {\n                                        if (e.target.checked) {\n                                            setTotalProfitCalculationType(TotalProfitType.ALL)\n                                        }\n                                    }}\n                                    radioGroup=\"calculationType\"\n                                />\n                                <Form.Check.Label htmlFor=\"calculationTypeAll\">Include all flips</Form.Check.Label>\n                            </Form.Check>\n                            <Form.Check>\n                                <Form.Check.Input\n                                    id=\"calculationTypePositive\"\n                                    type=\"radio\"\n                                    checked={totalProfitCalculationType === TotalProfitType.POSITIVE}\n                                    onChange={e => {\n                                        if (e.target.checked) {\n                                            setTotalProfitCalculationType(TotalProfitType.POSITIVE)\n                                        }\n                                    }}\n                                    radioGroup=\"calculationType\"\n                                />\n                                <Form.Check.Label htmlFor=\"calculationTypePositive\">Only include flips sold for a profit</Form.Check.Label>\n                            </Form.Check>\n                            <Form.Check>\n                                <Form.Check.Input\n                                    id=\"calculationTypeNegative\"\n                                    type=\"radio\"\n                                    checked={totalProfitCalculationType === TotalProfitType.NEGATIVE}\n                                    onChange={e => {\n                                        if (e.target.checked) {\n                                            setTotalProfitCalculationType(TotalProfitType.NEGATIVE)\n                                        }\n                                    }}\n                                    radioGroup=\"calculationType\"\n                                />\n                                <Form.Check.Label htmlFor=\"calculationTypeNegative\">Only include flips sold for a loss</Form.Check.Label>\n                            </Form.Check>\n                        </div>\n                    }\n                />\n\n                {totalProfitCalculationType === TotalProfitType.NEGATIVE\n                    ? 'Only Loss:'\n                    : totalProfitCalculationType === TotalProfitType.POSITIVE\n                    ? 'Only Profit:'\n                    : 'Total Profit:'}\n                <span style={{ color: 'gold' }}>\n                    <Number number={totalProfit} /> Coins{' '}\n                </span>\n            </p>\n        </b>\n    )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;;;;;;;;AAOA,IAAA,AAAK,yCAAA;;;;WAAA;EAAA;AAME,SAAS,mCAAmC,KAAY;;IAC3D,IAAI,CAAC,4BAA4B,8BAA8B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;IACzE,IAAI,cAAc;IAClB,MAAM,KAAK,CAAC,OAAO,CAAC,CAAA;QAChB,IAAI,MAAM,eAAe,CAAC,KAAK,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE;YAC9C;QACJ;QACA,IAAI,oCAA2D,KAAK,MAAM,IAAI,GAAG;YAC7E;QACJ;QACA,IAAI,oCAA2D,KAAK,MAAM,IAAI,GAAG;YAC7E;QACJ;QAEA,eAAe,KAAK,MAAM;IAC9B;IAEA,qBACI,6LAAC;kBACG,cAAA,6LAAC;YAAE,OAAO;gBAAE,UAAU;gBAAW,SAAS;gBAAQ,gBAAgB;gBAAS,YAAY;gBAAU,KAAK;YAAG;;8BACrG,6LAAC,oIAAA,CAAA,UAAO;oBACJ,uBAAS,6LAAC,gKAAA,CAAA,UAAY;wBAAC,OAAO;4BAAE,SAAS;wBAAO;;;;;;oBAChD,MAAK;oBACL,4BAAc,6LAAC;kCAAK;;;;;;oBACpB,8BACI,6LAAC;;0CACG,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;;kDACP,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK,CAAC,KAAK;wCACb,IAAG;wCACH,MAAK;wCACL,SAAS;wCACT,UAAU,CAAA;4CACN,IAAI,EAAE,MAAM,CAAC,OAAO,EAAE;gDAClB;4CACJ;wCACJ;wCACA,YAAW;;;;;;kDAEf,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK,CAAC,KAAK;wCAAC,SAAQ;kDAAqB;;;;;;;;;;;;0CAEnD,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;;kDACP,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK,CAAC,KAAK;wCACb,IAAG;wCACH,MAAK;wCACL,SAAS;wCACT,UAAU,CAAA;4CACN,IAAI,EAAE,MAAM,CAAC,OAAO,EAAE;gDAClB;4CACJ;wCACJ;wCACA,YAAW;;;;;;kDAEf,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK,CAAC,KAAK;wCAAC,SAAQ;kDAA0B;;;;;;;;;;;;0CAExD,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;;kDACP,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK,CAAC,KAAK;wCACb,IAAG;wCACH,MAAK;wCACL,SAAS;wCACT,UAAU,CAAA;4CACN,IAAI,EAAE,MAAM,CAAC,OAAO,EAAE;gDAClB;4CACJ;wCACJ;wCACA,YAAW;;;;;;kDAEf,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK,CAAC,KAAK;wCAAC,SAAQ;kDAA0B;;;;;;;;;;;;;;;;;;;;;;;gBAMnE,mCACK,eACA,mCACA,iBACA;8BACN,6LAAC;oBAAK,OAAO;wBAAE,OAAO;oBAAO;;sCACzB,6LAAC,kIAAA,CAAA,UAAM;4BAAC,QAAQ;;;;;;wBAAe;wBAAO;;;;;;;;;;;;;;;;;;AAK1D;GAnFgB;KAAA", "debugId": null}}, {"offset": {"line": 946, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/CopyButton/CopyButton.tsx"], "sourcesContent": ["'use client'\nimport { useMatomo } from '@jonkoops/matomo-tracker-react'\nimport React, { useEffect, useState, type JSX } from 'react'\nimport { Button } from 'react-bootstrap'\nimport { toast } from 'react-toastify'\nimport { isClientSideRendering } from '../../utils/SSRUtils'\nimport { canUseClipBoard, writeToClipboard } from '../../utils/ClipboardUtils'\n\ninterface Props {\n    onCopy?()\n    successMessage?: JSX.Element\n    copyValue?: string | null\n    buttonWrapperClass?: string\n    buttonClass?: string\n    buttonVariant?: string\n    forceIsCopied?: boolean\n    buttonStyle?: React.CSSProperties\n    buttonContent?: JSX.Element\n}\n\nexport function CopyButton(props: Props) {\n    let [isCopied, setIsCopied] = useState(false)\n    let { trackEvent } = useMatomo()\n    let [isSSR, setIsSSR] = useState(true)\n\n    useEffect(() => {\n        setIsSSR(false)\n    })\n\n    let copyIcon = (\n        <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" fill=\"currentColor\" className=\"bi bi-clipboard\" viewBox=\"0 0 16 16\">\n            <path d=\"M4 1.5H3a2 2 0 0 0-2 2V14a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V3.5a2 2 0 0 0-2-2h-1v1h1a1 1 0 0 1 1 1V14a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V3.5a1 1 0 0 1 1-1h1v-1z\" />\n            <path d=\"M9.5 1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5v-1a.5.5 0 0 1 .5-.5h3zm-3-1A1.5 1.5 0 0 0 5 1.5v1A1.5 1.5 0 0 0 6.5 4h3A1.5 1.5 0 0 0 11 2.5v-1A1.5 1.5 0 0 0 9.5 0h-3z\" />\n        </svg>\n    )\n\n    let copiedIcon = (\n        <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" fill=\"currentColor\" className=\"bi bi-clipboard-check\" viewBox=\"0 0 16 16\">\n            <path\n                fillRule=\"evenodd\"\n                d=\"M10.854 7.146a.5.5 0 0 1 0 .708l-3 3a.5.5 0 0 1-.708 0l-1.5-1.5a.5.5 0 1 1 .708-.708L7.5 9.793l2.646-2.647a.5.5 0 0 1 .708 0z\"\n            />\n            <path d=\"M4 1.5H3a2 2 0 0 0-2 2V14a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V3.5a2 2 0 0 0-2-2h-1v1h1a1 1 0 0 1 1 1V14a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V3.5a1 1 0 0 1 1-1h1v-1z\" />\n            <path d=\"M9.5 1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5v-1a.5.5 0 0 1 .5-.5h3zm-3-1A1.5 1.5 0 0 0 5 1.5v1A1.5 1.5 0 0 0 6.5 4h3A1.5 1.5 0 0 0 11 2.5v-1A1.5 1.5 0 0 0 9.5 0h-3z\" />\n        </svg>\n    )\n\n    function copyClick() {\n        setIsCopied(true)\n        if (props.copyValue) {\n            writeToClipboard(props.copyValue)\n            trackEvent({\n                category: 'copyButtonClick',\n                action: props.copyValue\n            })\n        }\n        if (props.onCopy) {\n            props.onCopy()\n        }\n        if (props.successMessage) {\n            toast.success(props.successMessage)\n        }\n    }\n\n    return (\n        <span>\n            {!isSSR && canUseClipBoard() ? (\n                <span className={props.buttonWrapperClass}>\n                    <Button\n                        style={props.buttonStyle}\n                        onMouseDown={copyClick}\n                        className={props.buttonClass}\n                        aria-label=\"copy to clipboard\"\n                        variant={props.buttonVariant || 'secondary'}\n                    >\n                        {isCopied || props.forceIsCopied ? copiedIcon : copyIcon}\n                        {props.buttonContent}\n                    </Button>\n                </span>\n            ) : (\n                ''\n            )}\n        </span>\n    )\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AAEA;;;AANA;;;;;;AAoBO,SAAS,WAAW,KAAY;;IACnC,IAAI,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,IAAI,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,sNAAA,CAAA,YAAS,AAAD;IAC7B,IAAI,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACN,SAAS;QACb;;IAEA,IAAI,yBACA,6LAAC;QAAI,OAAM;QAA6B,OAAM;QAAK,QAAO;QAAK,MAAK;QAAe,WAAU;QAAkB,SAAQ;;0BACnH,6LAAC;gBAAK,GAAE;;;;;;0BACR,6LAAC;gBAAK,GAAE;;;;;;;;;;;;IAIhB,IAAI,2BACA,6LAAC;QAAI,OAAM;QAA6B,OAAM;QAAK,QAAO;QAAK,MAAK;QAAe,WAAU;QAAwB,SAAQ;;0BACzH,6LAAC;gBACG,UAAS;gBACT,GAAE;;;;;;0BAEN,6LAAC;gBAAK,GAAE;;;;;;0BACR,6LAAC;gBAAK,GAAE;;;;;;;;;;;;IAIhB,SAAS;QACL,YAAY;QACZ,IAAI,MAAM,SAAS,EAAE;YACjB,CAAA,GAAA,2HAAA,CAAA,mBAAgB,AAAD,EAAE,MAAM,SAAS;YAChC,WAAW;gBACP,UAAU;gBACV,QAAQ,MAAM,SAAS;YAC3B;QACJ;QACA,IAAI,MAAM,MAAM,EAAE;YACd,MAAM,MAAM;QAChB;QACA,IAAI,MAAM,cAAc,EAAE;YACtB,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC,MAAM,cAAc;QACtC;IACJ;IAEA,qBACI,6LAAC;kBACI,CAAC,SAAS,CAAA,GAAA,2HAAA,CAAA,kBAAe,AAAD,oBACrB,6LAAC;YAAK,WAAW,MAAM,kBAAkB;sBACrC,cAAA,6LAAC,2LAAA,CAAA,SAAM;gBACH,OAAO,MAAM,WAAW;gBACxB,aAAa;gBACb,WAAW,MAAM,WAAW;gBAC5B,cAAW;gBACX,SAAS,MAAM,aAAa,IAAI;;oBAE/B,YAAY,MAAM,aAAa,GAAG,aAAa;oBAC/C,MAAM,aAAa;;;;;;;;;;;mBAI5B;;;;;;AAIhB;GAhEgB;;QAES,sNAAA,CAAA,YAAS;;;KAFlB", "debugId": null}}, {"offset": {"line": 1099, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/FlipTracking/FlipTrackingCopyButton.tsx"], "sourcesContent": ["'use client'\n\nimport { useParams } from 'next/navigation'\nimport { CopyButton } from '../CopyButton/CopyButton'\nimport { isClientSideRendering } from '../../utils/SSRUtils'\n\ninterface Props {\n    trackedFlip: FlipTrackingFlip\n}\n\nexport default function FlipTrackingCopyButton({ trackedFlip }: Props) {\n    let params = useParams()\n\n    return (\n        <CopyButton\n            copyValue={isClientSideRendering() ? `${window.location.origin}/player/${params.uuid}/flips/${trackedFlip.uId.toString(16)}` : ''}\n            successMessage={isClientSideRendering() ? <span>{`Copied link to flip!`}</span> : <span />}\n        />\n    )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAUe,SAAS,uBAAuB,EAAE,WAAW,EAAS;;IACjE,IAAI,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAErB,qBACI,6LAAC,0IAAA,CAAA,aAAU;QACP,WAAW,CAAA,GAAA,qHAAA,CAAA,wBAAqB,AAAD,MAAM,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,QAAQ,EAAE,OAAO,IAAI,CAAC,OAAO,EAAE,YAAY,GAAG,CAAC,QAAQ,CAAC,KAAK,GAAG;QAC/H,gBAAgB,CAAA,GAAA,qHAAA,CAAA,wBAAqB,AAAD,oBAAM,6LAAC;sBAAM,CAAC,oBAAoB,CAAC;;;;;mCAAW,6LAAC;;;;;;;;;;AAG/F;GATwB;;QACP,qIAAA,CAAA,YAAS;;;KADF", "debugId": null}}, {"offset": {"line": 1150, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/components/ShowMoreText/ShowMoreText.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"expanded\": \"ShowMoreText-module__4MH6ba__expanded\",\n  \"showMoreContainer\": \"ShowMoreText-module__4MH6ba__showMoreContainer\",\n  \"showMoreText\": \"ShowMoreText-module__4MH6ba__showMoreText\",\n  \"textContainer\": \"ShowMoreText-module__4MH6ba__textContainer\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 1162, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/ShowMoreText/ShowMoreText.tsx"], "sourcesContent": ["import React, { useState, type JSX } from 'react';\nimport styles from './ShowMoreText.module.css'\n\ninterface Props {\n    content: JSX.Element\n    initialHeight: number\n    alwaysShowAll?: boolean\n    allowShowLess?: boolean\n    onShowChange?: (isExpanded: boolean) => void\n    containerStyle?: React.CSSProperties\n}\n\nconst ShowMoreText = (props: Props) => {\n    const [isExpanded, setIsExpanded] = useState(false)\n\n    const toggleExpand = () => {\n        setIsExpanded(!isExpanded)\n        props.onShowChange && props.onShowChange(!isExpanded)\n    }\n\n    if (props.alwaysShowAll) {\n        return props.content\n    }\n\n    return (\n        <div\n            className={`${styles.textContainer} ${isExpanded ? styles.expanded : ''}`}\n            style={{ height: isExpanded ? 'auto' : props.initialHeight, ...props.containerStyle }}\n        >\n            {props.content}\n            <div className={styles.showMoreContainer} onClick={toggleExpand}>\n                <span className={styles.showMoreText}>\n                    {isExpanded && props.allowShowLess ? 'Show less' : null}\n                    {!isExpanded ? 'Show more' : null}\n                </span>\n            </div>\n        </div>\n    )\n}\n\nexport default ShowMoreText\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;;AAWA,MAAM,eAAe,CAAC;;IAClB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,eAAe;QACjB,cAAc,CAAC;QACf,MAAM,YAAY,IAAI,MAAM,YAAY,CAAC,CAAC;IAC9C;IAEA,IAAI,MAAM,aAAa,EAAE;QACrB,OAAO,MAAM,OAAO;IACxB;IAEA,qBACI,6LAAC;QACG,WAAW,GAAG,yJAAA,CAAA,UAAM,CAAC,aAAa,CAAC,CAAC,EAAE,aAAa,yJAAA,CAAA,UAAM,CAAC,QAAQ,GAAG,IAAI;QACzE,OAAO;YAAE,QAAQ,aAAa,SAAS,MAAM,aAAa;YAAE,GAAG,MAAM,cAAc;QAAC;;YAEnF,MAAM,OAAO;0BACd,6LAAC;gBAAI,WAAW,yJAAA,CAAA,UAAM,CAAC,iBAAiB;gBAAE,SAAS;0BAC/C,cAAA,6LAAC;oBAAK,WAAW,yJAAA,CAAA,UAAM,CAAC,YAAY;;wBAC/B,cAAc,MAAM,aAAa,GAAG,cAAc;wBAClD,CAAC,aAAa,cAAc;;;;;;;;;;;;;;;;;;AAKjD;GA1BM;KAAA;uCA4BS", "debugId": null}}, {"offset": {"line": 1230, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/FlipTracking/FlipTrackingListItem.tsx"], "sourcesContent": ["'use client'\nimport { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, ListGroup, Table } from 'react-bootstrap'\nimport styles from './FlipTracking.module.css'\nimport { MouseEventHandler, Suspense, useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport Image from 'next/image'\nimport { getMinecraftColorCodedElement, getStyleForTier } from '../../utils/Formatter'\nimport FlipTrackingCopyButton from './FlipTrackingCopyButton'\nimport Number from '../Number/Number'\nimport ArrowRightIcon from '@mui/icons-material/ArrowRightAlt'\nimport Tooltip from '../Tooltip/Tooltip'\nimport moment from 'moment'\nimport HelpIcon from '@mui/icons-material/Help'\nimport ShowMoreText from '../ShowMoreText/ShowMoreText'\nimport api from '../../api/ApiHelper'\n\ninterface Props {\n    trackedFlip: FlipTrackingFlip\n    isHighlighted: boolean\n    onContextMenu: MouseEventHandler<HTMLElement> | undefined\n    ignoreProfit: boolean\n    onRemoveFlipFromIgnoreMap(): void\n}\n\nexport function FlipTrackingListItem(props: Props) {\n    let router = useRouter()\n\n    function convertMilliSecondsIntoLegibleString(milliSecondsIn: number) {\n        var secsIn = milliSecondsIn / 1000\n\n        var days = Math.floor(secsIn / 86400),\n            remainderAfterDays = secsIn % 86400,\n            hours = Math.floor(remainderAfterDays / 3600),\n            remainderAfterHours = remainderAfterDays % 3600,\n            minutes = Math.floor(remainderAfterHours / 60)\n\n        return `${days > 0 ? days + 'days ' : ''}  ${hours > 0 ? hours + 'h ' : ''}  ${minutes > 0 ? minutes + 'min' : ''}`\n    }\n\n    return (\n        <ListGroup.Item\n            className={styles.listGroupItem}\n            onContextMenu={props.onContextMenu}\n            id={props.trackedFlip.uId.toString(16)}\n            style={{\n                borderColor: props.isHighlighted ? 'cornflowerblue' : undefined,\n                borderWidth: props.isHighlighted ? 5 : undefined\n            }}\n        >\n            <h1 style={{ padding: '10px', display: 'flex', justifyContent: 'space-between', fontSize: 'x-large' }}>\n                <div\n                    className=\"ellipse\"\n                    style={{ cursor: 'pointer' }}\n                    onClick={() => {\n                        router.push(`/item/${props.trackedFlip.item.tag}`)\n                    }}\n                >\n                    <Image\n                        crossOrigin=\"anonymous\"\n                        src={api.getItemImageUrl(props.trackedFlip.item) || ''}\n                        height=\"36\"\n                        width=\"36\"\n                        alt=\"\"\n                        style={{ marginRight: '5px' }}\n                        loading=\"lazy\"\n                    />\n                    <span style={{ whiteSpace: 'nowrap', ...getStyleForTier(props.trackedFlip.item.tier) }}>\n                        {getMinecraftColorCodedElement(props.trackedFlip.item.name)}\n                    </span>\n                </div>\n                {props.trackedFlip.profit > 0 ? (\n                    <span style={{ color: 'lime', whiteSpace: 'nowrap', marginLeft: '5px' }}>\n                        +<Number number={props.trackedFlip.profit} /> Coins\n                    </span>\n                ) : (\n                    <span style={{ color: 'red', whiteSpace: 'nowrap', marginLeft: '5px' }}>\n                        <Number number={props.trackedFlip.profit} /> Coins\n                    </span>\n                )}\n            </h1>\n            <hr />\n            <div style={{ display: 'flex', justifyContent: 'space-around', alignItems: 'center' }}>\n                <Card className={styles.profitNumberCard}>\n                    <a href={`/auction/${props.trackedFlip.originAuction}`} target={'_blank'} className=\"disableLinkStyle\">\n                        <Card.Header className={styles.profitNumberHeader}>\n                            <Card.Title style={{ margin: 0 }}>\n                                <Number number={props.trackedFlip.pricePaid} /> Coins\n                            </Card.Title>\n                        </Card.Header>\n                    </a>\n                </Card>\n                <ArrowRightIcon style={{ fontSize: '50px' }} />\n                <Card className={styles.profitNumberCard}>\n                    <a href={`/auction/${props.trackedFlip.soldAuction}`} target={'_blank'} className=\"disableLinkStyle\">\n                        <Card.Header className={styles.profitNumberHeader}>\n                            <Card.Title style={{ margin: 0 }}>\n                                <Number number={props.trackedFlip.soldFor} /> Coins\n                            </Card.Title>\n                        </Card.Header>\n                    </a>\n                </Card>\n            </div>\n            <div style={{ display: 'flex', justifyContent: 'space-between' }}>\n                <div style={{ maxWidth: '90%' }}>\n                    <p style={{ marginTop: '10px' }}>\n                        <Tooltip\n                            content={\n                                <span>\n                                    Finder: <Badge bg=\"dark\">{props.trackedFlip.finder.shortLabel}</Badge>\n                                </span>\n                            }\n                            tooltipContent={\n                                <span>\n                                    This is the first flip finder algorithm that reported this flip. Its possible that you used another one or even found this\n                                    flip on your own\n                                </span>\n                            }\n                            type={'hover'}\n                        />\n                        <span style={{ marginLeft: '15px' }}>\n                            <Tooltip\n                                type=\"hover\"\n                                content={\n                                    <span>\n                                        <span className={styles.label}></span>Sold {moment(props.trackedFlip.sellTime).fromNow()}\n                                    </span>\n                                }\n                                tooltipContent={\n                                    <div>\n                                        <p>Buy: {props.trackedFlip.buyTime.toLocaleDateString() + ' ' + props.trackedFlip.buyTime.toLocaleTimeString()}</p>\n                                        <p>Sell: {props.trackedFlip.sellTime.toLocaleDateString() + ' ' + props.trackedFlip.sellTime.toLocaleTimeString()}</p>\n                                        <p>\n                                            Sold in:{' '}\n                                            {convertMilliSecondsIntoLegibleString(props.trackedFlip.sellTime.getTime() - props.trackedFlip.buyTime.getTime())}\n                                        </p>\n                                    </div>\n                                }\n                            />\n                        </span>\n                    </p>\n                    <p>Profit changes:</p>\n                    <ShowMoreText\n                        alwaysShowAll={props.trackedFlip.propertyChanges.length < 2}\n                        allowShowLess={true}\n                        content={\n                            <div style={{ display: 'flex', justifyContent: 'space-between' }}>\n                                <Table>\n                                    <tbody>\n                                        {props.trackedFlip.propertyChanges.map((change, i) => (\n                                            <tr key={props.trackedFlip.uId + '-' + i}>\n                                                <td>{change.description}</td>\n                                                <td>\n                                                    {change.effect > 0 ? (\n                                                        <span style={{ color: 'lime', whiteSpace: 'nowrap', marginLeft: '5px' }}>\n                                                            +<Number number={change.effect} /> Coins\n                                                        </span>\n                                                    ) : (\n                                                        <span style={{ color: 'red', whiteSpace: 'nowrap', marginLeft: '5px' }}>\n                                                            <Number number={change.effect} />\n                                                        </span>\n                                                    )}\n                                                </td>\n                                            </tr>\n                                        ))}\n                                    </tbody>\n                                </Table>\n                            </div>\n                        }\n                        initialHeight={50}\n                    />\n                </div>\n                <div style={{ display: 'flex', alignItems: 'end' }}>\n                    <Suspense>\n                        <div>\n                            {!props.trackedFlip.flags.has('None') ? (\n                                <Tooltip\n                                    type=\"hover\"\n                                    content={<HelpIcon style={{ color: '#007bff', cursor: 'pointer', marginRight: 5 }} />}\n                                    tooltipContent={<p>Flags: {Array.from(props.trackedFlip.flags).join(', ')}</p>}\n                                />\n                            ) : null}\n                            <FlipTrackingCopyButton trackedFlip={props.trackedFlip} />\n                        </div>\n                    </Suspense>\n                </div>\n            </div>\n            {props.ignoreProfit ? (\n                <>\n                    <hr />\n                    <p style={{ color: 'yellow' }}>\n                        <b>This flip is ignored from the profit calculation</b>\n                        <Button\n                            variant=\"info\"\n                            style={{ marginLeft: '10px' }}\n                            onClick={() => {\n                                props.onRemoveFlipFromIgnoreMap()\n                            }}\n                        >\n                            Re-Add\n                        </Button>\n                    </p>\n                </>\n            ) : null}\n        </ListGroup.Item>\n    )\n}\n"], "names": [], "mappings": ";;;;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAdA;;;;;;;;;;;;;;;AAwBO,SAAS,qBAAqB,KAAY;;IAC7C,IAAI,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAErB,SAAS,qCAAqC,cAAsB;QAChE,IAAI,SAAS,iBAAiB;QAE9B,IAAI,OAAO,KAAK,KAAK,CAAC,SAAS,QAC3B,qBAAqB,SAAS,OAC9B,QAAQ,KAAK,KAAK,CAAC,qBAAqB,OACxC,sBAAsB,qBAAqB,MAC3C,UAAU,KAAK,KAAK,CAAC,sBAAsB;QAE/C,OAAO,GAAG,OAAO,IAAI,OAAO,UAAU,GAAG,EAAE,EAAE,QAAQ,IAAI,QAAQ,OAAO,GAAG,EAAE,EAAE,UAAU,IAAI,UAAU,QAAQ,IAAI;IACvH;IAEA,qBACI,6LAAC,iMAAA,CAAA,YAAS,CAAC,IAAI;QACX,WAAW,yJAAA,CAAA,UAAM,CAAC,aAAa;QAC/B,eAAe,MAAM,aAAa;QAClC,IAAI,MAAM,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC;QACnC,OAAO;YACH,aAAa,MAAM,aAAa,GAAG,mBAAmB;YACtD,aAAa,MAAM,aAAa,GAAG,IAAI;QAC3C;;0BAEA,6LAAC;gBAAG,OAAO;oBAAE,SAAS;oBAAQ,SAAS;oBAAQ,gBAAgB;oBAAiB,UAAU;gBAAU;;kCAChG,6LAAC;wBACG,WAAU;wBACV,OAAO;4BAAE,QAAQ;wBAAU;wBAC3B,SAAS;4BACL,OAAO,IAAI,CAAC,CAAC,MAAM,EAAE,MAAM,WAAW,CAAC,IAAI,CAAC,GAAG,EAAE;wBACrD;;0CAEA,6LAAC,gIAAA,CAAA,UAAK;gCACF,aAAY;gCACZ,KAAK,oHAAA,CAAA,UAAG,CAAC,eAAe,CAAC,MAAM,WAAW,CAAC,IAAI,KAAK;gCACpD,QAAO;gCACP,OAAM;gCACN,KAAI;gCACJ,OAAO;oCAAE,aAAa;gCAAM;gCAC5B,SAAQ;;;;;;0CAEZ,6LAAC;gCAAK,OAAO;oCAAE,YAAY;oCAAU,GAAG,CAAA,GAAA,sHAAA,CAAA,kBAAe,AAAD,EAAE,MAAM,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC;gCAAC;0CAChF,CAAA,GAAA,sHAAA,CAAA,gCAA6B,AAAD,EAAE,MAAM,WAAW,CAAC,IAAI,CAAC,IAAI;;;;;;;;;;;;oBAGjE,MAAM,WAAW,CAAC,MAAM,GAAG,kBACxB,6LAAC;wBAAK,OAAO;4BAAE,OAAO;4BAAQ,YAAY;4BAAU,YAAY;wBAAM;;4BAAG;0CACpE,6LAAC,kIAAA,CAAA,UAAM;gCAAC,QAAQ,MAAM,WAAW,CAAC,MAAM;;;;;;4BAAI;;;;;;6CAGjD,6LAAC;wBAAK,OAAO;4BAAE,OAAO;4BAAO,YAAY;4BAAU,YAAY;wBAAM;;0CACjE,6LAAC,kIAAA,CAAA,UAAM;gCAAC,QAAQ,MAAM,WAAW,CAAC,MAAM;;;;;;4BAAI;;;;;;;;;;;;;0BAIxD,6LAAC;;;;;0BACD,6LAAC;gBAAI,OAAO;oBAAE,SAAS;oBAAQ,gBAAgB;oBAAgB,YAAY;gBAAS;;kCAChF,6LAAC,uLAAA,CAAA,OAAI;wBAAC,WAAW,yJAAA,CAAA,UAAM,CAAC,gBAAgB;kCACpC,cAAA,6LAAC;4BAAE,MAAM,CAAC,SAAS,EAAE,MAAM,WAAW,CAAC,aAAa,EAAE;4BAAE,QAAQ;4BAAU,WAAU;sCAChF,cAAA,6LAAC,uLAAA,CAAA,OAAI,CAAC,MAAM;gCAAC,WAAW,yJAAA,CAAA,UAAM,CAAC,kBAAkB;0CAC7C,cAAA,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;oCAAC,OAAO;wCAAE,QAAQ;oCAAE;;sDAC3B,6LAAC,kIAAA,CAAA,UAAM;4CAAC,QAAQ,MAAM,WAAW,CAAC,SAAS;;;;;;wCAAI;;;;;;;;;;;;;;;;;;;;;;kCAK/D,6LAAC,qKAAA,CAAA,UAAc;wBAAC,OAAO;4BAAE,UAAU;wBAAO;;;;;;kCAC1C,6LAAC,uLAAA,CAAA,OAAI;wBAAC,WAAW,yJAAA,CAAA,UAAM,CAAC,gBAAgB;kCACpC,cAAA,6LAAC;4BAAE,MAAM,CAAC,SAAS,EAAE,MAAM,WAAW,CAAC,WAAW,EAAE;4BAAE,QAAQ;4BAAU,WAAU;sCAC9E,cAAA,6LAAC,uLAAA,CAAA,OAAI,CAAC,MAAM;gCAAC,WAAW,yJAAA,CAAA,UAAM,CAAC,kBAAkB;0CAC7C,cAAA,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;oCAAC,OAAO;wCAAE,QAAQ;oCAAE;;sDAC3B,6LAAC,kIAAA,CAAA,UAAM;4CAAC,QAAQ,MAAM,WAAW,CAAC,OAAO;;;;;;wCAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAMjE,6LAAC;gBAAI,OAAO;oBAAE,SAAS;oBAAQ,gBAAgB;gBAAgB;;kCAC3D,6LAAC;wBAAI,OAAO;4BAAE,UAAU;wBAAM;;0CAC1B,6LAAC;gCAAE,OAAO;oCAAE,WAAW;gCAAO;;kDAC1B,6LAAC,oIAAA,CAAA,UAAO;wCACJ,uBACI,6LAAC;;gDAAK;8DACM,6LAAC,yLAAA,CAAA,QAAK;oDAAC,IAAG;8DAAQ,MAAM,WAAW,CAAC,MAAM,CAAC,UAAU;;;;;;;;;;;;wCAGrE,8BACI,6LAAC;sDAAK;;;;;;wCAKV,MAAM;;;;;;kDAEV,6LAAC;wCAAK,OAAO;4CAAE,YAAY;wCAAO;kDAC9B,cAAA,6LAAC,oIAAA,CAAA,UAAO;4CACJ,MAAK;4CACL,uBACI,6LAAC;;kEACG,6LAAC;wDAAK,WAAW,yJAAA,CAAA,UAAM,CAAC,KAAK;;;;;;oDAAS;oDAAM,CAAA,GAAA,mIAAA,CAAA,UAAM,AAAD,EAAE,MAAM,WAAW,CAAC,QAAQ,EAAE,OAAO;;;;;;;4CAG9F,8BACI,6LAAC;;kEACG,6LAAC;;4DAAE;4DAAM,MAAM,WAAW,CAAC,OAAO,CAAC,kBAAkB,KAAK,MAAM,MAAM,WAAW,CAAC,OAAO,CAAC,kBAAkB;;;;;;;kEAC5G,6LAAC;;4DAAE;4DAAO,MAAM,WAAW,CAAC,QAAQ,CAAC,kBAAkB,KAAK,MAAM,MAAM,WAAW,CAAC,QAAQ,CAAC,kBAAkB;;;;;;;kEAC/G,6LAAC;;4DAAE;4DACU;4DACR,qCAAqC,MAAM,WAAW,CAAC,QAAQ,CAAC,OAAO,KAAK,MAAM,WAAW,CAAC,OAAO,CAAC,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOtI,6LAAC;0CAAE;;;;;;0CACH,6LAAC,8IAAA,CAAA,UAAY;gCACT,eAAe,MAAM,WAAW,CAAC,eAAe,CAAC,MAAM,GAAG;gCAC1D,eAAe;gCACf,uBACI,6LAAC;oCAAI,OAAO;wCAAE,SAAS;wCAAQ,gBAAgB;oCAAgB;8CAC3D,cAAA,6LAAC,yLAAA,CAAA,QAAK;kDACF,cAAA,6LAAC;sDACI,MAAM,WAAW,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,QAAQ,kBAC5C,6LAAC;;sEACG,6LAAC;sEAAI,OAAO,WAAW;;;;;;sEACvB,6LAAC;sEACI,OAAO,MAAM,GAAG,kBACb,6LAAC;gEAAK,OAAO;oEAAE,OAAO;oEAAQ,YAAY;oEAAU,YAAY;gEAAM;;oEAAG;kFACpE,6LAAC,kIAAA,CAAA,UAAM;wEAAC,QAAQ,OAAO,MAAM;;;;;;oEAAI;;;;;;uFAGtC,6LAAC;gEAAK,OAAO;oEAAE,OAAO;oEAAO,YAAY;oEAAU,YAAY;gEAAM;0EACjE,cAAA,6LAAC,kIAAA,CAAA,UAAM;oEAAC,QAAQ,OAAO,MAAM;;;;;;;;;;;;;;;;;mDATpC,MAAM,WAAW,CAAC,GAAG,GAAG,MAAM;;;;;;;;;;;;;;;;;;;;gCAmB3D,eAAe;;;;;;;;;;;;kCAGvB,6LAAC;wBAAI,OAAO;4BAAE,SAAS;4BAAQ,YAAY;wBAAM;kCAC7C,cAAA,6LAAC,6JAAA,CAAA,WAAQ;sCACL,cAAA,6LAAC;;oCACI,CAAC,MAAM,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,wBAC1B,6LAAC,oIAAA,CAAA,UAAO;wCACJ,MAAK;wCACL,uBAAS,6LAAC,4JAAA,CAAA,UAAQ;4CAAC,OAAO;gDAAE,OAAO;gDAAW,QAAQ;gDAAW,aAAa;4CAAE;;;;;;wCAChF,8BAAgB,6LAAC;;gDAAE;gDAAQ,MAAM,IAAI,CAAC,MAAM,WAAW,CAAC,KAAK,EAAE,IAAI,CAAC;;;;;;;;;;;+CAExE;kDACJ,6LAAC,wJAAA,CAAA,UAAsB;wCAAC,aAAa,MAAM,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAKrE,MAAM,YAAY,iBACf;;kCACI,6LAAC;;;;;kCACD,6LAAC;wBAAE,OAAO;4BAAE,OAAO;wBAAS;;0CACxB,6LAAC;0CAAE;;;;;;0CACH,6LAAC,2LAAA,CAAA,SAAM;gCACH,SAAQ;gCACR,OAAO;oCAAE,YAAY;gCAAO;gCAC5B,SAAS;oCACL,MAAM,yBAAyB;gCACnC;0CACH;;;;;;;;;;;;;+BAKT;;;;;;;AAGhB;GArLgB;;QACC,qIAAA,CAAA,YAAS;;;KADV", "debugId": null}}, {"offset": {"line": 1852, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/components/Search/Search.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"bar\": \"Search-module__Lg4wHG__bar\",\n  \"current\": \"Search-module__Lg4wHG__current\",\n  \"multiInputfield\": \"Search-module__Lg4wHG__multiInputfield\",\n  \"multiSearch\": \"Search-module__Lg4wHG__multiSearch\",\n  \"previousSearch\": \"Search-module__Lg4wHG__previousSearch\",\n  \"search\": \"Search-module__Lg4wHG__search\",\n  \"searchFormGroup\": \"Search-module__Lg4wHG__searchFormGroup\",\n  \"searchResultIcon\": \"Search-module__Lg4wHG__searchResultIcon\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 1868, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/Search/ApiSearchField.tsx"], "sourcesContent": ["'use client'\nimport { forwardRef, Ref, useRef, useState } from 'react'\nimport { AsyncTypeahead } from 'react-bootstrap-typeahead'\nimport { v4 as generateUUID } from 'uuid'\nimport Typeahead from 'react-bootstrap-typeahead/types/core/Typeahead'\nimport api from '../../api/ApiHelper'\nimport { Option } from 'react-bootstrap-typeahead/types/types'\nimport styles from './Search.module.css'\nimport Image from 'next/image'\nimport { getStyleForTier } from '../../utils/Formatter'\nimport { Form } from 'react-bootstrap'\n\ninterface Props {\n    onChange(selected: SearchResultItem[], searchText?: string)\n    disabled?: boolean\n    placeholder?: string\n    defaultValue?: string\n    searchFunction?(searchText: string): Promise<SearchResultItem[]>\n    selected?: SearchResultItem[]\n    defaultSelected?: SearchResultItem[]\n    className?: string\n    multiple: boolean\n}\n\nexport default (props: Props) => {\n    let [uuid] = useState(generateUUID())\n    let [results, setResults] = useState<SearchResultItem[]>([])\n    let [isLoading, setIsLoading] = useState(false)\n    let ref = useRef<Typeahead>(null)\n\n    function _onChange(selected: Option[]) {\n        props.onChange(selected as SearchResultItem[], ref?.current?.getInput()?.value)\n    }\n\n    function handleSearch(query) {\n        setIsLoading(true)\n\n        let searchFunction = props.searchFunction || api.search\n\n        searchFunction(query).then(results => {\n            setResults(\n                results.map(r => {\n                    return {\n                        label: r.dataItem.name || '-',\n                        ...r\n                    }\n                })\n            )\n            setIsLoading(false)\n        })\n    }\n\n    return (\n        <AsyncTypeahead\n            id={uuid}\n            className={`${styles.multiSearch} ${props.className}`}\n            disabled={props.disabled}\n            inputProps={{ className: styles.multiInputfield }}\n            filterBy={() => true}\n            isLoading={isLoading}\n            key={uuid}\n            labelKey=\"label\"\n            renderMenuItemChildren={(option, { text }) => {\n                let o: any = option\n                let isDuplicate = results.filter((element, index) => element.dataItem.name === o.dataItem.name).length > 1\n                return (\n                    <>\n                        <Image\n                            className={`${styles.searchResultIcon} playerHeadIcon`}\n                            crossOrigin=\"anonymous\"\n                            width={32}\n                            height={32}\n                            src={o.dataItem.iconUrl}\n                            alt=\"\"\n                        />\n                        <span style={isDuplicate ? getStyleForTier(o.tier) : undefined}>{o.label}</span>\n                    </>\n                )\n            }}\n            defaultSelected={props.defaultSelected}\n            minLength={1}\n            onSearch={handleSearch}\n            defaultInputValue={props.defaultValue}\n            selected={props.selected}\n            options={results}\n            placeholder={props.placeholder || 'Search item...'}\n            onChange={_onChange}\n            ref={ref}\n            multiple={props.multiple}\n        />\n    )\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AACA;AAEA;AAEA;AACA;AACA;;;AATA;;;;;;;;0CAwBe,CAAC;;IACZ,IAAI,CAAC,KAAK,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,GAAA,wLAAA,CAAA,KAAY,AAAD;IACjC,IAAI,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB,EAAE;IAC3D,IAAI,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,IAAI,MAAM,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAa;IAE5B,SAAS,UAAU,QAAkB;QACjC,MAAM,QAAQ,CAAC,UAAgC,KAAK,SAAS,YAAY;IAC7E;IAEA,SAAS,aAAa,KAAK;QACvB,aAAa;QAEb,IAAI,iBAAiB,MAAM,cAAc,IAAI,oHAAA,CAAA,UAAG,CAAC,MAAM;QAEvD,eAAe,OAAO,IAAI,CAAC,CAAA;YACvB,WACI,QAAQ,GAAG,CAAC,CAAA;gBACR,OAAO;oBACH,OAAO,EAAE,QAAQ,CAAC,IAAI,IAAI;oBAC1B,GAAG,CAAC;gBACR;YACJ;YAEJ,aAAa;QACjB;IACJ;IAEA,qBACI,6LAAC,uPAAA,CAAA,iBAAc;QACX,IAAI;QACJ,WAAW,GAAG,6IAAA,CAAA,UAAM,CAAC,WAAW,CAAC,CAAC,EAAE,MAAM,SAAS,EAAE;QACrD,UAAU,MAAM,QAAQ;QACxB,YAAY;YAAE,WAAW,6IAAA,CAAA,UAAM,CAAC,eAAe;QAAC;QAChD,UAAU,IAAM;QAChB,WAAW;QAEX,UAAS;QACT,wBAAwB,CAAC,QAAQ,EAAE,IAAI,EAAE;YACrC,IAAI,IAAS;YACb,IAAI,cAAc,QAAQ,MAAM,CAAC,CAAC,SAAS,QAAU,QAAQ,QAAQ,CAAC,IAAI,KAAK,EAAE,QAAQ,CAAC,IAAI,EAAE,MAAM,GAAG;YACzG,qBACI;;kCACI,6LAAC,gIAAA,CAAA,UAAK;wBACF,WAAW,GAAG,6IAAA,CAAA,UAAM,CAAC,gBAAgB,CAAC,eAAe,CAAC;wBACtD,aAAY;wBACZ,OAAO;wBACP,QAAQ;wBACR,KAAK,EAAE,QAAQ,CAAC,OAAO;wBACvB,KAAI;;;;;;kCAER,6LAAC;wBAAK,OAAO,cAAc,CAAA,GAAA,sHAAA,CAAA,kBAAe,AAAD,EAAE,EAAE,IAAI,IAAI;kCAAY,EAAE,KAAK;;;;;;;;QAGpF;QACA,iBAAiB,MAAM,eAAe;QACtC,WAAW;QACX,UAAU;QACV,mBAAmB,MAAM,YAAY;QACrC,UAAU,MAAM,QAAQ;QACxB,SAAS;QACT,aAAa,MAAM,WAAW,IAAI;QAClC,UAAU;QACV,KAAK;QACL,UAAU,MAAM,QAAQ;OA5BnB;;;;;AA+BjB", "debugId": null}}, {"offset": {"line": 1975, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/FlipTracking/FlipTracking.tsx"], "sourcesContent": ["'use client'\nimport DangerousIcon from '@mui/icons-material/Dangerous'\nimport Image from 'next/image'\nimport { ChangeEvent, useEffect, useState } from 'react'\nimport { Form, ListGroup } from 'react-bootstrap'\nimport { Item, Menu, useContextMenu } from 'react-contexify'\nimport { useForceUpdate, useQueryParamState, useWasAlreadyLoggedIn } from '../../utils/Hooks'\nimport { getSettingsObject, IGNORE_FLIP_TRACKING_PROFIT, setSetting } from '../../utils/SettingsUtils'\nimport { isClientSideRendering } from '../../utils/SSRUtils'\nimport styles from './FlipTracking.module.css'\nimport DatePicker from 'react-datepicker'\nimport api from '../../api/ApiHelper'\nimport GoogleSignIn from '../GoogleSignIn/GoogleSignIn'\nimport { PREMIUM_RANK, hasHighEnoughPremium } from '../../utils/PremiumTypeUtils'\nimport { getLoadingElement } from '../../utils/LoadingUtils'\nimport Link from 'next/link'\nimport { FlipTrackingTotalProfitCalculation } from './FlipTrackingTotalProfitCalculation'\nimport { FlipTrackingListItem } from './FlipTrackingListItem'\nimport ShowMoreText from '../ShowMoreText/ShowMoreText'\nimport { NumericFormat } from 'react-number-format'\nimport { getDecimalSeparator, getThousandSeparator } from '../../utils/Formatter'\nimport ApiSearchField from '../Search/ApiSearchField'\n\ninterface Props {\n    totalProfit?: number\n    trackedFlips?: FlipTrackingFlip[]\n    highlightedFlipUid?: string\n    playerUUID: string\n}\n\ninterface SortOption {\n    label: string\n    value: string\n    sortFunction(flips: FlipTrackingFlip[])\n}\n\ninterface FilterOption {\n    label: string\n    value: string\n    filterFunction(flips: FlipTrackingFlip[])\n}\n\nenum FlipTrackingFlags {\n    None = 'None',\n    DifferentBuyer = 'DifferentBuyer',\n    ViaTrade = 'ViaTrade',\n    MultiItemTrade = 'MultiItemTrade'\n}\n\nconst SORT_OPTIONS: SortOption[] = [\n    {\n        label: 'Time',\n        value: 'timeAsc',\n        sortFunction: flips => flips.sort((a, b) => b.sellTime.getTime() - a.sellTime.getTime())\n    },\n    {\n        label: 'Profit (highest => lowest)',\n        value: 'profitDec',\n        sortFunction: flips => flips.sort((a, b) => b.profit - a.profit)\n    },\n    {\n        label: 'Profit (lowest => highest)',\n        value: 'profitAsc',\n        sortFunction: flips => flips.sort((a, b) => a.profit - b.profit)\n    },\n    {\n        label: 'Sell price',\n        value: 'sellPrice',\n        sortFunction: flips => flips.sort((a, b) => b.soldFor - a.soldFor)\n    },\n    {\n        label: 'Profit%',\n        value: 'profitPercent',\n        sortFunction: flips => flips.sort((a, b) => b.profit / b.pricePaid - a.profit / a.pricePaid)\n    }\n]\n\nconst FILTER_OPTIONS: FilterOption[] = [\n    { label: '-', value: 'all', filterFunction: flips => flips },\n    { label: 'Only Trades', value: 'only-trades', filterFunction: flips => flips.filter(flip => flip.flags.has(FlipTrackingFlags.ViaTrade)) },\n    {\n        label: 'Only Same Buyer',\n        value: 'same-buyer',\n        filterFunction: flips => flips.filter(flip => !flip.flags.has(FlipTrackingFlags.DifferentBuyer))\n    }\n]\n\nconst TRACKED_FLIP_CONTEXT_MENU_ID = 'tracked-flip-context-menu'\n\nconst DEFAULT_TIME_FILTER_RANGE = 1000 * 60 * 60 * 24 * 7\nconst PREMIUM_TIME_FILTER_RANGE = 1000 * 60 * 60 * 24 * 30 * 2\n\nexport function FlipTracking(props: Props) {\n    let [trackedFlips, setTrackedFlips] = useState<FlipTrackingFlip[]>(props.trackedFlips || [])\n    let [ignoreProfitMap, setIgnoreProfitMap] = useState(getSettingsObject(IGNORE_FLIP_TRACKING_PROFIT, {}))\n    let [rangeStartDate, setRangeStartDate] = useState(new Date(new Date().getTime() - DEFAULT_TIME_FILTER_RANGE))\n    let [rangeEndDate, setRangeEndDate] = useState(new Date())\n    let [hasPremium, setHasPremium] = useState(false)\n    let [isLoading, setIsLoading] = useState(false)\n    let [isLoggedIn, setIsLoggedIn] = useState(false)\n    let [wasManualLoginClick, setWasManualLoginClick] = useState(false)\n    let wasAlreadyLoggedIn = useWasAlreadyLoggedIn()\n    let forceUpdate = useForceUpdate()\n\n    // Filters\n    let [_orderBy, _setOrderBy] = useQueryParamState<string>('order', SORT_OPTIONS[0].value)\n    let [_filterBy, _setFilterBy] = useQueryParamState<string>('filter', FILTER_OPTIONS[0].value)\n    let [minProfit, setMinProfit] = useQueryParamState<string | undefined>('minProfit', undefined)\n    let [maxProfit, setMaxProfit] = useQueryParamState<string | undefined>('maxProfit', undefined)\n    let [_item, _setItem] = useQueryParamState<string | undefined>('item', undefined)\n\n    let orderBy = SORT_OPTIONS.find(o => o.value === _orderBy)!\n    let filterBy = FILTER_OPTIONS.find(f => f.value === _filterBy)!\n    let item = _item ? (JSON.parse(_item) as SearchResultItem) : undefined\n\n    let setOrderBy = (newValue: SortOption) => {\n        _setOrderBy(newValue.value)\n    }\n    let setFilterBy = (newValue: FilterOption) => {\n        _setFilterBy(newValue.value)\n    }\n    let setItem = (newValue: SearchResultItem | undefined) => {\n        if (!newValue) {\n            _setItem(undefined)\n        } else {\n            _setItem(JSON.stringify(newValue))\n        }\n    }\n\n    const { show } = useContextMenu({\n        id: TRACKED_FLIP_CONTEXT_MENU_ID\n    })\n\n    useEffect(() => {\n        if (props.highlightedFlipUid && isClientSideRendering()) {\n            let element = document.getElementById(props.highlightedFlipUid) as HTMLElement\n            window.scrollTo({\n                top: element.offsetTop\n            })\n        }\n        refreshIgnoredFlipsInLocalstorage()\n    }, [])\n\n    /**\n     * Checks if flips are marked to be ignored for this player, but aren't there anymore, they are removed from the localStorage\n     */\n    function refreshIgnoredFlipsInLocalstorage() {\n        let newIgnoreMap = {}\n        trackedFlips.forEach(flip => {\n            if (ignoreProfitMap[flip.uId.toString(16)]) {\n                newIgnoreMap[flip.uId.toString(16)] = true\n            }\n        })\n        setSetting(IGNORE_FLIP_TRACKING_PROFIT, JSON.stringify(newIgnoreMap))\n        setIgnoreProfitMap(newIgnoreMap)\n    }\n\n    function updateOrderBy(event: ChangeEvent<HTMLSelectElement>) {\n        let selectedIndex = event.target.options.selectedIndex\n        let value = event.target.options[selectedIndex].getAttribute('value')!\n        let sortOption = SORT_OPTIONS.find(option => option.value === value)\n        if (sortOption) {\n            setOrderBy(sortOption)\n        }\n    }\n\n    function handleContextMenuForTrackedFlip(event) {\n        event.preventDefault()\n        show({ event: event, props: { uid: event.currentTarget.id } })\n    }\n\n    async function loadFlipsForTimespan(from: Date, to: Date) {\n        setIsLoading(true)\n        let newFlips = await api.getTrackedFlipsForPlayer(props.playerUUID, from, to)\n        setTrackedFlips(newFlips.flips)\n        setIsLoading(false)\n    }\n\n    function onAfterLogin() {\n        setIsLoggedIn(true)\n        api.refreshLoadPremiumProducts(products => {\n            let hasEnoughPremium = hasHighEnoughPremium(products, PREMIUM_RANK.PREMIUM)\n            setHasPremium(hasEnoughPremium)\n\n            if (wasManualLoginClick && hasEnoughPremium) {\n                window.scrollTo({ top: 0, behavior: 'smooth' })\n            }\n        })\n    }\n\n    let flipsToDisplay = [...trackedFlips]\n    if (orderBy) {\n        let sortOption = SORT_OPTIONS.find(option => option.value === orderBy.value)\n        flipsToDisplay = sortOption?.sortFunction(trackedFlips)\n    }\n\n    let currentItemContextMenuElement = (\n        <div>\n            <Menu id={TRACKED_FLIP_CONTEXT_MENU_ID} theme={'dark'}>\n                <Item\n                    onClick={params => {\n                        ignoreProfitMap[params.props.uid] = true\n                        setSetting(IGNORE_FLIP_TRACKING_PROFIT, JSON.stringify(ignoreProfitMap))\n                        setIgnoreProfitMap(ignoreProfitMap)\n                        forceUpdate()\n                    }}\n                >\n                    <DangerousIcon style={{ marginRight: '5px' }} />\n                    Ignore flip for profit calculation\n                </Item>\n            </Menu>\n        </div>\n    )\n\n    flipsToDisplay = filterBy.filterFunction(flipsToDisplay)\n    if (minProfit && !isNaN(parseInt(minProfit))) {\n        flipsToDisplay = flipsToDisplay.filter(flip => flip.profit >= parseInt(minProfit!))\n    }\n    if (maxProfit && !isNaN(parseInt(maxProfit))) {\n        flipsToDisplay = flipsToDisplay.filter(flip => flip.profit <= parseInt(maxProfit!))\n    }\n    if (item) {\n        flipsToDisplay = flipsToDisplay.filter(flip => flip.item.tag === item!.id)\n    }\n\n    let list = flipsToDisplay.map((trackedFlip, i) => {\n        return (\n            <FlipTrackingListItem\n                key={trackedFlip.uId + ' - ' + trackedFlip.sellTime.getTime() + '-' + (ignoreProfitMap[trackedFlip.uId.toString(16)] || '')}\n                trackedFlip={trackedFlip}\n                isHighlighted={props.highlightedFlipUid === trackedFlip.uId.toString(16)}\n                onContextMenu={handleContextMenuForTrackedFlip}\n                ignoreProfit={ignoreProfitMap[trackedFlip.uId.toString(16)] || false}\n                onRemoveFlipFromIgnoreMap={() => {\n                    let newIgnoreMap = { ...ignoreProfitMap }\n                    delete newIgnoreMap[trackedFlip.uId.toString(16)]\n                    setSetting(IGNORE_FLIP_TRACKING_PROFIT, JSON.stringify(newIgnoreMap))\n                    setIgnoreProfitMap(newIgnoreMap)\n                }}\n            />\n        )\n    })\n\n    return (\n        <div>\n            <FlipTrackingTotalProfitCalculation flips={trackedFlips} ignoreProfitMap={ignoreProfitMap} />\n            <div className={styles.topContainer}>\n                <ShowMoreText\n                    allowShowLess\n                    initialHeight={60}\n                    content={\n                        <div style={{ display: 'flex', gap: 10, flexDirection: 'column', paddingBottom: 20 }}>\n                            <div className={styles.filterContainer}>\n                                <label htmlFor=\"flag-filter\" className={styles.filterLabel}>\n                                    Sort:\n                                </label>\n                                <Form.Select className={styles.filterValueField} defaultValue={orderBy.value} onChange={updateOrderBy}>\n                                    {SORT_OPTIONS.map(option => (\n                                        <option key={option.value} value={option.value}>\n                                            {option.label}\n                                        </option>\n                                    ))}\n                                </Form.Select>\n                            </div>\n                            <div className={styles.filterContainer}>\n                                <label htmlFor=\"flag-filter\" className={styles.filterLabel}>\n                                    Filter:\n                                </label>\n                                <Form.Select\n                                    id=\"flag-filter\"\n                                    className={styles.filterValueField}\n                                    defaultValue={filterBy.value}\n                                    onChange={e => {\n                                        setFilterBy(FILTER_OPTIONS.find(option => option.value === e.target.value) || FILTER_OPTIONS[0])\n                                    }}\n                                >\n                                    {FILTER_OPTIONS.map(option => (\n                                        <option key={option.value} value={option.value}>\n                                            {option.label}\n                                        </option>\n                                    ))}\n                                </Form.Select>\n                            </div>\n                            <div className={styles.filterContainer}>\n                                <label className={styles.filterLabel}>Min. Profit:</label>\n                                <div className={styles.filterValueField}>\n                                    <NumericFormat\n                                        defaultValue={minProfit}\n                                        onValueChange={v => setMinProfit(v.floatValue?.toString())}\n                                        thousandSeparator={getThousandSeparator()}\n                                        decimalSeparator={getDecimalSeparator()}\n                                        allowNegative={false}\n                                        decimalScale={0}\n                                        customInput={Form.Control}\n                                    />\n                                </div>\n                            </div>\n                            <div className={styles.filterContainer}>\n                                <label className={styles.filterLabel}>Max. Profit:</label>\n                                <div className={styles.filterValueField}>\n                                    <NumericFormat\n                                        defaultValue={maxProfit}\n                                        onValueChange={v => setMaxProfit(v.floatValue?.toString())}\n                                        thousandSeparator={getThousandSeparator()}\n                                        decimalSeparator={getDecimalSeparator()}\n                                        allowNegative={false}\n                                        decimalScale={0}\n                                        customInput={Form.Control}\n                                    />\n                                </div>\n                            </div>\n                            <div className={styles.filterContainer}>\n                                <label className={styles.filterLabel}>Item:</label>\n                                <div className={`${styles.itemFilterContainer} ${styles.filterValueField}`}>\n                                    <ApiSearchField\n                                        multiple={false}\n                                        defaultSelected={item ? [item] : undefined}\n                                        className={styles.multiSearch}\n                                        onChange={items => {\n                                            if (!items || items.length === 0) {\n                                                setItem(undefined)\n                                            } else {\n                                                setItem(items[0])\n                                            }\n                                        }}\n                                        searchFunction={api.itemSearch}\n                                    />\n                                </div>\n                            </div>\n                        </div>\n                    }\n                />\n                <div style={{ display: 'flex', flexDirection: 'column', gap: 12, alignItems: 'center' }}>\n                    <div className={styles.filterContainer}>\n                        <label style={{ marginRight: 15 }}>From: </label>\n                        <div style={{ paddingRight: 15 }}>\n                            <DatePicker\n                                onChange={e => {\n                                    setRangeStartDate(e ?? new Date())\n                                    loadFlipsForTimespan(e ?? new Date(), rangeEndDate)\n                                }}\n                                className={'form-control'}\n                                minDate={new Date(new Date().getTime() - (hasPremium ? PREMIUM_TIME_FILTER_RANGE : DEFAULT_TIME_FILTER_RANGE))}\n                                maxDate={new Date()}\n                                selected={rangeStartDate}\n                            />\n                        </div>\n                        <label style={{ marginRight: 15 }}>To: </label>\n                        <DatePicker\n                            className={'form-control'}\n                            onChange={e => {\n                                setRangeEndDate(e ?? new Date())\n                                loadFlipsForTimespan(rangeStartDate, e ?? new Date())\n                            }}\n                            minDate={new Date(new Date().getTime() - (hasPremium ? PREMIUM_TIME_FILTER_RANGE : DEFAULT_TIME_FILTER_RANGE))}\n                            maxDate={new Date()}\n                            selected={rangeEndDate}\n                        />\n                    </div>\n                    <div className={styles.noPremiumInfoText}>\n                        Only auctions sold in the last 7 days are displayed here. <br /> You can see more with <Link href={'/premium'}>Premium</Link>\n                    </div>\n                </div>\n            </div>\n            {isLoading ? (\n                getLoadingElement()\n            ) : (\n                <div>\n                    {trackedFlips.length === 0 ? (\n                        <div className={styles.noAuctionFound}>\n                            <Image src=\"/Barrier.png\" width=\"24\" height=\"24\" alt=\"not found icon\" style={{ float: 'left', marginRight: '5px' }} />{' '}\n                            <p>We couldn't find any flips.</p>\n                        </div>\n                    ) : (\n                        <ListGroup className={styles.list}>{list}</ListGroup>\n                    )}\n                </div>\n            )}\n            <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>\n                <div style={wasAlreadyLoggedIn || isLoggedIn ? { visibility: 'collapse', height: 0 } : {}}>\n                    <p>\n                        You can get flips further in the past with{' '}\n                        <Link href={'/premium'} style={{ marginBottom: '15px' }}>\n                            Premium\n                        </Link>\n                        .\n                    </p>\n                    <GoogleSignIn\n                        onAfterLogin={onAfterLogin}\n                        onManualLoginClick={() => {\n                            setWasManualLoginClick(true)\n                        }}\n                    />\n                </div>\n            </div>\n            {currentItemContextMenuElement}\n        </div>\n    )\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AArBA;;;;;;;;;;;;;;;;;;;;;;AA0CA,IAAA,AAAK,2CAAA;;;;;WAAA;EAAA;AAOL,MAAM,eAA6B;IAC/B;QACI,OAAO;QACP,OAAO;QACP,cAAc,CAAA,QAAS,MAAM,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,QAAQ,CAAC,OAAO,KAAK,EAAE,QAAQ,CAAC,OAAO;IACzF;IACA;QACI,OAAO;QACP,OAAO;QACP,cAAc,CAAA,QAAS,MAAM,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,MAAM,GAAG,EAAE,MAAM;IACnE;IACA;QACI,OAAO;QACP,OAAO;QACP,cAAc,CAAA,QAAS,MAAM,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,MAAM,GAAG,EAAE,MAAM;IACnE;IACA;QACI,OAAO;QACP,OAAO;QACP,cAAc,CAAA,QAAS,MAAM,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,OAAO,GAAG,EAAE,OAAO;IACrE;IACA;QACI,OAAO;QACP,OAAO;QACP,cAAc,CAAA,QAAS,MAAM,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,MAAM,GAAG,EAAE,SAAS,GAAG,EAAE,MAAM,GAAG,EAAE,SAAS;IAC/F;CACH;AAED,MAAM,iBAAiC;IACnC;QAAE,OAAO;QAAK,OAAO;QAAO,gBAAgB,CAAA,QAAS;IAAM;IAC3D;QAAE,OAAO;QAAe,OAAO;QAAe,gBAAgB,CAAA,QAAS,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,KAAK,CAAC,GAAG;IAA8B;IACxI;QACI,OAAO;QACP,OAAO;QACP,gBAAgB,CAAA,QAAS,MAAM,MAAM,CAAC,CAAA,OAAQ,CAAC,KAAK,KAAK,CAAC,GAAG;IACjE;CACH;AAED,MAAM,+BAA+B;AAErC,MAAM,4BAA4B,OAAO,KAAK,KAAK,KAAK;AACxD,MAAM,4BAA4B,OAAO,KAAK,KAAK,KAAK,KAAK;AAEtD,SAAS,aAAa,KAAY;;IACrC,IAAI,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB,MAAM,YAAY,IAAI,EAAE;IAC3F,IAAI,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD,EAAE,0HAAA,CAAA,8BAA2B,EAAE,CAAC;IACrG,IAAI,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,KAAK,IAAI,OAAO,OAAO,KAAK;IACnF,IAAI,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,IAAI;IACnD,IAAI,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,IAAI,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,IAAI,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,IAAI,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,IAAI,qBAAqB,CAAA,GAAA,kHAAA,CAAA,wBAAqB,AAAD;IAC7C,IAAI,cAAc,CAAA,GAAA,kHAAA,CAAA,iBAAc,AAAD;IAE/B,UAAU;IACV,IAAI,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,kHAAA,CAAA,qBAAkB,AAAD,EAAU,SAAS,YAAY,CAAC,EAAE,CAAC,KAAK;IACvF,IAAI,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,kHAAA,CAAA,qBAAkB,AAAD,EAAU,UAAU,cAAc,CAAC,EAAE,CAAC,KAAK;IAC5F,IAAI,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,kHAAA,CAAA,qBAAkB,AAAD,EAAsB,aAAa;IACpF,IAAI,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,kHAAA,CAAA,qBAAkB,AAAD,EAAsB,aAAa;IACpF,IAAI,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,kHAAA,CAAA,qBAAkB,AAAD,EAAsB,QAAQ;IAEvE,IAAI,UAAU,aAAa,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK;IACjD,IAAI,WAAW,eAAe,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK;IACpD,IAAI,OAAO,QAAS,KAAK,KAAK,CAAC,SAA8B;IAE7D,IAAI,aAAa,CAAC;QACd,YAAY,SAAS,KAAK;IAC9B;IACA,IAAI,cAAc,CAAC;QACf,aAAa,SAAS,KAAK;IAC/B;IACA,IAAI,UAAU,CAAC;QACX,IAAI,CAAC,UAAU;YACX,SAAS;QACb,OAAO;YACH,SAAS,KAAK,SAAS,CAAC;QAC5B;IACJ;IAEA,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,uJAAA,CAAA,iBAAc,AAAD,EAAE;QAC5B,IAAI;IACR;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACN,IAAI,MAAM,kBAAkB,IAAI,CAAA,GAAA,qHAAA,CAAA,wBAAqB,AAAD,KAAK;gBACrD,IAAI,UAAU,SAAS,cAAc,CAAC,MAAM,kBAAkB;gBAC9D,OAAO,QAAQ,CAAC;oBACZ,KAAK,QAAQ,SAAS;gBAC1B;YACJ;YACA;QACJ;iCAAG,EAAE;IAEL;;KAEC,GACD,SAAS;QACL,IAAI,eAAe,CAAC;QACpB,aAAa,OAAO,CAAC,CAAA;YACjB,IAAI,eAAe,CAAC,KAAK,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE;gBACxC,YAAY,CAAC,KAAK,GAAG,CAAC,QAAQ,CAAC,IAAI,GAAG;YAC1C;QACJ;QACA,CAAA,GAAA,0HAAA,CAAA,aAAU,AAAD,EAAE,0HAAA,CAAA,8BAA2B,EAAE,KAAK,SAAS,CAAC;QACvD,mBAAmB;IACvB;IAEA,SAAS,cAAc,KAAqC;QACxD,IAAI,gBAAgB,MAAM,MAAM,CAAC,OAAO,CAAC,aAAa;QACtD,IAAI,QAAQ,MAAM,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,YAAY,CAAC;QAC7D,IAAI,aAAa,aAAa,IAAI,CAAC,CAAA,SAAU,OAAO,KAAK,KAAK;QAC9D,IAAI,YAAY;YACZ,WAAW;QACf;IACJ;IAEA,SAAS,gCAAgC,KAAK;QAC1C,MAAM,cAAc;QACpB,KAAK;YAAE,OAAO;YAAO,OAAO;gBAAE,KAAK,MAAM,aAAa,CAAC,EAAE;YAAC;QAAE;IAChE;IAEA,eAAe,qBAAqB,IAAU,EAAE,EAAQ;QACpD,aAAa;QACb,IAAI,WAAW,MAAM,oHAAA,CAAA,UAAG,CAAC,wBAAwB,CAAC,MAAM,UAAU,EAAE,MAAM;QAC1E,gBAAgB,SAAS,KAAK;QAC9B,aAAa;IACjB;IAEA,SAAS;QACL,cAAc;QACd,oHAAA,CAAA,UAAG,CAAC,0BAA0B,CAAC,CAAA;YAC3B,IAAI,mBAAmB,CAAA,GAAA,6HAAA,CAAA,uBAAoB,AAAD,EAAE,UAAU,6HAAA,CAAA,eAAY,CAAC,OAAO;YAC1E,cAAc;YAEd,IAAI,uBAAuB,kBAAkB;gBACzC,OAAO,QAAQ,CAAC;oBAAE,KAAK;oBAAG,UAAU;gBAAS;YACjD;QACJ;IACJ;IAEA,IAAI,iBAAiB;WAAI;KAAa;IACtC,IAAI,SAAS;QACT,IAAI,aAAa,aAAa,IAAI,CAAC,CAAA,SAAU,OAAO,KAAK,KAAK,QAAQ,KAAK;QAC3E,iBAAiB,YAAY,aAAa;IAC9C;IAEA,IAAI,8CACA,6LAAC;kBACG,cAAA,6LAAC,uJAAA,CAAA,OAAI;YAAC,IAAI;YAA8B,OAAO;sBAC3C,cAAA,6LAAC,uJAAA,CAAA,OAAI;gBACD,SAAS,CAAA;oBACL,eAAe,CAAC,OAAO,KAAK,CAAC,GAAG,CAAC,GAAG;oBACpC,CAAA,GAAA,0HAAA,CAAA,aAAU,AAAD,EAAE,0HAAA,CAAA,8BAA2B,EAAE,KAAK,SAAS,CAAC;oBACvD,mBAAmB;oBACnB;gBACJ;;kCAEA,6LAAC,iKAAA,CAAA,UAAa;wBAAC,OAAO;4BAAE,aAAa;wBAAM;;;;;;oBAAK;;;;;;;;;;;;;;;;;IAOhE,iBAAiB,SAAS,cAAc,CAAC;IACzC,IAAI,aAAa,CAAC,MAAM,SAAS,aAAa;QAC1C,iBAAiB,eAAe,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,IAAI,SAAS;IAC3E;IACA,IAAI,aAAa,CAAC,MAAM,SAAS,aAAa;QAC1C,iBAAiB,eAAe,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,IAAI,SAAS;IAC3E;IACA,IAAI,MAAM;QACN,iBAAiB,eAAe,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,CAAC,GAAG,KAAK,KAAM,EAAE;IAC7E;IAEA,IAAI,OAAO,eAAe,GAAG,CAAC,CAAC,aAAa;QACxC,qBACI,6LAAC,sJAAA,CAAA,uBAAoB;YAEjB,aAAa;YACb,eAAe,MAAM,kBAAkB,KAAK,YAAY,GAAG,CAAC,QAAQ,CAAC;YACrE,eAAe;YACf,cAAc,eAAe,CAAC,YAAY,GAAG,CAAC,QAAQ,CAAC,IAAI,IAAI;YAC/D,2BAA2B;gBACvB,IAAI,eAAe;oBAAE,GAAG,eAAe;gBAAC;gBACxC,OAAO,YAAY,CAAC,YAAY,GAAG,CAAC,QAAQ,CAAC,IAAI;gBACjD,CAAA,GAAA,0HAAA,CAAA,aAAU,AAAD,EAAE,0HAAA,CAAA,8BAA2B,EAAE,KAAK,SAAS,CAAC;gBACvD,mBAAmB;YACvB;WAVK,YAAY,GAAG,GAAG,QAAQ,YAAY,QAAQ,CAAC,OAAO,KAAK,MAAM,CAAC,eAAe,CAAC,YAAY,GAAG,CAAC,QAAQ,CAAC,IAAI,IAAI,EAAE;;;;;IAatI;IAEA,qBACI,6LAAC;;0BACG,6LAAC,oKAAA,CAAA,qCAAkC;gBAAC,OAAO;gBAAc,iBAAiB;;;;;;0BAC1E,6LAAC;gBAAI,WAAW,yJAAA,CAAA,UAAM,CAAC,YAAY;;kCAC/B,6LAAC,8IAAA,CAAA,UAAY;wBACT,aAAa;wBACb,eAAe;wBACf,uBACI,6LAAC;4BAAI,OAAO;gCAAE,SAAS;gCAAQ,KAAK;gCAAI,eAAe;gCAAU,eAAe;4BAAG;;8CAC/E,6LAAC;oCAAI,WAAW,yJAAA,CAAA,UAAM,CAAC,eAAe;;sDAClC,6LAAC;4CAAM,SAAQ;4CAAc,WAAW,yJAAA,CAAA,UAAM,CAAC,WAAW;sDAAE;;;;;;sDAG5D,6LAAC,uLAAA,CAAA,OAAI,CAAC,MAAM;4CAAC,WAAW,yJAAA,CAAA,UAAM,CAAC,gBAAgB;4CAAE,cAAc,QAAQ,KAAK;4CAAE,UAAU;sDACnF,aAAa,GAAG,CAAC,CAAA,uBACd,6LAAC;oDAA0B,OAAO,OAAO,KAAK;8DACzC,OAAO,KAAK;mDADJ,OAAO,KAAK;;;;;;;;;;;;;;;;8CAMrC,6LAAC;oCAAI,WAAW,yJAAA,CAAA,UAAM,CAAC,eAAe;;sDAClC,6LAAC;4CAAM,SAAQ;4CAAc,WAAW,yJAAA,CAAA,UAAM,CAAC,WAAW;sDAAE;;;;;;sDAG5D,6LAAC,uLAAA,CAAA,OAAI,CAAC,MAAM;4CACR,IAAG;4CACH,WAAW,yJAAA,CAAA,UAAM,CAAC,gBAAgB;4CAClC,cAAc,SAAS,KAAK;4CAC5B,UAAU,CAAA;gDACN,YAAY,eAAe,IAAI,CAAC,CAAA,SAAU,OAAO,KAAK,KAAK,EAAE,MAAM,CAAC,KAAK,KAAK,cAAc,CAAC,EAAE;4CACnG;sDAEC,eAAe,GAAG,CAAC,CAAA,uBAChB,6LAAC;oDAA0B,OAAO,OAAO,KAAK;8DACzC,OAAO,KAAK;mDADJ,OAAO,KAAK;;;;;;;;;;;;;;;;8CAMrC,6LAAC;oCAAI,WAAW,yJAAA,CAAA,UAAM,CAAC,eAAe;;sDAClC,6LAAC;4CAAM,WAAW,yJAAA,CAAA,UAAM,CAAC,WAAW;sDAAE;;;;;;sDACtC,6LAAC;4CAAI,WAAW,yJAAA,CAAA,UAAM,CAAC,gBAAgB;sDACnC,cAAA,6LAAC,uLAAA,CAAA,gBAAa;gDACV,cAAc;gDACd,eAAe,CAAA,IAAK,aAAa,EAAE,UAAU,EAAE;gDAC/C,mBAAmB,CAAA,GAAA,sHAAA,CAAA,uBAAoB,AAAD;gDACtC,kBAAkB,CAAA,GAAA,sHAAA,CAAA,sBAAmB,AAAD;gDACpC,eAAe;gDACf,cAAc;gDACd,aAAa,uLAAA,CAAA,OAAI,CAAC,OAAO;;;;;;;;;;;;;;;;;8CAIrC,6LAAC;oCAAI,WAAW,yJAAA,CAAA,UAAM,CAAC,eAAe;;sDAClC,6LAAC;4CAAM,WAAW,yJAAA,CAAA,UAAM,CAAC,WAAW;sDAAE;;;;;;sDACtC,6LAAC;4CAAI,WAAW,yJAAA,CAAA,UAAM,CAAC,gBAAgB;sDACnC,cAAA,6LAAC,uLAAA,CAAA,gBAAa;gDACV,cAAc;gDACd,eAAe,CAAA,IAAK,aAAa,EAAE,UAAU,EAAE;gDAC/C,mBAAmB,CAAA,GAAA,sHAAA,CAAA,uBAAoB,AAAD;gDACtC,kBAAkB,CAAA,GAAA,sHAAA,CAAA,sBAAmB,AAAD;gDACpC,eAAe;gDACf,cAAc;gDACd,aAAa,uLAAA,CAAA,OAAI,CAAC,OAAO;;;;;;;;;;;;;;;;;8CAIrC,6LAAC;oCAAI,WAAW,yJAAA,CAAA,UAAM,CAAC,eAAe;;sDAClC,6LAAC;4CAAM,WAAW,yJAAA,CAAA,UAAM,CAAC,WAAW;sDAAE;;;;;;sDACtC,6LAAC;4CAAI,WAAW,GAAG,yJAAA,CAAA,UAAM,CAAC,mBAAmB,CAAC,CAAC,EAAE,yJAAA,CAAA,UAAM,CAAC,gBAAgB,EAAE;sDACtE,cAAA,6LAAC,0IAAA,CAAA,UAAc;gDACX,UAAU;gDACV,iBAAiB,OAAO;oDAAC;iDAAK,GAAG;gDACjC,WAAW,yJAAA,CAAA,UAAM,CAAC,WAAW;gDAC7B,UAAU,CAAA;oDACN,IAAI,CAAC,SAAS,MAAM,MAAM,KAAK,GAAG;wDAC9B,QAAQ;oDACZ,OAAO;wDACH,QAAQ,KAAK,CAAC,EAAE;oDACpB;gDACJ;gDACA,gBAAgB,oHAAA,CAAA,UAAG,CAAC,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOtD,6LAAC;wBAAI,OAAO;4BAAE,SAAS;4BAAQ,eAAe;4BAAU,KAAK;4BAAI,YAAY;wBAAS;;0CAClF,6LAAC;gCAAI,WAAW,yJAAA,CAAA,UAAM,CAAC,eAAe;;kDAClC,6LAAC;wCAAM,OAAO;4CAAE,aAAa;wCAAG;kDAAG;;;;;;kDACnC,6LAAC;wCAAI,OAAO;4CAAE,cAAc;wCAAG;kDAC3B,cAAA,6LAAC,6JAAA,CAAA,UAAU;4CACP,UAAU,CAAA;gDACN,kBAAkB,KAAK,IAAI;gDAC3B,qBAAqB,KAAK,IAAI,QAAQ;4CAC1C;4CACA,WAAW;4CACX,SAAS,IAAI,KAAK,IAAI,OAAO,OAAO,KAAK,CAAC,aAAa,4BAA4B,yBAAyB;4CAC5G,SAAS,IAAI;4CACb,UAAU;;;;;;;;;;;kDAGlB,6LAAC;wCAAM,OAAO;4CAAE,aAAa;wCAAG;kDAAG;;;;;;kDACnC,6LAAC,6JAAA,CAAA,UAAU;wCACP,WAAW;wCACX,UAAU,CAAA;4CACN,gBAAgB,KAAK,IAAI;4CACzB,qBAAqB,gBAAgB,KAAK,IAAI;wCAClD;wCACA,SAAS,IAAI,KAAK,IAAI,OAAO,OAAO,KAAK,CAAC,aAAa,4BAA4B,yBAAyB;wCAC5G,SAAS,IAAI;wCACb,UAAU;;;;;;;;;;;;0CAGlB,6LAAC;gCAAI,WAAW,yJAAA,CAAA,UAAM,CAAC,iBAAiB;;oCAAE;kDACoB,6LAAC;;;;;oCAAK;kDAAuB,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAM;kDAAY;;;;;;;;;;;;;;;;;;;;;;;;YAI1H,YACG,CAAA,GAAA,yHAAA,CAAA,oBAAiB,AAAD,oBAEhB,6LAAC;0BACI,aAAa,MAAM,KAAK,kBACrB,6LAAC;oBAAI,WAAW,yJAAA,CAAA,UAAM,CAAC,cAAc;;sCACjC,6LAAC,gIAAA,CAAA,UAAK;4BAAC,KAAI;4BAAe,OAAM;4BAAK,QAAO;4BAAK,KAAI;4BAAiB,OAAO;gCAAE,OAAO;gCAAQ,aAAa;4BAAM;;;;;;wBAAM;sCACvH,6LAAC;sCAAE;;;;;;;;;;;yCAGP,6LAAC,iMAAA,CAAA,YAAS;oBAAC,WAAW,yJAAA,CAAA,UAAM,CAAC,IAAI;8BAAG;;;;;;;;;;;0BAIhD,6LAAC;gBAAI,OAAO;oBAAE,SAAS;oBAAQ,gBAAgB;oBAAU,YAAY;gBAAS;0BAC1E,cAAA,6LAAC;oBAAI,OAAO,sBAAsB,aAAa;wBAAE,YAAY;wBAAY,QAAQ;oBAAE,IAAI,CAAC;;sCACpF,6LAAC;;gCAAE;gCAC4C;8CAC3C,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAM;oCAAY,OAAO;wCAAE,cAAc;oCAAO;8CAAG;;;;;;gCAElD;;;;;;;sCAGX,6LAAC,8IAAA,CAAA,UAAY;4BACT,cAAc;4BACd,oBAAoB;gCAChB,uBAAuB;4BAC3B;;;;;;;;;;;;;;;;;YAIX;;;;;;;AAGb;GAlTgB;;QASa,kHAAA,CAAA,wBAAqB;QAC5B,kHAAA,CAAA,iBAAc;QAGF,kHAAA,CAAA,qBAAkB;QAChB,kHAAA,CAAA,qBAAkB;QAClB,kHAAA,CAAA,qBAAkB;QAClB,kHAAA,CAAA,qBAAkB;QAC1B,kHAAA,CAAA,qBAAkB;QAoBzB,uJAAA,CAAA,iBAAc;;;KArCnB", "debugId": null}}, {"offset": {"line": 2715, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/components/NavBar/NavBar.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"hamburgerIcon\": \"NavBar-module__yBvhsG__hamburgerIcon\",\n  \"logo\": \"NavBar-module__yBvhsG__logo\",\n  \"menuItem\": \"NavBar-module__yBvhsG__menuItem\",\n  \"navBar\": \"NavBar-module__yBvhsG__navBar\",\n  \"navClosing\": \"NavBar-module__yBvhsG__navClosing\",\n  \"navOpen\": \"NavBar-module__yBvhsG__navOpen\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 2729, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/NavBar/NavBar.tsx"], "sourcesContent": ["'use client'\nimport AccountBalanceIcon from '@mui/icons-material/AccountBalance'\nimport AccountIcon from '@mui/icons-material/AccountCircle'\nimport BuildIcon from '@mui/icons-material/Build'\nimport ChatIcon from '@mui/icons-material/Chat'\nimport DownloadIcon from '@mui/icons-material/Download'\nimport HomeIcon from '@mui/icons-material/Home'\nimport MenuIcon from '@mui/icons-material/Menu'\nimport NotificationIcon from '@mui/icons-material/NotificationsOutlined'\nimport PetsIcon from '@mui/icons-material/PetsOutlined'\nimport PolicyIcon from '@mui/icons-material/Policy'\nimport ShareIcon from '@mui/icons-material/ShareOutlined'\nimport StorefrontIcon from '@mui/icons-material/Storefront'\nimport CurrencyExchangeIcon from '@mui/icons-material/CurrencyExchange'\nimport Image from 'next/image'\nimport Link from 'next/link'\nimport React, { useEffect, useState } from 'react'\nimport { Menu, MenuItem, Sidebar } from 'react-pro-sidebar'\nimport { useForceUpdate } from '../../utils/Hooks'\nimport styles from './NavBar.module.css'\n\nlet resizePromise: NodeJS.Timeout | null = null\n\ninterface Props {\n    hamburgerIconStyle?: React.CSSProperties\n}\n\nfunction NavBar(props: Props) {\n    let [isWideOpen, setIsWideOpen] = useState(false)\n    let [isHovering, setIsHovering] = useState(false)\n    let [isSmall, setIsSmall] = useState(true)\n    let [collapsed, setCollapsed] = useState(true)\n    let forceUpdate = useForceUpdate()\n\n    useEffect(() => {\n        setIsSmall(document.body.clientWidth < 1500)\n\n        window.addEventListener('resize', resizeHandler)\n\n        return () => {\n            window.removeEventListener('resize', resizeHandler)\n        }\n    }, [])\n\n    useEffect(() => {\n        if (isWideOpen) {\n            document.addEventListener('click', outsideClickHandler, true)\n        } else {\n            document.removeEventListener('click', outsideClickHandler, true)\n        }\n\n        return () => {\n            document.removeEventListener('click', outsideClickHandler, true)\n        }\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [isWideOpen])\n\n    useEffect(() => {\n        setCollapsed(isCollapsed())\n    }, [isSmall, isWideOpen, isHovering])\n\n    function isCollapsed() {\n        if (isSmall) {\n            return false\n        }\n        return !isWideOpen && !isHovering\n    }\n\n    function outsideClickHandler(evt) {\n        const flyoutEl = document.getElementById('navBar')\n        const hamburgerEl = document.getElementById('hamburgerIcon')\n        let targetEl = evt.target\n\n        do {\n            if (targetEl === flyoutEl || targetEl === hamburgerEl) {\n                return\n            }\n            targetEl = (targetEl as any).parentNode\n        } while (targetEl)\n\n        if (isWideOpen) {\n            if (isSmall) {\n                let el = document.getElementById('pro-sidebar')\n                el?.classList.add(styles.navClosing)\n                el?.classList.remove(styles.navOpen)\n                setTimeout(() => {\n                    setIsWideOpen(false)\n                    el?.classList.remove(styles.navClosing)\n                }, 500)\n            } else {\n                setIsWideOpen(false)\n            }\n        }\n    }\n\n    function onMouseMove() {\n        setIsHovering(true)\n    }\n\n    function onMouseOut() {\n        setIsHovering(false)\n    }\n\n    function resizeHandler() {\n        if (resizePromise) {\n            return\n        }\n        resizePromise = setTimeout(() => {\n            setIsWideOpen(false)\n            setIsSmall(document.body.clientWidth < 1500)\n            forceUpdate()\n            resizePromise = null\n            let el = document.getElementById('pro-sidebar')\n            if (el) {\n                el.style.left = '0px'\n            }\n        }, 500)\n    }\n\n    function onHamburgerClick() {\n        if (isSmall && !isWideOpen) {\n            let el = document.getElementById('pro-sidebar')\n            if (el) {\n                el.hidden = false\n                el.style.left = '-270px'\n                setTimeout(() => {\n                    if (el) {\n                        el.classList.add(styles.navOpen)\n                    }\n                })\n                setTimeout(() => {\n                    setIsWideOpen(true)\n                }, 500)\n            }\n        } else {\n            setIsWideOpen(!isWideOpen)\n        }\n    }\n\n    return (\n        <span>\n            <aside className={styles.navBar} id=\"navBar\" onMouseEnter={onMouseMove} onMouseLeave={onMouseOut}>\n                <Sidebar id=\"pro-sidebar\" hidden={isSmall && !isWideOpen} backgroundColor=\"#1d1d1d\" collapsed={collapsed}>\n                    <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>\n                        <div>\n                            <div className={styles.logo}>\n                                <Image src=\"/logo512.png\" alt=\"Logo\" width={40} height={40} style={{ translate: '-5px' }} /> {!isCollapsed() ? 'Coflnet' : ''}\n                            </div>\n                        </div>\n                        <hr />\n                        <Menu>\n                            <MenuItem className={styles.menuItem} component={<Link href={'/'} />} icon={<HomeIcon />}>\n                                Home\n                            </MenuItem>\n                            <MenuItem className={styles.menuItem} component={<Link href={'/flipper'} />} icon={<StorefrontIcon />}>\n                                Item Flipper\n                            </MenuItem>\n                            <MenuItem className={styles.menuItem} component={<Link href={'/account'} />} icon={<AccountIcon />}>\n                                Account\n                            </MenuItem>\n                            <MenuItem className={styles.menuItem} component={<Link href={'/subscriptions'} />} icon={<NotificationIcon />}>\n                                Notifier\n                            </MenuItem>\n                            <MenuItem className={styles.menuItem} component={<Link href={'/crafts'} />} icon={<BuildIcon />}>\n                                Profitable Crafts\n                            </MenuItem>\n                            <MenuItem className={styles.menuItem} component={<Link href={'/premium'} />} icon={<AccountBalanceIcon />}>\n                                Premium / Shop\n                            </MenuItem>\n                            <MenuItem className={styles.menuItem} component={<Link href={'/trade'} />} icon={<CurrencyExchangeIcon />}>\n                                Trading\n                            </MenuItem>\n                            <MenuItem className={styles.menuItem} component={<Link href={'/kat'} />} icon={<PetsIcon />}>\n                                Kat Flips\n                            </MenuItem>\n                            <MenuItem className={styles.menuItem} component={<Link href={'/mod'} />} icon={<DownloadIcon />}>\n                                Mod\n                            </MenuItem>\n                            <MenuItem className={styles.menuItem} component={<Link href={'/ref'} />} icon={<ShareIcon />}>\n                                Referral\n                            </MenuItem>\n                            <MenuItem className={styles.menuItem} component={<Link href={'/about'} />} icon={<PolicyIcon />}>\n                                Links / Legal\n                            </MenuItem>\n                            <MenuItem className={styles.menuItem} component={<Link href={'/feedback'} />} icon={<ChatIcon />}>\n                                Feedback\n                            </MenuItem>\n                            <MenuItem\n                                className={styles.menuItem}\n                                component={<Link href={'https://discord.gg/wvKXfTgCfb'} target=\"_blank\" />}\n                                rel=\"noreferrer\"\n                                icon={<Image src=\"/discord_icon.svg\" alt=\"Discord icon\" height={24} width={32} />}\n                            >\n                                Discord\n                            </MenuItem>\n                        </Menu>\n                    </div>\n                </Sidebar>\n            </aside>\n            {isSmall ? (\n                <span onClick={onHamburgerClick} className={styles.hamburgerIcon} id=\"hamburgerIcon\" style={props.hamburgerIconStyle}>\n                    <MenuIcon fontSize=\"large\" />\n                </span>\n            ) : (\n                ''\n            )}\n        </span>\n    )\n}\n\nexport default NavBar\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAnBA;;;;;;;;;;;;;;;;;;;;AAqBA,IAAI,gBAAuC;AAM3C,SAAS,OAAO,KAAY;;IACxB,IAAI,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,IAAI,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,IAAI,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,IAAI,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,IAAI,cAAc,CAAA,GAAA,kHAAA,CAAA,iBAAc,AAAD;IAE/B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACN,WAAW,SAAS,IAAI,CAAC,WAAW,GAAG;YAEvC,OAAO,gBAAgB,CAAC,UAAU;YAElC;oCAAO;oBACH,OAAO,mBAAmB,CAAC,UAAU;gBACzC;;QACJ;2BAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACN,IAAI,YAAY;gBACZ,SAAS,gBAAgB,CAAC,SAAS,qBAAqB;YAC5D,OAAO;gBACH,SAAS,mBAAmB,CAAC,SAAS,qBAAqB;YAC/D;YAEA;oCAAO;oBACH,SAAS,mBAAmB,CAAC,SAAS,qBAAqB;gBAC/D;;QACA,uDAAuD;QAC3D;2BAAG;QAAC;KAAW;IAEf,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACN,aAAa;QACjB;2BAAG;QAAC;QAAS;QAAY;KAAW;IAEpC,SAAS;QACL,IAAI,SAAS;YACT,OAAO;QACX;QACA,OAAO,CAAC,cAAc,CAAC;IAC3B;IAEA,SAAS,oBAAoB,GAAG;QAC5B,MAAM,WAAW,SAAS,cAAc,CAAC;QACzC,MAAM,cAAc,SAAS,cAAc,CAAC;QAC5C,IAAI,WAAW,IAAI,MAAM;QAEzB,GAAG;YACC,IAAI,aAAa,YAAY,aAAa,aAAa;gBACnD;YACJ;YACA,WAAW,AAAC,SAAiB,UAAU;QAC3C,QAAS,SAAS;QAElB,IAAI,YAAY;YACZ,IAAI,SAAS;gBACT,IAAI,KAAK,SAAS,cAAc,CAAC;gBACjC,IAAI,UAAU,IAAI,6IAAA,CAAA,UAAM,CAAC,UAAU;gBACnC,IAAI,UAAU,OAAO,6IAAA,CAAA,UAAM,CAAC,OAAO;gBACnC,WAAW;oBACP,cAAc;oBACd,IAAI,UAAU,OAAO,6IAAA,CAAA,UAAM,CAAC,UAAU;gBAC1C,GAAG;YACP,OAAO;gBACH,cAAc;YAClB;QACJ;IACJ;IAEA,SAAS;QACL,cAAc;IAClB;IAEA,SAAS;QACL,cAAc;IAClB;IAEA,SAAS;QACL,IAAI,eAAe;YACf;QACJ;QACA,gBAAgB,WAAW;YACvB,cAAc;YACd,WAAW,SAAS,IAAI,CAAC,WAAW,GAAG;YACvC;YACA,gBAAgB;YAChB,IAAI,KAAK,SAAS,cAAc,CAAC;YACjC,IAAI,IAAI;gBACJ,GAAG,KAAK,CAAC,IAAI,GAAG;YACpB;QACJ,GAAG;IACP;IAEA,SAAS;QACL,IAAI,WAAW,CAAC,YAAY;YACxB,IAAI,KAAK,SAAS,cAAc,CAAC;YACjC,IAAI,IAAI;gBACJ,GAAG,MAAM,GAAG;gBACZ,GAAG,KAAK,CAAC,IAAI,GAAG;gBAChB,WAAW;oBACP,IAAI,IAAI;wBACJ,GAAG,SAAS,CAAC,GAAG,CAAC,6IAAA,CAAA,UAAM,CAAC,OAAO;oBACnC;gBACJ;gBACA,WAAW;oBACP,cAAc;gBAClB,GAAG;YACP;QACJ,OAAO;YACH,cAAc,CAAC;QACnB;IACJ;IAEA,qBACI,6LAAC;;0BACG,6LAAC;gBAAM,WAAW,6IAAA,CAAA,UAAM,CAAC,MAAM;gBAAE,IAAG;gBAAS,cAAc;gBAAa,cAAc;0BAClF,cAAA,6LAAC,iKAAA,CAAA,UAAO;oBAAC,IAAG;oBAAc,QAAQ,WAAW,CAAC;oBAAY,iBAAgB;oBAAU,WAAW;8BAC3F,cAAA,6LAAC;wBAAI,OAAO;4BAAE,QAAQ;4BAAQ,SAAS;4BAAQ,eAAe;wBAAS;;0CACnE,6LAAC;0CACG,cAAA,6LAAC;oCAAI,WAAW,6IAAA,CAAA,UAAM,CAAC,IAAI;;sDACvB,6LAAC,gIAAA,CAAA,UAAK;4CAAC,KAAI;4CAAe,KAAI;4CAAO,OAAO;4CAAI,QAAQ;4CAAI,OAAO;gDAAE,WAAW;4CAAO;;;;;;wCAAK;wCAAE,CAAC,gBAAgB,YAAY;;;;;;;;;;;;0CAGnI,6LAAC;;;;;0CACD,6LAAC,iKAAA,CAAA,OAAI;;kDACD,6LAAC,iKAAA,CAAA,WAAQ;wCAAC,WAAW,6IAAA,CAAA,UAAM,CAAC,QAAQ;wCAAE,yBAAW,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAM;;;;;;wCAAS,oBAAM,6LAAC,4JAAA,CAAA,UAAQ;;;;;kDAAK;;;;;;kDAG1F,6LAAC,iKAAA,CAAA,WAAQ;wCAAC,WAAW,6IAAA,CAAA,UAAM,CAAC,QAAQ;wCAAE,yBAAW,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAM;;;;;;wCAAgB,oBAAM,6LAAC,kKAAA,CAAA,UAAc;;;;;kDAAK;;;;;;kDAGvG,6LAAC,iKAAA,CAAA,WAAQ;wCAAC,WAAW,6IAAA,CAAA,UAAM,CAAC,QAAQ;wCAAE,yBAAW,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAM;;;;;;wCAAgB,oBAAM,6LAAC,qKAAA,CAAA,UAAW;;;;;kDAAK;;;;;;kDAGpG,6LAAC,iKAAA,CAAA,WAAQ;wCAAC,WAAW,6IAAA,CAAA,UAAM,CAAC,QAAQ;wCAAE,yBAAW,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAM;;;;;;wCAAsB,oBAAM,6LAAC,6KAAA,CAAA,UAAgB;;;;;kDAAK;;;;;;kDAG/G,6LAAC,iKAAA,CAAA,WAAQ;wCAAC,WAAW,6IAAA,CAAA,UAAM,CAAC,QAAQ;wCAAE,yBAAW,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAM;;;;;;wCAAe,oBAAM,6LAAC,6JAAA,CAAA,UAAS;;;;;kDAAK;;;;;;kDAGjG,6LAAC,iKAAA,CAAA,WAAQ;wCAAC,WAAW,6IAAA,CAAA,UAAM,CAAC,QAAQ;wCAAE,yBAAW,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAM;;;;;;wCAAgB,oBAAM,6LAAC,sKAAA,CAAA,UAAkB;;;;;kDAAK;;;;;;kDAG3G,6LAAC,iKAAA,CAAA,WAAQ;wCAAC,WAAW,6IAAA,CAAA,UAAM,CAAC,QAAQ;wCAAE,yBAAW,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAM;;;;;;wCAAc,oBAAM,6LAAC,wKAAA,CAAA,UAAoB;;;;;kDAAK;;;;;;kDAG3G,6LAAC,iKAAA,CAAA,WAAQ;wCAAC,WAAW,6IAAA,CAAA,UAAM,CAAC,QAAQ;wCAAE,yBAAW,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAM;;;;;;wCAAY,oBAAM,6LAAC,oKAAA,CAAA,UAAQ;;;;;kDAAK;;;;;;kDAG7F,6LAAC,iKAAA,CAAA,WAAQ;wCAAC,WAAW,6IAAA,CAAA,UAAM,CAAC,QAAQ;wCAAE,yBAAW,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAM;;;;;;wCAAY,oBAAM,6LAAC,gKAAA,CAAA,UAAY;;;;;kDAAK;;;;;;kDAGjG,6LAAC,iKAAA,CAAA,WAAQ;wCAAC,WAAW,6IAAA,CAAA,UAAM,CAAC,QAAQ;wCAAE,yBAAW,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAM;;;;;;wCAAY,oBAAM,6LAAC,qKAAA,CAAA,UAAS;;;;;kDAAK;;;;;;kDAG9F,6LAAC,iKAAA,CAAA,WAAQ;wCAAC,WAAW,6IAAA,CAAA,UAAM,CAAC,QAAQ;wCAAE,yBAAW,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAM;;;;;;wCAAc,oBAAM,6LAAC,8JAAA,CAAA,UAAU;;;;;kDAAK;;;;;;kDAGjG,6LAAC,iKAAA,CAAA,WAAQ;wCAAC,WAAW,6IAAA,CAAA,UAAM,CAAC,QAAQ;wCAAE,yBAAW,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAM;;;;;;wCAAiB,oBAAM,6LAAC,4JAAA,CAAA,UAAQ;;;;;kDAAK;;;;;;kDAGlG,6LAAC,iKAAA,CAAA,WAAQ;wCACL,WAAW,6IAAA,CAAA,UAAM,CAAC,QAAQ;wCAC1B,yBAAW,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAM;4CAAiC,QAAO;;;;;;wCAC/D,KAAI;wCACJ,oBAAM,6LAAC,gIAAA,CAAA,UAAK;4CAAC,KAAI;4CAAoB,KAAI;4CAAe,QAAQ;4CAAI,OAAO;;;;;;kDAC9E;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAOhB,wBACG,6LAAC;gBAAK,SAAS;gBAAkB,WAAW,6IAAA,CAAA,UAAM,CAAC,aAAa;gBAAE,IAAG;gBAAgB,OAAO,MAAM,kBAAkB;0BAChH,cAAA,6LAAC,4JAAA,CAAA,UAAQ;oBAAC,UAAS;;;;;;;;;;uBAGvB;;;;;;;AAIhB;GArLS;;QAKa,kHAAA,CAAA,iBAAc;;;KAL3B;uCAuLM", "debugId": null}}, {"offset": {"line": 3275, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/components/OptionsMenu/OptionsMenu.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"buttonsWrapper\": \"OptionsMenu-module__BxCVkW__buttonsWrapper\",\n  \"dropdown\": \"OptionsMenu-module__BxCVkW__dropdown\",\n  \"optionsMenu\": \"OptionsMenu-module__BxCVkW__optionsMenu\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 3286, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/OptionsMenu/OptionsMenu.tsx"], "sourcesContent": ["'use client'\nimport React from 'react'\nimport MoreVertIcon from '@mui/icons-material/MoreVert'\nimport styles from './OptionsMenu.module.css'\nimport { Button, Dropdown, DropdownButton } from 'react-bootstrap'\n\ninterface Props {\n    selected?: Player | Item\n}\ninterface AvailableLinks {\n    title: string\n    url: string\n}\n\nconst CustomToggle = React.forwardRef(({ children, onClick }: any, ref) => (\n    <span\n        ref={ref as any}\n        onClick={e => {\n            e.preventDefault()\n            onClick(e)\n        }}\n    >\n        {children}\n        <MoreVertIcon />\n    </span>\n))\n\nfunction OptionsMenu(props: Props) {\n    let available: AvailableLinks[] = []\n    const isItemPage = (props.selected as Item)?.tag !== undefined\n    const isPlayerPage = !isItemPage\n    if (isItemPage) {\n        let fandomName = props.selected?.name\n        let wikiName = props.selected?.name\n        let tag = (props.selected as Item).tag\n        if (tag.startsWith('ENCHANTMENT_')) {\n            fandomName = tag.replace('ENCHANTMENT_', '').replace('ULTIMATE_', '').replace(/_\\d/, '').toLowerCase()\n            fandomName = fandomName\n                .split('_')\n                .map(part => part.charAt(0).toUpperCase() + part.slice(1).toLowerCase())\n                .join('_')\n            wikiName = fandomName + '_Enchantment'\n        }\n        available.push({ title: 'Fandom', url: 'https://hypixel-skyblock.fandom.com/wiki/' + fandomName })\n        available.push({ title: 'Wiki', url: 'https://wiki.hypixel.net/' + wikiName })\n        if ((props.selected as Item).bazaar) {\n            available.push({ title: 'Skyblock.bz', url: 'https://Skyblock.bz/product/' + tag })\n        }\n    } else if (isPlayerPage) {\n        let player = props.selected as Player\n        available.push({ title: 'SkyCrypt', url: 'https://sky.shiiyu.moe/stats/' + player?.uuid })\n        available.push({ title: 'Plancke', url: 'https://plancke.io/hypixel/player/stats/' + player?.uuid })\n    }\n\n    const navigate = (url: string) => {\n        window.open(url, '_blank')\n    }\n\n    if (!props.selected || props.selected.name === undefined) {\n        return null\n    }\n\n    return (\n        <div className={styles.optionsMenu}>\n            <div className={styles.buttonsWrapper}>\n                {available.map((result, i) => (\n                    <a key={i} href={result.url} title={result.title} target=\"_blank\" rel=\"noreferrer\">\n                        <Button>{result.title}</Button>\n                    </a>\n                ))}\n            </div>\n\n            <Dropdown className={styles.dropdown}>\n                <Dropdown.Toggle as={CustomToggle}></Dropdown.Toggle>\n                <Dropdown.Menu id=\"dropdownMenuButton\">\n                    {available.map((result, i) => (\n                        <Dropdown.Item\n                            key={result.url}\n                            onClick={() => {\n                                navigate(result.url)\n                            }}\n                        >\n                            {result.title}\n                        </Dropdown.Item>\n                    ))}\n                </Dropdown.Menu>\n            </Dropdown>\n        </div>\n    )\n}\n\nexport default OptionsMenu\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AAAA;AAJA;;;;;;AAcA,MAAM,6BAAe,6JAAA,CAAA,UAAK,CAAC,UAAU,MAAC,CAAC,EAAE,QAAQ,EAAE,OAAO,EAAO,EAAE,oBAC/D,6LAAC;QACG,KAAK;QACL,SAAS,CAAA;YACL,EAAE,cAAc;YAChB,QAAQ;QACZ;;YAEC;0BACD,6LAAC,gKAAA,CAAA,UAAY;;;;;;;;;;;;AAIrB,SAAS,YAAY,KAAY;IAC7B,IAAI,YAA8B,EAAE;IACpC,MAAM,aAAa,AAAC,MAAM,QAAQ,EAAW,QAAQ;IACrD,MAAM,eAAe,CAAC;IACtB,IAAI,YAAY;QACZ,IAAI,aAAa,MAAM,QAAQ,EAAE;QACjC,IAAI,WAAW,MAAM,QAAQ,EAAE;QAC/B,IAAI,MAAM,AAAC,MAAM,QAAQ,CAAU,GAAG;QACtC,IAAI,IAAI,UAAU,CAAC,iBAAiB;YAChC,aAAa,IAAI,OAAO,CAAC,gBAAgB,IAAI,OAAO,CAAC,aAAa,IAAI,OAAO,CAAC,OAAO,IAAI,WAAW;YACpG,aAAa,WACR,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC,GAAG,WAAW,IACpE,IAAI,CAAC;YACV,WAAW,aAAa;QAC5B;QACA,UAAU,IAAI,CAAC;YAAE,OAAO;YAAU,KAAK,8CAA8C;QAAW;QAChG,UAAU,IAAI,CAAC;YAAE,OAAO;YAAQ,KAAK,8BAA8B;QAAS;QAC5E,IAAI,AAAC,MAAM,QAAQ,CAAU,MAAM,EAAE;YACjC,UAAU,IAAI,CAAC;gBAAE,OAAO;gBAAe,KAAK,iCAAiC;YAAI;QACrF;IACJ,OAAO,IAAI,cAAc;QACrB,IAAI,SAAS,MAAM,QAAQ;QAC3B,UAAU,IAAI,CAAC;YAAE,OAAO;YAAY,KAAK,kCAAkC,QAAQ;QAAK;QACxF,UAAU,IAAI,CAAC;YAAE,OAAO;YAAW,KAAK,6CAA6C,QAAQ;QAAK;IACtG;IAEA,MAAM,WAAW,CAAC;QACd,OAAO,IAAI,CAAC,KAAK;IACrB;IAEA,IAAI,CAAC,MAAM,QAAQ,IAAI,MAAM,QAAQ,CAAC,IAAI,KAAK,WAAW;QACtD,OAAO;IACX;IAEA,qBACI,6LAAC;QAAI,WAAW,uJAAA,CAAA,UAAM,CAAC,WAAW;;0BAC9B,6LAAC;gBAAI,WAAW,uJAAA,CAAA,UAAM,CAAC,cAAc;0BAChC,UAAU,GAAG,CAAC,CAAC,QAAQ,kBACpB,6LAAC;wBAAU,MAAM,OAAO,GAAG;wBAAE,OAAO,OAAO,KAAK;wBAAE,QAAO;wBAAS,KAAI;kCAClE,cAAA,6LAAC,2LAAA,CAAA,SAAM;sCAAE,OAAO,KAAK;;;;;;uBADjB;;;;;;;;;;0BAMhB,6LAAC,+LAAA,CAAA,WAAQ;gBAAC,WAAW,uJAAA,CAAA,UAAM,CAAC,QAAQ;;kCAChC,6LAAC,+LAAA,CAAA,WAAQ,CAAC,MAAM;wBAAC,IAAI;;;;;;kCACrB,6LAAC,+LAAA,CAAA,WAAQ,CAAC,IAAI;wBAAC,IAAG;kCACb,UAAU,GAAG,CAAC,CAAC,QAAQ,kBACpB,6LAAC,+LAAA,CAAA,WAAQ,CAAC,IAAI;gCAEV,SAAS;oCACL,SAAS,OAAO,GAAG;gCACvB;0CAEC,OAAO,KAAK;+BALR,OAAO,GAAG;;;;;;;;;;;;;;;;;;;;;;AAY3C;MA9DS;uCAgEM", "debugId": null}}, {"offset": {"line": 3447, "column": 0}, "map": {"version": 3, "sources": ["file:///app/utils/PreviousSearchUtils.tsx"], "sourcesContent": ["const PREVIOUS_SEARCHES_KEY = 'lastSearches'\nconst MAX_PREVIOUS_SEARCHES_TO_DISPLAY = 3\nconst MAX_PREVIOUS_SEARCHES_TO_STORE = 100\n\nexport function addClickedSearchResultToPreviousSearches(item: SearchResultItem, previousSearchKey?: string) {\n    let previousSearches: SearchResultItem[] = getPreviousSearchesFromLocalStorage(previousSearchKey)\n\n    let alreadyFoundIndex = previousSearches.findIndex(r => r.id === item.id)\n    if (alreadyFoundIndex !== -1) {\n        previousSearches.splice(alreadyFoundIndex, 1)\n    }\n    previousSearches.push(item)\n    if (previousSearches.length > MAX_PREVIOUS_SEARCHES_TO_STORE) {\n        previousSearches.shift()\n    }\n    localStorage.setItem(PREVIOUS_SEARCHES_KEY, JSON.stringify(previousSearches))\n}\n\nexport function pinSearchResult(item: SearchResultItem, previousSearchKey?: string) {\n    let previousSearches: SearchResultItem[] = getPreviousSearchesFromLocalStorage(previousSearchKey)\n    let alreadyFoundIndex = previousSearches.findIndex(r => r.id === item.id)\n    if (alreadyFoundIndex !== -1) {\n        previousSearches[alreadyFoundIndex].pinned = true\n    } else {\n        previousSearches.push({\n            ...item,\n            pinned: true\n        })\n    }\n    localStorage.setItem(PREVIOUS_SEARCHES_KEY, JSON.stringify(previousSearches))\n}\n\nexport function unpinSearchResult(item: SearchResultItem, previousSearchKey?: string) {\n    let previousSearches: SearchResultItem[] = getPreviousSearchesFromLocalStorage(previousSearchKey)\n    let index = previousSearches.findIndex(r => r.id === item.id)\n    if (index !== -1) {\n        previousSearches[index].pinned = false\n    } else {\n        // item to remove was not found\n        return\n    }\n    localStorage.setItem(PREVIOUS_SEARCHES_KEY, JSON.stringify(previousSearches))\n}\n\nexport function addPreviousSearchResultsToDisplay(searchText: string, searchResults: SearchResultItem[], previousSearchKey?: string) {\n    let newSearchResults = [...searchResults]\n    let previousSearches: SearchResultItem[] = getPreviousSearchesFromLocalStorage(previousSearchKey)\n    let matches: SearchResultItem[] = []\n    let matchingPreviousSearchesInResuls = 0\n\n    previousSearches.forEach(prevSearch => {\n        let alreadyFoundIndex = newSearchResults.findIndex(r => r.id === prevSearch.id)\n        if (alreadyFoundIndex !== -1) {\n            newSearchResults[alreadyFoundIndex] = prevSearch\n            matchingPreviousSearchesInResuls++\n        } else if (prevSearch.dataItem.name.toLowerCase().indexOf(searchText.toLowerCase()) !== -1) {\n            matches.unshift(prevSearch)\n        }\n    })\n\n    let pinnedSort = (a, b) => {\n        if (a.pinned) {\n            return -1\n        }\n        if (b.pinned) {\n            return 1\n        }\n        return 0\n    }\n\n    newSearchResults.sort(pinnedSort)\n    matches.sort(pinnedSort).slice(0, MAX_PREVIOUS_SEARCHES_TO_DISPLAY - matchingPreviousSearchesInResuls)\n\n    if (MAX_PREVIOUS_SEARCHES_TO_DISPLAY <= matchingPreviousSearchesInResuls) {\n        return newSearchResults\n    }\n\n    return [...matches, ...newSearchResults]\n}\n\nexport function getFirstPreviousSearches(amount: number, previousSearchKey?: string) {\n    let previousSearches: SearchResultItem[] = getPreviousSearchesFromLocalStorage(previousSearchKey)\n    return previousSearches\n        .sort((a, b) => {\n            if (a.pinned) {\n                return 1\n            }\n            if (b.pinned) {\n                return -1\n            }\n            return 0\n        })\n        .slice(-amount)\n        .reverse()\n}\n\nfunction getPreviousSearchesFromLocalStorage(keyForPinnedItems?: string) {\n    let previousSearches: SearchResultItem[] = localStorage.getItem(PREVIOUS_SEARCHES_KEY) ? JSON.parse(localStorage.getItem(PREVIOUS_SEARCHES_KEY)!) : []\n    return previousSearches\n        .filter(prevSearch => prevSearch.previousSearchKey === keyForPinnedItems)\n        .map(prevSearch => {\n            prevSearch.isPreviousSearch = true\n            return prevSearch\n        })\n}\n"], "names": [], "mappings": ";;;;;;;AAAA,MAAM,wBAAwB;AAC9B,MAAM,mCAAmC;AACzC,MAAM,iCAAiC;AAEhC,SAAS,yCAAyC,IAAsB,EAAE,iBAA0B;IACvG,IAAI,mBAAuC,oCAAoC;IAE/E,IAAI,oBAAoB,iBAAiB,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,KAAK,EAAE;IACxE,IAAI,sBAAsB,CAAC,GAAG;QAC1B,iBAAiB,MAAM,CAAC,mBAAmB;IAC/C;IACA,iBAAiB,IAAI,CAAC;IACtB,IAAI,iBAAiB,MAAM,GAAG,gCAAgC;QAC1D,iBAAiB,KAAK;IAC1B;IACA,aAAa,OAAO,CAAC,uBAAuB,KAAK,SAAS,CAAC;AAC/D;AAEO,SAAS,gBAAgB,IAAsB,EAAE,iBAA0B;IAC9E,IAAI,mBAAuC,oCAAoC;IAC/E,IAAI,oBAAoB,iBAAiB,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,KAAK,EAAE;IACxE,IAAI,sBAAsB,CAAC,GAAG;QAC1B,gBAAgB,CAAC,kBAAkB,CAAC,MAAM,GAAG;IACjD,OAAO;QACH,iBAAiB,IAAI,CAAC;YAClB,GAAG,IAAI;YACP,QAAQ;QACZ;IACJ;IACA,aAAa,OAAO,CAAC,uBAAuB,KAAK,SAAS,CAAC;AAC/D;AAEO,SAAS,kBAAkB,IAAsB,EAAE,iBAA0B;IAChF,IAAI,mBAAuC,oCAAoC;IAC/E,IAAI,QAAQ,iBAAiB,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,KAAK,EAAE;IAC5D,IAAI,UAAU,CAAC,GAAG;QACd,gBAAgB,CAAC,MAAM,CAAC,MAAM,GAAG;IACrC,OAAO;QACH,+BAA+B;QAC/B;IACJ;IACA,aAAa,OAAO,CAAC,uBAAuB,KAAK,SAAS,CAAC;AAC/D;AAEO,SAAS,kCAAkC,UAAkB,EAAE,aAAiC,EAAE,iBAA0B;IAC/H,IAAI,mBAAmB;WAAI;KAAc;IACzC,IAAI,mBAAuC,oCAAoC;IAC/E,IAAI,UAA8B,EAAE;IACpC,IAAI,mCAAmC;IAEvC,iBAAiB,OAAO,CAAC,CAAA;QACrB,IAAI,oBAAoB,iBAAiB,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,WAAW,EAAE;QAC9E,IAAI,sBAAsB,CAAC,GAAG;YAC1B,gBAAgB,CAAC,kBAAkB,GAAG;YACtC;QACJ,OAAO,IAAI,WAAW,QAAQ,CAAC,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,WAAW,QAAQ,CAAC,GAAG;YACxF,QAAQ,OAAO,CAAC;QACpB;IACJ;IAEA,IAAI,aAAa,CAAC,GAAG;QACjB,IAAI,EAAE,MAAM,EAAE;YACV,OAAO,CAAC;QACZ;QACA,IAAI,EAAE,MAAM,EAAE;YACV,OAAO;QACX;QACA,OAAO;IACX;IAEA,iBAAiB,IAAI,CAAC;IACtB,QAAQ,IAAI,CAAC,YAAY,KAAK,CAAC,GAAG,mCAAmC;IAErE,IAAI,oCAAoC,kCAAkC;QACtE,OAAO;IACX;IAEA,OAAO;WAAI;WAAY;KAAiB;AAC5C;AAEO,SAAS,yBAAyB,MAAc,EAAE,iBAA0B;IAC/E,IAAI,mBAAuC,oCAAoC;IAC/E,OAAO,iBACF,IAAI,CAAC,CAAC,GAAG;QACN,IAAI,EAAE,MAAM,EAAE;YACV,OAAO;QACX;QACA,IAAI,EAAE,MAAM,EAAE;YACV,OAAO,CAAC;QACZ;QACA,OAAO;IACX,GACC,KAAK,CAAC,CAAC,QACP,OAAO;AAChB;AAEA,SAAS,oCAAoC,iBAA0B;IACnE,IAAI,mBAAuC,aAAa,OAAO,CAAC,yBAAyB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,0BAA2B,EAAE;IACtJ,OAAO,iBACF,MAAM,CAAC,CAAA,aAAc,WAAW,iBAAiB,KAAK,mBACtD,GAAG,CAAC,CAAA;QACD,WAAW,gBAAgB,GAAG;QAC9B,OAAO;IACX;AACR", "debugId": null}}, {"offset": {"line": 3556, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/ClientOnly/ClientOnly.tsx"], "sourcesContent": ["import { useEffect, useState } from 'react'\n\nexport default function ClientOnly({ children }) {\n    const [mounted, setMounted] = useState(false)\n\n    useEffect(() => {\n        setMounted(true)\n    }, [])\n\n    return mounted ? children : null\n}\n"], "names": [], "mappings": ";;;AAAA;;;AAEe,SAAS,WAAW,EAAE,QAAQ,EAAE;;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACN,WAAW;QACf;+BAAG,EAAE;IAEL,OAAO,UAAU,WAAW;AAChC;GARwB;KAAA", "debugId": null}}, {"offset": {"line": 3585, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/Search/Search.tsx"], "sourcesContent": ["'use client'\nimport React, { ChangeEvent, useEffect, useRef, useState, type JSX } from 'react'\nimport api from '../../api/ApiHelper'\nimport { Form, InputGroup, ListGroup, Spinner } from 'react-bootstrap'\nimport { convertTagToName, getStyleForTier } from '../../utils/Formatter'\nimport NavBar from '../NavBar/NavBar'\nimport OptionsMenu from '../OptionsMenu/OptionsMenu'\nimport SearchIcon from '@mui/icons-material/SearchOutlined'\nimport WrongIcon from '@mui/icons-material/Dangerous'\nimport Refresh from '@mui/icons-material/Refresh'\nimport ClearIcon from '@mui/icons-material/Clear'\nimport { Item, Menu, useContextMenu, Separator } from 'react-contexify'\nimport { toast } from 'react-toastify'\nimport { isClientSideRendering } from '../../utils/SSRUtils'\nimport styles from './Search.module.css'\nimport { useForceUpdate, useIsMobile } from '../../utils/Hooks'\nimport Image from 'next/image'\nimport { useRouter } from 'next/navigation'\nimport PushPinIcon from '@mui/icons-material/PushPin'\nimport {\n    addClickedSearchResultToPreviousSearches,\n    addPreviousSearchResultsToDisplay,\n    getFirstPreviousSearches,\n    pinSearchResult,\n    unpinSearchResult\n} from '../../utils/PreviousSearchUtils'\nimport { ITEM_ICON_TYPE, getSetting, setSetting } from '../../utils/SettingsUtils'\nimport ClientOnly from '../ClientOnly/ClientOnly'\n\ninterface Props {\n    selected?: Player | Item\n    currentElement?: JSX.Element\n    backgroundColor?: string\n    backgroundColorSelected?: string\n    searchFunction?(searchText: string)\n    onSearchresultClick?(item: SearchResultItem)\n    hideNavbar?: boolean\n    placeholder?: string\n    type?: 'player' | 'item'\n    preventDisplayOfPreviousSearches?: boolean\n    enableReset?: boolean\n    onResetClick?()\n    hideOptions?: boolean\n    keyForPinnedItems?: string\n}\n\nconst PLAYER_SEARCH_CONEXT_MENU_ID = 'player-search-context-menu'\nconst SEARCH_RESULT_CONTEXT_MENU_ID = 'search-result-context-menu'\n\nfunction Search(props: Props) {\n    let router = useRouter()\n    let [searchText, setSearchText] = useState('')\n    let [results, setResults] = useState<SearchResultItem[]>([])\n    let [isSearching, setIsSearching] = useState(false)\n    let [noResultsFound, setNoResultsFound] = useState(false)\n    let [isSmall, setIsSmall] = useState(true)\n    let [selectedIndex, setSelectedIndex] = useState(0)\n    const { show: showPlayerContextMenu } = useContextMenu({\n        id: PLAYER_SEARCH_CONEXT_MENU_ID\n    })\n    const { show: showSearchItemContextMenu, hideAll: hideSearchItemContextMenu } = useContextMenu({\n        id: SEARCH_RESULT_CONTEXT_MENU_ID\n    })\n    const isMobile = useIsMobile()\n\n    let rememberEnterPressRef = useRef(false)\n\n    let searchElement = useRef(null)\n    let forceUpdate = useForceUpdate()\n\n    useEffect(() => {\n        if (isClientSideRendering()) {\n            setIsSmall(isClientSideRendering() ? document.body.clientWidth < 1500 : false)\n        }\n        document.addEventListener('click', outsideClickHandler, true)\n        return () => {\n            document.removeEventListener('click', outsideClickHandler, true)\n        }\n    }, [])\n\n    useEffect(() => {\n        setSearchText('')\n        setResults([])\n    }, [props.selected])\n\n    let search = () => {\n        let searchFor = searchText\n        let searchFunction = props.searchFunction || api.search\n        searchFunction(searchFor).then(searchResults => {\n            // has the searchtext changed?\n            if (\n                searchElement.current !== null &&\n                searchFor === ((searchElement.current as HTMLDivElement).querySelector('#search-bar') as HTMLInputElement).value\n            ) {\n                let searchResultsToShow = [...searchResults]\n                if (!props.preventDisplayOfPreviousSearches) {\n                    searchResultsToShow = addPreviousSearchResultsToDisplay(searchFor, searchResults, props.keyForPinnedItems)\n                }\n\n                setSelectedIndex(0)\n                setNoResultsFound(searchResultsToShow.length === 0)\n                setResults(searchResultsToShow)\n                setIsSearching(false)\n\n                if (rememberEnterPressRef.current) {\n                    onItemClick(searchResultsToShow[0])\n                    rememberEnterPressRef.current = false\n                }\n            }\n        })\n    }\n\n    let onSearchChange = (e: ChangeEvent) => {\n        let newSearchText: string = (e.target as HTMLInputElement).value\n        searchText = newSearchText\n        setSearchText(newSearchText)\n        setIsSearching(true)\n\n        if (newSearchText === '') {\n            setResults([])\n            setIsSearching(false)\n            return\n        }\n\n        setNoResultsFound(false)\n        search()\n    }\n\n    function outsideClickHandler(evt) {\n        const flyoutEl = searchElement.current\n        let targetEl = evt.target\n\n        do {\n            if (targetEl === flyoutEl) {\n                return\n            }\n            targetEl = (targetEl as any).parentNode\n        } while (targetEl)\n\n        setResults([])\n    }\n\n    let onKeyPress = (e: KeyboardEvent) => {\n        switch (e.key) {\n            case 'Enter':\n                e.preventDefault()\n                if (isSearching) {\n                    rememberEnterPressRef.current = true\n                    return\n                }\n                if (!results || results.length === 0) {\n                    return\n                }\n                onItemClick(results[selectedIndex])\n                break\n            case 'ArrowDown':\n                if (selectedIndex < results.length - 1) {\n                    setSelectedIndex(selectedIndex + 1)\n                }\n                break\n            case 'ArrowUp':\n                if (selectedIndex > 0) {\n                    setSelectedIndex(selectedIndex - 1)\n                }\n                break\n        }\n    }\n\n    let onItemClick = (item: SearchResultItem) => {\n        if (props.onSearchresultClick) {\n            props.onSearchresultClick(item)\n            return\n        }\n\n        if (item.urlSearchParams && new URLSearchParams(window.location.search).toString() !== item.urlSearchParams.toString()) {\n            setSearchText('')\n            setResults([])\n        }\n\n        addClickedSearchResultToPreviousSearches(item, props.keyForPinnedItems)\n\n        api.trackSearch(item.id, item.type)\n\n        let searchParams = new URLSearchParams()\n        let itemFilter = item.urlSearchParams?.get('itemFilter')\n        let apply = item.urlSearchParams?.get('apply')\n        if (itemFilter) {\n            searchParams.set('itemFilter', itemFilter)\n        }\n        if (apply) {\n            searchParams.set('apply', apply)\n        }\n\n        router.push(`${item.route}?${searchParams.toString()}`)\n    }\n\n    let noResultsFoundElement = (\n        <ListGroup.Item\n            key={-1}\n            style={getListItemStyle(-1)}\n            onContextMenu={e => {\n                handleSearchContextMenuForCurrentElement(e)\n            }}\n        >\n            <Image className={styles.searchResultIcon} height={32} width={32} src=\"/Barrier.png\" alt=\"\" />\n            No search results\n        </ListGroup.Item>\n    )\n\n    let getSelectedElement = (): JSX.Element => {\n        if (props.currentElement) {\n            return (\n                <h1 onContextMenu={e => handleSearchContextMenuForCurrentElement(e)} className={styles.current}>\n                    {props.currentElement}\n                </h1>\n            )\n        }\n        if (!props.selected) {\n            return <div />\n        }\n        return (\n            <h1 onContextMenu={e => handleSearchContextMenuForCurrentElement(e)} className={styles.current}>\n                <ClientOnly>\n                    <img\n                        crossOrigin=\"anonymous\"\n                        className=\"playerHeadIcon\"\n                        src={props.type === 'player' ? props.selected.iconUrl : api.getItemImageUrl({ tag: (props.selected as Item).tag })}\n                        height=\"32\"\n                        width=\"32\"\n                        alt=\"\"\n                        style={{ marginRight: '10px', cursor: 'pointer' }}\n                        onClick={() => {\n                            let type = getSetting(ITEM_ICON_TYPE, 'default')\n                            if (type === 'default') {\n                                setSetting(ITEM_ICON_TYPE, 'vanilla')\n                            } else {\n                                setSetting(ITEM_ICON_TYPE, 'default')\n                            }\n                            window.location.reload()\n                        }}\n                    />\n                </ClientOnly>\n                {props.selected.name || convertTagToName((props.selected as Item).tag)}\n                {props.enableReset ? (\n                    <ClearIcon onClick={props.onResetClick} style={{ cursor: 'pointer', color: 'red', marginLeft: '10px', fontWeight: 'bold' }} />\n                ) : null}\n            </h1>\n        )\n    }\n\n    let searchStyle: React.CSSProperties = {\n        backgroundColor: props.backgroundColor,\n        borderRadius: results.length > 0 || noResultsFound ? '0px 10px 0 0' : '0px 10px 10px 0px',\n        borderLeftWidth: 0,\n        borderBottomColor: results.length > 0 || noResultsFound ? '#444' : undefined\n    }\n\n    let searchIconStyle: React.CSSProperties = {\n        width: isSmall ? 'auto' : '58px',\n        borderRadius: results.length > 0 || noResultsFound ? '10px 0 0 0' : '10px 0px 0px 10px',\n        backgroundColor: props.backgroundColor || '#303030',\n        borderBottomColor: results.length > 0 || noResultsFound ? '#444' : undefined,\n        padding: isSmall ? '0px' : undefined\n    }\n\n    function getListItemStyle(i: number): React.CSSProperties {\n        let style = {\n            backgroundColor: i === selectedIndex ? props.backgroundColorSelected || '#444' : props.backgroundColor,\n            borderRadius: i === results.length - 1 ? '0 0 10px 10px' : '',\n            border: 0,\n            borderTop: i === 0 ? '1px solid #444' : 0,\n            borderTopWidth: i === 0 ? 0 : undefined,\n            fontWeigth: 'normal',\n            fontFamily: 'inherit'\n        }\n        if (results[i]) {\n            let isDuplicate = results.findIndex((element, index) => element.dataItem.name === results[i].dataItem.name && index !== i) !== -1\n            if (isDuplicate) {\n                return {\n                    ...getStyleForTier(results[i]?.tier),\n                    ...style\n                }\n            }\n        }\n        return style\n    }\n\n    function checkNameChange(uuid: string) {\n        api.triggerPlayerNameCheck(uuid).then(() => {\n            toast.success('A name check for the player was triggered. This may take a few minutes.')\n        })\n    }\n\n    function handleSearchContextMenuForCurrentElement(event) {\n        if (props.selected && props.type === 'player') {\n            event.preventDefault()\n            showPlayerContextMenu({ event: event })\n        }\n    }\n\n    function handleSearchContextMenuForSearchResult(event: React.MouseEvent<HTMLElement, MouseEvent>, searchResultItem: SearchResultItem) {\n        event.preventDefault()\n        showSearchItemContextMenu({ event: event, props: { item: searchResultItem } })\n    }\n\n    let currentItemContextMenuElement = (\n        <div>\n            <Menu id={PLAYER_SEARCH_CONEXT_MENU_ID} theme={'dark'}>\n                <Item\n                    onClick={params => {\n                        checkNameChange((props.selected as Player).uuid)\n                    }}\n                >\n                    <Refresh style={{ marginRight: '5px' }} />\n                    Trigger check if name has changed\n                </Item>\n            </Menu>\n        </div>\n    )\n\n    let searchItemContextMenuElement = (\n        <div>\n            <Menu id={SEARCH_RESULT_CONTEXT_MENU_ID} theme={'dark'}>\n                <Item\n                    onClick={params => {\n                        let item: SearchResultItem = params.props.item\n                        pinSearchResult(item, props.keyForPinnedItems)\n                        let index = results.findIndex(r => r.dataItem.name === item.dataItem.name)\n                        if (index !== -1) {\n                            let newResults = [...results]\n                            newResults[index].pinned = true\n                            newResults[index].isPreviousSearch = true\n                            setResults(newResults)\n                        }\n                        hideSearchItemContextMenu()\n                    }}\n                    hidden={params => !!params.props.item.pinned}\n                    closeOnClick\n                >\n                    <PushPinIcon />\n                    Pin search result\n                </Item>\n                <Item\n                    onClick={params => {\n                        let item: SearchResultItem = params.props.item\n                        unpinSearchResult(item, props.keyForPinnedItems)\n                        let index = results.findIndex(r => r.dataItem.name === item.dataItem.name)\n                        if (index !== -1) {\n                            let newResults = [...results]\n                            newResults[index].pinned = false\n                            setResults(newResults)\n                        }\n                        hideSearchItemContextMenu()\n                    }}\n                    hidden={params => !params.props.item.pinned}\n                    closeOnClick\n                >\n                    <PushPinIcon />\n                    Unpin search result\n                </Item>\n                <Separator />\n                <Item\n                    onClick={() => {\n                        api.sendFeedback('badSearchResults', {\n                            searchText: searchText,\n                            results: results\n                        })\n                    }}\n                >\n                    <WrongIcon style={{ color: 'red', marginRight: '5px' }} />I didn't find the thing I was looking for!\n                </Item>\n            </Menu>\n        </div>\n    )\n\n    return (\n        <div ref={searchElement} className={styles.search} style={isSmall ? { marginLeft: '-5px', marginRight: '-5px' } : {}}>\n            <Form autoComplete=\"off\">\n                <Form.Group className={styles.searchFormGroup}>\n                    {!isSmall && !props.hideNavbar ? <NavBar /> : ''}\n                    <InputGroup id=\"search-input-group\">\n                        <InputGroup.Text style={searchIconStyle}>\n                            {isSmall && !props.hideNavbar ? (\n                                <div style={{ width: '56px' }}>\n                                    <NavBar hamburgerIconStyle={{ marginRight: '0px', width: '56px' }} />\n                                </div>\n                            ) : (\n                                <SearchIcon />\n                            )}\n                        </InputGroup.Text>\n                        <Form.Control\n                            key=\"search\"\n                            autoFocus={!isMobile}\n                            style={searchStyle}\n                            type=\"text\"\n                            placeholder={props.placeholder || 'Search player/item'}\n                            id={'search-bar'}\n                            className=\"searchBar\"\n                            value={searchText}\n                            onChange={onSearchChange}\n                            onKeyDown={(e: any) => {\n                                onKeyPress(e)\n                            }}\n                            onClick={() => {\n                                if (!props.preventDisplayOfPreviousSearches && !noResultsFound && results.length === 0 && !searchText) {\n                                    let previousResuls = getFirstPreviousSearches(5, props.keyForPinnedItems)\n                                    setResults(previousResuls)\n                                }\n                            }}\n                        />\n                    </InputGroup>\n                </Form.Group>\n            </Form>\n            <ListGroup className={styles.searchResutList}>\n                {noResultsFound\n                    ? noResultsFoundElement\n                    : results.map((result, i) => (\n                          <ListGroup.Item\n                              key={result.id}\n                              action\n                              onClick={(e: any) => {\n                                  onItemClick(result)\n                              }}\n                              style={getListItemStyle(i)}\n                              className={result.isPreviousSearch ? styles.previousSearch : undefined}\n                              onContextMenu={event => {\n                                  handleSearchContextMenuForSearchResult(event, result)\n                              }}\n                          >\n                              {result.dataItem.iconUrl ? (\n                                  <Image\n                                      className={`${styles.searchResultIcon} playerHeadIcon`}\n                                      crossOrigin=\"anonymous\"\n                                      width={32}\n                                      height={32}\n                                      src={\n                                          result.dataItem._imageLoaded\n                                              ? result.dataItem.iconUrl\n                                              : 'data:image/gif;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mPMqAcAAVUA6UpAAT4AAAAASUVORK5CYII='\n                                      }\n                                      alt=\"\"\n                                      onLoad={() => {\n                                          result.dataItem._imageLoaded = true\n                                          setResults(results)\n                                          forceUpdate()\n                                      }}\n                                  />\n                              ) : (\n                                  <Spinner animation=\"border\" role=\"status\" variant=\"primary\" />\n                              )}\n                              {result.pinned ? <PushPinIcon style={{ marginRight: '5px' }} /> : null}\n                              {result.dataItem.name}\n                          </ListGroup.Item>\n                      ))}\n            </ListGroup>\n            <div className={styles.bar} style={{ marginTop: '20px' }}>\n                {getSelectedElement()}\n                {!props.hideOptions ? <OptionsMenu selected={props.selected} /> : null}\n            </div>\n            {searchItemContextMenuElement}\n            {currentItemContextMenuElement}\n        </div>\n    )\n}\n\nexport default Search\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAOA;AACA;;;AA3BA;;;;;;;;;;;;;;;;;;;;;;AA8CA,MAAM,+BAA+B;AACrC,MAAM,gCAAgC;AAEtC,SAAS,OAAO,KAAY;;IACxB,IAAI,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACrB,IAAI,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,IAAI,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB,EAAE;IAC3D,IAAI,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,IAAI,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,IAAI,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,IAAI,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,EAAE,MAAM,qBAAqB,EAAE,GAAG,CAAA,GAAA,uJAAA,CAAA,iBAAc,AAAD,EAAE;QACnD,IAAI;IACR;IACA,MAAM,EAAE,MAAM,yBAAyB,EAAE,SAAS,yBAAyB,EAAE,GAAG,CAAA,GAAA,uJAAA,CAAA,iBAAc,AAAD,EAAE;QAC3F,IAAI;IACR;IACA,MAAM,WAAW,CAAA,GAAA,kHAAA,CAAA,cAAW,AAAD;IAE3B,IAAI,wBAAwB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAEnC,IAAI,gBAAgB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC3B,IAAI,cAAc,CAAA,GAAA,kHAAA,CAAA,iBAAc,AAAD;IAE/B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACN,IAAI,CAAA,GAAA,qHAAA,CAAA,wBAAqB,AAAD,KAAK;gBACzB,WAAW,CAAA,GAAA,qHAAA,CAAA,wBAAqB,AAAD,MAAM,SAAS,IAAI,CAAC,WAAW,GAAG,OAAO;YAC5E;YACA,SAAS,gBAAgB,CAAC,SAAS,qBAAqB;YACxD;oCAAO;oBACH,SAAS,mBAAmB,CAAC,SAAS,qBAAqB;gBAC/D;;QACJ;2BAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACN,cAAc;YACd,WAAW,EAAE;QACjB;2BAAG;QAAC,MAAM,QAAQ;KAAC;IAEnB,IAAI,SAAS;QACT,IAAI,YAAY;QAChB,IAAI,iBAAiB,MAAM,cAAc,IAAI,oHAAA,CAAA,UAAG,CAAC,MAAM;QACvD,eAAe,WAAW,IAAI,CAAC,CAAA;YAC3B,8BAA8B;YAC9B,IACI,cAAc,OAAO,KAAK,QAC1B,cAAc,AAAC,AAAC,cAAc,OAAO,CAAoB,aAAa,CAAC,eAAoC,KAAK,EAClH;gBACE,IAAI,sBAAsB;uBAAI;iBAAc;gBAC5C,IAAI,CAAC,MAAM,gCAAgC,EAAE;oBACzC,sBAAsB,CAAA,GAAA,gIAAA,CAAA,oCAAiC,AAAD,EAAE,WAAW,eAAe,MAAM,iBAAiB;gBAC7G;gBAEA,iBAAiB;gBACjB,kBAAkB,oBAAoB,MAAM,KAAK;gBACjD,WAAW;gBACX,eAAe;gBAEf,IAAI,sBAAsB,OAAO,EAAE;oBAC/B,YAAY,mBAAmB,CAAC,EAAE;oBAClC,sBAAsB,OAAO,GAAG;gBACpC;YACJ;QACJ;IACJ;IAEA,IAAI,iBAAiB,CAAC;QAClB,IAAI,gBAAwB,AAAC,EAAE,MAAM,CAAsB,KAAK;QAChE,aAAa;QACb,cAAc;QACd,eAAe;QAEf,IAAI,kBAAkB,IAAI;YACtB,WAAW,EAAE;YACb,eAAe;YACf;QACJ;QAEA,kBAAkB;QAClB;IACJ;IAEA,SAAS,oBAAoB,GAAG;QAC5B,MAAM,WAAW,cAAc,OAAO;QACtC,IAAI,WAAW,IAAI,MAAM;QAEzB,GAAG;YACC,IAAI,aAAa,UAAU;gBACvB;YACJ;YACA,WAAW,AAAC,SAAiB,UAAU;QAC3C,QAAS,SAAS;QAElB,WAAW,EAAE;IACjB;IAEA,IAAI,aAAa,CAAC;QACd,OAAQ,EAAE,GAAG;YACT,KAAK;gBACD,EAAE,cAAc;gBAChB,IAAI,aAAa;oBACb,sBAAsB,OAAO,GAAG;oBAChC;gBACJ;gBACA,IAAI,CAAC,WAAW,QAAQ,MAAM,KAAK,GAAG;oBAClC;gBACJ;gBACA,YAAY,OAAO,CAAC,cAAc;gBAClC;YACJ,KAAK;gBACD,IAAI,gBAAgB,QAAQ,MAAM,GAAG,GAAG;oBACpC,iBAAiB,gBAAgB;gBACrC;gBACA;YACJ,KAAK;gBACD,IAAI,gBAAgB,GAAG;oBACnB,iBAAiB,gBAAgB;gBACrC;gBACA;QACR;IACJ;IAEA,IAAI,cAAc,CAAC;QACf,IAAI,MAAM,mBAAmB,EAAE;YAC3B,MAAM,mBAAmB,CAAC;YAC1B;QACJ;QAEA,IAAI,KAAK,eAAe,IAAI,IAAI,gBAAgB,OAAO,QAAQ,CAAC,MAAM,EAAE,QAAQ,OAAO,KAAK,eAAe,CAAC,QAAQ,IAAI;YACpH,cAAc;YACd,WAAW,EAAE;QACjB;QAEA,CAAA,GAAA,gIAAA,CAAA,2CAAwC,AAAD,EAAE,MAAM,MAAM,iBAAiB;QAEtE,oHAAA,CAAA,UAAG,CAAC,WAAW,CAAC,KAAK,EAAE,EAAE,KAAK,IAAI;QAElC,IAAI,eAAe,IAAI;QACvB,IAAI,aAAa,KAAK,eAAe,EAAE,IAAI;QAC3C,IAAI,QAAQ,KAAK,eAAe,EAAE,IAAI;QACtC,IAAI,YAAY;YACZ,aAAa,GAAG,CAAC,cAAc;QACnC;QACA,IAAI,OAAO;YACP,aAAa,GAAG,CAAC,SAAS;QAC9B;QAEA,OAAO,IAAI,CAAC,GAAG,KAAK,KAAK,CAAC,CAAC,EAAE,aAAa,QAAQ,IAAI;IAC1D;IAEA,IAAI,sCACA,6LAAC,iMAAA,CAAA,YAAS,CAAC,IAAI;QAEX,OAAO,iBAAiB,CAAC;QACzB,eAAe,CAAA;YACX,yCAAyC;QAC7C;;0BAEA,6LAAC,gIAAA,CAAA,UAAK;gBAAC,WAAW,6IAAA,CAAA,UAAM,CAAC,gBAAgB;gBAAE,QAAQ;gBAAI,OAAO;gBAAI,KAAI;gBAAe,KAAI;;;;;;YAAK;;OANzF,CAAC;;;;;IAWd,IAAI,qBAAqB;QACrB,IAAI,MAAM,cAAc,EAAE;YACtB,qBACI,6LAAC;gBAAG,eAAe,CAAA,IAAK,yCAAyC;gBAAI,WAAW,6IAAA,CAAA,UAAM,CAAC,OAAO;0BACzF,MAAM,cAAc;;;;;;QAGjC;QACA,IAAI,CAAC,MAAM,QAAQ,EAAE;YACjB,qBAAO,6LAAC;;;;;QACZ;QACA,qBACI,6LAAC;YAAG,eAAe,CAAA,IAAK,yCAAyC;YAAI,WAAW,6IAAA,CAAA,UAAM,CAAC,OAAO;;8BAC1F,6LAAC,0IAAA,CAAA,UAAU;8BACP,cAAA,6LAAC;wBACG,aAAY;wBACZ,WAAU;wBACV,KAAK,MAAM,IAAI,KAAK,WAAW,MAAM,QAAQ,CAAC,OAAO,GAAG,oHAAA,CAAA,UAAG,CAAC,eAAe,CAAC;4BAAE,KAAK,AAAC,MAAM,QAAQ,CAAU,GAAG;wBAAC;wBAChH,QAAO;wBACP,OAAM;wBACN,KAAI;wBACJ,OAAO;4BAAE,aAAa;4BAAQ,QAAQ;wBAAU;wBAChD,SAAS;4BACL,IAAI,OAAO,CAAA,GAAA,0HAAA,CAAA,aAAU,AAAD,EAAE,0HAAA,CAAA,iBAAc,EAAE;4BACtC,IAAI,SAAS,WAAW;gCACpB,CAAA,GAAA,0HAAA,CAAA,aAAU,AAAD,EAAE,0HAAA,CAAA,iBAAc,EAAE;4BAC/B,OAAO;gCACH,CAAA,GAAA,0HAAA,CAAA,aAAU,AAAD,EAAE,0HAAA,CAAA,iBAAc,EAAE;4BAC/B;4BACA,OAAO,QAAQ,CAAC,MAAM;wBAC1B;;;;;;;;;;;gBAGP,MAAM,QAAQ,CAAC,IAAI,IAAI,CAAA,GAAA,sHAAA,CAAA,mBAAgB,AAAD,EAAE,AAAC,MAAM,QAAQ,CAAU,GAAG;gBACpE,MAAM,WAAW,iBACd,6LAAC,6JAAA,CAAA,UAAS;oBAAC,SAAS,MAAM,YAAY;oBAAE,OAAO;wBAAE,QAAQ;wBAAW,OAAO;wBAAO,YAAY;wBAAQ,YAAY;oBAAO;;;;;2BACzH;;;;;;;IAGhB;IAEA,IAAI,cAAmC;QACnC,iBAAiB,MAAM,eAAe;QACtC,cAAc,QAAQ,MAAM,GAAG,KAAK,iBAAiB,iBAAiB;QACtE,iBAAiB;QACjB,mBAAmB,QAAQ,MAAM,GAAG,KAAK,iBAAiB,SAAS;IACvE;IAEA,IAAI,kBAAuC;QACvC,OAAO,UAAU,SAAS;QAC1B,cAAc,QAAQ,MAAM,GAAG,KAAK,iBAAiB,eAAe;QACpE,iBAAiB,MAAM,eAAe,IAAI;QAC1C,mBAAmB,QAAQ,MAAM,GAAG,KAAK,iBAAiB,SAAS;QACnE,SAAS,UAAU,QAAQ;IAC/B;IAEA,SAAS,iBAAiB,CAAS;QAC/B,IAAI,QAAQ;YACR,iBAAiB,MAAM,gBAAgB,MAAM,uBAAuB,IAAI,SAAS,MAAM,eAAe;YACtG,cAAc,MAAM,QAAQ,MAAM,GAAG,IAAI,kBAAkB;YAC3D,QAAQ;YACR,WAAW,MAAM,IAAI,mBAAmB;YACxC,gBAAgB,MAAM,IAAI,IAAI;YAC9B,YAAY;YACZ,YAAY;QAChB;QACA,IAAI,OAAO,CAAC,EAAE,EAAE;YACZ,IAAI,cAAc,QAAQ,SAAS,CAAC,CAAC,SAAS,QAAU,QAAQ,QAAQ,CAAC,IAAI,KAAK,OAAO,CAAC,EAAE,CAAC,QAAQ,CAAC,IAAI,IAAI,UAAU,OAAO,CAAC;YAChI,IAAI,aAAa;gBACb,OAAO;oBACH,GAAG,CAAA,GAAA,sHAAA,CAAA,kBAAe,AAAD,EAAE,OAAO,CAAC,EAAE,EAAE,KAAK;oBACpC,GAAG,KAAK;gBACZ;YACJ;QACJ;QACA,OAAO;IACX;IAEA,SAAS,gBAAgB,IAAY;QACjC,oHAAA,CAAA,UAAG,CAAC,sBAAsB,CAAC,MAAM,IAAI,CAAC;YAClC,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAClB;IACJ;IAEA,SAAS,yCAAyC,KAAK;QACnD,IAAI,MAAM,QAAQ,IAAI,MAAM,IAAI,KAAK,UAAU;YAC3C,MAAM,cAAc;YACpB,sBAAsB;gBAAE,OAAO;YAAM;QACzC;IACJ;IAEA,SAAS,uCAAuC,KAAgD,EAAE,gBAAkC;QAChI,MAAM,cAAc;QACpB,0BAA0B;YAAE,OAAO;YAAO,OAAO;gBAAE,MAAM;YAAiB;QAAE;IAChF;IAEA,IAAI,8CACA,6LAAC;kBACG,cAAA,6LAAC,uJAAA,CAAA,OAAI;YAAC,IAAI;YAA8B,OAAO;sBAC3C,cAAA,6LAAC,uJAAA,CAAA,OAAI;gBACD,SAAS,CAAA;oBACL,gBAAgB,AAAC,MAAM,QAAQ,CAAY,IAAI;gBACnD;;kCAEA,6LAAC,+JAAA,CAAA,UAAO;wBAAC,OAAO;4BAAE,aAAa;wBAAM;;;;;;oBAAK;;;;;;;;;;;;;;;;;IAO1D,IAAI,6CACA,6LAAC;kBACG,cAAA,6LAAC,uJAAA,CAAA,OAAI;YAAC,IAAI;YAA+B,OAAO;;8BAC5C,6LAAC,uJAAA,CAAA,OAAI;oBACD,SAAS,CAAA;wBACL,IAAI,OAAyB,OAAO,KAAK,CAAC,IAAI;wBAC9C,CAAA,GAAA,gIAAA,CAAA,kBAAe,AAAD,EAAE,MAAM,MAAM,iBAAiB;wBAC7C,IAAI,QAAQ,QAAQ,SAAS,CAAC,CAAA,IAAK,EAAE,QAAQ,CAAC,IAAI,KAAK,KAAK,QAAQ,CAAC,IAAI;wBACzE,IAAI,UAAU,CAAC,GAAG;4BACd,IAAI,aAAa;mCAAI;6BAAQ;4BAC7B,UAAU,CAAC,MAAM,CAAC,MAAM,GAAG;4BAC3B,UAAU,CAAC,MAAM,CAAC,gBAAgB,GAAG;4BACrC,WAAW;wBACf;wBACA;oBACJ;oBACA,QAAQ,CAAA,SAAU,CAAC,CAAC,OAAO,KAAK,CAAC,IAAI,CAAC,MAAM;oBAC5C,YAAY;;sCAEZ,6LAAC,+JAAA,CAAA,UAAW;;;;;wBAAG;;;;;;;8BAGnB,6LAAC,uJAAA,CAAA,OAAI;oBACD,SAAS,CAAA;wBACL,IAAI,OAAyB,OAAO,KAAK,CAAC,IAAI;wBAC9C,CAAA,GAAA,gIAAA,CAAA,oBAAiB,AAAD,EAAE,MAAM,MAAM,iBAAiB;wBAC/C,IAAI,QAAQ,QAAQ,SAAS,CAAC,CAAA,IAAK,EAAE,QAAQ,CAAC,IAAI,KAAK,KAAK,QAAQ,CAAC,IAAI;wBACzE,IAAI,UAAU,CAAC,GAAG;4BACd,IAAI,aAAa;mCAAI;6BAAQ;4BAC7B,UAAU,CAAC,MAAM,CAAC,MAAM,GAAG;4BAC3B,WAAW;wBACf;wBACA;oBACJ;oBACA,QAAQ,CAAA,SAAU,CAAC,OAAO,KAAK,CAAC,IAAI,CAAC,MAAM;oBAC3C,YAAY;;sCAEZ,6LAAC,+JAAA,CAAA,UAAW;;;;;wBAAG;;;;;;;8BAGnB,6LAAC,uJAAA,CAAA,YAAS;;;;;8BACV,6LAAC,uJAAA,CAAA,OAAI;oBACD,SAAS;wBACL,oHAAA,CAAA,UAAG,CAAC,YAAY,CAAC,oBAAoB;4BACjC,YAAY;4BACZ,SAAS;wBACb;oBACJ;;sCAEA,6LAAC,iKAAA,CAAA,UAAS;4BAAC,OAAO;gCAAE,OAAO;gCAAO,aAAa;4BAAM;;;;;;wBAAK;;;;;;;;;;;;;;;;;;IAM1E,qBACI,6LAAC;QAAI,KAAK;QAAe,WAAW,6IAAA,CAAA,UAAM,CAAC,MAAM;QAAE,OAAO,UAAU;YAAE,YAAY;YAAQ,aAAa;QAAO,IAAI,CAAC;;0BAC/G,6LAAC,uLAAA,CAAA,OAAI;gBAAC,cAAa;0BACf,cAAA,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;oBAAC,WAAW,6IAAA,CAAA,UAAM,CAAC,eAAe;;wBACxC,CAAC,WAAW,CAAC,MAAM,UAAU,iBAAG,6LAAC,kIAAA,CAAA,UAAM;;;;mCAAM;sCAC9C,6LAAC,mMAAA,CAAA,aAAU;4BAAC,IAAG;;8CACX,6LAAC,mMAAA,CAAA,aAAU,CAAC,IAAI;oCAAC,OAAO;8CACnB,WAAW,CAAC,MAAM,UAAU,iBACzB,6LAAC;wCAAI,OAAO;4CAAE,OAAO;wCAAO;kDACxB,cAAA,6LAAC,kIAAA,CAAA,UAAM;4CAAC,oBAAoB;gDAAE,aAAa;gDAAO,OAAO;4CAAO;;;;;;;;;;6DAGpE,6LAAC,sKAAA,CAAA,UAAU;;;;;;;;;;8CAGnB,6LAAC,uLAAA,CAAA,OAAI,CAAC,OAAO;oCAET,WAAW,CAAC;oCACZ,OAAO;oCACP,MAAK;oCACL,aAAa,MAAM,WAAW,IAAI;oCAClC,IAAI;oCACJ,WAAU;oCACV,OAAO;oCACP,UAAU;oCACV,WAAW,CAAC;wCACR,WAAW;oCACf;oCACA,SAAS;wCACL,IAAI,CAAC,MAAM,gCAAgC,IAAI,CAAC,kBAAkB,QAAQ,MAAM,KAAK,KAAK,CAAC,YAAY;4CACnG,IAAI,iBAAiB,CAAA,GAAA,gIAAA,CAAA,2BAAwB,AAAD,EAAE,GAAG,MAAM,iBAAiB;4CACxE,WAAW;wCACf;oCACJ;mCAjBI;;;;;;;;;;;;;;;;;;;;;;0BAsBpB,6LAAC,iMAAA,CAAA,YAAS;gBAAC,WAAW,6IAAA,CAAA,UAAM,CAAC,eAAe;0BACvC,iBACK,wBACA,QAAQ,GAAG,CAAC,CAAC,QAAQ,kBACjB,6LAAC,iMAAA,CAAA,YAAS,CAAC,IAAI;wBAEX,MAAM;wBACN,SAAS,CAAC;4BACN,YAAY;wBAChB;wBACA,OAAO,iBAAiB;wBACxB,WAAW,OAAO,gBAAgB,GAAG,6IAAA,CAAA,UAAM,CAAC,cAAc,GAAG;wBAC7D,eAAe,CAAA;4BACX,uCAAuC,OAAO;wBAClD;;4BAEC,OAAO,QAAQ,CAAC,OAAO,iBACpB,6LAAC,gIAAA,CAAA,UAAK;gCACF,WAAW,GAAG,6IAAA,CAAA,UAAM,CAAC,gBAAgB,CAAC,eAAe,CAAC;gCACtD,aAAY;gCACZ,OAAO;gCACP,QAAQ;gCACR,KACI,OAAO,QAAQ,CAAC,YAAY,GACtB,OAAO,QAAQ,CAAC,OAAO,GACvB;gCAEV,KAAI;gCACJ,QAAQ;oCACJ,OAAO,QAAQ,CAAC,YAAY,GAAG;oCAC/B,WAAW;oCACX;gCACJ;;;;;qDAGJ,6LAAC,6LAAA,CAAA,UAAO;gCAAC,WAAU;gCAAS,MAAK;gCAAS,SAAQ;;;;;;4BAErD,OAAO,MAAM,iBAAG,6LAAC,+JAAA,CAAA,UAAW;gCAAC,OAAO;oCAAE,aAAa;gCAAM;;;;;uCAAQ;4BACjE,OAAO,QAAQ,CAAC,IAAI;;uBAjChB,OAAO,EAAE;;;;;;;;;;0BAqChC,6LAAC;gBAAI,WAAW,6IAAA,CAAA,UAAM,CAAC,GAAG;gBAAE,OAAO;oBAAE,WAAW;gBAAO;;oBAClD;oBACA,CAAC,MAAM,WAAW,iBAAG,6LAAC,4IAAA,CAAA,UAAW;wBAAC,UAAU,MAAM,QAAQ;;;;;+BAAO;;;;;;;YAErE;YACA;;;;;;;AAGb;GA9ZS;;QACQ,qIAAA,CAAA,YAAS;QAOkB,uJAAA,CAAA,iBAAc;QAG0B,uJAAA,CAAA,iBAAc;QAG7E,kHAAA,CAAA,cAAW;QAKV,kHAAA,CAAA,iBAAc;;;KAnB3B;uCAgaM", "debugId": null}}]}