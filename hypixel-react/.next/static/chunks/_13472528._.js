(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["static/chunks/_13472528._.js", {

"[project]/components/Number/Number.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>NumberElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Formatter$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/Formatter.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
function NumberElement(props) {
    _s();
    let [isSSR, setIsSSR] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    let value = Number(props.number);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "NumberElement.useEffect": ()=>{
            setIsSSR(false);
        }
    }["NumberElement.useEffect"], []);
    // Use consistent formatting to prevent hydration mismatches
    // Always use comma as thousand separator and period as decimal separator
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
        suppressHydrationWarning: true,
        children: (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Formatter$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["numberWithThousandsSeparators"])(value, ',', '.')
    }, void 0, false, {
        fileName: "[project]/components/Number/Number.tsx",
        lineNumber: 20,
        columnNumber: 12
    }, this);
}
_s(NumberElement, "DdT/RTUMRscU32S7lal5R4JlHrU=");
_c = NumberElement;
var _c;
__turbopack_context__.k.register(_c, "NumberElement");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/Startpage/Startpage.module.css [app-client] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "SSRcardsWrapper": "Startpage-module__7qS_na__SSRcardsWrapper",
  "cardWrapper": "Startpage-module__7qS_na__cardWrapper",
  "cardsWrapper": "Startpage-module__7qS_na__cardsWrapper",
  "changelogItem": "Startpage-module__7qS_na__changelogItem",
  "ellipsis": "Startpage-module__7qS_na__ellipsis",
  "joinNote": "Startpage-module__7qS_na__joinNote",
  "startpageCard": "Startpage-module__7qS_na__startpageCard",
  "startpageListElementWrapper": "Startpage-module__7qS_na__startpageListElementWrapper",
  "statusTitle": "Startpage-module__7qS_na__statusTitle",
});
}}),
"[project]/components/Startpage/StartpageLargeElementSkeleton.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "StartpageLargeElementSkeleton": (()=>StartpageLargeElementSkeleton)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Startpage$2f$Startpage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/components/Startpage/Startpage.module.css [app-client] (css module)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Card$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/Card.js [app-client] (ecmascript) <export default as Card>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$loading$2d$skeleton$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-loading-skeleton/dist/index.js [app-client] (ecmascript)");
;
;
;
;
function StartpageLargeElementSkeleton(props) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `${__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Startpage$2f$Startpage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].cardWrapper}`,
        style: props.style,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Card$3e$__["Card"], {
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Card$3e$__["Card"].Header, {
                    style: {
                        padding: '10px'
                    },
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        style: {
                            display: 'flex',
                            alignItems: 'center',
                            height: 32,
                            marginBottom: '1rem'
                        },
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$loading$2d$skeleton$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                circle: true,
                                height: 32,
                                width: 32,
                                style: {
                                    marginRight: '5px'
                                },
                                baseColor: "#333",
                                enableAnimation: false
                            }, void 0, false, {
                                fileName: "[project]/components/Startpage/StartpageLargeElementSkeleton.tsx",
                                lineNumber: 16,
                                columnNumber: 25
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$loading$2d$skeleton$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                width: '130px',
                                baseColor: "#333",
                                enableAnimation: false
                            }, void 0, false, {
                                fileName: "[project]/components/Startpage/StartpageLargeElementSkeleton.tsx",
                                lineNumber: 17,
                                columnNumber: 25
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/Startpage/StartpageLargeElementSkeleton.tsx",
                        lineNumber: 15,
                        columnNumber: 21
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/components/Startpage/StartpageLargeElementSkeleton.tsx",
                    lineNumber: 14,
                    columnNumber: 17
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Card$3e$__["Card"].Body, {
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$loading$2d$skeleton$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                        baseColor: "#444",
                                        enableAnimation: false
                                    }, void 0, false, {
                                        fileName: "[project]/components/Startpage/StartpageLargeElementSkeleton.tsx",
                                        lineNumber: 23,
                                        columnNumber: 29
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$loading$2d$skeleton$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                        baseColor: "#444",
                                        enableAnimation: false,
                                        width: 60
                                    }, void 0, false, {
                                        fileName: "[project]/components/Startpage/StartpageLargeElementSkeleton.tsx",
                                        lineNumber: 24,
                                        columnNumber: 29
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/components/Startpage/StartpageLargeElementSkeleton.tsx",
                                lineNumber: 22,
                                columnNumber: 25
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$loading$2d$skeleton$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    baseColor: "#444",
                                    enableAnimation: false
                                }, void 0, false, {
                                    fileName: "[project]/components/Startpage/StartpageLargeElementSkeleton.tsx",
                                    lineNumber: 27,
                                    columnNumber: 29
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/components/Startpage/StartpageLargeElementSkeleton.tsx",
                                lineNumber: 26,
                                columnNumber: 25
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$loading$2d$skeleton$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    baseColor: "#444",
                                    enableAnimation: false,
                                    width: 40
                                }, void 0, false, {
                                    fileName: "[project]/components/Startpage/StartpageLargeElementSkeleton.tsx",
                                    lineNumber: 30,
                                    columnNumber: 29
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/components/Startpage/StartpageLargeElementSkeleton.tsx",
                                lineNumber: 29,
                                columnNumber: 25
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/Startpage/StartpageLargeElementSkeleton.tsx",
                        lineNumber: 21,
                        columnNumber: 21
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/components/Startpage/StartpageLargeElementSkeleton.tsx",
                    lineNumber: 20,
                    columnNumber: 17
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/components/Startpage/StartpageLargeElementSkeleton.tsx",
            lineNumber: 13,
            columnNumber: 13
        }, this)
    }, void 0, false, {
        fileName: "[project]/components/Startpage/StartpageLargeElementSkeleton.tsx",
        lineNumber: 12,
        columnNumber: 9
    }, this);
}
_c = StartpageLargeElementSkeleton;
var _c;
__turbopack_context__.k.register(_c, "StartpageLargeElementSkeleton");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/Startpage/Startpage.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$jonkoops$2f$matomo$2d$tracker$2d$react$2f$es$2f$useMatomo$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useMatomo$3e$__ = __turbopack_context__.i("[project]/node_modules/@jonkoops/matomo-tracker-react/es/useMatomo.js [app-client] (ecmascript) <export default as useMatomo>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$FiberNew$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/icons-material/esm/FiberNew.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Fireplace$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/icons-material/esm/Fireplace.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Person$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/icons-material/esm/Person.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Timer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/icons-material/esm/Timer.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$moment$2f$moment$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/moment/moment.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/Card.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Badge$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/Badge.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$window$2f$dist$2f$index$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-window/dist/index.esm.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/api/ApiHelper.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Formatter$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/Formatter.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Number$2f$Number$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/Number/Number.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Startpage$2f$Startpage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/components/Startpage/Startpage.module.css [app-client] (css module)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Startpage$2f$StartpageLargeElementSkeleton$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/Startpage/StartpageLargeElementSkeleton.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
function Startpage(props) {
    _s();
    let { trackEvent } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$jonkoops$2f$matomo$2d$tracker$2d$react$2f$es$2f$useMatomo$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useMatomo$3e$__["useMatomo"])();
    let [newAuctions, setNewAuctions] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(props.newAuctions || []);
    let [endedAuctions, setEndedAuctions] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    let [popularSearches, setPopularSearches] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(props.popularSearches || []);
    let [newPlayers, setNewPlayers] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(props.newPlayers || []);
    let [newItems, setNewItems] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(props.newItems || []);
    let [isSSR, setIsSSR] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "Startpage.useEffect": ()=>{
            setIsSSR(false);
            setTimeout({
                "Startpage.useEffect": ()=>{
                    attachScrollEvent(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Startpage$2f$Startpage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].startpageListElementWrapper);
                }
            }["Startpage.useEffect"], 500);
            loadEndedAuctions();
        // eslint-disable-next-line react-hooks/exhaustive-deps
        }
    }["Startpage.useEffect"], []);
    function loadEndedAuctions() {
        __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getEndedAuctions().then((endedAuctions)=>{
            setEndedAuctions(endedAuctions);
        });
    }
    function getEndString(end) {
        let momentDate = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$moment$2f$moment$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(end);
        return end.getTime() < new Date().getTime() ? 'Ended ' + momentDate.fromNow() : 'Ends ' + momentDate.fromNow();
    }
    function getAuctionElement(auction, style) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: `${__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Startpage$2f$Startpage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].cardWrapper}`,
            style: style,
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                href: `/auction/${auction.uuid}`,
                className: "disableLinkStyle",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Header, {
                            style: {
                                padding: '10px'
                            },
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Startpage$2f$Startpage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].ellipsis,
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                        crossOrigin: "anonymous",
                                        src: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getItemImageUrl(auction.item) || '',
                                        height: "32",
                                        width: "32",
                                        alt: "",
                                        style: {
                                            marginRight: '5px'
                                        }
                                    }, void 0, false, {
                                        fileName: "[project]/components/Startpage/Startpage.tsx",
                                        lineNumber: 65,
                                        columnNumber: 33
                                    }, this),
                                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Formatter$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getMinecraftColorCodedElement"])(auction.item.name)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/components/Startpage/Startpage.tsx",
                                lineNumber: 64,
                                columnNumber: 29
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/components/Startpage/Startpage.tsx",
                            lineNumber: 63,
                            columnNumber: 25
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Body, {
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                            children: getEndString(auction.end)
                                        }, void 0, false, {
                                            fileName: "[project]/components/Startpage/Startpage.tsx",
                                            lineNumber: 79,
                                            columnNumber: 37
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Number$2f$Number$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                    number: auction.highestBid || auction.startingBid
                                                }, void 0, false, {
                                                    fileName: "[project]/components/Startpage/Startpage.tsx",
                                                    lineNumber: 81,
                                                    columnNumber: 41
                                                }, this),
                                                " Coins"
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/components/Startpage/Startpage.tsx",
                                            lineNumber: 80,
                                            columnNumber: 37
                                        }, this),
                                        auction.bin ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Badge$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                style: {
                                                    marginLeft: '5px'
                                                },
                                                bg: "success",
                                                children: "BIN"
                                            }, void 0, false, {
                                                fileName: "[project]/components/Startpage/Startpage.tsx",
                                                lineNumber: 85,
                                                columnNumber: 45
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/components/Startpage/Startpage.tsx",
                                            lineNumber: 84,
                                            columnNumber: 41
                                        }, this) : ''
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/components/Startpage/Startpage.tsx",
                                    lineNumber: 78,
                                    columnNumber: 33
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/components/Startpage/Startpage.tsx",
                                lineNumber: 77,
                                columnNumber: 29
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/components/Startpage/Startpage.tsx",
                            lineNumber: 76,
                            columnNumber: 25
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/components/Startpage/Startpage.tsx",
                    lineNumber: 62,
                    columnNumber: 21
                }, this)
            }, void 0, false, {
                fileName: "[project]/components/Startpage/Startpage.tsx",
                lineNumber: 61,
                columnNumber: 17
            }, this)
        }, auction.uuid, false, {
            fileName: "[project]/components/Startpage/Startpage.tsx",
            lineNumber: 60,
            columnNumber: 13
        }, this);
    }
    function getNewPlayerElement(newPlayer, style) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: `${__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Startpage$2f$Startpage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].cardWrapper} ${__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Startpage$2f$Startpage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].disableLinkStyle}`,
            style: style,
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                href: `/player/${newPlayer.uuid}`,
                className: "disableLinkStyle",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Header, {
                        style: {
                            height: '100%',
                            padding: '20px'
                        },
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                style: {
                                    float: 'left'
                                },
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    crossOrigin: "anonymous",
                                    className: "playerHeadIcon",
                                    src: newPlayer.iconUrl || '',
                                    height: "32",
                                    width: "32",
                                    alt: "",
                                    style: {
                                        marginRight: '5px'
                                    },
                                    loading: "lazy"
                                }, void 0, false, {
                                    fileName: "[project]/components/Startpage/Startpage.tsx",
                                    lineNumber: 108,
                                    columnNumber: 33
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/components/Startpage/Startpage.tsx",
                                lineNumber: 107,
                                columnNumber: 29
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Title, {
                                className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Startpage$2f$Startpage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].ellipsis,
                                children: newPlayer.name
                            }, void 0, false, {
                                fileName: "[project]/components/Startpage/Startpage.tsx",
                                lineNumber: 119,
                                columnNumber: 29
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/Startpage/Startpage.tsx",
                        lineNumber: 106,
                        columnNumber: 25
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/components/Startpage/Startpage.tsx",
                    lineNumber: 105,
                    columnNumber: 21
                }, this)
            }, void 0, false, {
                fileName: "[project]/components/Startpage/Startpage.tsx",
                lineNumber: 104,
                columnNumber: 17
            }, this)
        }, newPlayer.name, false, {
            fileName: "[project]/components/Startpage/Startpage.tsx",
            lineNumber: 103,
            columnNumber: 13
        }, this);
    }
    function getPopularSearchElement(search, style) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: `${__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Startpage$2f$Startpage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].cardWrapper} ${__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Startpage$2f$Startpage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].disableLinkStyle}`,
            style: style,
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                href: search.url,
                className: "disableLinkStyle",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Header, {
                        style: {
                            height: '100%'
                        },
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                style: {
                                    float: 'left'
                                },
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    crossOrigin: "anonymous",
                                    className: "playerHeadIcon",
                                    src: search.url.includes('/player') ? search.img + '?size=8' : search.img,
                                    height: "32",
                                    width: "32",
                                    alt: "",
                                    style: {
                                        marginRight: '5px'
                                    },
                                    loading: "lazy"
                                }, void 0, false, {
                                    fileName: "[project]/components/Startpage/Startpage.tsx",
                                    lineNumber: 134,
                                    columnNumber: 33
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/components/Startpage/Startpage.tsx",
                                lineNumber: 133,
                                columnNumber: 29
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Title, {
                                className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Startpage$2f$Startpage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].ellipsis,
                                children: search.title
                            }, void 0, false, {
                                fileName: "[project]/components/Startpage/Startpage.tsx",
                                lineNumber: 145,
                                columnNumber: 29
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/Startpage/Startpage.tsx",
                        lineNumber: 132,
                        columnNumber: 25
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/components/Startpage/Startpage.tsx",
                    lineNumber: 131,
                    columnNumber: 21
                }, this)
            }, void 0, false, {
                fileName: "[project]/components/Startpage/Startpage.tsx",
                lineNumber: 130,
                columnNumber: 17
            }, this)
        }, search.url, false, {
            fileName: "[project]/components/Startpage/Startpage.tsx",
            lineNumber: 129,
            columnNumber: 13
        }, this);
    }
    function getNewItemElement(newItem, style) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: `${__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Startpage$2f$Startpage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].cardWrapper} ${__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Startpage$2f$Startpage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].disableLinkStyle}`,
            style: style,
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                href: `/item/${newItem.tag}`,
                className: "disableLinkStyle",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Header, {
                        style: {
                            height: '100%',
                            padding: '20px'
                        },
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                style: {
                                    float: 'left'
                                },
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    crossOrigin: "anonymous",
                                    src: newItem.iconUrl || '',
                                    height: "32",
                                    width: "32",
                                    alt: "",
                                    style: {
                                        marginRight: '5px'
                                    },
                                    loading: "lazy"
                                }, void 0, false, {
                                    fileName: "[project]/components/Startpage/Startpage.tsx",
                                    lineNumber: 160,
                                    columnNumber: 33
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/components/Startpage/Startpage.tsx",
                                lineNumber: 159,
                                columnNumber: 29
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Title, {
                                className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Startpage$2f$Startpage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].ellipsis,
                                children: newItem.name
                            }, void 0, false, {
                                fileName: "[project]/components/Startpage/Startpage.tsx",
                                lineNumber: 170,
                                columnNumber: 29
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/Startpage/Startpage.tsx",
                        lineNumber: 158,
                        columnNumber: 25
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/components/Startpage/Startpage.tsx",
                    lineNumber: 157,
                    columnNumber: 21
                }, this)
            }, void 0, false, {
                fileName: "[project]/components/Startpage/Startpage.tsx",
                lineNumber: 156,
                columnNumber: 17
            }, this)
        }, newItem.tag, false, {
            fileName: "[project]/components/Startpage/Startpage.tsx",
            lineNumber: 155,
            columnNumber: 13
        }, this);
    }
    function onRecentChangesClick() {
        trackEvent({
            category: 'recentChanges',
            action: 'recentChangesClicked'
        });
    }
    function attachScrollEvent(className) {
        let scrollContainers = document.getElementsByClassName(className);
        for(var i = 0; i < scrollContainers.length; i++){
            let container = scrollContainers.item(i);
            if (container) {
                container.addEventListener('wheel', (evt)=>{
                    evt.preventDefault();
                    let scrollAmount = 0;
                    var slideTimer = setInterval(()=>{
                        container.scrollLeft += evt.deltaY / 10;
                        scrollAmount += Math.abs(evt.deltaY) / 10;
                        if (scrollAmount >= Math.abs(evt.deltaY)) {
                            clearInterval(slideTimer);
                        }
                    }, 25);
                });
            }
        }
    }
    let newAuctionsElement = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        id: "new-auctions-element",
        className: `${__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Startpage$2f$Startpage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].cardsWrapper} ${__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Startpage$2f$Startpage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].newAuctions}`,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$window$2f$dist$2f$index$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FixedSizeList"], {
            className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Startpage$2f$Startpage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].startpageListElementWrapper,
            height: 260 - 15,
            itemCount: newAuctions.length,
            itemSize: 200,
            layout: "horizontal",
            width: isSSR ? 10000 : document.getElementById('new-auctions-element')?.offsetWidth,
            children: ({ index, style })=>{
                return getAuctionElement(newAuctions[index], style);
            }
        }, void 0, false, {
            fileName: "[project]/components/Startpage/Startpage.tsx",
            lineNumber: 207,
            columnNumber: 13
        }, this)
    }, void 0, false, {
        fileName: "[project]/components/Startpage/Startpage.tsx",
        lineNumber: 206,
        columnNumber: 9
    }, this);
    let popularSearchesElement = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `${__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Startpage$2f$Startpage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].cardsWrapper} ${__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Startpage$2f$Startpage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].popularSearches}`,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$window$2f$dist$2f$index$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FixedSizeList"], {
            className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Startpage$2f$Startpage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].startpageListElementWrapper,
            height: 130 - 15,
            itemCount: popularSearches.length,
            itemSize: 200,
            layout: "horizontal",
            width: isSSR ? 10000 : document.getElementById('new-auctions-element')?.offsetWidth,
            children: ({ index, style })=>{
                return getPopularSearchElement(popularSearches[index], style);
            }
        }, void 0, false, {
            fileName: "[project]/components/Startpage/Startpage.tsx",
            lineNumber: 224,
            columnNumber: 13
        }, this)
    }, void 0, false, {
        fileName: "[project]/components/Startpage/Startpage.tsx",
        lineNumber: 223,
        columnNumber: 9
    }, this);
    let endedAuctionsElement = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `${__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Startpage$2f$Startpage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].cardsWrapper} ${__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Startpage$2f$Startpage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].endedAuctions}`,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$window$2f$dist$2f$index$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FixedSizeList"], {
            className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Startpage$2f$Startpage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].startpageListElementWrapper,
            height: 260 - 15,
            itemCount: endedAuctions.length === 0 ? 20 : endedAuctions.length,
            itemSize: 200,
            layout: "horizontal",
            width: isSSR ? 10000 : document.getElementById('new-auctions-element')?.offsetWidth,
            children: ({ index, style })=>{
                return endedAuctions.length === 0 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Startpage$2f$StartpageLargeElementSkeleton$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["StartpageLargeElementSkeleton"], {
                    style: style
                }, void 0, false, {
                    fileName: "[project]/components/Startpage/Startpage.tsx",
                    lineNumber: 250,
                    columnNumber: 57
                }, this) : getAuctionElement(endedAuctions[index], style);
            }
        }, void 0, false, {
            fileName: "[project]/components/Startpage/Startpage.tsx",
            lineNumber: 241,
            columnNumber: 13
        }, this)
    }, void 0, false, {
        fileName: "[project]/components/Startpage/Startpage.tsx",
        lineNumber: 240,
        columnNumber: 9
    }, this);
    let newPlayersElement = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `${__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Startpage$2f$Startpage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].cardsWrapper} ${__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Startpage$2f$Startpage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].newPlayers}`,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$window$2f$dist$2f$index$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FixedSizeList"], {
            className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Startpage$2f$Startpage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].startpageListElementWrapper,
            height: 130 - 15,
            itemCount: newPlayers.length,
            itemSize: 200,
            layout: "horizontal",
            width: isSSR ? 10000 : document.getElementById('new-auctions-element')?.offsetWidth,
            children: ({ index, style })=>{
                return getNewPlayerElement(newPlayers[index], style);
            }
        }, void 0, false, {
            fileName: "[project]/components/Startpage/Startpage.tsx",
            lineNumber: 258,
            columnNumber: 13
        }, this)
    }, void 0, false, {
        fileName: "[project]/components/Startpage/Startpage.tsx",
        lineNumber: 257,
        columnNumber: 9
    }, this);
    let newItemsElement = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `${__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Startpage$2f$Startpage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].cardsWrapper} ${__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Startpage$2f$Startpage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].newItems}`,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$window$2f$dist$2f$index$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FixedSizeList"], {
            className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Startpage$2f$Startpage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].startpageListElementWrapper,
            height: 130 - 15,
            itemCount: newItems.length,
            itemSize: 200,
            layout: "horizontal",
            width: isSSR ? 10000 : document.getElementById('new-auctions-element')?.offsetWidth,
            children: ({ index, style })=>{
                return getNewItemElement(newItems[index], style);
            }
        }, void 0, false, {
            fileName: "[project]/components/Startpage/Startpage.tsx",
            lineNumber: 274,
            columnNumber: 13
        }, this)
    }, void 0, false, {
        fileName: "[project]/components/Startpage/Startpage.tsx",
        lineNumber: 273,
        columnNumber: 9
    }, this);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                style: {
                    textAlign: 'center'
                },
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("hr", {}, void 0, false, {
                        fileName: "[project]/components/Startpage/Startpage.tsx",
                        lineNumber: 292,
                        columnNumber: 17
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                        children: "Skyblock Auction House History"
                    }, void 0, false, {
                        fileName: "[project]/components/Startpage/Startpage.tsx",
                        lineNumber: 293,
                        columnNumber: 17
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        style: {
                            fontSize: 'larger'
                        },
                        children: "Browse through over 600 million auctions, over two million players and the bazaar of hypixel skyblock"
                    }, void 0, false, {
                        fileName: "[project]/components/Startpage/Startpage.tsx",
                        lineNumber: 294,
                        columnNumber: 17
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("hr", {}, void 0, false, {
                        fileName: "[project]/components/Startpage/Startpage.tsx",
                        lineNumber: 295,
                        columnNumber: 17
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/components/Startpage/Startpage.tsx",
                lineNumber: 291,
                columnNumber: 13
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Startpage$2f$Startpage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].startpageCard,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Header, {
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Title, {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$FiberNew$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                    fileName: "[project]/components/Startpage/Startpage.tsx",
                                    lineNumber: 300,
                                    columnNumber: 25
                                }, this),
                                " New Auctions"
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/Startpage/Startpage.tsx",
                            lineNumber: 299,
                            columnNumber: 21
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/components/Startpage/Startpage.tsx",
                        lineNumber: 298,
                        columnNumber: 17
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Body, {
                        className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Startpage$2f$Startpage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].startpageCardBody,
                        id: "new-auctions-body",
                        children: newAuctionsElement
                    }, void 0, false, {
                        fileName: "[project]/components/Startpage/Startpage.tsx",
                        lineNumber: 303,
                        columnNumber: 17
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/components/Startpage/Startpage.tsx",
                lineNumber: 297,
                columnNumber: 13
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Startpage$2f$Startpage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].startpageCard,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Header, {
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Title, {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Timer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                    fileName: "[project]/components/Startpage/Startpage.tsx",
                                    lineNumber: 311,
                                    columnNumber: 25
                                }, this),
                                " Ended Auctions"
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/Startpage/Startpage.tsx",
                            lineNumber: 310,
                            columnNumber: 21
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/components/Startpage/Startpage.tsx",
                        lineNumber: 309,
                        columnNumber: 17
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Body, {
                        className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Startpage$2f$Startpage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].startpageCardBody,
                        id: "ended-auctions-body",
                        children: endedAuctionsElement
                    }, void 0, false, {
                        fileName: "[project]/components/Startpage/Startpage.tsx",
                        lineNumber: 314,
                        columnNumber: 17
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/components/Startpage/Startpage.tsx",
                lineNumber: 308,
                columnNumber: 13
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Startpage$2f$Startpage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].startpageCard,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Header, {
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Title, {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Person$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                    fileName: "[project]/components/Startpage/Startpage.tsx",
                                    lineNumber: 322,
                                    columnNumber: 25
                                }, this),
                                " New Players"
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/Startpage/Startpage.tsx",
                            lineNumber: 321,
                            columnNumber: 21
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/components/Startpage/Startpage.tsx",
                        lineNumber: 320,
                        columnNumber: 17
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Body, {
                        className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Startpage$2f$Startpage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].startpageCardBody,
                        id: "new-players-body",
                        children: newPlayersElement
                    }, void 0, false, {
                        fileName: "[project]/components/Startpage/Startpage.tsx",
                        lineNumber: 325,
                        columnNumber: 17
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/components/Startpage/Startpage.tsx",
                lineNumber: 319,
                columnNumber: 13
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Startpage$2f$Startpage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].startpageCard,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Header, {
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Title, {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Fireplace$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                    fileName: "[project]/components/Startpage/Startpage.tsx",
                                    lineNumber: 333,
                                    columnNumber: 25
                                }, this),
                                " Popular Searches"
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/Startpage/Startpage.tsx",
                            lineNumber: 332,
                            columnNumber: 21
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/components/Startpage/Startpage.tsx",
                        lineNumber: 331,
                        columnNumber: 17
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Body, {
                        className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Startpage$2f$Startpage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].startpageCardBody,
                        id: "popular-searches-body",
                        children: popularSearchesElement
                    }, void 0, false, {
                        fileName: "[project]/components/Startpage/Startpage.tsx",
                        lineNumber: 336,
                        columnNumber: 17
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/components/Startpage/Startpage.tsx",
                lineNumber: 330,
                columnNumber: 13
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Startpage$2f$Startpage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].startpageCard,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Header, {
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Title, {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$FiberNew$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                    fileName: "[project]/components/Startpage/Startpage.tsx",
                                    lineNumber: 344,
                                    columnNumber: 25
                                }, this),
                                " New Items"
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/Startpage/Startpage.tsx",
                            lineNumber: 343,
                            columnNumber: 21
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/components/Startpage/Startpage.tsx",
                        lineNumber: 342,
                        columnNumber: 17
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Body, {
                        className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Startpage$2f$Startpage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].startpageCardBody,
                        id: "new-items-body",
                        children: newItemsElement
                    }, void 0, false, {
                        fileName: "[project]/components/Startpage/Startpage.tsx",
                        lineNumber: 347,
                        columnNumber: 17
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/components/Startpage/Startpage.tsx",
                lineNumber: 341,
                columnNumber: 13
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Startpage$2f$Startpage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].startpageCard,
                style: {
                    marginTop: '40px'
                },
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Header, {
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Title, {
                            children: "Hypixel Auction House History"
                        }, void 0, false, {
                            fileName: "[project]/components/Startpage/Startpage.tsx",
                            lineNumber: 354,
                            columnNumber: 21
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/components/Startpage/Startpage.tsx",
                        lineNumber: 353,
                        columnNumber: 17
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Body, {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                children: "View, search, browse, and filter by reforge or enchantment."
                            }, void 0, false, {
                                fileName: "[project]/components/Startpage/Startpage.tsx",
                                lineNumber: 357,
                                columnNumber: 21
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                children: "You can find all current and historic prices for the auction house and bazaar on this web tracker."
                            }, void 0, false, {
                                fileName: "[project]/components/Startpage/Startpage.tsx",
                                lineNumber: 358,
                                columnNumber: 21
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                children: "We're tracking over 600 million auctions. We've saved more than 350 million bazaar prices in intervals of 10 seconds. Furthermore, there are over three million skyblock players that you can search by their Minecraft usernames. You can browse through the auctions they made over the past two years. New items are added automatically and are available within two minutes after the first auction is started."
                            }, void 0, false, {
                                fileName: "[project]/components/Startpage/Startpage.tsx",
                                lineNumber: 359,
                                columnNumber: 21
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                children: "The search autocomplete is ranked by popularity and allows you to find whatever item you want faster. Quick URLs allow you to link to specific sites. /p/Steve or /i/Oak allows you to create a link without visiting the site first."
                            }, void 0, false, {
                                fileName: "[project]/components/Startpage/Startpage.tsx",
                                lineNumber: 364,
                                columnNumber: 21
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                children: [
                                    "The free accessible",
                                    ' ',
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                        href: "/flipper",
                                        style: {
                                            backgroundColor: 'white',
                                            textDecoration: 'none',
                                            color: 'black',
                                            borderRadius: '3px'
                                        },
                                        children: "auction house flipper ↗️"
                                    }, void 0, false, {
                                        fileName: "[project]/components/Startpage/Startpage.tsx",
                                        lineNumber: 370,
                                        columnNumber: 25
                                    }, this),
                                    ' ',
                                    "allows you to find profitable AH flips in no time. It supplements the option to browse all of the Skyblock history on the web tracker. What's more is that you can see what auctions were used as reference to determine if a flip is profitable."
                                ]
                            }, void 0, true, {
                                fileName: "[project]/components/Startpage/Startpage.tsx",
                                lineNumber: 368,
                                columnNumber: 21
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                children: [
                                    "We allow you to subscribe to auctions, item prices and being outbid with more to come. Please use the contact on the",
                                    ' ',
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                        href: "/feedback",
                                        style: {
                                            backgroundColor: 'white',
                                            textDecoration: 'none',
                                            color: 'black',
                                            borderRadius: '3px'
                                        },
                                        children: "Feedback site ↗️"
                                    }, void 0, false, {
                                        fileName: "[project]/components/Startpage/Startpage.tsx",
                                        lineNumber: 378,
                                        columnNumber: 25
                                    }, this),
                                    ' ',
                                    "to send us suggestions or bug reports."
                                ]
                            }, void 0, true, {
                                fileName: "[project]/components/Startpage/Startpage.tsx",
                                lineNumber: 376,
                                columnNumber: 21
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/Startpage/Startpage.tsx",
                        lineNumber: 356,
                        columnNumber: 17
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/components/Startpage/Startpage.tsx",
                lineNumber: 352,
                columnNumber: 13
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/components/Startpage/Startpage.tsx",
        lineNumber: 290,
        columnNumber: 9
    }, this);
}
_s(Startpage, "xlrlX8HgIsEFiP0EsxaVwA1EZR0=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$jonkoops$2f$matomo$2d$tracker$2d$react$2f$es$2f$useMatomo$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useMatomo$3e$__["useMatomo"]
    ];
});
_c = Startpage;
const __TURBOPACK__default__export__ = Startpage;
var _c;
__turbopack_context__.k.register(_c, "Startpage");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/utils/Hooks.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "useCoflCoins": (()=>useCoflCoins),
    "useDebounce": (()=>useDebounce),
    "useForceUpdate": (()=>useForceUpdate),
    "useIsMobile": (()=>useIsMobile),
    "useQueryParamState": (()=>useQueryParamState),
    "useStateWithRef": (()=>useStateWithRef),
    "useSwipe": (()=>useSwipe),
    "useWasAlreadyLoggedIn": (()=>useWasAlreadyLoggedIn)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$CoflCoinsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/CoflCoinsUtils.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SSRUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/SSRUtils.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Parser$2f$URLParser$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/Parser/URLParser.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature(), _s3 = __turbopack_context__.k.signature(), _s4 = __turbopack_context__.k.signature(), _s5 = __turbopack_context__.k.signature(), _s6 = __turbopack_context__.k.signature();
;
;
;
;
;
function useForceUpdate() {
    _s();
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const [update, setUpdate] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(0);
    return ()=>setUpdate((update)=>update + 1);
}
_s(useForceUpdate, "U5iovX6txcuyzahsHyptxdF1Nws=");
function useSwipe(onSwipeUp, onSwipeRight, onSwipeDown, onSwipeLeft) {
    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SSRUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isClientSideRendering"])()) {
        return;
    }
    document.addEventListener('touchstart', handleTouchStart, false);
    document.addEventListener('touchmove', handleTouchMove, false);
    var xDown = null;
    var yDown = null;
    function getTouches(evt) {
        return evt.touches || // browser API
        evt.originalEvent.touches; // jQuery
    }
    function handleTouchStart(evt) {
        const firstTouch = getTouches(evt)[0];
        xDown = firstTouch.clientX;
        yDown = firstTouch.clientY;
    }
    function handleTouchMove(evt) {
        if (xDown === null || yDown === null) {
            return;
        }
        var xUp = evt.touches[0].clientX;
        var yUp = evt.touches[0].clientY;
        var xDiff = xDown - xUp;
        var yDiff = yDown - yUp;
        if (Math.abs(xDiff) > Math.abs(yDiff)) {
            /*most significant*/ if (xDiff > 0) {
                if (onSwipeLeft) {
                    onSwipeLeft();
                }
            } else {
                if (onSwipeRight) {
                    onSwipeRight();
                }
            }
        } else {
            if (yDiff > 0) {
                if (onSwipeUp) {
                    onSwipeUp();
                }
            } else {
                if (onSwipeDown) {
                    onSwipeDown();
                }
            }
        }
        /* reset values */ xDown = null;
        yDown = null;
    }
    return ()=>{
        document.removeEventListener('touchstart', handleTouchStart, false);
        document.removeEventListener('touchmove', handleTouchMove, false);
    };
}
function useCoflCoins() {
    _s1();
    const [coflCoins, setCoflCoins] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$CoflCoinsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getCurrentCoflCoins"])());
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useCoflCoins.useEffect": ()=>{
            let unsubscribe = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$CoflCoinsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["subscribeToCoflcoinChange"])(setCoflCoins);
            return ({
                "useCoflCoins.useEffect": ()=>{
                    unsubscribe();
                }
            })["useCoflCoins.useEffect"];
        }
    }["useCoflCoins.useEffect"], []);
    return coflCoins;
}
_s1(useCoflCoins, "C+enwg4fK/g34CESb/vw33ukaqE=");
function useWasAlreadyLoggedIn() {
    _s2();
    const [wasAlreadyLoggedIn, setWasAlreadyLoggedIn] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useWasAlreadyLoggedIn.useEffect": ()=>{
            setWasAlreadyLoggedIn(localStorage.getItem('googleId') !== null);
        }
    }["useWasAlreadyLoggedIn.useEffect"], []);
    return wasAlreadyLoggedIn;
}
_s2(useWasAlreadyLoggedIn, "1f04SSxs2Qtmf783W7lV2PTlbiw=");
function useDebounce(value, delay) {
    _s3();
    const [debouncedValue, setDebouncedValue] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(value);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useDebounce.useEffect": ()=>{
            const handler = setTimeout({
                "useDebounce.useEffect.handler": ()=>{
                    setDebouncedValue(value);
                }
            }["useDebounce.useEffect.handler"], delay);
            return ({
                "useDebounce.useEffect": ()=>{
                    clearTimeout(handler);
                }
            })["useDebounce.useEffect"];
        }
    }["useDebounce.useEffect"], [
        value,
        delay
    ]);
    return debouncedValue;
}
_s3(useDebounce, "KDuPAtDOgxm8PU6legVJOb3oOmA=");
function useStateWithRef(defaultValue) {
    _s4();
    const [state, _setState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(defaultValue);
    let stateRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(state);
    const setState = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useStateWithRef.useCallback[setState]": (newState)=>{
            stateRef.current = newState;
            _setState(newState);
        }
    }["useStateWithRef.useCallback[setState]"], []);
    return [
        state,
        setState,
        stateRef
    ];
}
_s4(useStateWithRef, "2zUIfANzaBXnh0dIWyeU0rgp5no=");
function useQueryParamState(key, defaultValue) {
    _s5();
    const [state, setState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(getDefaultValue() || defaultValue);
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    function getDefaultValue() {
        let param = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Parser$2f$URLParser$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getURLSearchParam"])(key);
        if (!param) {
            return undefined;
        }
        return JSON.parse(decodeURIComponent(param));
    }
    function _setState(newState) {
        setState(newState);
        let urlparams = new URLSearchParams(window.location.search);
        if (!newState) {
            urlparams.delete(key);
        } else {
            urlparams.set(key, encodeURIComponent(JSON.stringify(newState)));
        }
        router.replace(`${window.location.pathname}?${urlparams.toString()}`);
    }
    return [
        state,
        _setState
    ];
}
_s5(useQueryParamState, "gFWI+omlRaxP5wHoiPXmgkzW71U=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"]
    ];
});
function useIsMobile() {
    _s6();
    let [isMobile, setIsMobile] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useIsMobile.useEffect": ()=>{
            setIsMobile(isMobileCheck());
        }
    }["useIsMobile.useEffect"], []);
    function isMobileCheck() {
        let check = false;
        (function(a) {
            if (/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i.test(a) || /1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i.test(a.substr(0, 4))) check = true;
        })(navigator.userAgent || navigator.vendor || window.opera);
        return check;
    }
    return isMobile;
}
_s6(useIsMobile, "0VTTNJATKABQPGLm9RVT0tKGUgU=");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/NavBar/NavBar.module.css [app-client] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "hamburgerIcon": "NavBar-module__yBvhsG__hamburgerIcon",
  "logo": "NavBar-module__yBvhsG__logo",
  "menuItem": "NavBar-module__yBvhsG__menuItem",
  "navBar": "NavBar-module__yBvhsG__navBar",
  "navClosing": "NavBar-module__yBvhsG__navClosing",
  "navOpen": "NavBar-module__yBvhsG__navOpen",
});
}}),
"[project]/components/NavBar/NavBar.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$AccountBalance$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/icons-material/esm/AccountBalance.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$AccountCircle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/icons-material/esm/AccountCircle.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Build$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/icons-material/esm/Build.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Chat$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/icons-material/esm/Chat.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Download$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/icons-material/esm/Download.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Home$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/icons-material/esm/Home.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Menu$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/icons-material/esm/Menu.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$NotificationsOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/icons-material/esm/NotificationsOutlined.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$PetsOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/icons-material/esm/PetsOutlined.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Policy$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/icons-material/esm/Policy.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$ShareOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/icons-material/esm/ShareOutlined.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Storefront$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/icons-material/esm/Storefront.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$CurrencyExchange$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/icons-material/esm/CurrencyExchange.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pro$2d$sidebar$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-pro-sidebar/dist/index.es.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Hooks$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/Hooks.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$NavBar$2f$NavBar$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/components/NavBar/NavBar.module.css [app-client] (css module)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
let resizePromise = null;
function NavBar(props) {
    _s();
    let [isWideOpen, setIsWideOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    let [isHovering, setIsHovering] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    let [isSmall, setIsSmall] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    let [collapsed, setCollapsed] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    let forceUpdate = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Hooks$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForceUpdate"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "NavBar.useEffect": ()=>{
            setIsSmall(document.body.clientWidth < 1500);
            window.addEventListener('resize', resizeHandler);
            return ({
                "NavBar.useEffect": ()=>{
                    window.removeEventListener('resize', resizeHandler);
                }
            })["NavBar.useEffect"];
        }
    }["NavBar.useEffect"], []);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "NavBar.useEffect": ()=>{
            if (isWideOpen) {
                document.addEventListener('click', outsideClickHandler, true);
            } else {
                document.removeEventListener('click', outsideClickHandler, true);
            }
            return ({
                "NavBar.useEffect": ()=>{
                    document.removeEventListener('click', outsideClickHandler, true);
                }
            })["NavBar.useEffect"];
        // eslint-disable-next-line react-hooks/exhaustive-deps
        }
    }["NavBar.useEffect"], [
        isWideOpen
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "NavBar.useEffect": ()=>{
            setCollapsed(isCollapsed());
        }
    }["NavBar.useEffect"], [
        isSmall,
        isWideOpen,
        isHovering
    ]);
    function isCollapsed() {
        if (isSmall) {
            return false;
        }
        return !isWideOpen && !isHovering;
    }
    function outsideClickHandler(evt) {
        const flyoutEl = document.getElementById('navBar');
        const hamburgerEl = document.getElementById('hamburgerIcon');
        let targetEl = evt.target;
        do {
            if (targetEl === flyoutEl || targetEl === hamburgerEl) {
                return;
            }
            targetEl = targetEl.parentNode;
        }while (targetEl)
        if (isWideOpen) {
            if (isSmall) {
                let el = document.getElementById('pro-sidebar');
                el?.classList.add(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$NavBar$2f$NavBar$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].navClosing);
                el?.classList.remove(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$NavBar$2f$NavBar$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].navOpen);
                setTimeout(()=>{
                    setIsWideOpen(false);
                    el?.classList.remove(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$NavBar$2f$NavBar$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].navClosing);
                }, 500);
            } else {
                setIsWideOpen(false);
            }
        }
    }
    function onMouseMove() {
        setIsHovering(true);
    }
    function onMouseOut() {
        setIsHovering(false);
    }
    function resizeHandler() {
        if (resizePromise) {
            return;
        }
        resizePromise = setTimeout(()=>{
            setIsWideOpen(false);
            setIsSmall(document.body.clientWidth < 1500);
            forceUpdate();
            resizePromise = null;
            let el = document.getElementById('pro-sidebar');
            if (el) {
                el.style.left = '0px';
            }
        }, 500);
    }
    function onHamburgerClick() {
        if (isSmall && !isWideOpen) {
            let el = document.getElementById('pro-sidebar');
            if (el) {
                el.hidden = false;
                el.style.left = '-270px';
                setTimeout(()=>{
                    if (el) {
                        el.classList.add(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$NavBar$2f$NavBar$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].navOpen);
                    }
                });
                setTimeout(()=>{
                    setIsWideOpen(true);
                }, 500);
            }
        } else {
            setIsWideOpen(!isWideOpen);
        }
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("aside", {
                className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$NavBar$2f$NavBar$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].navBar,
                id: "navBar",
                onMouseEnter: onMouseMove,
                onMouseLeave: onMouseOut,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pro$2d$sidebar$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Sidebar"], {
                    id: "pro-sidebar",
                    hidden: isSmall && !isWideOpen,
                    backgroundColor: "#1d1d1d",
                    collapsed: collapsed,
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        style: {
                            height: '100%',
                            display: 'flex',
                            flexDirection: 'column'
                        },
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$NavBar$2f$NavBar$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].logo,
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            src: "/logo512.png",
                                            alt: "Logo",
                                            width: 40,
                                            height: 40,
                                            style: {
                                                translate: '-5px'
                                            }
                                        }, void 0, false, {
                                            fileName: "[project]/components/NavBar/NavBar.tsx",
                                            lineNumber: 147,
                                            columnNumber: 33
                                        }, this),
                                        " ",
                                        !isCollapsed() ? 'Coflnet' : ''
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/components/NavBar/NavBar.tsx",
                                    lineNumber: 146,
                                    columnNumber: 29
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/components/NavBar/NavBar.tsx",
                                lineNumber: 145,
                                columnNumber: 25
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("hr", {}, void 0, false, {
                                fileName: "[project]/components/NavBar/NavBar.tsx",
                                lineNumber: 150,
                                columnNumber: 25
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pro$2d$sidebar$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Menu"], {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pro$2d$sidebar$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MenuItem"], {
                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$NavBar$2f$NavBar$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].menuItem,
                                        component: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            href: '/'
                                        }, void 0, false, {
                                            fileName: "[project]/components/NavBar/NavBar.tsx",
                                            lineNumber: 152,
                                            columnNumber: 78
                                        }, void 0),
                                        icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Home$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                            fileName: "[project]/components/NavBar/NavBar.tsx",
                                            lineNumber: 152,
                                            columnNumber: 105
                                        }, void 0),
                                        children: "Home"
                                    }, void 0, false, {
                                        fileName: "[project]/components/NavBar/NavBar.tsx",
                                        lineNumber: 152,
                                        columnNumber: 29
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pro$2d$sidebar$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MenuItem"], {
                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$NavBar$2f$NavBar$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].menuItem,
                                        component: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            href: '/flipper'
                                        }, void 0, false, {
                                            fileName: "[project]/components/NavBar/NavBar.tsx",
                                            lineNumber: 155,
                                            columnNumber: 78
                                        }, void 0),
                                        icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Storefront$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                            fileName: "[project]/components/NavBar/NavBar.tsx",
                                            lineNumber: 155,
                                            columnNumber: 112
                                        }, void 0),
                                        children: "Item Flipper"
                                    }, void 0, false, {
                                        fileName: "[project]/components/NavBar/NavBar.tsx",
                                        lineNumber: 155,
                                        columnNumber: 29
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pro$2d$sidebar$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MenuItem"], {
                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$NavBar$2f$NavBar$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].menuItem,
                                        component: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            href: '/account'
                                        }, void 0, false, {
                                            fileName: "[project]/components/NavBar/NavBar.tsx",
                                            lineNumber: 158,
                                            columnNumber: 78
                                        }, void 0),
                                        icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$AccountCircle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                            fileName: "[project]/components/NavBar/NavBar.tsx",
                                            lineNumber: 158,
                                            columnNumber: 112
                                        }, void 0),
                                        children: "Account"
                                    }, void 0, false, {
                                        fileName: "[project]/components/NavBar/NavBar.tsx",
                                        lineNumber: 158,
                                        columnNumber: 29
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pro$2d$sidebar$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MenuItem"], {
                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$NavBar$2f$NavBar$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].menuItem,
                                        component: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            href: '/subscriptions'
                                        }, void 0, false, {
                                            fileName: "[project]/components/NavBar/NavBar.tsx",
                                            lineNumber: 161,
                                            columnNumber: 78
                                        }, void 0),
                                        icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$NotificationsOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                            fileName: "[project]/components/NavBar/NavBar.tsx",
                                            lineNumber: 161,
                                            columnNumber: 118
                                        }, void 0),
                                        children: "Notifier"
                                    }, void 0, false, {
                                        fileName: "[project]/components/NavBar/NavBar.tsx",
                                        lineNumber: 161,
                                        columnNumber: 29
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pro$2d$sidebar$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MenuItem"], {
                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$NavBar$2f$NavBar$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].menuItem,
                                        component: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            href: '/crafts'
                                        }, void 0, false, {
                                            fileName: "[project]/components/NavBar/NavBar.tsx",
                                            lineNumber: 164,
                                            columnNumber: 78
                                        }, void 0),
                                        icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Build$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                            fileName: "[project]/components/NavBar/NavBar.tsx",
                                            lineNumber: 164,
                                            columnNumber: 111
                                        }, void 0),
                                        children: "Profitable Crafts"
                                    }, void 0, false, {
                                        fileName: "[project]/components/NavBar/NavBar.tsx",
                                        lineNumber: 164,
                                        columnNumber: 29
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pro$2d$sidebar$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MenuItem"], {
                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$NavBar$2f$NavBar$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].menuItem,
                                        component: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            href: '/premium'
                                        }, void 0, false, {
                                            fileName: "[project]/components/NavBar/NavBar.tsx",
                                            lineNumber: 167,
                                            columnNumber: 78
                                        }, void 0),
                                        icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$AccountBalance$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                            fileName: "[project]/components/NavBar/NavBar.tsx",
                                            lineNumber: 167,
                                            columnNumber: 112
                                        }, void 0),
                                        children: "Premium / Shop"
                                    }, void 0, false, {
                                        fileName: "[project]/components/NavBar/NavBar.tsx",
                                        lineNumber: 167,
                                        columnNumber: 29
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pro$2d$sidebar$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MenuItem"], {
                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$NavBar$2f$NavBar$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].menuItem,
                                        component: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            href: '/trade'
                                        }, void 0, false, {
                                            fileName: "[project]/components/NavBar/NavBar.tsx",
                                            lineNumber: 170,
                                            columnNumber: 78
                                        }, void 0),
                                        icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$CurrencyExchange$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                            fileName: "[project]/components/NavBar/NavBar.tsx",
                                            lineNumber: 170,
                                            columnNumber: 110
                                        }, void 0),
                                        children: "Trading"
                                    }, void 0, false, {
                                        fileName: "[project]/components/NavBar/NavBar.tsx",
                                        lineNumber: 170,
                                        columnNumber: 29
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pro$2d$sidebar$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MenuItem"], {
                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$NavBar$2f$NavBar$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].menuItem,
                                        component: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            href: '/kat'
                                        }, void 0, false, {
                                            fileName: "[project]/components/NavBar/NavBar.tsx",
                                            lineNumber: 173,
                                            columnNumber: 78
                                        }, void 0),
                                        icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$PetsOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                            fileName: "[project]/components/NavBar/NavBar.tsx",
                                            lineNumber: 173,
                                            columnNumber: 108
                                        }, void 0),
                                        children: "Kat Flips"
                                    }, void 0, false, {
                                        fileName: "[project]/components/NavBar/NavBar.tsx",
                                        lineNumber: 173,
                                        columnNumber: 29
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pro$2d$sidebar$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MenuItem"], {
                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$NavBar$2f$NavBar$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].menuItem,
                                        component: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            href: '/mod'
                                        }, void 0, false, {
                                            fileName: "[project]/components/NavBar/NavBar.tsx",
                                            lineNumber: 176,
                                            columnNumber: 78
                                        }, void 0),
                                        icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Download$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                            fileName: "[project]/components/NavBar/NavBar.tsx",
                                            lineNumber: 176,
                                            columnNumber: 108
                                        }, void 0),
                                        children: "Mod"
                                    }, void 0, false, {
                                        fileName: "[project]/components/NavBar/NavBar.tsx",
                                        lineNumber: 176,
                                        columnNumber: 29
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pro$2d$sidebar$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MenuItem"], {
                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$NavBar$2f$NavBar$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].menuItem,
                                        component: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            href: '/ref'
                                        }, void 0, false, {
                                            fileName: "[project]/components/NavBar/NavBar.tsx",
                                            lineNumber: 179,
                                            columnNumber: 78
                                        }, void 0),
                                        icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$ShareOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                            fileName: "[project]/components/NavBar/NavBar.tsx",
                                            lineNumber: 179,
                                            columnNumber: 108
                                        }, void 0),
                                        children: "Referral"
                                    }, void 0, false, {
                                        fileName: "[project]/components/NavBar/NavBar.tsx",
                                        lineNumber: 179,
                                        columnNumber: 29
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pro$2d$sidebar$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MenuItem"], {
                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$NavBar$2f$NavBar$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].menuItem,
                                        component: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            href: '/about'
                                        }, void 0, false, {
                                            fileName: "[project]/components/NavBar/NavBar.tsx",
                                            lineNumber: 182,
                                            columnNumber: 78
                                        }, void 0),
                                        icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Policy$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                            fileName: "[project]/components/NavBar/NavBar.tsx",
                                            lineNumber: 182,
                                            columnNumber: 110
                                        }, void 0),
                                        children: "Links / Legal"
                                    }, void 0, false, {
                                        fileName: "[project]/components/NavBar/NavBar.tsx",
                                        lineNumber: 182,
                                        columnNumber: 29
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pro$2d$sidebar$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MenuItem"], {
                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$NavBar$2f$NavBar$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].menuItem,
                                        component: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            href: '/feedback'
                                        }, void 0, false, {
                                            fileName: "[project]/components/NavBar/NavBar.tsx",
                                            lineNumber: 185,
                                            columnNumber: 78
                                        }, void 0),
                                        icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Chat$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                            fileName: "[project]/components/NavBar/NavBar.tsx",
                                            lineNumber: 185,
                                            columnNumber: 113
                                        }, void 0),
                                        children: "Feedback"
                                    }, void 0, false, {
                                        fileName: "[project]/components/NavBar/NavBar.tsx",
                                        lineNumber: 185,
                                        columnNumber: 29
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pro$2d$sidebar$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MenuItem"], {
                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$NavBar$2f$NavBar$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].menuItem,
                                        component: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            href: 'https://discord.gg/wvKXfTgCfb',
                                            target: "_blank"
                                        }, void 0, false, {
                                            fileName: "[project]/components/NavBar/NavBar.tsx",
                                            lineNumber: 190,
                                            columnNumber: 44
                                        }, void 0),
                                        rel: "noreferrer",
                                        icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            src: "/discord_icon.svg",
                                            alt: "Discord icon",
                                            height: 24,
                                            width: 32
                                        }, void 0, false, {
                                            fileName: "[project]/components/NavBar/NavBar.tsx",
                                            lineNumber: 192,
                                            columnNumber: 39
                                        }, void 0),
                                        children: "Discord"
                                    }, void 0, false, {
                                        fileName: "[project]/components/NavBar/NavBar.tsx",
                                        lineNumber: 188,
                                        columnNumber: 29
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/components/NavBar/NavBar.tsx",
                                lineNumber: 151,
                                columnNumber: 25
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/NavBar/NavBar.tsx",
                        lineNumber: 144,
                        columnNumber: 21
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/components/NavBar/NavBar.tsx",
                    lineNumber: 143,
                    columnNumber: 17
                }, this)
            }, void 0, false, {
                fileName: "[project]/components/NavBar/NavBar.tsx",
                lineNumber: 142,
                columnNumber: 13
            }, this),
            isSmall ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                onClick: onHamburgerClick,
                className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$NavBar$2f$NavBar$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].hamburgerIcon,
                id: "hamburgerIcon",
                style: props.hamburgerIconStyle,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Menu$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    fontSize: "large"
                }, void 0, false, {
                    fileName: "[project]/components/NavBar/NavBar.tsx",
                    lineNumber: 202,
                    columnNumber: 21
                }, this)
            }, void 0, false, {
                fileName: "[project]/components/NavBar/NavBar.tsx",
                lineNumber: 201,
                columnNumber: 17
            }, this) : ''
        ]
    }, void 0, true, {
        fileName: "[project]/components/NavBar/NavBar.tsx",
        lineNumber: 141,
        columnNumber: 9
    }, this);
}
_s(NavBar, "ejfVnX45tW8OIsOm1ZQEJxnncxQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Hooks$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForceUpdate"]
    ];
});
_c = NavBar;
const __TURBOPACK__default__export__ = NavBar;
var _c;
__turbopack_context__.k.register(_c, "NavBar");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/OptionsMenu/OptionsMenu.module.css [app-client] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "buttonsWrapper": "OptionsMenu-module__BxCVkW__buttonsWrapper",
  "dropdown": "OptionsMenu-module__BxCVkW__dropdown",
  "optionsMenu": "OptionsMenu-module__BxCVkW__optionsMenu",
});
}}),
"[project]/components/OptionsMenu/OptionsMenu.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$MoreVert$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/icons-material/esm/MoreVert.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$OptionsMenu$2f$OptionsMenu$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/components/OptionsMenu/OptionsMenu.module.css [app-client] (css module)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Button$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/Button.js [app-client] (ecmascript) <export default as Button>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Dropdown$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Dropdown$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/Dropdown.js [app-client] (ecmascript) <export default as Dropdown>");
'use client';
;
;
;
;
;
const CustomToggle = /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].forwardRef(_c = ({ children, onClick }, ref)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
        ref: ref,
        onClick: (e)=>{
            e.preventDefault();
            onClick(e);
        },
        children: [
            children,
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$MoreVert$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                fileName: "[project]/components/OptionsMenu/OptionsMenu.tsx",
                lineNumber: 24,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/components/OptionsMenu/OptionsMenu.tsx",
        lineNumber: 16,
        columnNumber: 5
    }, this));
_c1 = CustomToggle;
function OptionsMenu(props) {
    let available = [];
    const isItemPage = props.selected?.tag !== undefined;
    const isPlayerPage = !isItemPage;
    if (isItemPage) {
        let fandomName = props.selected?.name;
        let wikiName = props.selected?.name;
        let tag = props.selected.tag;
        if (tag.startsWith('ENCHANTMENT_')) {
            fandomName = tag.replace('ENCHANTMENT_', '').replace('ULTIMATE_', '').replace(/_\d/, '').toLowerCase();
            fandomName = fandomName.split('_').map((part)=>part.charAt(0).toUpperCase() + part.slice(1).toLowerCase()).join('_');
            wikiName = fandomName + '_Enchantment';
        }
        available.push({
            title: 'Fandom',
            url: 'https://hypixel-skyblock.fandom.com/wiki/' + fandomName
        });
        available.push({
            title: 'Wiki',
            url: 'https://wiki.hypixel.net/' + wikiName
        });
        if (props.selected.bazaar) {
            available.push({
                title: 'Skyblock.bz',
                url: 'https://Skyblock.bz/product/' + tag
            });
        }
    } else if (isPlayerPage) {
        let player = props.selected;
        available.push({
            title: 'SkyCrypt',
            url: 'https://sky.shiiyu.moe/stats/' + player?.uuid
        });
        available.push({
            title: 'Plancke',
            url: 'https://plancke.io/hypixel/player/stats/' + player?.uuid
        });
    }
    const navigate = (url)=>{
        window.open(url, '_blank');
    };
    if (!props.selected || props.selected.name === undefined) {
        return null;
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$OptionsMenu$2f$OptionsMenu$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].optionsMenu,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$OptionsMenu$2f$OptionsMenu$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].buttonsWrapper,
                children: available.map((result, i)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                        href: result.url,
                        title: result.title,
                        target: "_blank",
                        rel: "noreferrer",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Button$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__["Button"], {
                            children: result.title
                        }, void 0, false, {
                            fileName: "[project]/components/OptionsMenu/OptionsMenu.tsx",
                            lineNumber: 68,
                            columnNumber: 25
                        }, this)
                    }, i, false, {
                        fileName: "[project]/components/OptionsMenu/OptionsMenu.tsx",
                        lineNumber: 67,
                        columnNumber: 21
                    }, this))
            }, void 0, false, {
                fileName: "[project]/components/OptionsMenu/OptionsMenu.tsx",
                lineNumber: 65,
                columnNumber: 13
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Dropdown$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Dropdown$3e$__["Dropdown"], {
                className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$OptionsMenu$2f$OptionsMenu$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].dropdown,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Dropdown$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Dropdown$3e$__["Dropdown"].Toggle, {
                        as: CustomToggle
                    }, void 0, false, {
                        fileName: "[project]/components/OptionsMenu/OptionsMenu.tsx",
                        lineNumber: 74,
                        columnNumber: 17
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Dropdown$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Dropdown$3e$__["Dropdown"].Menu, {
                        id: "dropdownMenuButton",
                        children: available.map((result, i)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Dropdown$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Dropdown$3e$__["Dropdown"].Item, {
                                onClick: ()=>{
                                    navigate(result.url);
                                },
                                children: result.title
                            }, result.url, false, {
                                fileName: "[project]/components/OptionsMenu/OptionsMenu.tsx",
                                lineNumber: 77,
                                columnNumber: 25
                            }, this))
                    }, void 0, false, {
                        fileName: "[project]/components/OptionsMenu/OptionsMenu.tsx",
                        lineNumber: 75,
                        columnNumber: 17
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/components/OptionsMenu/OptionsMenu.tsx",
                lineNumber: 73,
                columnNumber: 13
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/components/OptionsMenu/OptionsMenu.tsx",
        lineNumber: 64,
        columnNumber: 9
    }, this);
}
_c2 = OptionsMenu;
const __TURBOPACK__default__export__ = OptionsMenu;
var _c, _c1, _c2;
__turbopack_context__.k.register(_c, "CustomToggle$React.forwardRef");
__turbopack_context__.k.register(_c1, "CustomToggle");
__turbopack_context__.k.register(_c2, "OptionsMenu");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/Search/Search.module.css [app-client] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "bar": "Search-module__Lg4wHG__bar",
  "current": "Search-module__Lg4wHG__current",
  "multiInputfield": "Search-module__Lg4wHG__multiInputfield",
  "multiSearch": "Search-module__Lg4wHG__multiSearch",
  "previousSearch": "Search-module__Lg4wHG__previousSearch",
  "search": "Search-module__Lg4wHG__search",
  "searchFormGroup": "Search-module__Lg4wHG__searchFormGroup",
  "searchResultIcon": "Search-module__Lg4wHG__searchResultIcon",
});
}}),
"[project]/utils/PreviousSearchUtils.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "addClickedSearchResultToPreviousSearches": (()=>addClickedSearchResultToPreviousSearches),
    "addPreviousSearchResultsToDisplay": (()=>addPreviousSearchResultsToDisplay),
    "getFirstPreviousSearches": (()=>getFirstPreviousSearches),
    "pinSearchResult": (()=>pinSearchResult),
    "unpinSearchResult": (()=>unpinSearchResult)
});
const PREVIOUS_SEARCHES_KEY = 'lastSearches';
const MAX_PREVIOUS_SEARCHES_TO_DISPLAY = 3;
const MAX_PREVIOUS_SEARCHES_TO_STORE = 100;
function addClickedSearchResultToPreviousSearches(item, previousSearchKey) {
    let previousSearches = getPreviousSearchesFromLocalStorage(previousSearchKey);
    let alreadyFoundIndex = previousSearches.findIndex((r)=>r.id === item.id);
    if (alreadyFoundIndex !== -1) {
        previousSearches.splice(alreadyFoundIndex, 1);
    }
    previousSearches.push(item);
    if (previousSearches.length > MAX_PREVIOUS_SEARCHES_TO_STORE) {
        previousSearches.shift();
    }
    localStorage.setItem(PREVIOUS_SEARCHES_KEY, JSON.stringify(previousSearches));
}
function pinSearchResult(item, previousSearchKey) {
    let previousSearches = getPreviousSearchesFromLocalStorage(previousSearchKey);
    let alreadyFoundIndex = previousSearches.findIndex((r)=>r.id === item.id);
    if (alreadyFoundIndex !== -1) {
        previousSearches[alreadyFoundIndex].pinned = true;
    } else {
        previousSearches.push({
            ...item,
            pinned: true
        });
    }
    localStorage.setItem(PREVIOUS_SEARCHES_KEY, JSON.stringify(previousSearches));
}
function unpinSearchResult(item, previousSearchKey) {
    let previousSearches = getPreviousSearchesFromLocalStorage(previousSearchKey);
    let index = previousSearches.findIndex((r)=>r.id === item.id);
    if (index !== -1) {
        previousSearches[index].pinned = false;
    } else {
        // item to remove was not found
        return;
    }
    localStorage.setItem(PREVIOUS_SEARCHES_KEY, JSON.stringify(previousSearches));
}
function addPreviousSearchResultsToDisplay(searchText, searchResults, previousSearchKey) {
    let newSearchResults = [
        ...searchResults
    ];
    let previousSearches = getPreviousSearchesFromLocalStorage(previousSearchKey);
    let matches = [];
    let matchingPreviousSearchesInResuls = 0;
    previousSearches.forEach((prevSearch)=>{
        let alreadyFoundIndex = newSearchResults.findIndex((r)=>r.id === prevSearch.id);
        if (alreadyFoundIndex !== -1) {
            newSearchResults[alreadyFoundIndex] = prevSearch;
            matchingPreviousSearchesInResuls++;
        } else if (prevSearch.dataItem.name.toLowerCase().indexOf(searchText.toLowerCase()) !== -1) {
            matches.unshift(prevSearch);
        }
    });
    let pinnedSort = (a, b)=>{
        if (a.pinned) {
            return -1;
        }
        if (b.pinned) {
            return 1;
        }
        return 0;
    };
    newSearchResults.sort(pinnedSort);
    matches.sort(pinnedSort).slice(0, MAX_PREVIOUS_SEARCHES_TO_DISPLAY - matchingPreviousSearchesInResuls);
    if (MAX_PREVIOUS_SEARCHES_TO_DISPLAY <= matchingPreviousSearchesInResuls) {
        return newSearchResults;
    }
    return [
        ...matches,
        ...newSearchResults
    ];
}
function getFirstPreviousSearches(amount, previousSearchKey) {
    let previousSearches = getPreviousSearchesFromLocalStorage(previousSearchKey);
    return previousSearches.sort((a, b)=>{
        if (a.pinned) {
            return 1;
        }
        if (b.pinned) {
            return -1;
        }
        return 0;
    }).slice(-amount).reverse();
}
function getPreviousSearchesFromLocalStorage(keyForPinnedItems) {
    let previousSearches = localStorage.getItem(PREVIOUS_SEARCHES_KEY) ? JSON.parse(localStorage.getItem(PREVIOUS_SEARCHES_KEY)) : [];
    return previousSearches.filter((prevSearch)=>prevSearch.previousSearchKey === keyForPinnedItems).map((prevSearch)=>{
        prevSearch.isPreviousSearch = true;
        return prevSearch;
    });
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/ClientOnly/ClientOnly.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>ClientOnly)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature();
;
function ClientOnly({ children }) {
    _s();
    const [mounted, setMounted] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ClientOnly.useEffect": ()=>{
            setMounted(true);
        }
    }["ClientOnly.useEffect"], []);
    return mounted ? children : null;
}
_s(ClientOnly, "LrrVfNW3d1raFE0BNzCTILYmIfo=");
_c = ClientOnly;
var _c;
__turbopack_context__.k.register(_c, "ClientOnly");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/Search/Search.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/api/ApiHelper.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/Form.js [app-client] (ecmascript) <export default as Form>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$InputGroup$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__InputGroup$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/InputGroup.js [app-client] (ecmascript) <export default as InputGroup>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$ListGroup$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ListGroup$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/ListGroup.js [app-client] (ecmascript) <export default as ListGroup>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Spinner$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Spinner$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/Spinner.js [app-client] (ecmascript) <export default as Spinner>");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Formatter$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/Formatter.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$NavBar$2f$NavBar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/NavBar/NavBar.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$OptionsMenu$2f$OptionsMenu$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/OptionsMenu/OptionsMenu.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$SearchOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/icons-material/esm/SearchOutlined.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Dangerous$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/icons-material/esm/Dangerous.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Refresh$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/icons-material/esm/Refresh.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Clear$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/icons-material/esm/Clear.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$contexify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-contexify/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-toastify/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SSRUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/SSRUtils.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Search$2f$Search$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/components/Search/Search.module.css [app-client] (css module)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Hooks$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/Hooks.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$PushPin$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/icons-material/esm/PushPin.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$PreviousSearchUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/PreviousSearchUtils.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SettingsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/SettingsUtils.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ClientOnly$2f$ClientOnly$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ClientOnly/ClientOnly.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const PLAYER_SEARCH_CONEXT_MENU_ID = 'player-search-context-menu';
const SEARCH_RESULT_CONTEXT_MENU_ID = 'search-result-context-menu';
function Search(props) {
    _s();
    let router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    let [searchText, setSearchText] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    let [results, setResults] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    let [isSearching, setIsSearching] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    let [noResultsFound, setNoResultsFound] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    let [isSmall, setIsSmall] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    let [selectedIndex, setSelectedIndex] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(0);
    const { show: showPlayerContextMenu } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$contexify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContextMenu"])({
        id: PLAYER_SEARCH_CONEXT_MENU_ID
    });
    const { show: showSearchItemContextMenu, hideAll: hideSearchItemContextMenu } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$contexify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContextMenu"])({
        id: SEARCH_RESULT_CONTEXT_MENU_ID
    });
    const isMobile = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Hooks$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useIsMobile"])();
    let rememberEnterPressRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(false);
    let searchElement = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    let forceUpdate = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Hooks$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForceUpdate"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "Search.useEffect": ()=>{
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SSRUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isClientSideRendering"])()) {
                setIsSmall((0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SSRUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isClientSideRendering"])() ? document.body.clientWidth < 1500 : false);
            }
            document.addEventListener('click', outsideClickHandler, true);
            return ({
                "Search.useEffect": ()=>{
                    document.removeEventListener('click', outsideClickHandler, true);
                }
            })["Search.useEffect"];
        }
    }["Search.useEffect"], []);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "Search.useEffect": ()=>{
            setSearchText('');
            setResults([]);
        }
    }["Search.useEffect"], [
        props.selected
    ]);
    let search = ()=>{
        let searchFor = searchText;
        let searchFunction = props.searchFunction || __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].search;
        searchFunction(searchFor).then((searchResults)=>{
            // has the searchtext changed?
            if (searchElement.current !== null && searchFor === searchElement.current.querySelector('#search-bar').value) {
                let searchResultsToShow = [
                    ...searchResults
                ];
                if (!props.preventDisplayOfPreviousSearches) {
                    searchResultsToShow = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$PreviousSearchUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["addPreviousSearchResultsToDisplay"])(searchFor, searchResults, props.keyForPinnedItems);
                }
                setSelectedIndex(0);
                setNoResultsFound(searchResultsToShow.length === 0);
                setResults(searchResultsToShow);
                setIsSearching(false);
                if (rememberEnterPressRef.current) {
                    onItemClick(searchResultsToShow[0]);
                    rememberEnterPressRef.current = false;
                }
            }
        });
    };
    let onSearchChange = (e)=>{
        let newSearchText = e.target.value;
        searchText = newSearchText;
        setSearchText(newSearchText);
        setIsSearching(true);
        if (newSearchText === '') {
            setResults([]);
            setIsSearching(false);
            return;
        }
        setNoResultsFound(false);
        search();
    };
    function outsideClickHandler(evt) {
        const flyoutEl = searchElement.current;
        let targetEl = evt.target;
        do {
            if (targetEl === flyoutEl) {
                return;
            }
            targetEl = targetEl.parentNode;
        }while (targetEl)
        setResults([]);
    }
    let onKeyPress = (e)=>{
        switch(e.key){
            case 'Enter':
                e.preventDefault();
                if (isSearching) {
                    rememberEnterPressRef.current = true;
                    return;
                }
                if (!results || results.length === 0) {
                    return;
                }
                onItemClick(results[selectedIndex]);
                break;
            case 'ArrowDown':
                if (selectedIndex < results.length - 1) {
                    setSelectedIndex(selectedIndex + 1);
                }
                break;
            case 'ArrowUp':
                if (selectedIndex > 0) {
                    setSelectedIndex(selectedIndex - 1);
                }
                break;
        }
    };
    let onItemClick = (item)=>{
        if (props.onSearchresultClick) {
            props.onSearchresultClick(item);
            return;
        }
        if (item.urlSearchParams && new URLSearchParams(window.location.search).toString() !== item.urlSearchParams.toString()) {
            setSearchText('');
            setResults([]);
        }
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$PreviousSearchUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["addClickedSearchResultToPreviousSearches"])(item, props.keyForPinnedItems);
        __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].trackSearch(item.id, item.type);
        let searchParams = new URLSearchParams();
        let itemFilter = item.urlSearchParams?.get('itemFilter');
        let apply = item.urlSearchParams?.get('apply');
        if (itemFilter) {
            searchParams.set('itemFilter', itemFilter);
        }
        if (apply) {
            searchParams.set('apply', apply);
        }
        router.push(`${item.route}?${searchParams.toString()}`);
    };
    let noResultsFoundElement = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$ListGroup$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ListGroup$3e$__["ListGroup"].Item, {
        style: getListItemStyle(-1),
        onContextMenu: (e)=>{
            handleSearchContextMenuForCurrentElement(e);
        },
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Search$2f$Search$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].searchResultIcon,
                height: 32,
                width: 32,
                src: "/Barrier.png",
                alt: ""
            }, void 0, false, {
                fileName: "[project]/components/Search/Search.tsx",
                lineNumber: 205,
                columnNumber: 13
            }, this),
            "No search results"
        ]
    }, -1, true, {
        fileName: "[project]/components/Search/Search.tsx",
        lineNumber: 198,
        columnNumber: 9
    }, this);
    let getSelectedElement = ()=>{
        if (props.currentElement) {
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                onContextMenu: (e)=>handleSearchContextMenuForCurrentElement(e),
                className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Search$2f$Search$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].current,
                children: props.currentElement
            }, void 0, false, {
                fileName: "[project]/components/Search/Search.tsx",
                lineNumber: 213,
                columnNumber: 17
            }, this);
        }
        if (!props.selected) {
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {}, void 0, false, {
                fileName: "[project]/components/Search/Search.tsx",
                lineNumber: 219,
                columnNumber: 20
            }, this);
        }
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
            onContextMenu: (e)=>handleSearchContextMenuForCurrentElement(e),
            className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Search$2f$Search$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].current,
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ClientOnly$2f$ClientOnly$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                        crossOrigin: "anonymous",
                        className: "playerHeadIcon",
                        src: props.type === 'player' ? props.selected.iconUrl : __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getItemImageUrl({
                            tag: props.selected.tag
                        }),
                        height: "32",
                        width: "32",
                        alt: "",
                        style: {
                            marginRight: '10px',
                            cursor: 'pointer'
                        },
                        onClick: ()=>{
                            let type = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SettingsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getSetting"])(__TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SettingsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ITEM_ICON_TYPE"], 'default');
                            if (type === 'default') {
                                (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SettingsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setSetting"])(__TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SettingsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ITEM_ICON_TYPE"], 'vanilla');
                            } else {
                                (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SettingsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setSetting"])(__TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SettingsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ITEM_ICON_TYPE"], 'default');
                            }
                            window.location.reload();
                        }
                    }, void 0, false, {
                        fileName: "[project]/components/Search/Search.tsx",
                        lineNumber: 224,
                        columnNumber: 21
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/components/Search/Search.tsx",
                    lineNumber: 223,
                    columnNumber: 17
                }, this),
                props.selected.name || (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Formatter$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["convertTagToName"])(props.selected.tag),
                props.enableReset ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Clear$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    onClick: props.onResetClick,
                    style: {
                        cursor: 'pointer',
                        color: 'red',
                        marginLeft: '10px',
                        fontWeight: 'bold'
                    }
                }, void 0, false, {
                    fileName: "[project]/components/Search/Search.tsx",
                    lineNumber: 245,
                    columnNumber: 21
                }, this) : null
            ]
        }, void 0, true, {
            fileName: "[project]/components/Search/Search.tsx",
            lineNumber: 222,
            columnNumber: 13
        }, this);
    };
    let searchStyle = {
        backgroundColor: props.backgroundColor,
        borderRadius: results.length > 0 || noResultsFound ? '0px 10px 0 0' : '0px 10px 10px 0px',
        borderLeftWidth: 0,
        borderBottomColor: results.length > 0 || noResultsFound ? '#444' : undefined
    };
    let searchIconStyle = {
        width: isSmall ? 'auto' : '58px',
        borderRadius: results.length > 0 || noResultsFound ? '10px 0 0 0' : '10px 0px 0px 10px',
        backgroundColor: props.backgroundColor || '#303030',
        borderBottomColor: results.length > 0 || noResultsFound ? '#444' : undefined,
        padding: isSmall ? '0px' : undefined
    };
    function getListItemStyle(i) {
        let style = {
            backgroundColor: i === selectedIndex ? props.backgroundColorSelected || '#444' : props.backgroundColor,
            borderRadius: i === results.length - 1 ? '0 0 10px 10px' : '',
            border: 0,
            borderTop: i === 0 ? '1px solid #444' : 0,
            borderTopWidth: i === 0 ? 0 : undefined,
            fontWeigth: 'normal',
            fontFamily: 'inherit'
        };
        if (results[i]) {
            let isDuplicate = results.findIndex((element, index)=>element.dataItem.name === results[i].dataItem.name && index !== i) !== -1;
            if (isDuplicate) {
                return {
                    ...(0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Formatter$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getStyleForTier"])(results[i]?.tier),
                    ...style
                };
            }
        }
        return style;
    }
    function checkNameChange(uuid) {
        __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].triggerPlayerNameCheck(uuid).then(()=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success('A name check for the player was triggered. This may take a few minutes.');
        });
    }
    function handleSearchContextMenuForCurrentElement(event) {
        if (props.selected && props.type === 'player') {
            event.preventDefault();
            showPlayerContextMenu({
                event: event
            });
        }
    }
    function handleSearchContextMenuForSearchResult(event, searchResultItem) {
        event.preventDefault();
        showSearchItemContextMenu({
            event: event,
            props: {
                item: searchResultItem
            }
        });
    }
    let currentItemContextMenuElement = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$contexify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Menu"], {
            id: PLAYER_SEARCH_CONEXT_MENU_ID,
            theme: 'dark',
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$contexify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Item"], {
                onClick: (params)=>{
                    checkNameChange(props.selected.uuid);
                },
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Refresh$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                        style: {
                            marginRight: '5px'
                        }
                    }, void 0, false, {
                        fileName: "[project]/components/Search/Search.tsx",
                        lineNumber: 314,
                        columnNumber: 21
                    }, this),
                    "Trigger check if name has changed"
                ]
            }, void 0, true, {
                fileName: "[project]/components/Search/Search.tsx",
                lineNumber: 309,
                columnNumber: 17
            }, this)
        }, void 0, false, {
            fileName: "[project]/components/Search/Search.tsx",
            lineNumber: 308,
            columnNumber: 13
        }, this)
    }, void 0, false, {
        fileName: "[project]/components/Search/Search.tsx",
        lineNumber: 307,
        columnNumber: 9
    }, this);
    let searchItemContextMenuElement = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$contexify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Menu"], {
            id: SEARCH_RESULT_CONTEXT_MENU_ID,
            theme: 'dark',
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$contexify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Item"], {
                    onClick: (params)=>{
                        let item = params.props.item;
                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$PreviousSearchUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["pinSearchResult"])(item, props.keyForPinnedItems);
                        let index = results.findIndex((r)=>r.dataItem.name === item.dataItem.name);
                        if (index !== -1) {
                            let newResults = [
                                ...results
                            ];
                            newResults[index].pinned = true;
                            newResults[index].isPreviousSearch = true;
                            setResults(newResults);
                        }
                        hideSearchItemContextMenu();
                    },
                    hidden: (params)=>!!params.props.item.pinned,
                    closeOnClick: true,
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$PushPin$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                            fileName: "[project]/components/Search/Search.tsx",
                            lineNumber: 340,
                            columnNumber: 21
                        }, this),
                        "Pin search result"
                    ]
                }, void 0, true, {
                    fileName: "[project]/components/Search/Search.tsx",
                    lineNumber: 324,
                    columnNumber: 17
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$contexify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Item"], {
                    onClick: (params)=>{
                        let item = params.props.item;
                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$PreviousSearchUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unpinSearchResult"])(item, props.keyForPinnedItems);
                        let index = results.findIndex((r)=>r.dataItem.name === item.dataItem.name);
                        if (index !== -1) {
                            let newResults = [
                                ...results
                            ];
                            newResults[index].pinned = false;
                            setResults(newResults);
                        }
                        hideSearchItemContextMenu();
                    },
                    hidden: (params)=>!params.props.item.pinned,
                    closeOnClick: true,
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$PushPin$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                            fileName: "[project]/components/Search/Search.tsx",
                            lineNumber: 358,
                            columnNumber: 21
                        }, this),
                        "Unpin search result"
                    ]
                }, void 0, true, {
                    fileName: "[project]/components/Search/Search.tsx",
                    lineNumber: 343,
                    columnNumber: 17
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$contexify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Separator"], {}, void 0, false, {
                    fileName: "[project]/components/Search/Search.tsx",
                    lineNumber: 361,
                    columnNumber: 17
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$contexify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Item"], {
                    onClick: ()=>{
                        __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].sendFeedback('badSearchResults', {
                            searchText: searchText,
                            results: results
                        });
                    },
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Dangerous$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                            style: {
                                color: 'red',
                                marginRight: '5px'
                            }
                        }, void 0, false, {
                            fileName: "[project]/components/Search/Search.tsx",
                            lineNumber: 370,
                            columnNumber: 21
                        }, this),
                        "I didn't find the thing I was looking for!"
                    ]
                }, void 0, true, {
                    fileName: "[project]/components/Search/Search.tsx",
                    lineNumber: 362,
                    columnNumber: 17
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/components/Search/Search.tsx",
            lineNumber: 323,
            columnNumber: 13
        }, this)
    }, void 0, false, {
        fileName: "[project]/components/Search/Search.tsx",
        lineNumber: 322,
        columnNumber: 9
    }, this);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        ref: searchElement,
        className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Search$2f$Search$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].search,
        style: isSmall ? {
            marginLeft: '-5px',
            marginRight: '-5px'
        } : {},
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__["Form"], {
                autoComplete: "off",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__["Form"].Group, {
                    className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Search$2f$Search$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].searchFormGroup,
                    children: [
                        !isSmall && !props.hideNavbar ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$NavBar$2f$NavBar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                            fileName: "[project]/components/Search/Search.tsx",
                            lineNumber: 380,
                            columnNumber: 54
                        }, this) : '',
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$InputGroup$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__InputGroup$3e$__["InputGroup"], {
                            id: "search-input-group",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$InputGroup$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__InputGroup$3e$__["InputGroup"].Text, {
                                    style: searchIconStyle,
                                    children: isSmall && !props.hideNavbar ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        style: {
                                            width: '56px'
                                        },
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$NavBar$2f$NavBar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            hamburgerIconStyle: {
                                                marginRight: '0px',
                                                width: '56px'
                                            }
                                        }, void 0, false, {
                                            fileName: "[project]/components/Search/Search.tsx",
                                            lineNumber: 385,
                                            columnNumber: 37
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/components/Search/Search.tsx",
                                        lineNumber: 384,
                                        columnNumber: 33
                                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$SearchOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                        fileName: "[project]/components/Search/Search.tsx",
                                        lineNumber: 388,
                                        columnNumber: 33
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/components/Search/Search.tsx",
                                    lineNumber: 382,
                                    columnNumber: 25
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__["Form"].Control, {
                                    autoFocus: !isMobile,
                                    style: searchStyle,
                                    type: "text",
                                    placeholder: props.placeholder || 'Search player/item',
                                    id: 'search-bar',
                                    className: "searchBar",
                                    value: searchText,
                                    onChange: onSearchChange,
                                    onKeyDown: (e)=>{
                                        onKeyPress(e);
                                    },
                                    onClick: ()=>{
                                        if (!props.preventDisplayOfPreviousSearches && !noResultsFound && results.length === 0 && !searchText) {
                                            let previousResuls = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$PreviousSearchUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getFirstPreviousSearches"])(5, props.keyForPinnedItems);
                                            setResults(previousResuls);
                                        }
                                    }
                                }, "search", false, {
                                    fileName: "[project]/components/Search/Search.tsx",
                                    lineNumber: 391,
                                    columnNumber: 25
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/Search/Search.tsx",
                            lineNumber: 381,
                            columnNumber: 21
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/components/Search/Search.tsx",
                    lineNumber: 379,
                    columnNumber: 17
                }, this)
            }, void 0, false, {
                fileName: "[project]/components/Search/Search.tsx",
                lineNumber: 378,
                columnNumber: 13
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$ListGroup$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ListGroup$3e$__["ListGroup"], {
                className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Search$2f$Search$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].searchResutList,
                children: noResultsFound ? noResultsFoundElement : results.map((result, i)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$ListGroup$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ListGroup$3e$__["ListGroup"].Item, {
                        action: true,
                        onClick: (e)=>{
                            onItemClick(result);
                        },
                        style: getListItemStyle(i),
                        className: result.isPreviousSearch ? __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Search$2f$Search$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].previousSearch : undefined,
                        onContextMenu: (event)=>{
                            handleSearchContextMenuForSearchResult(event, result);
                        },
                        children: [
                            result.dataItem.iconUrl ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                className: `${__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Search$2f$Search$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].searchResultIcon} playerHeadIcon`,
                                crossOrigin: "anonymous",
                                width: 32,
                                height: 32,
                                src: result.dataItem._imageLoaded ? result.dataItem.iconUrl : 'data:image/gif;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mPMqAcAAVUA6UpAAT4AAAAASUVORK5CYII=',
                                alt: "",
                                onLoad: ()=>{
                                    result.dataItem._imageLoaded = true;
                                    setResults(results);
                                    forceUpdate();
                                }
                            }, void 0, false, {
                                fileName: "[project]/components/Search/Search.tsx",
                                lineNumber: 431,
                                columnNumber: 35
                            }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Spinner$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Spinner$3e$__["Spinner"], {
                                animation: "border",
                                role: "status",
                                variant: "primary"
                            }, void 0, false, {
                                fileName: "[project]/components/Search/Search.tsx",
                                lineNumber: 449,
                                columnNumber: 35
                            }, this),
                            result.pinned ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$PushPin$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                style: {
                                    marginRight: '5px'
                                }
                            }, void 0, false, {
                                fileName: "[project]/components/Search/Search.tsx",
                                lineNumber: 451,
                                columnNumber: 48
                            }, this) : null,
                            result.dataItem.name
                        ]
                    }, result.id, true, {
                        fileName: "[project]/components/Search/Search.tsx",
                        lineNumber: 418,
                        columnNumber: 27
                    }, this))
            }, void 0, false, {
                fileName: "[project]/components/Search/Search.tsx",
                lineNumber: 414,
                columnNumber: 13
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Search$2f$Search$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].bar,
                style: {
                    marginTop: '20px'
                },
                children: [
                    getSelectedElement(),
                    !props.hideOptions ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$OptionsMenu$2f$OptionsMenu$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                        selected: props.selected
                    }, void 0, false, {
                        fileName: "[project]/components/Search/Search.tsx",
                        lineNumber: 458,
                        columnNumber: 39
                    }, this) : null
                ]
            }, void 0, true, {
                fileName: "[project]/components/Search/Search.tsx",
                lineNumber: 456,
                columnNumber: 13
            }, this),
            searchItemContextMenuElement,
            currentItemContextMenuElement
        ]
    }, void 0, true, {
        fileName: "[project]/components/Search/Search.tsx",
        lineNumber: 377,
        columnNumber: 9
    }, this);
}
_s(Search, "f0dAAbFethNW0o55OKrvEUlhCPo=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$contexify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContextMenu"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$contexify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContextMenu"],
        __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Hooks$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useIsMobile"],
        __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Hooks$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForceUpdate"]
    ];
});
_c = Search;
const __TURBOPACK__default__export__ = Search;
var _c;
__turbopack_context__.k.register(_c, "Search");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=_13472528._.js.map