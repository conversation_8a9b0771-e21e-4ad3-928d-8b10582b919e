/* [project]/components/CoflCoins/CoflCoinsPurchase.module.css [app-client] (css) */
.CoflCoinsPurchase-module__25Dhdq__productGrid {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-direction: initial;
  flex-wrap: wrap;
}

.CoflCoinsPurchase-module__25Dhdq__premiumPlanCard {
  width: 49%;
  margin-bottom: 25px;
}

@media (width <= 768px) {
  .CoflCoinsPurchase-module__25Dhdq__premiumPlanCard {
    width: 100%;
  }
}

.CoflCoinsPurchase-module__25Dhdq__premiumPrice {
  font-size: larger;
}

.CoflCoinsPurchase-module__25Dhdq__paymentOption {
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  padding-bottom: 1rem;
}

.CoflCoinsPurchase-module__25Dhdq__discount {
  color: #0f0;
  float: right;
}

.CoflCoinsPurchase-module__25Dhdq__manualRedirectLink {
  margin: 0;
}

.CoflCoinsPurchase-module__25Dhdq__paymentButtonWrapper {
  width: 40%;
}

.CoflCoinsPurchase-module__25Dhdq__paymentButton {
  width: 100%;
}

.CoflCoinsPurchase-module__25Dhdq__paymentLabel {
  width: 50%;
}

/*# sourceMappingURL=components_CoflCoins_CoflCoinsPurchase_module_b52d8e88.css.map*/