/* [project]/components/FlipTracking/FlipTracking.module.css [app-client] (css) */
.FlipTracking-module__IBfYNG__list {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  flex-direction: initial;
  width: 100%;
}

.FlipTracking-module__IBfYNG__listGroupItem {
  width: 100%;
  margin-bottom: 15px;
  border-radius: 20px !important;
}

.FlipTracking-module__IBfYNG__noAuctionFound {
  margin-right: auto;
  margin-left: auto;
  width: max-content;
  text-align: center;
  margin-top: 5vh;
}

.FlipTracking-module__IBfYNG__profitNumberCard {
  width: 45%;
  text-align: center;
  cursor: pointer;
  border-color: #0000 !important;
}

.FlipTracking-module__IBfYNG__profitNumberHeader {
  border-radius: 20px !important;
}

.FlipTracking-module__IBfYNG__profitNumberHeader:hover {
  background-color: #3a3a3a;
}

@media (width >= 1200px) {
  .FlipTracking-module__IBfYNG__listGroupItem {
    width: 49%;
  }
}

.FlipTracking-module__IBfYNG__datePicker {
  margin-right: 15px;
}

.FlipTracking-module__IBfYNG__filterContainer {
  display: flex;
  align-items: center;
}

.FlipTracking-module__IBfYNG__rangeFilter {
  width: 200px;
}

.FlipTracking-module__IBfYNG__itemFilterContainer:has(.show.dropdown-menu) {
  z-index: 12;
  position: relative;
  height: 38px;
}

.FlipTracking-module__IBfYNG__multiSearch {
  flex: 1;
}

.FlipTracking-module__IBfYNG__noPremiumInfoText {
  font-size: small;
}

.FlipTracking-module__IBfYNG__topContainer {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.FlipTracking-module__IBfYNG__filterLabel {
  min-width: 100px;
}

.FlipTracking-module__IBfYNG__filterValueField {
  max-width: 200px;
}

@media (width <= 768px) {
  .FlipTracking-module__IBfYNG__noPremiumInfoText {
    float: none;
    text-align: center;
  }

  .FlipTracking-module__IBfYNG__topContainer {
    display: flex;
    flex-direction: column-reverse;
    margin-bottom: 20px;
    gap: 10px;
  }
}

/*# sourceMappingURL=components_FlipTracking_FlipTracking_module_b52d8e88.css.map*/