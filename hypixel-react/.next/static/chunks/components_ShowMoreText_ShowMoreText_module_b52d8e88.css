/* [project]/components/ShowMoreText/ShowMoreText.module.css [app-client] (css) */
.ShowMoreText-module__4MH6ba__textContainer {
  position: relative;
  overflow: hidden;
}

.ShowMoreText-module__4MH6ba__textContainer:after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 30px;
  background: linear-gradient(#0000, #252525);
}

.ShowMoreText-module__4MH6ba__textContainer.ShowMoreText-module__4MH6ba__expanded {
  max-height: none;
  overflow: visible;
}

.ShowMoreText-module__4MH6ba__textContainer.ShowMoreText-module__4MH6ba__expanded:after {
  display: none;
}

.ShowMoreText-module__4MH6ba__showMoreContainer {
  width: 100%;
  position: absolute;
  bottom: -4px;
  cursor: pointer;
  z-index: 2;
}

.ShowMoreText-module__4MH6ba__showMoreText {
  display: flex;
  justify-content: center;
  color: #d3d3d3;
}

/*# sourceMappingURL=components_ShowMoreText_ShowMoreText_module_b52d8e88.css.map*/