{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/components/ShowMoreText/ShowMoreText.module.css"], "sourcesContent": [".textContainer {\n    position: relative;\n    overflow: hidden;\n}\n  \n.textContainer::after {\n    content: '';\n    position: absolute;\n    bottom: 0;\n    left: 0;\n    width: 100%;\n    height: 30px;\n    background: linear-gradient(to bottom, transparent, #252525);\n}\n\n.textContainer.expanded {\n    max-height: none;\n    overflow: visible;\n}\n\n.textContainer.expanded::after {\n    display: none;\n}\n\n.showMoreContainer {\n    width: 100%;\n    position: absolute;\n    bottom: -4px;\n    cursor: pointer;\n    z-index: 2;\n}\n\n.showMoreText {\n    display: flex;\n    justify-content: center;\n    color: lightgrey;\n}"], "names": [], "mappings": "AAAA;;;;;AAKA;;;;;;;;;;AAUA;;;;;AAKA;;;;AAIA;;;;;;;;AAQA"}}]}