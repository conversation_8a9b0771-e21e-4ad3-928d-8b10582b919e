/* [project]/components/Premium/BuyPremium/BuyPremium.module.css [app-client] (css) */
.BuyPremium-module__gXX4Mq__label {
  width: 200px;
  float: left;
  margin: 0;
}

.BuyPremium-module__gXX4Mq__coinBalance {
  float: right;
}

.BuyPremium-module__gXX4Mq__dropdown {
  display: inline;
  width: 110px;
}

.BuyPremium-module__gXX4Mq__dropdown::-webkit-scrollbar {
  background-color: #272b30;
}

.BuyPremium-module__gXX4Mq__dropdown::-webkit-scrollbar {
  width: 20px;
}

.BuyPremium-module__gXX4Mq__dropdown::-webkit-scrollbar-track {
  box-shadow: inset 0 0 5px gray;
  border-radius: 10px;
}

.BuyPremium-module__gXX4Mq__dropdown::-webkit-scrollbar-thumb {
  background: #3a3f44;
  border-radius: 10px;
}

.BuyPremium-module__gXX4Mq__dropdown::-webkit-scrollbar-thumb:hover {
  background: #7a8288;
}

@media (width <= 768px) {
  .BuyPremium-module__gXX4Mq__label {
    width: auto;
    margin-right: 15px;
  }

  .BuyPremium-module__gXX4Mq__coinBalance {
    float: none;
    margin-top: 20px;
  }

  .BuyPremium-module__gXX4Mq__coinBalance b {
    font-size: medium !important;
  }
}

@media (width <= 350px) {
  .BuyPremium-module__gXX4Mq__label {
    width: 100%;
  }
}

.BuyPremium-module__gXX4Mq__purchaseCard :not(.btn-check:checked) + .BuyPremium-module__gXX4Mq__priceRangeButton {
  background-color: #2a3644;
  border-color: #2a3644;
  filter: grayscale(70%);
  font-weight: bold;
  color: #d3d3d3;
}

.BuyPremium-module__gXX4Mq__purchaseCard .btn-check:checked + .BuyPremium-module__gXX4Mq__priceRangeButton {
  border-color: #fff;
  font-weight: bold;
}

/*# sourceMappingURL=components_Premium_BuyPremium_BuyPremium_module_b52d8e88.css.map*/