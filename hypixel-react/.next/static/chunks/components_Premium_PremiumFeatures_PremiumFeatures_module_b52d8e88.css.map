{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/components/Premium/PremiumFeatures/PremiumFeatures.module.css"], "sourcesContent": [".premiumFeatures .featureCard {\n    margin-bottom: 20px;\n}\n\n.featureColumn {\n    font-size: larger;\n    text-align: left;\n}\n\n.featureColumnHeading {\n    text-align: left;\n}\n\n.premiumFeatures {\n    overflow-x: auto;\n}\n\n.premiumProductHeading {\n    text-align: center;\n}\n\n.premiumProductHeading {\n    text-align: center;\n}\n\n.premiumProductColumn {\n    text-align: center;\n}\n\n#tooltipHoverId :global(.tooltip-inner){\n    max-width: 100%;\n}\n\n.ingamePriceHoverImage {\n    width: 610px;\n    height: 324px;\n}\n\n@media all and (max-width: 992px) {\n    .ingamePriceHoverImage {\n        width: 305px;\n        height: 162px;\n    }\n}"], "names": [], "mappings": "AAAA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;EACI"}}]}