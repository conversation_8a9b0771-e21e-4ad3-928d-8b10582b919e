{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/Number/Number.tsx"], "sourcesContent": ["'use client'\nimport React, { useEffect, useState } from 'react'\nimport { numberWithThousandsSeparators } from '../../utils/Formatter'\n\ninterface Props {\n    number: number | string\n}\n\nexport default function NumberElement(props: Props) {\n    let [isSSR, setIsSSR] = useState(true)\n\n    let value = Number(props.number)\n\n    useEffect(() => {\n        setIsSSR(false)\n    }, [])\n\n    // Use consistent formatting to prevent hydration mismatches\n    // Always use comma as thousand separator and period as decimal separator\n    return <span suppressHydrationWarning>{numberWithThousandsSeparators(value, ',', '.')}</span>\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;;;AAFA;;;AAQe,SAAS,cAAc,KAAY;;IAC9C,IAAI,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjC,IAAI,QAAQ,OAAO,MAAM,MAAM;IAE/B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACN,SAAS;QACb;kCAAG,EAAE;IAEL,4DAA4D;IAC5D,yEAAyE;IACzE,qBAAO,6LAAC;QAAK,wBAAwB;kBAAE,CAAA,GAAA,sHAAA,CAAA,gCAA6B,AAAD,EAAE,OAAO,KAAK;;;;;;AACrF;GAZwB;KAAA", "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/components/Startpage/Startpage.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"SSRcardsWrapper\": \"Startpage-module__7qS_na__SSRcardsWrapper\",\n  \"cardWrapper\": \"Startpage-module__7qS_na__cardWrapper\",\n  \"cardsWrapper\": \"Startpage-module__7qS_na__cardsWrapper\",\n  \"changelogItem\": \"Startpage-module__7qS_na__changelogItem\",\n  \"ellipsis\": \"Startpage-module__7qS_na__ellipsis\",\n  \"joinNote\": \"Startpage-module__7qS_na__joinNote\",\n  \"startpageCard\": \"Startpage-module__7qS_na__startpageCard\",\n  \"startpageListElementWrapper\": \"Startpage-module__7qS_na__startpageListElementWrapper\",\n  \"statusTitle\": \"Startpage-module__7qS_na__statusTitle\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 67, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/Startpage/StartpageLargeElementSkeleton.tsx"], "sourcesContent": ["import { CSSProperties } from 'react'\nimport styles from './Startpage.module.css'\nimport { Card } from 'react-bootstrap'\nimport Skeleton from 'react-loading-skeleton'\n\ninterface Props {\n    style: CSSProperties\n}\n\nexport function StartpageLargeElementSkeleton(props: Props) {\n    return (\n        <div className={`${styles.cardWrapper}`} style={props.style}>\n            <Card>\n                <Card.Header style={{ padding: '10px' }}>\n                    <div style={{ display: 'flex', alignItems: 'center', height: 32, marginBottom: '1rem' }}>\n                        <Skeleton circle height={32} width={32} style={{ marginRight: '5px' }} baseColor=\"#333\" enableAnimation={false} />\n                        <Skeleton width={'130px'} baseColor=\"#333\" enableAnimation={false} />\n                    </div>\n                </Card.Header>\n                <Card.Body>\n                    <ul>\n                        <li>\n                            <Skeleton baseColor=\"#444\" enableAnimation={false} />\n                            <Skeleton baseColor=\"#444\" enableAnimation={false} width={60} />\n                        </li>\n                        <li>\n                            <Skeleton baseColor=\"#444\" enableAnimation={false} />\n                        </li>\n                        <li>\n                            <Skeleton baseColor=\"#444\" enableAnimation={false} width={40} />\n                        </li>\n                    </ul>\n                </Card.Body>\n            </Card>\n        </div>\n    )\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;;;AAMO,SAAS,8BAA8B,KAAY;IACtD,qBACI,6LAAC;QAAI,WAAW,GAAG,mJAAA,CAAA,UAAM,CAAC,WAAW,EAAE;QAAE,OAAO,MAAM,KAAK;kBACvD,cAAA,6LAAC,uLAAA,CAAA,OAAI;;8BACD,6LAAC,uLAAA,CAAA,OAAI,CAAC,MAAM;oBAAC,OAAO;wBAAE,SAAS;oBAAO;8BAClC,cAAA,6LAAC;wBAAI,OAAO;4BAAE,SAAS;4BAAQ,YAAY;4BAAU,QAAQ;4BAAI,cAAc;wBAAO;;0CAClF,6LAAC,gKAAA,CAAA,UAAQ;gCAAC,MAAM;gCAAC,QAAQ;gCAAI,OAAO;gCAAI,OAAO;oCAAE,aAAa;gCAAM;gCAAG,WAAU;gCAAO,iBAAiB;;;;;;0CACzG,6LAAC,gKAAA,CAAA,UAAQ;gCAAC,OAAO;gCAAS,WAAU;gCAAO,iBAAiB;;;;;;;;;;;;;;;;;8BAGpE,6LAAC,uLAAA,CAAA,OAAI,CAAC,IAAI;8BACN,cAAA,6LAAC;;0CACG,6LAAC;;kDACG,6LAAC,gKAAA,CAAA,UAAQ;wCAAC,WAAU;wCAAO,iBAAiB;;;;;;kDAC5C,6LAAC,gKAAA,CAAA,UAAQ;wCAAC,WAAU;wCAAO,iBAAiB;wCAAO,OAAO;;;;;;;;;;;;0CAE9D,6LAAC;0CACG,cAAA,6LAAC,gKAAA,CAAA,UAAQ;oCAAC,WAAU;oCAAO,iBAAiB;;;;;;;;;;;0CAEhD,6LAAC;0CACG,cAAA,6LAAC,gKAAA,CAAA,UAAQ;oCAAC,WAAU;oCAAO,iBAAiB;oCAAO,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOtF;KA3BgB", "debugId": null}}, {"offset": {"line": 222, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/Startpage/Startpage.tsx"], "sourcesContent": ["'use client'\n\nimport { useMatomo } from '@jonkoops/matomo-tracker-react'\nimport NewIcon from '@mui/icons-material/FiberNew'\nimport FireIcon from '@mui/icons-material/Fireplace'\nimport PersonIcon from '@mui/icons-material/Person'\nimport TimerIcon from '@mui/icons-material/Timer'\nimport moment from 'moment'\nimport Image from 'next/image'\nimport Link from 'next/link'\nimport React, { useEffect, useState } from 'react'\nimport Card from 'react-bootstrap/Card'\nimport Badge from 'react-bootstrap/Badge'\nimport { FixedSizeList as List } from 'react-window'\nimport api from '../../api/ApiHelper'\nimport { getMinecraftColorCodedElement } from '../../utils/Formatter'\nimport Number from '../Number/Number'\nimport styles from './Startpage.module.css'\nimport { StartpageLargeElementSkeleton } from './StartpageLargeElementSkeleton'\n\ninterface Props {\n    newAuctions?: Auction[]\n    popularSearches?: PopularSearch[]\n    newPlayers?: Player[]\n    newItems?: Item[]\n}\n\nfunction Startpage(props: Props) {\n    let { trackEvent } = useMatomo()\n\n    let [newAuctions, setNewAuctions] = useState<Auction[]>(props.newAuctions || [])\n    let [endedAuctions, setEndedAuctions] = useState<Auction[]>([])\n    let [popularSearches, setPopularSearches] = useState<PopularSearch[]>(props.popularSearches || [])\n    let [newPlayers, setNewPlayers] = useState<Player[]>(props.newPlayers || [])\n    let [newItems, setNewItems] = useState<Item[]>(props.newItems || [])\n    let [isSSR, setIsSSR] = useState(true)\n\n    useEffect(() => {\n        setIsSSR(false)\n        setTimeout(() => {\n            attachScrollEvent(styles.startpageListElementWrapper)\n        }, 500)\n        loadEndedAuctions()\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [])\n\n    function loadEndedAuctions() {\n        api.getEndedAuctions().then(endedAuctions => {\n            setEndedAuctions(endedAuctions)\n        })\n    }\n\n    function getEndString(end: Date) {\n        let momentDate = moment(end)\n        return end.getTime() < new Date().getTime() ? 'Ended ' + momentDate.fromNow() : 'Ends ' + momentDate.fromNow()\n    }\n\n    function getAuctionElement(auction: Auction, style: React.CSSProperties) {\n        return (\n            <div className={`${styles.cardWrapper}`} key={auction.uuid} style={style}>\n                <Link href={`/auction/${auction.uuid}`} className=\"disableLinkStyle\">\n                    <Card>\n                        <Card.Header style={{ padding: '10px' }}>\n                            <p className={styles.ellipsis}>\n                                <Image\n                                    crossOrigin=\"anonymous\"\n                                    src={api.getItemImageUrl(auction.item) || ''}\n                                    height=\"32\"\n                                    width=\"32\"\n                                    alt=\"\"\n                                    style={{ marginRight: '5px' }}\n                                />\n                                {getMinecraftColorCodedElement(auction.item.name)}\n                            </p>\n                        </Card.Header>\n                        <Card.Body>\n                            <div>\n                                <ul>\n                                    <li>{getEndString(auction.end)}</li>\n                                    <li>\n                                        <Number number={auction.highestBid || auction.startingBid} /> Coins\n                                    </li>\n                                    {auction.bin ? (\n                                        <li>\n                                            <Badge style={{ marginLeft: '5px' }} bg=\"success\">\n                                                BIN\n                                            </Badge>\n                                        </li>\n                                    ) : (\n                                        ''\n                                    )}\n                                </ul>\n                            </div>\n                        </Card.Body>\n                    </Card>\n                </Link>\n            </div>\n        )\n    }\n\n    function getNewPlayerElement(newPlayer: Player, style: React.CSSProperties) {\n        return (\n            <div className={`${styles.cardWrapper} ${styles.disableLinkStyle}`} key={newPlayer.name} style={style}>\n                <Link href={`/player/${newPlayer.uuid}`} className=\"disableLinkStyle\">\n                    <Card>\n                        <Card.Header style={{ height: '100%', padding: '20px' }}>\n                            <div style={{ float: 'left' }}>\n                                <Image\n                                    crossOrigin=\"anonymous\"\n                                    className=\"playerHeadIcon\"\n                                    src={newPlayer.iconUrl || ''}\n                                    height=\"32\"\n                                    width=\"32\"\n                                    alt=\"\"\n                                    style={{ marginRight: '5px' }}\n                                    loading=\"lazy\"\n                                />\n                            </div>\n                            <Card.Title className={styles.ellipsis}>{newPlayer.name}</Card.Title>\n                        </Card.Header>\n                    </Card>\n                </Link>\n            </div>\n        )\n    }\n\n    function getPopularSearchElement(search: PopularSearch, style: React.CSSProperties) {\n        return (\n            <div className={`${styles.cardWrapper} ${styles.disableLinkStyle}`} key={search.url} style={style}>\n                <Link href={search.url} className=\"disableLinkStyle\">\n                    <Card>\n                        <Card.Header style={{ height: '100%' }}>\n                            <div style={{ float: 'left' }}>\n                                <Image\n                                    crossOrigin=\"anonymous\"\n                                    className=\"playerHeadIcon\"\n                                    src={search.url.includes('/player') ? search.img + '?size=8' : search.img}\n                                    height=\"32\"\n                                    width=\"32\"\n                                    alt=\"\"\n                                    style={{ marginRight: '5px' }}\n                                    loading=\"lazy\"\n                                />\n                            </div>\n                            <Card.Title className={styles.ellipsis}>{search.title}</Card.Title>\n                        </Card.Header>\n                    </Card>\n                </Link>\n            </div>\n        )\n    }\n\n    function getNewItemElement(newItem: Item, style: React.CSSProperties) {\n        return (\n            <div className={`${styles.cardWrapper} ${styles.disableLinkStyle}`} key={newItem.tag} style={style}>\n                <Link href={`/item/${newItem.tag}`} className=\"disableLinkStyle\">\n                    <Card>\n                        <Card.Header style={{ height: '100%', padding: '20px' }}>\n                            <div style={{ float: 'left' }}>\n                                <Image\n                                    crossOrigin=\"anonymous\"\n                                    src={newItem.iconUrl || ''}\n                                    height=\"32\"\n                                    width=\"32\"\n                                    alt=\"\"\n                                    style={{ marginRight: '5px' }}\n                                    loading=\"lazy\"\n                                />\n                            </div>\n                            <Card.Title className={styles.ellipsis}>{newItem.name}</Card.Title>\n                        </Card.Header>\n                    </Card>\n                </Link>\n            </div>\n        )\n    }\n\n    function onRecentChangesClick() {\n        trackEvent({\n            category: 'recentChanges',\n            action: 'recentChangesClicked'\n        })\n    }\n\n    function attachScrollEvent(className: string) {\n        let scrollContainers = document.getElementsByClassName(className)\n        for (var i = 0; i < scrollContainers.length; i++) {\n            let container = scrollContainers.item(i)\n            if (container) {\n                container.addEventListener('wheel', evt => {\n                    evt.preventDefault()\n                    let scrollAmount = 0\n                    var slideTimer = setInterval(() => {\n                        container!.scrollLeft += (evt as WheelEvent).deltaY / 10\n                        scrollAmount += Math.abs((evt as WheelEvent).deltaY) / 10\n                        if (scrollAmount >= Math.abs((evt as WheelEvent).deltaY)) {\n                            clearInterval(slideTimer)\n                        }\n                    }, 25)\n                })\n            }\n        }\n    }\n\n    let newAuctionsElement = (\n        <div id=\"new-auctions-element\" className={`${styles.cardsWrapper} ${styles.newAuctions}`}>\n            <List\n                className={styles.startpageListElementWrapper}\n                height={260 - 15}\n                itemCount={newAuctions.length}\n                itemSize={200}\n                layout=\"horizontal\"\n                width={isSSR ? 10000 : document.getElementById('new-auctions-element')?.offsetWidth}\n            >\n                {({ index, style }) => {\n                    return getAuctionElement(newAuctions[index], style)\n                }}\n            </List>\n        </div>\n    )\n\n    let popularSearchesElement = (\n        <div className={`${styles.cardsWrapper} ${styles.popularSearches}`}>\n            <List\n                className={styles.startpageListElementWrapper}\n                height={130 - 15}\n                itemCount={popularSearches.length}\n                itemSize={200}\n                layout=\"horizontal\"\n                width={isSSR ? 10000 : document.getElementById('new-auctions-element')?.offsetWidth}\n            >\n                {({ index, style }) => {\n                    return getPopularSearchElement(popularSearches[index], style)\n                }}\n            </List>\n        </div>\n    )\n\n    let endedAuctionsElement = (\n        <div className={`${styles.cardsWrapper} ${styles.endedAuctions}`}>\n            <List\n                className={styles.startpageListElementWrapper}\n                height={260 - 15}\n                itemCount={endedAuctions.length === 0 ? 20 : endedAuctions.length}\n                itemSize={200}\n                layout=\"horizontal\"\n                width={isSSR ? 10000 : document.getElementById('new-auctions-element')?.offsetWidth}\n            >\n                {({ index, style }) => {\n                    return endedAuctions.length === 0 ? <StartpageLargeElementSkeleton style={style} /> : getAuctionElement(endedAuctions[index], style)\n                }}\n            </List>\n        </div>\n    )\n\n    let newPlayersElement = (\n        <div className={`${styles.cardsWrapper} ${styles.newPlayers}`}>\n            <List\n                className={styles.startpageListElementWrapper}\n                height={130 - 15}\n                itemCount={newPlayers.length}\n                itemSize={200}\n                layout=\"horizontal\"\n                width={isSSR ? 10000 : document.getElementById('new-auctions-element')?.offsetWidth}\n            >\n                {({ index, style }) => {\n                    return getNewPlayerElement(newPlayers[index], style)\n                }}\n            </List>\n        </div>\n    )\n    let newItemsElement = (\n        <div className={`${styles.cardsWrapper} ${styles.newItems}`}>\n            <List\n                className={styles.startpageListElementWrapper}\n                height={130 - 15}\n                itemCount={newItems.length}\n                itemSize={200}\n                layout=\"horizontal\"\n                width={isSSR ? 10000 : document.getElementById('new-auctions-element')?.offsetWidth}\n            >\n                {({ index, style }) => {\n                    return getNewItemElement(newItems[index], style)\n                }}\n            </List>\n        </div>\n    )\n\n    return (\n        <div>\n            <div style={{ textAlign: 'center' }}>\n                <hr />\n                <h1>Skyblock Auction House History</h1>\n                <p style={{ fontSize: 'larger' }}>Browse through over 600 million auctions, over two million players and the bazaar of hypixel skyblock</p>\n                <hr />\n            </div>\n            <Card className={styles.startpageCard}>\n                <Card.Header>\n                    <Card.Title>\n                        <NewIcon /> New Auctions\n                    </Card.Title>\n                </Card.Header>\n                <Card.Body className={styles.startpageCardBody} id=\"new-auctions-body\">\n                    {newAuctionsElement}\n                </Card.Body>\n            </Card>\n\n            <Card className={styles.startpageCard}>\n                <Card.Header>\n                    <Card.Title>\n                        <TimerIcon /> Ended Auctions\n                    </Card.Title>\n                </Card.Header>\n                <Card.Body className={styles.startpageCardBody} id=\"ended-auctions-body\">\n                    {endedAuctionsElement}\n                </Card.Body>\n            </Card>\n\n            <Card className={styles.startpageCard}>\n                <Card.Header>\n                    <Card.Title>\n                        <PersonIcon /> New Players\n                    </Card.Title>\n                </Card.Header>\n                <Card.Body className={styles.startpageCardBody} id=\"new-players-body\">\n                    {newPlayersElement}\n                </Card.Body>\n            </Card>\n\n            <Card className={styles.startpageCard}>\n                <Card.Header>\n                    <Card.Title>\n                        <FireIcon /> Popular Searches\n                    </Card.Title>\n                </Card.Header>\n                <Card.Body className={styles.startpageCardBody} id=\"popular-searches-body\">\n                    {popularSearchesElement}\n                </Card.Body>\n            </Card>\n\n            <Card className={styles.startpageCard}>\n                <Card.Header>\n                    <Card.Title>\n                        <NewIcon /> New Items\n                    </Card.Title>\n                </Card.Header>\n                <Card.Body className={styles.startpageCardBody} id=\"new-items-body\">\n                    {newItemsElement}\n                </Card.Body>\n            </Card>\n\n            <Card className={styles.startpageCard} style={{ marginTop: '40px' }}>\n                <Card.Header>\n                    <Card.Title>Hypixel Auction House History</Card.Title>\n                </Card.Header>\n                <Card.Body>\n                    <p>View, search, browse, and filter by reforge or enchantment.</p>\n                    <p>You can find all current and historic prices for the auction house and bazaar on this web tracker.</p>\n                    <p>\n                        We're tracking over 600 million auctions. We've saved more than 350 million bazaar prices in intervals of 10 seconds. Furthermore, there\n                        are over three million skyblock players that you can search by their Minecraft usernames. You can browse through the auctions they made\n                        over the past two years. New items are added automatically and are available within two minutes after the first auction is started.\n                    </p>\n                    <p>\n                        The search autocomplete is ranked by popularity and allows you to find whatever item you want faster. Quick URLs allow you to link to\n                        specific sites. /p/Steve or /i/Oak allows you to create a link without visiting the site first.\n                    </p>\n                    <p>\n                        The free accessible{' '}\n                        <Link href=\"/flipper\" style={{ backgroundColor: 'white', textDecoration: 'none', color: 'black', borderRadius: '3px' }}>\n                            auction house flipper ↗️\n                        </Link>{' '}\n                        allows you to find profitable AH flips in no time. It supplements the option to browse all of the Skyblock history on the web tracker.\n                        What's more is that you can see what auctions were used as reference to determine if a flip is profitable.\n                    </p>\n                    <p>\n                        We allow you to subscribe to auctions, item prices and being outbid with more to come. Please use the contact on the{' '}\n                        <Link href=\"/feedback\" style={{ backgroundColor: 'white', textDecoration: 'none', color: 'black', borderRadius: '3px' }}>\n                            Feedback site ↗️\n                        </Link>{' '}\n                        to send us suggestions or bug reports.\n                    </p>\n                </Card.Body>\n            </Card>\n        </div>\n    )\n}\n\nexport default Startpage\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAlBA;;;;;;;;;;;;;;;;;;AA2BA,SAAS,UAAU,KAAY;;IAC3B,IAAI,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,sNAAA,CAAA,YAAS,AAAD;IAE7B,IAAI,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,MAAM,WAAW,IAAI,EAAE;IAC/E,IAAI,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IAC9D,IAAI,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB,MAAM,eAAe,IAAI,EAAE;IACjG,IAAI,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,MAAM,UAAU,IAAI,EAAE;IAC3E,IAAI,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,MAAM,QAAQ,IAAI,EAAE;IACnE,IAAI,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACN,SAAS;YACT;uCAAW;oBACP,kBAAkB,mJAAA,CAAA,UAAM,CAAC,2BAA2B;gBACxD;sCAAG;YACH;QACA,uDAAuD;QAC3D;8BAAG,EAAE;IAEL,SAAS;QACL,oHAAA,CAAA,UAAG,CAAC,gBAAgB,GAAG,IAAI,CAAC,CAAA;YACxB,iBAAiB;QACrB;IACJ;IAEA,SAAS,aAAa,GAAS;QAC3B,IAAI,aAAa,CAAA,GAAA,mIAAA,CAAA,UAAM,AAAD,EAAE;QACxB,OAAO,IAAI,OAAO,KAAK,IAAI,OAAO,OAAO,KAAK,WAAW,WAAW,OAAO,KAAK,UAAU,WAAW,OAAO;IAChH;IAEA,SAAS,kBAAkB,OAAgB,EAAE,KAA0B;QACnE,qBACI,6LAAC;YAAI,WAAW,GAAG,mJAAA,CAAA,UAAM,CAAC,WAAW,EAAE;YAAqB,OAAO;sBAC/D,cAAA,6LAAC,+JAAA,CAAA,UAAI;gBAAC,MAAM,CAAC,SAAS,EAAE,QAAQ,IAAI,EAAE;gBAAE,WAAU;0BAC9C,cAAA,6LAAC,oJAAA,CAAA,UAAI;;sCACD,6LAAC,oJAAA,CAAA,UAAI,CAAC,MAAM;4BAAC,OAAO;gCAAE,SAAS;4BAAO;sCAClC,cAAA,6LAAC;gCAAE,WAAW,mJAAA,CAAA,UAAM,CAAC,QAAQ;;kDACzB,6LAAC,gIAAA,CAAA,UAAK;wCACF,aAAY;wCACZ,KAAK,oHAAA,CAAA,UAAG,CAAC,eAAe,CAAC,QAAQ,IAAI,KAAK;wCAC1C,QAAO;wCACP,OAAM;wCACN,KAAI;wCACJ,OAAO;4CAAE,aAAa;wCAAM;;;;;;oCAE/B,CAAA,GAAA,sHAAA,CAAA,gCAA6B,AAAD,EAAE,QAAQ,IAAI,CAAC,IAAI;;;;;;;;;;;;sCAGxD,6LAAC,oJAAA,CAAA,UAAI,CAAC,IAAI;sCACN,cAAA,6LAAC;0CACG,cAAA,6LAAC;;sDACG,6LAAC;sDAAI,aAAa,QAAQ,GAAG;;;;;;sDAC7B,6LAAC;;8DACG,6LAAC,kIAAA,CAAA,UAAM;oDAAC,QAAQ,QAAQ,UAAU,IAAI,QAAQ,WAAW;;;;;;gDAAI;;;;;;;wCAEhE,QAAQ,GAAG,iBACR,6LAAC;sDACG,cAAA,6LAAC,qJAAA,CAAA,UAAK;gDAAC,OAAO;oDAAE,YAAY;gDAAM;gDAAG,IAAG;0DAAU;;;;;;;;;;mDAKtD;;;;;;;;;;;;;;;;;;;;;;;;;;;;WA9BkB,QAAQ,IAAI;;;;;IAuClE;IAEA,SAAS,oBAAoB,SAAiB,EAAE,KAA0B;QACtE,qBACI,6LAAC;YAAI,WAAW,GAAG,mJAAA,CAAA,UAAM,CAAC,WAAW,CAAC,CAAC,EAAE,mJAAA,CAAA,UAAM,CAAC,gBAAgB,EAAE;YAAuB,OAAO;sBAC5F,cAAA,6LAAC,+JAAA,CAAA,UAAI;gBAAC,MAAM,CAAC,QAAQ,EAAE,UAAU,IAAI,EAAE;gBAAE,WAAU;0BAC/C,cAAA,6LAAC,oJAAA,CAAA,UAAI;8BACD,cAAA,6LAAC,oJAAA,CAAA,UAAI,CAAC,MAAM;wBAAC,OAAO;4BAAE,QAAQ;4BAAQ,SAAS;wBAAO;;0CAClD,6LAAC;gCAAI,OAAO;oCAAE,OAAO;gCAAO;0CACxB,cAAA,6LAAC,gIAAA,CAAA,UAAK;oCACF,aAAY;oCACZ,WAAU;oCACV,KAAK,UAAU,OAAO,IAAI;oCAC1B,QAAO;oCACP,OAAM;oCACN,KAAI;oCACJ,OAAO;wCAAE,aAAa;oCAAM;oCAC5B,SAAQ;;;;;;;;;;;0CAGhB,6LAAC,oJAAA,CAAA,UAAI,CAAC,KAAK;gCAAC,WAAW,mJAAA,CAAA,UAAM,CAAC,QAAQ;0CAAG,UAAU,IAAI;;;;;;;;;;;;;;;;;;;;;;WAhBE,UAAU,IAAI;;;;;IAsB/F;IAEA,SAAS,wBAAwB,MAAqB,EAAE,KAA0B;QAC9E,qBACI,6LAAC;YAAI,WAAW,GAAG,mJAAA,CAAA,UAAM,CAAC,WAAW,CAAC,CAAC,EAAE,mJAAA,CAAA,UAAM,CAAC,gBAAgB,EAAE;YAAmB,OAAO;sBACxF,cAAA,6LAAC,+JAAA,CAAA,UAAI;gBAAC,MAAM,OAAO,GAAG;gBAAE,WAAU;0BAC9B,cAAA,6LAAC,oJAAA,CAAA,UAAI;8BACD,cAAA,6LAAC,oJAAA,CAAA,UAAI,CAAC,MAAM;wBAAC,OAAO;4BAAE,QAAQ;wBAAO;;0CACjC,6LAAC;gCAAI,OAAO;oCAAE,OAAO;gCAAO;0CACxB,cAAA,6LAAC,gIAAA,CAAA,UAAK;oCACF,aAAY;oCACZ,WAAU;oCACV,KAAK,OAAO,GAAG,CAAC,QAAQ,CAAC,aAAa,OAAO,GAAG,GAAG,YAAY,OAAO,GAAG;oCACzE,QAAO;oCACP,OAAM;oCACN,KAAI;oCACJ,OAAO;wCAAE,aAAa;oCAAM;oCAC5B,SAAQ;;;;;;;;;;;0CAGhB,6LAAC,oJAAA,CAAA,UAAI,CAAC,KAAK;gCAAC,WAAW,mJAAA,CAAA,UAAM,CAAC,QAAQ;0CAAG,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;;WAhBI,OAAO,GAAG;;;;;IAsB3F;IAEA,SAAS,kBAAkB,OAAa,EAAE,KAA0B;QAChE,qBACI,6LAAC;YAAI,WAAW,GAAG,mJAAA,CAAA,UAAM,CAAC,WAAW,CAAC,CAAC,EAAE,mJAAA,CAAA,UAAM,CAAC,gBAAgB,EAAE;YAAoB,OAAO;sBACzF,cAAA,6LAAC,+JAAA,CAAA,UAAI;gBAAC,MAAM,CAAC,MAAM,EAAE,QAAQ,GAAG,EAAE;gBAAE,WAAU;0BAC1C,cAAA,6LAAC,oJAAA,CAAA,UAAI;8BACD,cAAA,6LAAC,oJAAA,CAAA,UAAI,CAAC,MAAM;wBAAC,OAAO;4BAAE,QAAQ;4BAAQ,SAAS;wBAAO;;0CAClD,6LAAC;gCAAI,OAAO;oCAAE,OAAO;gCAAO;0CACxB,cAAA,6LAAC,gIAAA,CAAA,UAAK;oCACF,aAAY;oCACZ,KAAK,QAAQ,OAAO,IAAI;oCACxB,QAAO;oCACP,OAAM;oCACN,KAAI;oCACJ,OAAO;wCAAE,aAAa;oCAAM;oCAC5B,SAAQ;;;;;;;;;;;0CAGhB,6LAAC,oJAAA,CAAA,UAAI,CAAC,KAAK;gCAAC,WAAW,mJAAA,CAAA,UAAM,CAAC,QAAQ;0CAAG,QAAQ,IAAI;;;;;;;;;;;;;;;;;;;;;;WAfI,QAAQ,GAAG;;;;;IAqB5F;IAEA,SAAS;QACL,WAAW;YACP,UAAU;YACV,QAAQ;QACZ;IACJ;IAEA,SAAS,kBAAkB,SAAiB;QACxC,IAAI,mBAAmB,SAAS,sBAAsB,CAAC;QACvD,IAAK,IAAI,IAAI,GAAG,IAAI,iBAAiB,MAAM,EAAE,IAAK;YAC9C,IAAI,YAAY,iBAAiB,IAAI,CAAC;YACtC,IAAI,WAAW;gBACX,UAAU,gBAAgB,CAAC,SAAS,CAAA;oBAChC,IAAI,cAAc;oBAClB,IAAI,eAAe;oBACnB,IAAI,aAAa,YAAY;wBACzB,UAAW,UAAU,IAAI,AAAC,IAAmB,MAAM,GAAG;wBACtD,gBAAgB,KAAK,GAAG,CAAC,AAAC,IAAmB,MAAM,IAAI;wBACvD,IAAI,gBAAgB,KAAK,GAAG,CAAC,AAAC,IAAmB,MAAM,GAAG;4BACtD,cAAc;wBAClB;oBACJ,GAAG;gBACP;YACJ;QACJ;IACJ;IAEA,IAAI,mCACA,6LAAC;QAAI,IAAG;QAAuB,WAAW,GAAG,mJAAA,CAAA,UAAM,CAAC,YAAY,CAAC,CAAC,EAAE,mJAAA,CAAA,UAAM,CAAC,WAAW,EAAE;kBACpF,cAAA,6LAAC,0JAAA,CAAA,gBAAI;YACD,WAAW,mJAAA,CAAA,UAAM,CAAC,2BAA2B;YAC7C,QAAQ,MAAM;YACd,WAAW,YAAY,MAAM;YAC7B,UAAU;YACV,QAAO;YACP,OAAO,QAAQ,QAAQ,SAAS,cAAc,CAAC,yBAAyB;sBAEvE,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE;gBACd,OAAO,kBAAkB,WAAW,CAAC,MAAM,EAAE;YACjD;;;;;;;;;;;IAKZ,IAAI,uCACA,6LAAC;QAAI,WAAW,GAAG,mJAAA,CAAA,UAAM,CAAC,YAAY,CAAC,CAAC,EAAE,mJAAA,CAAA,UAAM,CAAC,eAAe,EAAE;kBAC9D,cAAA,6LAAC,0JAAA,CAAA,gBAAI;YACD,WAAW,mJAAA,CAAA,UAAM,CAAC,2BAA2B;YAC7C,QAAQ,MAAM;YACd,WAAW,gBAAgB,MAAM;YACjC,UAAU;YACV,QAAO;YACP,OAAO,QAAQ,QAAQ,SAAS,cAAc,CAAC,yBAAyB;sBAEvE,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE;gBACd,OAAO,wBAAwB,eAAe,CAAC,MAAM,EAAE;YAC3D;;;;;;;;;;;IAKZ,IAAI,qCACA,6LAAC;QAAI,WAAW,GAAG,mJAAA,CAAA,UAAM,CAAC,YAAY,CAAC,CAAC,EAAE,mJAAA,CAAA,UAAM,CAAC,aAAa,EAAE;kBAC5D,cAAA,6LAAC,0JAAA,CAAA,gBAAI;YACD,WAAW,mJAAA,CAAA,UAAM,CAAC,2BAA2B;YAC7C,QAAQ,MAAM;YACd,WAAW,cAAc,MAAM,KAAK,IAAI,KAAK,cAAc,MAAM;YACjE,UAAU;YACV,QAAO;YACP,OAAO,QAAQ,QAAQ,SAAS,cAAc,CAAC,yBAAyB;sBAEvE,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE;gBACd,OAAO,cAAc,MAAM,KAAK,kBAAI,6LAAC,4JAAA,CAAA,gCAA6B;oBAAC,OAAO;;;;;2BAAY,kBAAkB,aAAa,CAAC,MAAM,EAAE;YAClI;;;;;;;;;;;IAKZ,IAAI,kCACA,6LAAC;QAAI,WAAW,GAAG,mJAAA,CAAA,UAAM,CAAC,YAAY,CAAC,CAAC,EAAE,mJAAA,CAAA,UAAM,CAAC,UAAU,EAAE;kBACzD,cAAA,6LAAC,0JAAA,CAAA,gBAAI;YACD,WAAW,mJAAA,CAAA,UAAM,CAAC,2BAA2B;YAC7C,QAAQ,MAAM;YACd,WAAW,WAAW,MAAM;YAC5B,UAAU;YACV,QAAO;YACP,OAAO,QAAQ,QAAQ,SAAS,cAAc,CAAC,yBAAyB;sBAEvE,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE;gBACd,OAAO,oBAAoB,UAAU,CAAC,MAAM,EAAE;YAClD;;;;;;;;;;;IAIZ,IAAI,gCACA,6LAAC;QAAI,WAAW,GAAG,mJAAA,CAAA,UAAM,CAAC,YAAY,CAAC,CAAC,EAAE,mJAAA,CAAA,UAAM,CAAC,QAAQ,EAAE;kBACvD,cAAA,6LAAC,0JAAA,CAAA,gBAAI;YACD,WAAW,mJAAA,CAAA,UAAM,CAAC,2BAA2B;YAC7C,QAAQ,MAAM;YACd,WAAW,SAAS,MAAM;YAC1B,UAAU;YACV,QAAO;YACP,OAAO,QAAQ,QAAQ,SAAS,cAAc,CAAC,yBAAyB;sBAEvE,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE;gBACd,OAAO,kBAAkB,QAAQ,CAAC,MAAM,EAAE;YAC9C;;;;;;;;;;;IAKZ,qBACI,6LAAC;;0BACG,6LAAC;gBAAI,OAAO;oBAAE,WAAW;gBAAS;;kCAC9B,6LAAC;;;;;kCACD,6LAAC;kCAAG;;;;;;kCACJ,6LAAC;wBAAE,OAAO;4BAAE,UAAU;wBAAS;kCAAG;;;;;;kCAClC,6LAAC;;;;;;;;;;;0BAEL,6LAAC,oJAAA,CAAA,UAAI;gBAAC,WAAW,mJAAA,CAAA,UAAM,CAAC,aAAa;;kCACjC,6LAAC,oJAAA,CAAA,UAAI,CAAC,MAAM;kCACR,cAAA,6LAAC,oJAAA,CAAA,UAAI,CAAC,KAAK;;8CACP,6LAAC,gKAAA,CAAA,UAAO;;;;;gCAAG;;;;;;;;;;;;kCAGnB,6LAAC,oJAAA,CAAA,UAAI,CAAC,IAAI;wBAAC,WAAW,mJAAA,CAAA,UAAM,CAAC,iBAAiB;wBAAE,IAAG;kCAC9C;;;;;;;;;;;;0BAIT,6LAAC,oJAAA,CAAA,UAAI;gBAAC,WAAW,mJAAA,CAAA,UAAM,CAAC,aAAa;;kCACjC,6LAAC,oJAAA,CAAA,UAAI,CAAC,MAAM;kCACR,cAAA,6LAAC,oJAAA,CAAA,UAAI,CAAC,KAAK;;8CACP,6LAAC,6JAAA,CAAA,UAAS;;;;;gCAAG;;;;;;;;;;;;kCAGrB,6LAAC,oJAAA,CAAA,UAAI,CAAC,IAAI;wBAAC,WAAW,mJAAA,CAAA,UAAM,CAAC,iBAAiB;wBAAE,IAAG;kCAC9C;;;;;;;;;;;;0BAIT,6LAAC,oJAAA,CAAA,UAAI;gBAAC,WAAW,mJAAA,CAAA,UAAM,CAAC,aAAa;;kCACjC,6LAAC,oJAAA,CAAA,UAAI,CAAC,MAAM;kCACR,cAAA,6LAAC,oJAAA,CAAA,UAAI,CAAC,KAAK;;8CACP,6LAAC,8JAAA,CAAA,UAAU;;;;;gCAAG;;;;;;;;;;;;kCAGtB,6LAAC,oJAAA,CAAA,UAAI,CAAC,IAAI;wBAAC,WAAW,mJAAA,CAAA,UAAM,CAAC,iBAAiB;wBAAE,IAAG;kCAC9C;;;;;;;;;;;;0BAIT,6LAAC,oJAAA,CAAA,UAAI;gBAAC,WAAW,mJAAA,CAAA,UAAM,CAAC,aAAa;;kCACjC,6LAAC,oJAAA,CAAA,UAAI,CAAC,MAAM;kCACR,cAAA,6LAAC,oJAAA,CAAA,UAAI,CAAC,KAAK;;8CACP,6LAAC,iKAAA,CAAA,UAAQ;;;;;gCAAG;;;;;;;;;;;;kCAGpB,6LAAC,oJAAA,CAAA,UAAI,CAAC,IAAI;wBAAC,WAAW,mJAAA,CAAA,UAAM,CAAC,iBAAiB;wBAAE,IAAG;kCAC9C;;;;;;;;;;;;0BAIT,6LAAC,oJAAA,CAAA,UAAI;gBAAC,WAAW,mJAAA,CAAA,UAAM,CAAC,aAAa;;kCACjC,6LAAC,oJAAA,CAAA,UAAI,CAAC,MAAM;kCACR,cAAA,6LAAC,oJAAA,CAAA,UAAI,CAAC,KAAK;;8CACP,6LAAC,gKAAA,CAAA,UAAO;;;;;gCAAG;;;;;;;;;;;;kCAGnB,6LAAC,oJAAA,CAAA,UAAI,CAAC,IAAI;wBAAC,WAAW,mJAAA,CAAA,UAAM,CAAC,iBAAiB;wBAAE,IAAG;kCAC9C;;;;;;;;;;;;0BAIT,6LAAC,oJAAA,CAAA,UAAI;gBAAC,WAAW,mJAAA,CAAA,UAAM,CAAC,aAAa;gBAAE,OAAO;oBAAE,WAAW;gBAAO;;kCAC9D,6LAAC,oJAAA,CAAA,UAAI,CAAC,MAAM;kCACR,cAAA,6LAAC,oJAAA,CAAA,UAAI,CAAC,KAAK;sCAAC;;;;;;;;;;;kCAEhB,6LAAC,oJAAA,CAAA,UAAI,CAAC,IAAI;;0CACN,6LAAC;0CAAE;;;;;;0CACH,6LAAC;0CAAE;;;;;;0CACH,6LAAC;0CAAE;;;;;;0CAKH,6LAAC;0CAAE;;;;;;0CAIH,6LAAC;;oCAAE;oCACqB;kDACpB,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,OAAO;4CAAE,iBAAiB;4CAAS,gBAAgB;4CAAQ,OAAO;4CAAS,cAAc;wCAAM;kDAAG;;;;;;oCAEhH;oCAAI;;;;;;;0CAIhB,6LAAC;;oCAAE;oCACsH;kDACrH,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAY,OAAO;4CAAE,iBAAiB;4CAAS,gBAAgB;4CAAQ,OAAO;4CAAS,cAAc;wCAAM;kDAAG;;;;;;oCAEjH;oCAAI;;;;;;;;;;;;;;;;;;;;;;;;;AAOpC;GAvWS;;QACgB,sNAAA,CAAA,YAAS;;;KADzB;uCAyWM", "debugId": null}}, {"offset": {"line": 1127, "column": 0}, "map": {"version": 3, "sources": ["file:///app/utils/Hooks.tsx"], "sourcesContent": ["import { Dispatch, SetStateAction, useCallback, useEffect, useRef, useState } from 'react'\nimport api from '../api/ApiHelper'\nimport { CUSTOM_EVENTS } from '../api/ApiTypes.d'\nimport { getCurrentCoflCoins, subscribeToCoflcoinChange } from './CoflCoinsUtils'\nimport { isClientSideRendering } from './SSRUtils'\nimport { getURLSearchParam } from './Parser/URLParser'\nimport { useRouter } from 'next/navigation'\n\nexport function useForceUpdate() {\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    const [update, setUpdate] = useState(0)\n    return () => setUpdate(update => update + 1)\n}\n\nexport function useSwipe(onSwipeUp?: Function, onSwipeRight?: Function, onSwipeDown?: Function, onSwipeLeft?: Function) {\n    if (!isClientSideRendering()) {\n        return\n    }\n\n    document.addEventListener('touchstart', handleTouchStart, false)\n    document.addEventListener('touchmove', handleTouchMove, false)\n\n    var xDown = null\n    var yDown = null\n\n    function getTouches(evt) {\n        return (\n            evt.touches || // browser API\n            evt.originalEvent.touches\n        ) // jQuery\n    }\n\n    function handleTouchStart(evt) {\n        const firstTouch = getTouches(evt)[0]\n        xDown = firstTouch.clientX\n        yDown = firstTouch.clientY\n    }\n\n    function handleTouchMove(evt) {\n        if (xDown === null || yDown === null) {\n            return\n        }\n\n        var xUp = evt.touches[0].clientX\n        var yUp = evt.touches[0].clientY\n\n        var xDiff = xDown! - xUp\n        var yDiff = yDown! - yUp\n\n        if (Math.abs(xDiff) > Math.abs(yDiff)) {\n            /*most significant*/\n            if (xDiff > 0) {\n                if (onSwipeLeft) {\n                    onSwipeLeft()\n                }\n            } else {\n                if (onSwipeRight) {\n                    onSwipeRight()\n                }\n            }\n        } else {\n            if (yDiff > 0) {\n                if (onSwipeUp) {\n                    onSwipeUp()\n                }\n            } else {\n                if (onSwipeDown) {\n                    onSwipeDown()\n                }\n            }\n        }\n        /* reset values */\n        xDown = null\n        yDown = null\n    }\n\n    return () => {\n        document.removeEventListener('touchstart', handleTouchStart, false)\n        document.removeEventListener('touchmove', handleTouchMove, false)\n    }\n}\n\nexport function useCoflCoins() {\n    const [coflCoins, setCoflCoins] = useState(getCurrentCoflCoins())\n\n    useEffect(() => {\n        let unsubscribe = subscribeToCoflcoinChange(setCoflCoins)\n\n        return () => {\n            unsubscribe()\n        }\n    }, [])\n\n    return coflCoins\n}\n\nexport function useWasAlreadyLoggedIn() {\n    const [wasAlreadyLoggedIn, setWasAlreadyLoggedIn] = useState(false)\n    useEffect(() => {\n        setWasAlreadyLoggedIn(localStorage.getItem('googleId') !== null)\n    }, [])\n\n    return wasAlreadyLoggedIn\n}\n\nexport function useDebounce(value, delay) {\n    const [debouncedValue, setDebouncedValue] = useState(value)\n    useEffect(() => {\n        const handler = setTimeout(() => {\n            setDebouncedValue(value)\n        }, delay)\n        return () => {\n            clearTimeout(handler)\n        }\n    }, [value, delay])\n    return debouncedValue\n}\n\ntype ReadonlyRef<T> = {\n    readonly current: T\n}\n\nexport function useStateWithRef<T>(defaultValue: T): [T, Dispatch<SetStateAction<T>>, ReadonlyRef<T>] {\n    const [state, _setState] = useState(defaultValue)\n    let stateRef = useRef(state)\n\n    const setState: typeof _setState = useCallback((newState: T) => {\n        stateRef.current = newState\n        _setState(newState)\n    }, [])\n\n    return [state, setState, stateRef]\n}\n\nexport function useQueryParamState<T>(key: string, defaultValue: T): [T, Dispatch<SetStateAction<T>>] {\n    const [state, setState] = useState<T>(getDefaultValue() || defaultValue)\n    const router = useRouter()\n\n    function getDefaultValue(): T | undefined {\n        let param = getURLSearchParam(key)\n        if (!param) {\n            return undefined\n        }\n        return JSON.parse(decodeURIComponent(param)) as T\n    }\n\n    function _setState(newState: T) {\n        setState(newState)\n        let urlparams = new URLSearchParams(window.location.search)\n        if (!newState) {\n            urlparams.delete(key)\n        } else {\n            urlparams.set(key, encodeURIComponent(JSON.stringify(newState)))\n        }\n        router.replace(`${window.location.pathname}?${urlparams.toString()}`)\n    }\n\n    return [state, _setState]\n}\n\nexport function useIsMobile() {\n    let [isMobile, setIsMobile] = useState(false)\n\n    useEffect(() => {\n        setIsMobile(isMobileCheck())\n    }, [])\n\n    function isMobileCheck() {\n        let check = false\n        // eslint-disable-next-line no-useless-escape\n        ;(function (a) {\n            if (\n                /(android|bb\\d+|meego).+mobile|avantgo|bada\\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i.test(\n                    a\n                ) ||\n                /1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\\-(n|u)|c55\\/|capi|ccwa|cdm\\-|cell|chtm|cldc|cmd\\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\\-s|devi|dica|dmob|do(c|p)o|ds(12|\\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\\-|_)|g1 u|g560|gene|gf\\-5|g\\-mo|go(\\.w|od)|gr(ad|un)|haie|hcit|hd\\-(m|p|t)|hei\\-|hi(pt|ta)|hp( i|ip)|hs\\-c|ht(c(\\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\\-(20|go|ma)|i230|iac( |\\-|\\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\\/)|klon|kpt |kwc\\-|kyo(c|k)|le(no|xi)|lg( g|\\/(k|l|u)|50|54|\\-[a-w])|libw|lynx|m1\\-w|m3ga|m50\\/|ma(te|ui|xo)|mc(01|21|ca)|m\\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\\-2|po(ck|rt|se)|prox|psio|pt\\-g|qa\\-a|qc(07|12|21|32|60|\\-[2-7]|i\\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\\-|oo|p\\-)|sdk\\/|se(c(\\-|0|1)|47|mc|nd|ri)|sgh\\-|shar|sie(\\-|m)|sk\\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\\-|v\\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\\-|tdg\\-|tel(i|m)|tim\\-|t\\-mo|to(pl|sh)|ts(70|m\\-|m3|m5)|tx\\-9|up(\\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\\-|your|zeto|zte\\-/i.test(\n                    a.substr(0, 4)\n                )\n            )\n                check = true\n        })(navigator.userAgent || navigator.vendor || (window as any).opera)\n        return check\n    }\n\n    return isMobile\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AAGA;AACA;AACA;AACA;;;;;;;AAEO,SAAS;;IACZ,6DAA6D;IAC7D,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,OAAO,IAAM,UAAU,CAAA,SAAU,SAAS;AAC9C;GAJgB;AAMT,SAAS,SAAS,SAAoB,EAAE,YAAuB,EAAE,WAAsB,EAAE,WAAsB;IAClH,IAAI,CAAC,CAAA,GAAA,qHAAA,CAAA,wBAAqB,AAAD,KAAK;QAC1B;IACJ;IAEA,SAAS,gBAAgB,CAAC,cAAc,kBAAkB;IAC1D,SAAS,gBAAgB,CAAC,aAAa,iBAAiB;IAExD,IAAI,QAAQ;IACZ,IAAI,QAAQ;IAEZ,SAAS,WAAW,GAAG;QACnB,OACI,IAAI,OAAO,IAAI,cAAc;QAC7B,IAAI,aAAa,CAAC,OAAO,EAC3B,SAAS;IACf;IAEA,SAAS,iBAAiB,GAAG;QACzB,MAAM,aAAa,WAAW,IAAI,CAAC,EAAE;QACrC,QAAQ,WAAW,OAAO;QAC1B,QAAQ,WAAW,OAAO;IAC9B;IAEA,SAAS,gBAAgB,GAAG;QACxB,IAAI,UAAU,QAAQ,UAAU,MAAM;YAClC;QACJ;QAEA,IAAI,MAAM,IAAI,OAAO,CAAC,EAAE,CAAC,OAAO;QAChC,IAAI,MAAM,IAAI,OAAO,CAAC,EAAE,CAAC,OAAO;QAEhC,IAAI,QAAQ,QAAS;QACrB,IAAI,QAAQ,QAAS;QAErB,IAAI,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC,QAAQ;YACnC,kBAAkB,GAClB,IAAI,QAAQ,GAAG;gBACX,IAAI,aAAa;oBACb;gBACJ;YACJ,OAAO;gBACH,IAAI,cAAc;oBACd;gBACJ;YACJ;QACJ,OAAO;YACH,IAAI,QAAQ,GAAG;gBACX,IAAI,WAAW;oBACX;gBACJ;YACJ,OAAO;gBACH,IAAI,aAAa;oBACb;gBACJ;YACJ;QACJ;QACA,gBAAgB,GAChB,QAAQ;QACR,QAAQ;IACZ;IAEA,OAAO;QACH,SAAS,mBAAmB,CAAC,cAAc,kBAAkB;QAC7D,SAAS,mBAAmB,CAAC,aAAa,iBAAiB;IAC/D;AACJ;AAEO,SAAS;;IACZ,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,GAAA,2HAAA,CAAA,sBAAmB,AAAD;IAE7D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACN,IAAI,cAAc,CAAA,GAAA,2HAAA,CAAA,4BAAyB,AAAD,EAAE;YAE5C;0CAAO;oBACH;gBACJ;;QACJ;iCAAG,EAAE;IAEL,OAAO;AACX;IAZgB;AAcT,SAAS;;IACZ,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2CAAE;YACN,sBAAsB,aAAa,OAAO,CAAC,gBAAgB;QAC/D;0CAAG,EAAE;IAEL,OAAO;AACX;IAPgB;AAST,SAAS,YAAY,KAAK,EAAE,KAAK;;IACpC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACN,MAAM,UAAU;iDAAW;oBACvB,kBAAkB;gBACtB;gDAAG;YACH;yCAAO;oBACH,aAAa;gBACjB;;QACJ;gCAAG;QAAC;QAAO;KAAM;IACjB,OAAO;AACX;IAXgB;AAiBT,SAAS,gBAAmB,YAAe;;IAC9C,MAAM,CAAC,OAAO,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACpC,IAAI,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAEtB,MAAM,WAA6B,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iDAAE,CAAC;YAC5C,SAAS,OAAO,GAAG;YACnB,UAAU;QACd;gDAAG,EAAE;IAEL,OAAO;QAAC;QAAO;QAAU;KAAS;AACtC;IAVgB;AAYT,SAAS,mBAAsB,GAAW,EAAE,YAAe;;IAC9D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAK,qBAAqB;IAC3D,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,SAAS;QACL,IAAI,QAAQ,CAAA,GAAA,gIAAA,CAAA,oBAAiB,AAAD,EAAE;QAC9B,IAAI,CAAC,OAAO;YACR,OAAO;QACX;QACA,OAAO,KAAK,KAAK,CAAC,mBAAmB;IACzC;IAEA,SAAS,UAAU,QAAW;QAC1B,SAAS;QACT,IAAI,YAAY,IAAI,gBAAgB,OAAO,QAAQ,CAAC,MAAM;QAC1D,IAAI,CAAC,UAAU;YACX,UAAU,MAAM,CAAC;QACrB,OAAO;YACH,UAAU,GAAG,CAAC,KAAK,mBAAmB,KAAK,SAAS,CAAC;QACzD;QACA,OAAO,OAAO,CAAC,GAAG,OAAO,QAAQ,CAAC,QAAQ,CAAC,CAAC,EAAE,UAAU,QAAQ,IAAI;IACxE;IAEA,OAAO;QAAC;QAAO;KAAU;AAC7B;IAxBgB;;QAEG,qIAAA,CAAA,YAAS;;;AAwBrB,SAAS;;IACZ,IAAI,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACN,YAAY;QAChB;gCAAG,EAAE;IAEL,SAAS;QACL,IAAI,QAAQ;QAEX,CAAC,SAAU,CAAC;YACT,IACI,2TAA2T,IAAI,CAC3T,MAEJ,0kDAA0kD,IAAI,CAC1kD,EAAE,MAAM,CAAC,GAAG,KAGhB,QAAQ;QAChB,CAAC,EAAE,UAAU,SAAS,IAAI,UAAU,MAAM,IAAI,AAAC,OAAe,KAAK;QACnE,OAAO;IACX;IAEA,OAAO;AACX;IAzBgB", "debugId": null}}, {"offset": {"line": 1333, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/components/NavBar/NavBar.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"hamburgerIcon\": \"NavBar-module__yBvhsG__hamburgerIcon\",\n  \"logo\": \"NavBar-module__yBvhsG__logo\",\n  \"menuItem\": \"NavBar-module__yBvhsG__menuItem\",\n  \"navBar\": \"NavBar-module__yBvhsG__navBar\",\n  \"navClosing\": \"NavBar-module__yBvhsG__navClosing\",\n  \"navOpen\": \"NavBar-module__yBvhsG__navOpen\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 1347, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/NavBar/NavBar.tsx"], "sourcesContent": ["'use client'\nimport AccountBalanceIcon from '@mui/icons-material/AccountBalance'\nimport AccountIcon from '@mui/icons-material/AccountCircle'\nimport BuildIcon from '@mui/icons-material/Build'\nimport ChatIcon from '@mui/icons-material/Chat'\nimport DownloadIcon from '@mui/icons-material/Download'\nimport HomeIcon from '@mui/icons-material/Home'\nimport MenuIcon from '@mui/icons-material/Menu'\nimport NotificationIcon from '@mui/icons-material/NotificationsOutlined'\nimport PetsIcon from '@mui/icons-material/PetsOutlined'\nimport PolicyIcon from '@mui/icons-material/Policy'\nimport ShareIcon from '@mui/icons-material/ShareOutlined'\nimport StorefrontIcon from '@mui/icons-material/Storefront'\nimport CurrencyExchangeIcon from '@mui/icons-material/CurrencyExchange'\nimport Image from 'next/image'\nimport Link from 'next/link'\nimport React, { useEffect, useState } from 'react'\nimport { Menu, MenuItem, Sidebar } from 'react-pro-sidebar'\nimport { useForceUpdate } from '../../utils/Hooks'\nimport styles from './NavBar.module.css'\n\nlet resizePromise: NodeJS.Timeout | null = null\n\ninterface Props {\n    hamburgerIconStyle?: React.CSSProperties\n}\n\nfunction NavBar(props: Props) {\n    let [isWideOpen, setIsWideOpen] = useState(false)\n    let [isHovering, setIsHovering] = useState(false)\n    let [isSmall, setIsSmall] = useState(true)\n    let [collapsed, setCollapsed] = useState(true)\n    let forceUpdate = useForceUpdate()\n\n    useEffect(() => {\n        setIsSmall(document.body.clientWidth < 1500)\n\n        window.addEventListener('resize', resizeHandler)\n\n        return () => {\n            window.removeEventListener('resize', resizeHandler)\n        }\n    }, [])\n\n    useEffect(() => {\n        if (isWideOpen) {\n            document.addEventListener('click', outsideClickHandler, true)\n        } else {\n            document.removeEventListener('click', outsideClickHandler, true)\n        }\n\n        return () => {\n            document.removeEventListener('click', outsideClickHandler, true)\n        }\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [isWideOpen])\n\n    useEffect(() => {\n        setCollapsed(isCollapsed())\n    }, [isSmall, isWideOpen, isHovering])\n\n    function isCollapsed() {\n        if (isSmall) {\n            return false\n        }\n        return !isWideOpen && !isHovering\n    }\n\n    function outsideClickHandler(evt) {\n        const flyoutEl = document.getElementById('navBar')\n        const hamburgerEl = document.getElementById('hamburgerIcon')\n        let targetEl = evt.target\n\n        do {\n            if (targetEl === flyoutEl || targetEl === hamburgerEl) {\n                return\n            }\n            targetEl = (targetEl as any).parentNode\n        } while (targetEl)\n\n        if (isWideOpen) {\n            if (isSmall) {\n                let el = document.getElementById('pro-sidebar')\n                el?.classList.add(styles.navClosing)\n                el?.classList.remove(styles.navOpen)\n                setTimeout(() => {\n                    setIsWideOpen(false)\n                    el?.classList.remove(styles.navClosing)\n                }, 500)\n            } else {\n                setIsWideOpen(false)\n            }\n        }\n    }\n\n    function onMouseMove() {\n        setIsHovering(true)\n    }\n\n    function onMouseOut() {\n        setIsHovering(false)\n    }\n\n    function resizeHandler() {\n        if (resizePromise) {\n            return\n        }\n        resizePromise = setTimeout(() => {\n            setIsWideOpen(false)\n            setIsSmall(document.body.clientWidth < 1500)\n            forceUpdate()\n            resizePromise = null\n            let el = document.getElementById('pro-sidebar')\n            if (el) {\n                el.style.left = '0px'\n            }\n        }, 500)\n    }\n\n    function onHamburgerClick() {\n        if (isSmall && !isWideOpen) {\n            let el = document.getElementById('pro-sidebar')\n            if (el) {\n                el.hidden = false\n                el.style.left = '-270px'\n                setTimeout(() => {\n                    if (el) {\n                        el.classList.add(styles.navOpen)\n                    }\n                })\n                setTimeout(() => {\n                    setIsWideOpen(true)\n                }, 500)\n            }\n        } else {\n            setIsWideOpen(!isWideOpen)\n        }\n    }\n\n    return (\n        <span>\n            <aside className={styles.navBar} id=\"navBar\" onMouseEnter={onMouseMove} onMouseLeave={onMouseOut}>\n                <Sidebar id=\"pro-sidebar\" hidden={isSmall && !isWideOpen} backgroundColor=\"#1d1d1d\" collapsed={collapsed}>\n                    <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>\n                        <div>\n                            <div className={styles.logo}>\n                                <Image src=\"/logo512.png\" alt=\"Logo\" width={40} height={40} style={{ translate: '-5px' }} /> {!isCollapsed() ? 'Coflnet' : ''}\n                            </div>\n                        </div>\n                        <hr />\n                        <Menu>\n                            <MenuItem className={styles.menuItem} component={<Link href={'/'} />} icon={<HomeIcon />}>\n                                Home\n                            </MenuItem>\n                            <MenuItem className={styles.menuItem} component={<Link href={'/flipper'} />} icon={<StorefrontIcon />}>\n                                Item Flipper\n                            </MenuItem>\n                            <MenuItem className={styles.menuItem} component={<Link href={'/account'} />} icon={<AccountIcon />}>\n                                Account\n                            </MenuItem>\n                            <MenuItem className={styles.menuItem} component={<Link href={'/subscriptions'} />} icon={<NotificationIcon />}>\n                                Notifier\n                            </MenuItem>\n                            <MenuItem className={styles.menuItem} component={<Link href={'/crafts'} />} icon={<BuildIcon />}>\n                                Profitable Crafts\n                            </MenuItem>\n                            <MenuItem className={styles.menuItem} component={<Link href={'/premium'} />} icon={<AccountBalanceIcon />}>\n                                Premium / Shop\n                            </MenuItem>\n                            <MenuItem className={styles.menuItem} component={<Link href={'/trade'} />} icon={<CurrencyExchangeIcon />}>\n                                Trading\n                            </MenuItem>\n                            <MenuItem className={styles.menuItem} component={<Link href={'/kat'} />} icon={<PetsIcon />}>\n                                Kat Flips\n                            </MenuItem>\n                            <MenuItem className={styles.menuItem} component={<Link href={'/mod'} />} icon={<DownloadIcon />}>\n                                Mod\n                            </MenuItem>\n                            <MenuItem className={styles.menuItem} component={<Link href={'/ref'} />} icon={<ShareIcon />}>\n                                Referral\n                            </MenuItem>\n                            <MenuItem className={styles.menuItem} component={<Link href={'/about'} />} icon={<PolicyIcon />}>\n                                Links / Legal\n                            </MenuItem>\n                            <MenuItem className={styles.menuItem} component={<Link href={'/feedback'} />} icon={<ChatIcon />}>\n                                Feedback\n                            </MenuItem>\n                            <MenuItem\n                                className={styles.menuItem}\n                                component={<Link href={'https://discord.gg/wvKXfTgCfb'} target=\"_blank\" />}\n                                rel=\"noreferrer\"\n                                icon={<Image src=\"/discord_icon.svg\" alt=\"Discord icon\" height={24} width={32} />}\n                            >\n                                Discord\n                            </MenuItem>\n                        </Menu>\n                    </div>\n                </Sidebar>\n            </aside>\n            {isSmall ? (\n                <span onClick={onHamburgerClick} className={styles.hamburgerIcon} id=\"hamburgerIcon\" style={props.hamburgerIconStyle}>\n                    <MenuIcon fontSize=\"large\" />\n                </span>\n            ) : (\n                ''\n            )}\n        </span>\n    )\n}\n\nexport default NavBar\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAnBA;;;;;;;;;;;;;;;;;;;;AAqBA,IAAI,gBAAuC;AAM3C,SAAS,OAAO,KAAY;;IACxB,IAAI,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,IAAI,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,IAAI,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,IAAI,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,IAAI,cAAc,CAAA,GAAA,kHAAA,CAAA,iBAAc,AAAD;IAE/B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACN,WAAW,SAAS,IAAI,CAAC,WAAW,GAAG;YAEvC,OAAO,gBAAgB,CAAC,UAAU;YAElC;oCAAO;oBACH,OAAO,mBAAmB,CAAC,UAAU;gBACzC;;QACJ;2BAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACN,IAAI,YAAY;gBACZ,SAAS,gBAAgB,CAAC,SAAS,qBAAqB;YAC5D,OAAO;gBACH,SAAS,mBAAmB,CAAC,SAAS,qBAAqB;YAC/D;YAEA;oCAAO;oBACH,SAAS,mBAAmB,CAAC,SAAS,qBAAqB;gBAC/D;;QACA,uDAAuD;QAC3D;2BAAG;QAAC;KAAW;IAEf,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACN,aAAa;QACjB;2BAAG;QAAC;QAAS;QAAY;KAAW;IAEpC,SAAS;QACL,IAAI,SAAS;YACT,OAAO;QACX;QACA,OAAO,CAAC,cAAc,CAAC;IAC3B;IAEA,SAAS,oBAAoB,GAAG;QAC5B,MAAM,WAAW,SAAS,cAAc,CAAC;QACzC,MAAM,cAAc,SAAS,cAAc,CAAC;QAC5C,IAAI,WAAW,IAAI,MAAM;QAEzB,GAAG;YACC,IAAI,aAAa,YAAY,aAAa,aAAa;gBACnD;YACJ;YACA,WAAW,AAAC,SAAiB,UAAU;QAC3C,QAAS,SAAS;QAElB,IAAI,YAAY;YACZ,IAAI,SAAS;gBACT,IAAI,KAAK,SAAS,cAAc,CAAC;gBACjC,IAAI,UAAU,IAAI,6IAAA,CAAA,UAAM,CAAC,UAAU;gBACnC,IAAI,UAAU,OAAO,6IAAA,CAAA,UAAM,CAAC,OAAO;gBACnC,WAAW;oBACP,cAAc;oBACd,IAAI,UAAU,OAAO,6IAAA,CAAA,UAAM,CAAC,UAAU;gBAC1C,GAAG;YACP,OAAO;gBACH,cAAc;YAClB;QACJ;IACJ;IAEA,SAAS;QACL,cAAc;IAClB;IAEA,SAAS;QACL,cAAc;IAClB;IAEA,SAAS;QACL,IAAI,eAAe;YACf;QACJ;QACA,gBAAgB,WAAW;YACvB,cAAc;YACd,WAAW,SAAS,IAAI,CAAC,WAAW,GAAG;YACvC;YACA,gBAAgB;YAChB,IAAI,KAAK,SAAS,cAAc,CAAC;YACjC,IAAI,IAAI;gBACJ,GAAG,KAAK,CAAC,IAAI,GAAG;YACpB;QACJ,GAAG;IACP;IAEA,SAAS;QACL,IAAI,WAAW,CAAC,YAAY;YACxB,IAAI,KAAK,SAAS,cAAc,CAAC;YACjC,IAAI,IAAI;gBACJ,GAAG,MAAM,GAAG;gBACZ,GAAG,KAAK,CAAC,IAAI,GAAG;gBAChB,WAAW;oBACP,IAAI,IAAI;wBACJ,GAAG,SAAS,CAAC,GAAG,CAAC,6IAAA,CAAA,UAAM,CAAC,OAAO;oBACnC;gBACJ;gBACA,WAAW;oBACP,cAAc;gBAClB,GAAG;YACP;QACJ,OAAO;YACH,cAAc,CAAC;QACnB;IACJ;IAEA,qBACI,6LAAC;;0BACG,6LAAC;gBAAM,WAAW,6IAAA,CAAA,UAAM,CAAC,MAAM;gBAAE,IAAG;gBAAS,cAAc;gBAAa,cAAc;0BAClF,cAAA,6LAAC,iKAAA,CAAA,UAAO;oBAAC,IAAG;oBAAc,QAAQ,WAAW,CAAC;oBAAY,iBAAgB;oBAAU,WAAW;8BAC3F,cAAA,6LAAC;wBAAI,OAAO;4BAAE,QAAQ;4BAAQ,SAAS;4BAAQ,eAAe;wBAAS;;0CACnE,6LAAC;0CACG,cAAA,6LAAC;oCAAI,WAAW,6IAAA,CAAA,UAAM,CAAC,IAAI;;sDACvB,6LAAC,gIAAA,CAAA,UAAK;4CAAC,KAAI;4CAAe,KAAI;4CAAO,OAAO;4CAAI,QAAQ;4CAAI,OAAO;gDAAE,WAAW;4CAAO;;;;;;wCAAK;wCAAE,CAAC,gBAAgB,YAAY;;;;;;;;;;;;0CAGnI,6LAAC;;;;;0CACD,6LAAC,iKAAA,CAAA,OAAI;;kDACD,6LAAC,iKAAA,CAAA,WAAQ;wCAAC,WAAW,6IAAA,CAAA,UAAM,CAAC,QAAQ;wCAAE,yBAAW,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAM;;;;;;wCAAS,oBAAM,6LAAC,4JAAA,CAAA,UAAQ;;;;;kDAAK;;;;;;kDAG1F,6LAAC,iKAAA,CAAA,WAAQ;wCAAC,WAAW,6IAAA,CAAA,UAAM,CAAC,QAAQ;wCAAE,yBAAW,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAM;;;;;;wCAAgB,oBAAM,6LAAC,kKAAA,CAAA,UAAc;;;;;kDAAK;;;;;;kDAGvG,6LAAC,iKAAA,CAAA,WAAQ;wCAAC,WAAW,6IAAA,CAAA,UAAM,CAAC,QAAQ;wCAAE,yBAAW,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAM;;;;;;wCAAgB,oBAAM,6LAAC,qKAAA,CAAA,UAAW;;;;;kDAAK;;;;;;kDAGpG,6LAAC,iKAAA,CAAA,WAAQ;wCAAC,WAAW,6IAAA,CAAA,UAAM,CAAC,QAAQ;wCAAE,yBAAW,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAM;;;;;;wCAAsB,oBAAM,6LAAC,6KAAA,CAAA,UAAgB;;;;;kDAAK;;;;;;kDAG/G,6LAAC,iKAAA,CAAA,WAAQ;wCAAC,WAAW,6IAAA,CAAA,UAAM,CAAC,QAAQ;wCAAE,yBAAW,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAM;;;;;;wCAAe,oBAAM,6LAAC,6JAAA,CAAA,UAAS;;;;;kDAAK;;;;;;kDAGjG,6LAAC,iKAAA,CAAA,WAAQ;wCAAC,WAAW,6IAAA,CAAA,UAAM,CAAC,QAAQ;wCAAE,yBAAW,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAM;;;;;;wCAAgB,oBAAM,6LAAC,sKAAA,CAAA,UAAkB;;;;;kDAAK;;;;;;kDAG3G,6LAAC,iKAAA,CAAA,WAAQ;wCAAC,WAAW,6IAAA,CAAA,UAAM,CAAC,QAAQ;wCAAE,yBAAW,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAM;;;;;;wCAAc,oBAAM,6LAAC,wKAAA,CAAA,UAAoB;;;;;kDAAK;;;;;;kDAG3G,6LAAC,iKAAA,CAAA,WAAQ;wCAAC,WAAW,6IAAA,CAAA,UAAM,CAAC,QAAQ;wCAAE,yBAAW,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAM;;;;;;wCAAY,oBAAM,6LAAC,oKAAA,CAAA,UAAQ;;;;;kDAAK;;;;;;kDAG7F,6LAAC,iKAAA,CAAA,WAAQ;wCAAC,WAAW,6IAAA,CAAA,UAAM,CAAC,QAAQ;wCAAE,yBAAW,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAM;;;;;;wCAAY,oBAAM,6LAAC,gKAAA,CAAA,UAAY;;;;;kDAAK;;;;;;kDAGjG,6LAAC,iKAAA,CAAA,WAAQ;wCAAC,WAAW,6IAAA,CAAA,UAAM,CAAC,QAAQ;wCAAE,yBAAW,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAM;;;;;;wCAAY,oBAAM,6LAAC,qKAAA,CAAA,UAAS;;;;;kDAAK;;;;;;kDAG9F,6LAAC,iKAAA,CAAA,WAAQ;wCAAC,WAAW,6IAAA,CAAA,UAAM,CAAC,QAAQ;wCAAE,yBAAW,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAM;;;;;;wCAAc,oBAAM,6LAAC,8JAAA,CAAA,UAAU;;;;;kDAAK;;;;;;kDAGjG,6LAAC,iKAAA,CAAA,WAAQ;wCAAC,WAAW,6IAAA,CAAA,UAAM,CAAC,QAAQ;wCAAE,yBAAW,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAM;;;;;;wCAAiB,oBAAM,6LAAC,4JAAA,CAAA,UAAQ;;;;;kDAAK;;;;;;kDAGlG,6LAAC,iKAAA,CAAA,WAAQ;wCACL,WAAW,6IAAA,CAAA,UAAM,CAAC,QAAQ;wCAC1B,yBAAW,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAM;4CAAiC,QAAO;;;;;;wCAC/D,KAAI;wCACJ,oBAAM,6LAAC,gIAAA,CAAA,UAAK;4CAAC,KAAI;4CAAoB,KAAI;4CAAe,QAAQ;4CAAI,OAAO;;;;;;kDAC9E;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAOhB,wBACG,6LAAC;gBAAK,SAAS;gBAAkB,WAAW,6IAAA,CAAA,UAAM,CAAC,aAAa;gBAAE,IAAG;gBAAgB,OAAO,MAAM,kBAAkB;0BAChH,cAAA,6LAAC,4JAAA,CAAA,UAAQ;oBAAC,UAAS;;;;;;;;;;uBAGvB;;;;;;;AAIhB;GArLS;;QAKa,kHAAA,CAAA,iBAAc;;;KAL3B;uCAuLM", "debugId": null}}, {"offset": {"line": 1893, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/components/OptionsMenu/OptionsMenu.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"buttonsWrapper\": \"OptionsMenu-module__BxCVkW__buttonsWrapper\",\n  \"dropdown\": \"OptionsMenu-module__BxCVkW__dropdown\",\n  \"optionsMenu\": \"OptionsMenu-module__BxCVkW__optionsMenu\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 1904, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/OptionsMenu/OptionsMenu.tsx"], "sourcesContent": ["'use client'\nimport React from 'react'\nimport MoreVertIcon from '@mui/icons-material/MoreVert'\nimport styles from './OptionsMenu.module.css'\nimport { Button, Dropdown, DropdownButton } from 'react-bootstrap'\n\ninterface Props {\n    selected?: Player | Item\n}\ninterface AvailableLinks {\n    title: string\n    url: string\n}\n\nconst CustomToggle = React.forwardRef(({ children, onClick }: any, ref) => (\n    <span\n        ref={ref as any}\n        onClick={e => {\n            e.preventDefault()\n            onClick(e)\n        }}\n    >\n        {children}\n        <MoreVertIcon />\n    </span>\n))\n\nfunction OptionsMenu(props: Props) {\n    let available: AvailableLinks[] = []\n    const isItemPage = (props.selected as Item)?.tag !== undefined\n    const isPlayerPage = !isItemPage\n    if (isItemPage) {\n        let fandomName = props.selected?.name\n        let wikiName = props.selected?.name\n        let tag = (props.selected as Item).tag\n        if (tag.startsWith('ENCHANTMENT_')) {\n            fandomName = tag.replace('ENCHANTMENT_', '').replace('ULTIMATE_', '').replace(/_\\d/, '').toLowerCase()\n            fandomName = fandomName\n                .split('_')\n                .map(part => part.charAt(0).toUpperCase() + part.slice(1).toLowerCase())\n                .join('_')\n            wikiName = fandomName + '_Enchantment'\n        }\n        available.push({ title: 'Fandom', url: 'https://hypixel-skyblock.fandom.com/wiki/' + fandomName })\n        available.push({ title: 'Wiki', url: 'https://wiki.hypixel.net/' + wikiName })\n        if ((props.selected as Item).bazaar) {\n            available.push({ title: 'Skyblock.bz', url: 'https://Skyblock.bz/product/' + tag })\n        }\n    } else if (isPlayerPage) {\n        let player = props.selected as Player\n        available.push({ title: 'SkyCrypt', url: 'https://sky.shiiyu.moe/stats/' + player?.uuid })\n        available.push({ title: 'Plancke', url: 'https://plancke.io/hypixel/player/stats/' + player?.uuid })\n    }\n\n    const navigate = (url: string) => {\n        window.open(url, '_blank')\n    }\n\n    if (!props.selected || props.selected.name === undefined) {\n        return null\n    }\n\n    return (\n        <div className={styles.optionsMenu}>\n            <div className={styles.buttonsWrapper}>\n                {available.map((result, i) => (\n                    <a key={i} href={result.url} title={result.title} target=\"_blank\" rel=\"noreferrer\">\n                        <Button>{result.title}</Button>\n                    </a>\n                ))}\n            </div>\n\n            <Dropdown className={styles.dropdown}>\n                <Dropdown.Toggle as={CustomToggle}></Dropdown.Toggle>\n                <Dropdown.Menu id=\"dropdownMenuButton\">\n                    {available.map((result, i) => (\n                        <Dropdown.Item\n                            key={result.url}\n                            onClick={() => {\n                                navigate(result.url)\n                            }}\n                        >\n                            {result.title}\n                        </Dropdown.Item>\n                    ))}\n                </Dropdown.Menu>\n            </Dropdown>\n        </div>\n    )\n}\n\nexport default OptionsMenu\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AAAA;AAJA;;;;;;AAcA,MAAM,6BAAe,6JAAA,CAAA,UAAK,CAAC,UAAU,MAAC,CAAC,EAAE,QAAQ,EAAE,OAAO,EAAO,EAAE,oBAC/D,6LAAC;QACG,KAAK;QACL,SAAS,CAAA;YACL,EAAE,cAAc;YAChB,QAAQ;QACZ;;YAEC;0BACD,6LAAC,gKAAA,CAAA,UAAY;;;;;;;;;;;;AAIrB,SAAS,YAAY,KAAY;IAC7B,IAAI,YAA8B,EAAE;IACpC,MAAM,aAAa,AAAC,MAAM,QAAQ,EAAW,QAAQ;IACrD,MAAM,eAAe,CAAC;IACtB,IAAI,YAAY;QACZ,IAAI,aAAa,MAAM,QAAQ,EAAE;QACjC,IAAI,WAAW,MAAM,QAAQ,EAAE;QAC/B,IAAI,MAAM,AAAC,MAAM,QAAQ,CAAU,GAAG;QACtC,IAAI,IAAI,UAAU,CAAC,iBAAiB;YAChC,aAAa,IAAI,OAAO,CAAC,gBAAgB,IAAI,OAAO,CAAC,aAAa,IAAI,OAAO,CAAC,OAAO,IAAI,WAAW;YACpG,aAAa,WACR,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC,GAAG,WAAW,IACpE,IAAI,CAAC;YACV,WAAW,aAAa;QAC5B;QACA,UAAU,IAAI,CAAC;YAAE,OAAO;YAAU,KAAK,8CAA8C;QAAW;QAChG,UAAU,IAAI,CAAC;YAAE,OAAO;YAAQ,KAAK,8BAA8B;QAAS;QAC5E,IAAI,AAAC,MAAM,QAAQ,CAAU,MAAM,EAAE;YACjC,UAAU,IAAI,CAAC;gBAAE,OAAO;gBAAe,KAAK,iCAAiC;YAAI;QACrF;IACJ,OAAO,IAAI,cAAc;QACrB,IAAI,SAAS,MAAM,QAAQ;QAC3B,UAAU,IAAI,CAAC;YAAE,OAAO;YAAY,KAAK,kCAAkC,QAAQ;QAAK;QACxF,UAAU,IAAI,CAAC;YAAE,OAAO;YAAW,KAAK,6CAA6C,QAAQ;QAAK;IACtG;IAEA,MAAM,WAAW,CAAC;QACd,OAAO,IAAI,CAAC,KAAK;IACrB;IAEA,IAAI,CAAC,MAAM,QAAQ,IAAI,MAAM,QAAQ,CAAC,IAAI,KAAK,WAAW;QACtD,OAAO;IACX;IAEA,qBACI,6LAAC;QAAI,WAAW,uJAAA,CAAA,UAAM,CAAC,WAAW;;0BAC9B,6LAAC;gBAAI,WAAW,uJAAA,CAAA,UAAM,CAAC,cAAc;0BAChC,UAAU,GAAG,CAAC,CAAC,QAAQ,kBACpB,6LAAC;wBAAU,MAAM,OAAO,GAAG;wBAAE,OAAO,OAAO,KAAK;wBAAE,QAAO;wBAAS,KAAI;kCAClE,cAAA,6LAAC,2LAAA,CAAA,SAAM;sCAAE,OAAO,KAAK;;;;;;uBADjB;;;;;;;;;;0BAMhB,6LAAC,+LAAA,CAAA,WAAQ;gBAAC,WAAW,uJAAA,CAAA,UAAM,CAAC,QAAQ;;kCAChC,6LAAC,+LAAA,CAAA,WAAQ,CAAC,MAAM;wBAAC,IAAI;;;;;;kCACrB,6LAAC,+LAAA,CAAA,WAAQ,CAAC,IAAI;wBAAC,IAAG;kCACb,UAAU,GAAG,CAAC,CAAC,QAAQ,kBACpB,6LAAC,+LAAA,CAAA,WAAQ,CAAC,IAAI;gCAEV,SAAS;oCACL,SAAS,OAAO,GAAG;gCACvB;0CAEC,OAAO,KAAK;+BALR,OAAO,GAAG;;;;;;;;;;;;;;;;;;;;;;AAY3C;MA9DS;uCAgEM", "debugId": null}}, {"offset": {"line": 2064, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/components/Search/Search.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"bar\": \"Search-module__Lg4wHG__bar\",\n  \"current\": \"Search-module__Lg4wHG__current\",\n  \"multiInputfield\": \"Search-module__Lg4wHG__multiInputfield\",\n  \"multiSearch\": \"Search-module__Lg4wHG__multiSearch\",\n  \"previousSearch\": \"Search-module__Lg4wHG__previousSearch\",\n  \"search\": \"Search-module__Lg4wHG__search\",\n  \"searchFormGroup\": \"Search-module__Lg4wHG__searchFormGroup\",\n  \"searchResultIcon\": \"Search-module__Lg4wHG__searchResultIcon\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 2080, "column": 0}, "map": {"version": 3, "sources": ["file:///app/utils/PreviousSearchUtils.tsx"], "sourcesContent": ["const PREVIOUS_SEARCHES_KEY = 'lastSearches'\nconst MAX_PREVIOUS_SEARCHES_TO_DISPLAY = 3\nconst MAX_PREVIOUS_SEARCHES_TO_STORE = 100\n\nexport function addClickedSearchResultToPreviousSearches(item: SearchResultItem, previousSearchKey?: string) {\n    let previousSearches: SearchResultItem[] = getPreviousSearchesFromLocalStorage(previousSearchKey)\n\n    let alreadyFoundIndex = previousSearches.findIndex(r => r.id === item.id)\n    if (alreadyFoundIndex !== -1) {\n        previousSearches.splice(alreadyFoundIndex, 1)\n    }\n    previousSearches.push(item)\n    if (previousSearches.length > MAX_PREVIOUS_SEARCHES_TO_STORE) {\n        previousSearches.shift()\n    }\n    localStorage.setItem(PREVIOUS_SEARCHES_KEY, JSON.stringify(previousSearches))\n}\n\nexport function pinSearchResult(item: SearchResultItem, previousSearchKey?: string) {\n    let previousSearches: SearchResultItem[] = getPreviousSearchesFromLocalStorage(previousSearchKey)\n    let alreadyFoundIndex = previousSearches.findIndex(r => r.id === item.id)\n    if (alreadyFoundIndex !== -1) {\n        previousSearches[alreadyFoundIndex].pinned = true\n    } else {\n        previousSearches.push({\n            ...item,\n            pinned: true\n        })\n    }\n    localStorage.setItem(PREVIOUS_SEARCHES_KEY, JSON.stringify(previousSearches))\n}\n\nexport function unpinSearchResult(item: SearchResultItem, previousSearchKey?: string) {\n    let previousSearches: SearchResultItem[] = getPreviousSearchesFromLocalStorage(previousSearchKey)\n    let index = previousSearches.findIndex(r => r.id === item.id)\n    if (index !== -1) {\n        previousSearches[index].pinned = false\n    } else {\n        // item to remove was not found\n        return\n    }\n    localStorage.setItem(PREVIOUS_SEARCHES_KEY, JSON.stringify(previousSearches))\n}\n\nexport function addPreviousSearchResultsToDisplay(searchText: string, searchResults: SearchResultItem[], previousSearchKey?: string) {\n    let newSearchResults = [...searchResults]\n    let previousSearches: SearchResultItem[] = getPreviousSearchesFromLocalStorage(previousSearchKey)\n    let matches: SearchResultItem[] = []\n    let matchingPreviousSearchesInResuls = 0\n\n    previousSearches.forEach(prevSearch => {\n        let alreadyFoundIndex = newSearchResults.findIndex(r => r.id === prevSearch.id)\n        if (alreadyFoundIndex !== -1) {\n            newSearchResults[alreadyFoundIndex] = prevSearch\n            matchingPreviousSearchesInResuls++\n        } else if (prevSearch.dataItem.name.toLowerCase().indexOf(searchText.toLowerCase()) !== -1) {\n            matches.unshift(prevSearch)\n        }\n    })\n\n    let pinnedSort = (a, b) => {\n        if (a.pinned) {\n            return -1\n        }\n        if (b.pinned) {\n            return 1\n        }\n        return 0\n    }\n\n    newSearchResults.sort(pinnedSort)\n    matches.sort(pinnedSort).slice(0, MAX_PREVIOUS_SEARCHES_TO_DISPLAY - matchingPreviousSearchesInResuls)\n\n    if (MAX_PREVIOUS_SEARCHES_TO_DISPLAY <= matchingPreviousSearchesInResuls) {\n        return newSearchResults\n    }\n\n    return [...matches, ...newSearchResults]\n}\n\nexport function getFirstPreviousSearches(amount: number, previousSearchKey?: string) {\n    let previousSearches: SearchResultItem[] = getPreviousSearchesFromLocalStorage(previousSearchKey)\n    return previousSearches\n        .sort((a, b) => {\n            if (a.pinned) {\n                return 1\n            }\n            if (b.pinned) {\n                return -1\n            }\n            return 0\n        })\n        .slice(-amount)\n        .reverse()\n}\n\nfunction getPreviousSearchesFromLocalStorage(keyForPinnedItems?: string) {\n    let previousSearches: SearchResultItem[] = localStorage.getItem(PREVIOUS_SEARCHES_KEY) ? JSON.parse(localStorage.getItem(PREVIOUS_SEARCHES_KEY)!) : []\n    return previousSearches\n        .filter(prevSearch => prevSearch.previousSearchKey === keyForPinnedItems)\n        .map(prevSearch => {\n            prevSearch.isPreviousSearch = true\n            return prevSearch\n        })\n}\n"], "names": [], "mappings": ";;;;;;;AAAA,MAAM,wBAAwB;AAC9B,MAAM,mCAAmC;AACzC,MAAM,iCAAiC;AAEhC,SAAS,yCAAyC,IAAsB,EAAE,iBAA0B;IACvG,IAAI,mBAAuC,oCAAoC;IAE/E,IAAI,oBAAoB,iBAAiB,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,KAAK,EAAE;IACxE,IAAI,sBAAsB,CAAC,GAAG;QAC1B,iBAAiB,MAAM,CAAC,mBAAmB;IAC/C;IACA,iBAAiB,IAAI,CAAC;IACtB,IAAI,iBAAiB,MAAM,GAAG,gCAAgC;QAC1D,iBAAiB,KAAK;IAC1B;IACA,aAAa,OAAO,CAAC,uBAAuB,KAAK,SAAS,CAAC;AAC/D;AAEO,SAAS,gBAAgB,IAAsB,EAAE,iBAA0B;IAC9E,IAAI,mBAAuC,oCAAoC;IAC/E,IAAI,oBAAoB,iBAAiB,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,KAAK,EAAE;IACxE,IAAI,sBAAsB,CAAC,GAAG;QAC1B,gBAAgB,CAAC,kBAAkB,CAAC,MAAM,GAAG;IACjD,OAAO;QACH,iBAAiB,IAAI,CAAC;YAClB,GAAG,IAAI;YACP,QAAQ;QACZ;IACJ;IACA,aAAa,OAAO,CAAC,uBAAuB,KAAK,SAAS,CAAC;AAC/D;AAEO,SAAS,kBAAkB,IAAsB,EAAE,iBAA0B;IAChF,IAAI,mBAAuC,oCAAoC;IAC/E,IAAI,QAAQ,iBAAiB,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,KAAK,EAAE;IAC5D,IAAI,UAAU,CAAC,GAAG;QACd,gBAAgB,CAAC,MAAM,CAAC,MAAM,GAAG;IACrC,OAAO;QACH,+BAA+B;QAC/B;IACJ;IACA,aAAa,OAAO,CAAC,uBAAuB,KAAK,SAAS,CAAC;AAC/D;AAEO,SAAS,kCAAkC,UAAkB,EAAE,aAAiC,EAAE,iBAA0B;IAC/H,IAAI,mBAAmB;WAAI;KAAc;IACzC,IAAI,mBAAuC,oCAAoC;IAC/E,IAAI,UAA8B,EAAE;IACpC,IAAI,mCAAmC;IAEvC,iBAAiB,OAAO,CAAC,CAAA;QACrB,IAAI,oBAAoB,iBAAiB,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,WAAW,EAAE;QAC9E,IAAI,sBAAsB,CAAC,GAAG;YAC1B,gBAAgB,CAAC,kBAAkB,GAAG;YACtC;QACJ,OAAO,IAAI,WAAW,QAAQ,CAAC,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,WAAW,QAAQ,CAAC,GAAG;YACxF,QAAQ,OAAO,CAAC;QACpB;IACJ;IAEA,IAAI,aAAa,CAAC,GAAG;QACjB,IAAI,EAAE,MAAM,EAAE;YACV,OAAO,CAAC;QACZ;QACA,IAAI,EAAE,MAAM,EAAE;YACV,OAAO;QACX;QACA,OAAO;IACX;IAEA,iBAAiB,IAAI,CAAC;IACtB,QAAQ,IAAI,CAAC,YAAY,KAAK,CAAC,GAAG,mCAAmC;IAErE,IAAI,oCAAoC,kCAAkC;QACtE,OAAO;IACX;IAEA,OAAO;WAAI;WAAY;KAAiB;AAC5C;AAEO,SAAS,yBAAyB,MAAc,EAAE,iBAA0B;IAC/E,IAAI,mBAAuC,oCAAoC;IAC/E,OAAO,iBACF,IAAI,CAAC,CAAC,GAAG;QACN,IAAI,EAAE,MAAM,EAAE;YACV,OAAO;QACX;QACA,IAAI,EAAE,MAAM,EAAE;YACV,OAAO,CAAC;QACZ;QACA,OAAO;IACX,GACC,KAAK,CAAC,CAAC,QACP,OAAO;AAChB;AAEA,SAAS,oCAAoC,iBAA0B;IACnE,IAAI,mBAAuC,aAAa,OAAO,CAAC,yBAAyB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,0BAA2B,EAAE;IACtJ,OAAO,iBACF,MAAM,CAAC,CAAA,aAAc,WAAW,iBAAiB,KAAK,mBACtD,GAAG,CAAC,CAAA;QACD,WAAW,gBAAgB,GAAG;QAC9B,OAAO;IACX;AACR", "debugId": null}}, {"offset": {"line": 2189, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/ClientOnly/ClientOnly.tsx"], "sourcesContent": ["import { useEffect, useState } from 'react'\n\nexport default function ClientOnly({ children }) {\n    const [mounted, setMounted] = useState(false)\n\n    useEffect(() => {\n        setMounted(true)\n    }, [])\n\n    return mounted ? children : null\n}\n"], "names": [], "mappings": ";;;AAAA;;;AAEe,SAAS,WAAW,EAAE,QAAQ,EAAE;;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACN,WAAW;QACf;+BAAG,EAAE;IAEL,OAAO,UAAU,WAAW;AAChC;GARwB;KAAA", "debugId": null}}, {"offset": {"line": 2218, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/Search/Search.tsx"], "sourcesContent": ["'use client'\nimport React, { ChangeEvent, useEffect, useRef, useState, type JSX } from 'react'\nimport api from '../../api/ApiHelper'\nimport { Form, InputGroup, ListGroup, Spinner } from 'react-bootstrap'\nimport { convertTagToName, getStyleForTier } from '../../utils/Formatter'\nimport NavBar from '../NavBar/NavBar'\nimport OptionsMenu from '../OptionsMenu/OptionsMenu'\nimport SearchIcon from '@mui/icons-material/SearchOutlined'\nimport WrongIcon from '@mui/icons-material/Dangerous'\nimport Refresh from '@mui/icons-material/Refresh'\nimport ClearIcon from '@mui/icons-material/Clear'\nimport { Item, Menu, useContextMenu, Separator } from 'react-contexify'\nimport { toast } from 'react-toastify'\nimport { isClientSideRendering } from '../../utils/SSRUtils'\nimport styles from './Search.module.css'\nimport { useForceUpdate, useIsMobile } from '../../utils/Hooks'\nimport Image from 'next/image'\nimport { useRouter } from 'next/navigation'\nimport PushPinIcon from '@mui/icons-material/PushPin'\nimport {\n    addClickedSearchResultToPreviousSearches,\n    addPreviousSearchResultsToDisplay,\n    getFirstPreviousSearches,\n    pinSearchResult,\n    unpinSearchResult\n} from '../../utils/PreviousSearchUtils'\nimport { ITEM_ICON_TYPE, getSetting, setSetting } from '../../utils/SettingsUtils'\nimport ClientOnly from '../ClientOnly/ClientOnly'\n\ninterface Props {\n    selected?: Player | Item\n    currentElement?: JSX.Element\n    backgroundColor?: string\n    backgroundColorSelected?: string\n    searchFunction?(searchText: string)\n    onSearchresultClick?(item: SearchResultItem)\n    hideNavbar?: boolean\n    placeholder?: string\n    type?: 'player' | 'item'\n    preventDisplayOfPreviousSearches?: boolean\n    enableReset?: boolean\n    onResetClick?()\n    hideOptions?: boolean\n    keyForPinnedItems?: string\n}\n\nconst PLAYER_SEARCH_CONEXT_MENU_ID = 'player-search-context-menu'\nconst SEARCH_RESULT_CONTEXT_MENU_ID = 'search-result-context-menu'\n\nfunction Search(props: Props) {\n    let router = useRouter()\n    let [searchText, setSearchText] = useState('')\n    let [results, setResults] = useState<SearchResultItem[]>([])\n    let [isSearching, setIsSearching] = useState(false)\n    let [noResultsFound, setNoResultsFound] = useState(false)\n    let [isSmall, setIsSmall] = useState(true)\n    let [selectedIndex, setSelectedIndex] = useState(0)\n    const { show: showPlayerContextMenu } = useContextMenu({\n        id: PLAYER_SEARCH_CONEXT_MENU_ID\n    })\n    const { show: showSearchItemContextMenu, hideAll: hideSearchItemContextMenu } = useContextMenu({\n        id: SEARCH_RESULT_CONTEXT_MENU_ID\n    })\n    const isMobile = useIsMobile()\n\n    let rememberEnterPressRef = useRef(false)\n\n    let searchElement = useRef(null)\n    let forceUpdate = useForceUpdate()\n\n    useEffect(() => {\n        if (isClientSideRendering()) {\n            setIsSmall(isClientSideRendering() ? document.body.clientWidth < 1500 : false)\n        }\n        document.addEventListener('click', outsideClickHandler, true)\n        return () => {\n            document.removeEventListener('click', outsideClickHandler, true)\n        }\n    }, [])\n\n    useEffect(() => {\n        setSearchText('')\n        setResults([])\n    }, [props.selected])\n\n    let search = () => {\n        let searchFor = searchText\n        let searchFunction = props.searchFunction || api.search\n        searchFunction(searchFor).then(searchResults => {\n            // has the searchtext changed?\n            if (\n                searchElement.current !== null &&\n                searchFor === ((searchElement.current as HTMLDivElement).querySelector('#search-bar') as HTMLInputElement).value\n            ) {\n                let searchResultsToShow = [...searchResults]\n                if (!props.preventDisplayOfPreviousSearches) {\n                    searchResultsToShow = addPreviousSearchResultsToDisplay(searchFor, searchResults, props.keyForPinnedItems)\n                }\n\n                setSelectedIndex(0)\n                setNoResultsFound(searchResultsToShow.length === 0)\n                setResults(searchResultsToShow)\n                setIsSearching(false)\n\n                if (rememberEnterPressRef.current) {\n                    onItemClick(searchResultsToShow[0])\n                    rememberEnterPressRef.current = false\n                }\n            }\n        })\n    }\n\n    let onSearchChange = (e: ChangeEvent) => {\n        let newSearchText: string = (e.target as HTMLInputElement).value\n        searchText = newSearchText\n        setSearchText(newSearchText)\n        setIsSearching(true)\n\n        if (newSearchText === '') {\n            setResults([])\n            setIsSearching(false)\n            return\n        }\n\n        setNoResultsFound(false)\n        search()\n    }\n\n    function outsideClickHandler(evt) {\n        const flyoutEl = searchElement.current\n        let targetEl = evt.target\n\n        do {\n            if (targetEl === flyoutEl) {\n                return\n            }\n            targetEl = (targetEl as any).parentNode\n        } while (targetEl)\n\n        setResults([])\n    }\n\n    let onKeyPress = (e: KeyboardEvent) => {\n        switch (e.key) {\n            case 'Enter':\n                e.preventDefault()\n                if (isSearching) {\n                    rememberEnterPressRef.current = true\n                    return\n                }\n                if (!results || results.length === 0) {\n                    return\n                }\n                onItemClick(results[selectedIndex])\n                break\n            case 'ArrowDown':\n                if (selectedIndex < results.length - 1) {\n                    setSelectedIndex(selectedIndex + 1)\n                }\n                break\n            case 'ArrowUp':\n                if (selectedIndex > 0) {\n                    setSelectedIndex(selectedIndex - 1)\n                }\n                break\n        }\n    }\n\n    let onItemClick = (item: SearchResultItem) => {\n        if (props.onSearchresultClick) {\n            props.onSearchresultClick(item)\n            return\n        }\n\n        if (item.urlSearchParams && new URLSearchParams(window.location.search).toString() !== item.urlSearchParams.toString()) {\n            setSearchText('')\n            setResults([])\n        }\n\n        addClickedSearchResultToPreviousSearches(item, props.keyForPinnedItems)\n\n        api.trackSearch(item.id, item.type)\n\n        let searchParams = new URLSearchParams()\n        let itemFilter = item.urlSearchParams?.get('itemFilter')\n        let apply = item.urlSearchParams?.get('apply')\n        if (itemFilter) {\n            searchParams.set('itemFilter', itemFilter)\n        }\n        if (apply) {\n            searchParams.set('apply', apply)\n        }\n\n        router.push(`${item.route}?${searchParams.toString()}`)\n    }\n\n    let noResultsFoundElement = (\n        <ListGroup.Item\n            key={-1}\n            style={getListItemStyle(-1)}\n            onContextMenu={e => {\n                handleSearchContextMenuForCurrentElement(e)\n            }}\n        >\n            <Image className={styles.searchResultIcon} height={32} width={32} src=\"/Barrier.png\" alt=\"\" />\n            No search results\n        </ListGroup.Item>\n    )\n\n    let getSelectedElement = (): JSX.Element => {\n        if (props.currentElement) {\n            return (\n                <h1 onContextMenu={e => handleSearchContextMenuForCurrentElement(e)} className={styles.current}>\n                    {props.currentElement}\n                </h1>\n            )\n        }\n        if (!props.selected) {\n            return <div />\n        }\n        return (\n            <h1 onContextMenu={e => handleSearchContextMenuForCurrentElement(e)} className={styles.current}>\n                <ClientOnly>\n                    <img\n                        crossOrigin=\"anonymous\"\n                        className=\"playerHeadIcon\"\n                        src={props.type === 'player' ? props.selected.iconUrl : api.getItemImageUrl({ tag: (props.selected as Item).tag })}\n                        height=\"32\"\n                        width=\"32\"\n                        alt=\"\"\n                        style={{ marginRight: '10px', cursor: 'pointer' }}\n                        onClick={() => {\n                            let type = getSetting(ITEM_ICON_TYPE, 'default')\n                            if (type === 'default') {\n                                setSetting(ITEM_ICON_TYPE, 'vanilla')\n                            } else {\n                                setSetting(ITEM_ICON_TYPE, 'default')\n                            }\n                            window.location.reload()\n                        }}\n                    />\n                </ClientOnly>\n                {props.selected.name || convertTagToName((props.selected as Item).tag)}\n                {props.enableReset ? (\n                    <ClearIcon onClick={props.onResetClick} style={{ cursor: 'pointer', color: 'red', marginLeft: '10px', fontWeight: 'bold' }} />\n                ) : null}\n            </h1>\n        )\n    }\n\n    let searchStyle: React.CSSProperties = {\n        backgroundColor: props.backgroundColor,\n        borderRadius: results.length > 0 || noResultsFound ? '0px 10px 0 0' : '0px 10px 10px 0px',\n        borderLeftWidth: 0,\n        borderBottomColor: results.length > 0 || noResultsFound ? '#444' : undefined\n    }\n\n    let searchIconStyle: React.CSSProperties = {\n        width: isSmall ? 'auto' : '58px',\n        borderRadius: results.length > 0 || noResultsFound ? '10px 0 0 0' : '10px 0px 0px 10px',\n        backgroundColor: props.backgroundColor || '#303030',\n        borderBottomColor: results.length > 0 || noResultsFound ? '#444' : undefined,\n        padding: isSmall ? '0px' : undefined\n    }\n\n    function getListItemStyle(i: number): React.CSSProperties {\n        let style = {\n            backgroundColor: i === selectedIndex ? props.backgroundColorSelected || '#444' : props.backgroundColor,\n            borderRadius: i === results.length - 1 ? '0 0 10px 10px' : '',\n            border: 0,\n            borderTop: i === 0 ? '1px solid #444' : 0,\n            borderTopWidth: i === 0 ? 0 : undefined,\n            fontWeigth: 'normal',\n            fontFamily: 'inherit'\n        }\n        if (results[i]) {\n            let isDuplicate = results.findIndex((element, index) => element.dataItem.name === results[i].dataItem.name && index !== i) !== -1\n            if (isDuplicate) {\n                return {\n                    ...getStyleForTier(results[i]?.tier),\n                    ...style\n                }\n            }\n        }\n        return style\n    }\n\n    function checkNameChange(uuid: string) {\n        api.triggerPlayerNameCheck(uuid).then(() => {\n            toast.success('A name check for the player was triggered. This may take a few minutes.')\n        })\n    }\n\n    function handleSearchContextMenuForCurrentElement(event) {\n        if (props.selected && props.type === 'player') {\n            event.preventDefault()\n            showPlayerContextMenu({ event: event })\n        }\n    }\n\n    function handleSearchContextMenuForSearchResult(event: React.MouseEvent<HTMLElement, MouseEvent>, searchResultItem: SearchResultItem) {\n        event.preventDefault()\n        showSearchItemContextMenu({ event: event, props: { item: searchResultItem } })\n    }\n\n    let currentItemContextMenuElement = (\n        <div>\n            <Menu id={PLAYER_SEARCH_CONEXT_MENU_ID} theme={'dark'}>\n                <Item\n                    onClick={params => {\n                        checkNameChange((props.selected as Player).uuid)\n                    }}\n                >\n                    <Refresh style={{ marginRight: '5px' }} />\n                    Trigger check if name has changed\n                </Item>\n            </Menu>\n        </div>\n    )\n\n    let searchItemContextMenuElement = (\n        <div>\n            <Menu id={SEARCH_RESULT_CONTEXT_MENU_ID} theme={'dark'}>\n                <Item\n                    onClick={params => {\n                        let item: SearchResultItem = params.props.item\n                        pinSearchResult(item, props.keyForPinnedItems)\n                        let index = results.findIndex(r => r.dataItem.name === item.dataItem.name)\n                        if (index !== -1) {\n                            let newResults = [...results]\n                            newResults[index].pinned = true\n                            newResults[index].isPreviousSearch = true\n                            setResults(newResults)\n                        }\n                        hideSearchItemContextMenu()\n                    }}\n                    hidden={params => !!params.props.item.pinned}\n                    closeOnClick\n                >\n                    <PushPinIcon />\n                    Pin search result\n                </Item>\n                <Item\n                    onClick={params => {\n                        let item: SearchResultItem = params.props.item\n                        unpinSearchResult(item, props.keyForPinnedItems)\n                        let index = results.findIndex(r => r.dataItem.name === item.dataItem.name)\n                        if (index !== -1) {\n                            let newResults = [...results]\n                            newResults[index].pinned = false\n                            setResults(newResults)\n                        }\n                        hideSearchItemContextMenu()\n                    }}\n                    hidden={params => !params.props.item.pinned}\n                    closeOnClick\n                >\n                    <PushPinIcon />\n                    Unpin search result\n                </Item>\n                <Separator />\n                <Item\n                    onClick={() => {\n                        api.sendFeedback('badSearchResults', {\n                            searchText: searchText,\n                            results: results\n                        })\n                    }}\n                >\n                    <WrongIcon style={{ color: 'red', marginRight: '5px' }} />I didn't find the thing I was looking for!\n                </Item>\n            </Menu>\n        </div>\n    )\n\n    return (\n        <div ref={searchElement} className={styles.search} style={isSmall ? { marginLeft: '-5px', marginRight: '-5px' } : {}}>\n            <Form autoComplete=\"off\">\n                <Form.Group className={styles.searchFormGroup}>\n                    {!isSmall && !props.hideNavbar ? <NavBar /> : ''}\n                    <InputGroup id=\"search-input-group\">\n                        <InputGroup.Text style={searchIconStyle}>\n                            {isSmall && !props.hideNavbar ? (\n                                <div style={{ width: '56px' }}>\n                                    <NavBar hamburgerIconStyle={{ marginRight: '0px', width: '56px' }} />\n                                </div>\n                            ) : (\n                                <SearchIcon />\n                            )}\n                        </InputGroup.Text>\n                        <Form.Control\n                            key=\"search\"\n                            autoFocus={!isMobile}\n                            style={searchStyle}\n                            type=\"text\"\n                            placeholder={props.placeholder || 'Search player/item'}\n                            id={'search-bar'}\n                            className=\"searchBar\"\n                            value={searchText}\n                            onChange={onSearchChange}\n                            onKeyDown={(e: any) => {\n                                onKeyPress(e)\n                            }}\n                            onClick={() => {\n                                if (!props.preventDisplayOfPreviousSearches && !noResultsFound && results.length === 0 && !searchText) {\n                                    let previousResuls = getFirstPreviousSearches(5, props.keyForPinnedItems)\n                                    setResults(previousResuls)\n                                }\n                            }}\n                        />\n                    </InputGroup>\n                </Form.Group>\n            </Form>\n            <ListGroup className={styles.searchResutList}>\n                {noResultsFound\n                    ? noResultsFoundElement\n                    : results.map((result, i) => (\n                          <ListGroup.Item\n                              key={result.id}\n                              action\n                              onClick={(e: any) => {\n                                  onItemClick(result)\n                              }}\n                              style={getListItemStyle(i)}\n                              className={result.isPreviousSearch ? styles.previousSearch : undefined}\n                              onContextMenu={event => {\n                                  handleSearchContextMenuForSearchResult(event, result)\n                              }}\n                          >\n                              {result.dataItem.iconUrl ? (\n                                  <Image\n                                      className={`${styles.searchResultIcon} playerHeadIcon`}\n                                      crossOrigin=\"anonymous\"\n                                      width={32}\n                                      height={32}\n                                      src={\n                                          result.dataItem._imageLoaded\n                                              ? result.dataItem.iconUrl\n                                              : 'data:image/gif;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mPMqAcAAVUA6UpAAT4AAAAASUVORK5CYII='\n                                      }\n                                      alt=\"\"\n                                      onLoad={() => {\n                                          result.dataItem._imageLoaded = true\n                                          setResults(results)\n                                          forceUpdate()\n                                      }}\n                                  />\n                              ) : (\n                                  <Spinner animation=\"border\" role=\"status\" variant=\"primary\" />\n                              )}\n                              {result.pinned ? <PushPinIcon style={{ marginRight: '5px' }} /> : null}\n                              {result.dataItem.name}\n                          </ListGroup.Item>\n                      ))}\n            </ListGroup>\n            <div className={styles.bar} style={{ marginTop: '20px' }}>\n                {getSelectedElement()}\n                {!props.hideOptions ? <OptionsMenu selected={props.selected} /> : null}\n            </div>\n            {searchItemContextMenuElement}\n            {currentItemContextMenuElement}\n        </div>\n    )\n}\n\nexport default Search\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAOA;AACA;;;AA3BA;;;;;;;;;;;;;;;;;;;;;;AA8CA,MAAM,+BAA+B;AACrC,MAAM,gCAAgC;AAEtC,SAAS,OAAO,KAAY;;IACxB,IAAI,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACrB,IAAI,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,IAAI,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB,EAAE;IAC3D,IAAI,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,IAAI,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,IAAI,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,IAAI,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,EAAE,MAAM,qBAAqB,EAAE,GAAG,CAAA,GAAA,uJAAA,CAAA,iBAAc,AAAD,EAAE;QACnD,IAAI;IACR;IACA,MAAM,EAAE,MAAM,yBAAyB,EAAE,SAAS,yBAAyB,EAAE,GAAG,CAAA,GAAA,uJAAA,CAAA,iBAAc,AAAD,EAAE;QAC3F,IAAI;IACR;IACA,MAAM,WAAW,CAAA,GAAA,kHAAA,CAAA,cAAW,AAAD;IAE3B,IAAI,wBAAwB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAEnC,IAAI,gBAAgB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC3B,IAAI,cAAc,CAAA,GAAA,kHAAA,CAAA,iBAAc,AAAD;IAE/B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACN,IAAI,CAAA,GAAA,qHAAA,CAAA,wBAAqB,AAAD,KAAK;gBACzB,WAAW,CAAA,GAAA,qHAAA,CAAA,wBAAqB,AAAD,MAAM,SAAS,IAAI,CAAC,WAAW,GAAG,OAAO;YAC5E;YACA,SAAS,gBAAgB,CAAC,SAAS,qBAAqB;YACxD;oCAAO;oBACH,SAAS,mBAAmB,CAAC,SAAS,qBAAqB;gBAC/D;;QACJ;2BAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACN,cAAc;YACd,WAAW,EAAE;QACjB;2BAAG;QAAC,MAAM,QAAQ;KAAC;IAEnB,IAAI,SAAS;QACT,IAAI,YAAY;QAChB,IAAI,iBAAiB,MAAM,cAAc,IAAI,oHAAA,CAAA,UAAG,CAAC,MAAM;QACvD,eAAe,WAAW,IAAI,CAAC,CAAA;YAC3B,8BAA8B;YAC9B,IACI,cAAc,OAAO,KAAK,QAC1B,cAAc,AAAC,AAAC,cAAc,OAAO,CAAoB,aAAa,CAAC,eAAoC,KAAK,EAClH;gBACE,IAAI,sBAAsB;uBAAI;iBAAc;gBAC5C,IAAI,CAAC,MAAM,gCAAgC,EAAE;oBACzC,sBAAsB,CAAA,GAAA,gIAAA,CAAA,oCAAiC,AAAD,EAAE,WAAW,eAAe,MAAM,iBAAiB;gBAC7G;gBAEA,iBAAiB;gBACjB,kBAAkB,oBAAoB,MAAM,KAAK;gBACjD,WAAW;gBACX,eAAe;gBAEf,IAAI,sBAAsB,OAAO,EAAE;oBAC/B,YAAY,mBAAmB,CAAC,EAAE;oBAClC,sBAAsB,OAAO,GAAG;gBACpC;YACJ;QACJ;IACJ;IAEA,IAAI,iBAAiB,CAAC;QAClB,IAAI,gBAAwB,AAAC,EAAE,MAAM,CAAsB,KAAK;QAChE,aAAa;QACb,cAAc;QACd,eAAe;QAEf,IAAI,kBAAkB,IAAI;YACtB,WAAW,EAAE;YACb,eAAe;YACf;QACJ;QAEA,kBAAkB;QAClB;IACJ;IAEA,SAAS,oBAAoB,GAAG;QAC5B,MAAM,WAAW,cAAc,OAAO;QACtC,IAAI,WAAW,IAAI,MAAM;QAEzB,GAAG;YACC,IAAI,aAAa,UAAU;gBACvB;YACJ;YACA,WAAW,AAAC,SAAiB,UAAU;QAC3C,QAAS,SAAS;QAElB,WAAW,EAAE;IACjB;IAEA,IAAI,aAAa,CAAC;QACd,OAAQ,EAAE,GAAG;YACT,KAAK;gBACD,EAAE,cAAc;gBAChB,IAAI,aAAa;oBACb,sBAAsB,OAAO,GAAG;oBAChC;gBACJ;gBACA,IAAI,CAAC,WAAW,QAAQ,MAAM,KAAK,GAAG;oBAClC;gBACJ;gBACA,YAAY,OAAO,CAAC,cAAc;gBAClC;YACJ,KAAK;gBACD,IAAI,gBAAgB,QAAQ,MAAM,GAAG,GAAG;oBACpC,iBAAiB,gBAAgB;gBACrC;gBACA;YACJ,KAAK;gBACD,IAAI,gBAAgB,GAAG;oBACnB,iBAAiB,gBAAgB;gBACrC;gBACA;QACR;IACJ;IAEA,IAAI,cAAc,CAAC;QACf,IAAI,MAAM,mBAAmB,EAAE;YAC3B,MAAM,mBAAmB,CAAC;YAC1B;QACJ;QAEA,IAAI,KAAK,eAAe,IAAI,IAAI,gBAAgB,OAAO,QAAQ,CAAC,MAAM,EAAE,QAAQ,OAAO,KAAK,eAAe,CAAC,QAAQ,IAAI;YACpH,cAAc;YACd,WAAW,EAAE;QACjB;QAEA,CAAA,GAAA,gIAAA,CAAA,2CAAwC,AAAD,EAAE,MAAM,MAAM,iBAAiB;QAEtE,oHAAA,CAAA,UAAG,CAAC,WAAW,CAAC,KAAK,EAAE,EAAE,KAAK,IAAI;QAElC,IAAI,eAAe,IAAI;QACvB,IAAI,aAAa,KAAK,eAAe,EAAE,IAAI;QAC3C,IAAI,QAAQ,KAAK,eAAe,EAAE,IAAI;QACtC,IAAI,YAAY;YACZ,aAAa,GAAG,CAAC,cAAc;QACnC;QACA,IAAI,OAAO;YACP,aAAa,GAAG,CAAC,SAAS;QAC9B;QAEA,OAAO,IAAI,CAAC,GAAG,KAAK,KAAK,CAAC,CAAC,EAAE,aAAa,QAAQ,IAAI;IAC1D;IAEA,IAAI,sCACA,6LAAC,iMAAA,CAAA,YAAS,CAAC,IAAI;QAEX,OAAO,iBAAiB,CAAC;QACzB,eAAe,CAAA;YACX,yCAAyC;QAC7C;;0BAEA,6LAAC,gIAAA,CAAA,UAAK;gBAAC,WAAW,6IAAA,CAAA,UAAM,CAAC,gBAAgB;gBAAE,QAAQ;gBAAI,OAAO;gBAAI,KAAI;gBAAe,KAAI;;;;;;YAAK;;OANzF,CAAC;;;;;IAWd,IAAI,qBAAqB;QACrB,IAAI,MAAM,cAAc,EAAE;YACtB,qBACI,6LAAC;gBAAG,eAAe,CAAA,IAAK,yCAAyC;gBAAI,WAAW,6IAAA,CAAA,UAAM,CAAC,OAAO;0BACzF,MAAM,cAAc;;;;;;QAGjC;QACA,IAAI,CAAC,MAAM,QAAQ,EAAE;YACjB,qBAAO,6LAAC;;;;;QACZ;QACA,qBACI,6LAAC;YAAG,eAAe,CAAA,IAAK,yCAAyC;YAAI,WAAW,6IAAA,CAAA,UAAM,CAAC,OAAO;;8BAC1F,6LAAC,0IAAA,CAAA,UAAU;8BACP,cAAA,6LAAC;wBACG,aAAY;wBACZ,WAAU;wBACV,KAAK,MAAM,IAAI,KAAK,WAAW,MAAM,QAAQ,CAAC,OAAO,GAAG,oHAAA,CAAA,UAAG,CAAC,eAAe,CAAC;4BAAE,KAAK,AAAC,MAAM,QAAQ,CAAU,GAAG;wBAAC;wBAChH,QAAO;wBACP,OAAM;wBACN,KAAI;wBACJ,OAAO;4BAAE,aAAa;4BAAQ,QAAQ;wBAAU;wBAChD,SAAS;4BACL,IAAI,OAAO,CAAA,GAAA,0HAAA,CAAA,aAAU,AAAD,EAAE,0HAAA,CAAA,iBAAc,EAAE;4BACtC,IAAI,SAAS,WAAW;gCACpB,CAAA,GAAA,0HAAA,CAAA,aAAU,AAAD,EAAE,0HAAA,CAAA,iBAAc,EAAE;4BAC/B,OAAO;gCACH,CAAA,GAAA,0HAAA,CAAA,aAAU,AAAD,EAAE,0HAAA,CAAA,iBAAc,EAAE;4BAC/B;4BACA,OAAO,QAAQ,CAAC,MAAM;wBAC1B;;;;;;;;;;;gBAGP,MAAM,QAAQ,CAAC,IAAI,IAAI,CAAA,GAAA,sHAAA,CAAA,mBAAgB,AAAD,EAAE,AAAC,MAAM,QAAQ,CAAU,GAAG;gBACpE,MAAM,WAAW,iBACd,6LAAC,6JAAA,CAAA,UAAS;oBAAC,SAAS,MAAM,YAAY;oBAAE,OAAO;wBAAE,QAAQ;wBAAW,OAAO;wBAAO,YAAY;wBAAQ,YAAY;oBAAO;;;;;2BACzH;;;;;;;IAGhB;IAEA,IAAI,cAAmC;QACnC,iBAAiB,MAAM,eAAe;QACtC,cAAc,QAAQ,MAAM,GAAG,KAAK,iBAAiB,iBAAiB;QACtE,iBAAiB;QACjB,mBAAmB,QAAQ,MAAM,GAAG,KAAK,iBAAiB,SAAS;IACvE;IAEA,IAAI,kBAAuC;QACvC,OAAO,UAAU,SAAS;QAC1B,cAAc,QAAQ,MAAM,GAAG,KAAK,iBAAiB,eAAe;QACpE,iBAAiB,MAAM,eAAe,IAAI;QAC1C,mBAAmB,QAAQ,MAAM,GAAG,KAAK,iBAAiB,SAAS;QACnE,SAAS,UAAU,QAAQ;IAC/B;IAEA,SAAS,iBAAiB,CAAS;QAC/B,IAAI,QAAQ;YACR,iBAAiB,MAAM,gBAAgB,MAAM,uBAAuB,IAAI,SAAS,MAAM,eAAe;YACtG,cAAc,MAAM,QAAQ,MAAM,GAAG,IAAI,kBAAkB;YAC3D,QAAQ;YACR,WAAW,MAAM,IAAI,mBAAmB;YACxC,gBAAgB,MAAM,IAAI,IAAI;YAC9B,YAAY;YACZ,YAAY;QAChB;QACA,IAAI,OAAO,CAAC,EAAE,EAAE;YACZ,IAAI,cAAc,QAAQ,SAAS,CAAC,CAAC,SAAS,QAAU,QAAQ,QAAQ,CAAC,IAAI,KAAK,OAAO,CAAC,EAAE,CAAC,QAAQ,CAAC,IAAI,IAAI,UAAU,OAAO,CAAC;YAChI,IAAI,aAAa;gBACb,OAAO;oBACH,GAAG,CAAA,GAAA,sHAAA,CAAA,kBAAe,AAAD,EAAE,OAAO,CAAC,EAAE,EAAE,KAAK;oBACpC,GAAG,KAAK;gBACZ;YACJ;QACJ;QACA,OAAO;IACX;IAEA,SAAS,gBAAgB,IAAY;QACjC,oHAAA,CAAA,UAAG,CAAC,sBAAsB,CAAC,MAAM,IAAI,CAAC;YAClC,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAClB;IACJ;IAEA,SAAS,yCAAyC,KAAK;QACnD,IAAI,MAAM,QAAQ,IAAI,MAAM,IAAI,KAAK,UAAU;YAC3C,MAAM,cAAc;YACpB,sBAAsB;gBAAE,OAAO;YAAM;QACzC;IACJ;IAEA,SAAS,uCAAuC,KAAgD,EAAE,gBAAkC;QAChI,MAAM,cAAc;QACpB,0BAA0B;YAAE,OAAO;YAAO,OAAO;gBAAE,MAAM;YAAiB;QAAE;IAChF;IAEA,IAAI,8CACA,6LAAC;kBACG,cAAA,6LAAC,uJAAA,CAAA,OAAI;YAAC,IAAI;YAA8B,OAAO;sBAC3C,cAAA,6LAAC,uJAAA,CAAA,OAAI;gBACD,SAAS,CAAA;oBACL,gBAAgB,AAAC,MAAM,QAAQ,CAAY,IAAI;gBACnD;;kCAEA,6LAAC,+JAAA,CAAA,UAAO;wBAAC,OAAO;4BAAE,aAAa;wBAAM;;;;;;oBAAK;;;;;;;;;;;;;;;;;IAO1D,IAAI,6CACA,6LAAC;kBACG,cAAA,6LAAC,uJAAA,CAAA,OAAI;YAAC,IAAI;YAA+B,OAAO;;8BAC5C,6LAAC,uJAAA,CAAA,OAAI;oBACD,SAAS,CAAA;wBACL,IAAI,OAAyB,OAAO,KAAK,CAAC,IAAI;wBAC9C,CAAA,GAAA,gIAAA,CAAA,kBAAe,AAAD,EAAE,MAAM,MAAM,iBAAiB;wBAC7C,IAAI,QAAQ,QAAQ,SAAS,CAAC,CAAA,IAAK,EAAE,QAAQ,CAAC,IAAI,KAAK,KAAK,QAAQ,CAAC,IAAI;wBACzE,IAAI,UAAU,CAAC,GAAG;4BACd,IAAI,aAAa;mCAAI;6BAAQ;4BAC7B,UAAU,CAAC,MAAM,CAAC,MAAM,GAAG;4BAC3B,UAAU,CAAC,MAAM,CAAC,gBAAgB,GAAG;4BACrC,WAAW;wBACf;wBACA;oBACJ;oBACA,QAAQ,CAAA,SAAU,CAAC,CAAC,OAAO,KAAK,CAAC,IAAI,CAAC,MAAM;oBAC5C,YAAY;;sCAEZ,6LAAC,+JAAA,CAAA,UAAW;;;;;wBAAG;;;;;;;8BAGnB,6LAAC,uJAAA,CAAA,OAAI;oBACD,SAAS,CAAA;wBACL,IAAI,OAAyB,OAAO,KAAK,CAAC,IAAI;wBAC9C,CAAA,GAAA,gIAAA,CAAA,oBAAiB,AAAD,EAAE,MAAM,MAAM,iBAAiB;wBAC/C,IAAI,QAAQ,QAAQ,SAAS,CAAC,CAAA,IAAK,EAAE,QAAQ,CAAC,IAAI,KAAK,KAAK,QAAQ,CAAC,IAAI;wBACzE,IAAI,UAAU,CAAC,GAAG;4BACd,IAAI,aAAa;mCAAI;6BAAQ;4BAC7B,UAAU,CAAC,MAAM,CAAC,MAAM,GAAG;4BAC3B,WAAW;wBACf;wBACA;oBACJ;oBACA,QAAQ,CAAA,SAAU,CAAC,OAAO,KAAK,CAAC,IAAI,CAAC,MAAM;oBAC3C,YAAY;;sCAEZ,6LAAC,+JAAA,CAAA,UAAW;;;;;wBAAG;;;;;;;8BAGnB,6LAAC,uJAAA,CAAA,YAAS;;;;;8BACV,6LAAC,uJAAA,CAAA,OAAI;oBACD,SAAS;wBACL,oHAAA,CAAA,UAAG,CAAC,YAAY,CAAC,oBAAoB;4BACjC,YAAY;4BACZ,SAAS;wBACb;oBACJ;;sCAEA,6LAAC,iKAAA,CAAA,UAAS;4BAAC,OAAO;gCAAE,OAAO;gCAAO,aAAa;4BAAM;;;;;;wBAAK;;;;;;;;;;;;;;;;;;IAM1E,qBACI,6LAAC;QAAI,KAAK;QAAe,WAAW,6IAAA,CAAA,UAAM,CAAC,MAAM;QAAE,OAAO,UAAU;YAAE,YAAY;YAAQ,aAAa;QAAO,IAAI,CAAC;;0BAC/G,6LAAC,uLAAA,CAAA,OAAI;gBAAC,cAAa;0BACf,cAAA,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;oBAAC,WAAW,6IAAA,CAAA,UAAM,CAAC,eAAe;;wBACxC,CAAC,WAAW,CAAC,MAAM,UAAU,iBAAG,6LAAC,kIAAA,CAAA,UAAM;;;;mCAAM;sCAC9C,6LAAC,mMAAA,CAAA,aAAU;4BAAC,IAAG;;8CACX,6LAAC,mMAAA,CAAA,aAAU,CAAC,IAAI;oCAAC,OAAO;8CACnB,WAAW,CAAC,MAAM,UAAU,iBACzB,6LAAC;wCAAI,OAAO;4CAAE,OAAO;wCAAO;kDACxB,cAAA,6LAAC,kIAAA,CAAA,UAAM;4CAAC,oBAAoB;gDAAE,aAAa;gDAAO,OAAO;4CAAO;;;;;;;;;;6DAGpE,6LAAC,sKAAA,CAAA,UAAU;;;;;;;;;;8CAGnB,6LAAC,uLAAA,CAAA,OAAI,CAAC,OAAO;oCAET,WAAW,CAAC;oCACZ,OAAO;oCACP,MAAK;oCACL,aAAa,MAAM,WAAW,IAAI;oCAClC,IAAI;oCACJ,WAAU;oCACV,OAAO;oCACP,UAAU;oCACV,WAAW,CAAC;wCACR,WAAW;oCACf;oCACA,SAAS;wCACL,IAAI,CAAC,MAAM,gCAAgC,IAAI,CAAC,kBAAkB,QAAQ,MAAM,KAAK,KAAK,CAAC,YAAY;4CACnG,IAAI,iBAAiB,CAAA,GAAA,gIAAA,CAAA,2BAAwB,AAAD,EAAE,GAAG,MAAM,iBAAiB;4CACxE,WAAW;wCACf;oCACJ;mCAjBI;;;;;;;;;;;;;;;;;;;;;;0BAsBpB,6LAAC,iMAAA,CAAA,YAAS;gBAAC,WAAW,6IAAA,CAAA,UAAM,CAAC,eAAe;0BACvC,iBACK,wBACA,QAAQ,GAAG,CAAC,CAAC,QAAQ,kBACjB,6LAAC,iMAAA,CAAA,YAAS,CAAC,IAAI;wBAEX,MAAM;wBACN,SAAS,CAAC;4BACN,YAAY;wBAChB;wBACA,OAAO,iBAAiB;wBACxB,WAAW,OAAO,gBAAgB,GAAG,6IAAA,CAAA,UAAM,CAAC,cAAc,GAAG;wBAC7D,eAAe,CAAA;4BACX,uCAAuC,OAAO;wBAClD;;4BAEC,OAAO,QAAQ,CAAC,OAAO,iBACpB,6LAAC,gIAAA,CAAA,UAAK;gCACF,WAAW,GAAG,6IAAA,CAAA,UAAM,CAAC,gBAAgB,CAAC,eAAe,CAAC;gCACtD,aAAY;gCACZ,OAAO;gCACP,QAAQ;gCACR,KACI,OAAO,QAAQ,CAAC,YAAY,GACtB,OAAO,QAAQ,CAAC,OAAO,GACvB;gCAEV,KAAI;gCACJ,QAAQ;oCACJ,OAAO,QAAQ,CAAC,YAAY,GAAG;oCAC/B,WAAW;oCACX;gCACJ;;;;;qDAGJ,6LAAC,6LAAA,CAAA,UAAO;gCAAC,WAAU;gCAAS,MAAK;gCAAS,SAAQ;;;;;;4BAErD,OAAO,MAAM,iBAAG,6LAAC,+JAAA,CAAA,UAAW;gCAAC,OAAO;oCAAE,aAAa;gCAAM;;;;;uCAAQ;4BACjE,OAAO,QAAQ,CAAC,IAAI;;uBAjChB,OAAO,EAAE;;;;;;;;;;0BAqChC,6LAAC;gBAAI,WAAW,6IAAA,CAAA,UAAM,CAAC,GAAG;gBAAE,OAAO;oBAAE,WAAW;gBAAO;;oBAClD;oBACA,CAAC,MAAM,WAAW,iBAAG,6LAAC,4IAAA,CAAA,UAAW;wBAAC,UAAU,MAAM,QAAQ;;;;;+BAAO;;;;;;;YAErE;YACA;;;;;;;AAGb;GA9ZS;;QACQ,qIAAA,CAAA,YAAS;QAOkB,uJAAA,CAAA,iBAAc;QAG0B,uJAAA,CAAA,iBAAc;QAG7E,kHAAA,CAAA,cAAW;QAKV,kHAAA,CAAA,iBAAc;;;KAnB3B;uCAgaM", "debugId": null}}]}