/* [project]/components/Premium/PremiumFeatures/PremiumFeatures.module.css [app-client] (css) */
.PremiumFeatures-module__Tm37xq__premiumFeatures .PremiumFeatures-module__Tm37xq__featureCard {
  margin-bottom: 20px;
}

.PremiumFeatures-module__Tm37xq__featureColumn {
  font-size: larger;
  text-align: left;
}

.PremiumFeatures-module__Tm37xq__featureColumnHeading {
  text-align: left;
}

.PremiumFeatures-module__Tm37xq__premiumFeatures {
  overflow-x: auto;
}

.PremiumFeatures-module__Tm37xq__premiumProductHeading {
  text-align: center;
}

.PremiumFeatures-module__Tm37xq__premiumProductHeading {
  text-align: center;
}

.PremiumFeatures-module__Tm37xq__premiumProductColumn {
  text-align: center;
}

#PremiumFeatures-module__Tm37xq__tooltipHoverId .tooltip-inner {
  max-width: 100%;
}

.PremiumFeatures-module__Tm37xq__ingamePriceHoverImage {
  width: 610px;
  height: 324px;
}

@media (width <= 992px) {
  .PremiumFeatures-module__Tm37xq__ingamePriceHoverImage {
    width: 305px;
    height: 162px;
  }
}

/*# sourceMappingURL=components_Premium_PremiumFeatures_PremiumFeatures_module_b52d8e88.css.map*/