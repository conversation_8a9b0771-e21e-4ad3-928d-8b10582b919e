/* [project]/components/GoogleSignIn/GoogleSignIn.module.css [app-client] (css) */
.GoogleSignIn-module__lTSYOa__googleButton {
  width: 250px;
  color-scheme: light;
}


/* [project]/components/NavBar/NavBar.module.css [app-client] (css) */
.NavBar-module__yBvhsG__navClosing {
  left: -270px;
  transition: all .5s;
}

.NavBar-module__yBvhsG__navOpen {
  transition: left .5s;
  left: 0 !important;
}

.NavBar-module__yBvhsG__hamburgerIcon {
  display: inline;
  width: 36px;
  height: 36px;
  cursor: pointer;
  margin-right: 12px;
}

.NavBar-module__yBvhsG__navBar {
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  z-index: 20;
  font-size: 1rem;
}

.NavBar-module__yBvhsG__logo {
  padding: 24px;
  font-weight: bold;
  font-size: 20px;
  letter-spacing: 1px;
  overflow: hidden;
  white-space: nowrap;
  display: flex;
  align-items: center;
}

.NavBar-module__yBvhsG__navBar #pro-sidebar {
  position: absolute;
  bottom: 0;
  z-index: 100;
  left: 0;
  top: 0;
  min-height: 100vh;
  border: none;
}

.NavBar-module__yBvhsG__navBar .ps-menu-button:hover {
  background-color: #505050 !important;
}

.NavBar-module__yBvhsG__menuItem {
  display: block !important;
}


/* [project]/components/Premium/PremiumFeatures/PremiumFeatures.module.css [app-client] (css) */
.PremiumFeatures-module__Tm37xq__premiumFeatures .PremiumFeatures-module__Tm37xq__featureCard {
  margin-bottom: 20px;
}

.PremiumFeatures-module__Tm37xq__featureColumn {
  font-size: larger;
  text-align: left;
}

.PremiumFeatures-module__Tm37xq__featureColumnHeading {
  text-align: left;
}

.PremiumFeatures-module__Tm37xq__premiumFeatures {
  overflow-x: auto;
}

.PremiumFeatures-module__Tm37xq__premiumProductHeading {
  text-align: center;
}

.PremiumFeatures-module__Tm37xq__premiumProductHeading {
  text-align: center;
}

.PremiumFeatures-module__Tm37xq__premiumProductColumn {
  text-align: center;
}

#PremiumFeatures-module__Tm37xq__tooltipHoverId .tooltip-inner {
  max-width: 100%;
}

.PremiumFeatures-module__Tm37xq__ingamePriceHoverImage {
  width: 610px;
  height: 324px;
}

@media (width <= 992px) {
  .PremiumFeatures-module__Tm37xq__ingamePriceHoverImage {
    width: 305px;
    height: 162px;
  }
}


/* [project]/components/Premium/Premium.module.css [app-client] (css) */
.Premium-module__qLYXbW__premiumPayment {
  margin-top: "";
}

.Premium-module__qLYXbW__label {
  width: 300px;
  float: left;
  margin: 0;
}

.Premium-module__qLYXbW__premiumProducts {
  display: flex;
  justify-content: space-around;
  align-content: center;
}

.Premium-module__qLYXbW__premiumProduct {
  width: calc(25% - 10px);
}

.Premium-module__qLYXbW__premiumProductLabel {
  text-align: center;
}

.Premium-module__qLYXbW__premiumPrice {
  text-align: center;
  font-size: x-large;
  font-weight: bold;
  margin: 0;
}

@media (width <= 1024px) {
  .Premium-module__qLYXbW__premiumPrice {
    font-size: medium;
  }

  .Premium-module__qLYXbW__premiumProductLabel {
    font-size: x-large;
  }

  .Premium-module__qLYXbW__premiumProduct {
    width: auto;
    margin-bottom: 20px;
  }

  .Premium-module__qLYXbW__premiumProducts {
    display: block;
  }
}

.Premium-module__qLYXbW__purchaseCard {
  margin-left: 50px;
}

.Premium-module__qLYXbW__sendCoflCoinsButton {
  float: right;
}

.Premium-module__qLYXbW__cancellationRightCheckbox {
  position: relative !important;
}

@media (width <= 480px) {
  .Premium-module__qLYXbW__sendCoflCoinsButton {
    float: none;
  }
}


/* [project]/components/CoflCoins/CoflCoinsPurchase.module.css [app-client] (css) */
.CoflCoinsPurchase-module__25Dhdq__productGrid {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-direction: initial;
  flex-wrap: wrap;
}

.CoflCoinsPurchase-module__25Dhdq__premiumPlanCard {
  width: 49%;
  margin-bottom: 25px;
}

@media (width <= 768px) {
  .CoflCoinsPurchase-module__25Dhdq__premiumPlanCard {
    width: 100%;
  }
}

.CoflCoinsPurchase-module__25Dhdq__premiumPrice {
  font-size: larger;
}

.CoflCoinsPurchase-module__25Dhdq__paymentOption {
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  padding-bottom: 1rem;
}

.CoflCoinsPurchase-module__25Dhdq__discount {
  color: #0f0;
  float: right;
}

.CoflCoinsPurchase-module__25Dhdq__manualRedirectLink {
  margin: 0;
}

.CoflCoinsPurchase-module__25Dhdq__paymentButtonWrapper {
  width: 40%;
}

.CoflCoinsPurchase-module__25Dhdq__paymentButton {
  width: 100%;
}

.CoflCoinsPurchase-module__25Dhdq__paymentLabel {
  width: 50%;
}


/* [project]/components/CoflCoins/CoflCoinsDisplay.module.css [app-client] (css) */
.CoflCoinsDisplay-module__3S8iUW__border {
  padding: .5rem;
  width: 420px;
  background-color: #505050;
  border-radius: .5rem;
}

.CoflCoinsDisplay-module__3S8iUW__border > :first-child {
  margin-top: 0;
}

.CoflCoinsDisplay-module__3S8iUW__border > :last-child {
  margin-bottom: 0;
}


/* [project]/components/Premium/BuyPremium/BuyPremium.module.css [app-client] (css) */
.BuyPremium-module__gXX4Mq__label {
  width: 200px;
  float: left;
  margin: 0;
}

.BuyPremium-module__gXX4Mq__coinBalance {
  float: right;
}

.BuyPremium-module__gXX4Mq__dropdown {
  display: inline;
  width: 110px;
}

.BuyPremium-module__gXX4Mq__dropdown::-webkit-scrollbar {
  background-color: #272b30;
}

.BuyPremium-module__gXX4Mq__dropdown::-webkit-scrollbar {
  width: 20px;
}

.BuyPremium-module__gXX4Mq__dropdown::-webkit-scrollbar-track {
  box-shadow: inset 0 0 5px gray;
  border-radius: 10px;
}

.BuyPremium-module__gXX4Mq__dropdown::-webkit-scrollbar-thumb {
  background: #3a3f44;
  border-radius: 10px;
}

.BuyPremium-module__gXX4Mq__dropdown::-webkit-scrollbar-thumb:hover {
  background: #7a8288;
}

@media (width <= 768px) {
  .BuyPremium-module__gXX4Mq__label {
    width: auto;
    margin-right: 15px;
  }

  .BuyPremium-module__gXX4Mq__coinBalance {
    float: none;
    margin-top: 20px;
  }

  .BuyPremium-module__gXX4Mq__coinBalance b {
    font-size: medium !important;
  }
}

@media (width <= 350px) {
  .BuyPremium-module__gXX4Mq__label {
    width: 100%;
  }
}

.BuyPremium-module__gXX4Mq__purchaseCard :not(.btn-check:checked) + .BuyPremium-module__gXX4Mq__priceRangeButton {
  background-color: #2a3644;
  border-color: #2a3644;
  filter: grayscale(70%);
  font-weight: bold;
  color: #d3d3d3;
}

.BuyPremium-module__gXX4Mq__purchaseCard .btn-check:checked + .BuyPremium-module__gXX4Mq__priceRangeButton {
  border-color: #fff;
  font-weight: bold;
}


/* [project]/components/Premium/BuyPremiumConfirmationDialog/BuyPremiumConfirmationDialog.module.css [app-client] (css) */
.BuyPremiumConfirmationDialog-module__o90etG__label {
  width: 200px;
  float: left;
  margin: 0;
}


/* [project]/components/TransferCoflCoins/TransferCoflCoinsSummary.module.css [app-client] (css) */
.TransferCoflCoinsSummary-module__ZfhhcG__label {
  float: left;
  width: 150px;
  font-weight: bold;
}

.TransferCoflCoinsSummary-module__ZfhhcG__returnButton {
  float: left;
  width: 40%;
}

.TransferCoflCoinsSummary-module__ZfhhcG__sendButton {
  float: right;
  width: 40%;
}


/* [project]/components/Premium/PremiumStatus/PremiumStatus.module.css [app-client] (css) */
.PremiumStatus-module__89CruW__premiumStatusLabel {
  width: 150px;
  float: left;
  margin: 0;
}


/* [project]/components/Premium/BuySubscription/BuySubscription.module.css [app-client] (css) */
.BuySubscription-module__Rzk2-G__purchaseButtonContainer {
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.BuySubscription-module__Rzk2-G__purchaseButton {
  width: 80%;
}


/*# sourceMappingURL=components_8cfc444e._.css.map*/
