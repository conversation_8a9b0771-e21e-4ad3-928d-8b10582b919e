(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["static/chunks/_46bd9f06._.js", {

"[project]/components/Providers/Providers.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Providers": (()=>Providers)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$jonkoops$2f$matomo$2d$tracker$2d$react$2f$es$2f$MatomoProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__MatomoProvider$3e$__ = __turbopack_context__.i("[project]/node_modules/@jonkoops/matomo-tracker-react/es/MatomoProvider.js [app-client] (ecmascript) <export default as MatomoProvider>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$jonkoops$2f$matomo$2d$tracker$2d$react$2f$es$2f$instance$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__createInstance$3e$__ = __turbopack_context__.i("[project]/node_modules/@jonkoops/matomo-tracker-react/es/instance.js [app-client] (ecmascript) <export default as createInstance>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$oauth$2f$google$2f$dist$2f$index$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@react-oauth/google/dist/index.esm.js [app-client] (ecmascript)");
'use client';
;
;
;
const matomoTrackingInstance = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$jonkoops$2f$matomo$2d$tracker$2d$react$2f$es$2f$instance$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__createInstance$3e$__["createInstance"])({
    urlBase: 'https://track.coflnet.com',
    siteId: 1
});
function Providers({ children }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$jonkoops$2f$matomo$2d$tracker$2d$react$2f$es$2f$MatomoProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__MatomoProvider$3e$__["MatomoProvider"], {
        value: matomoTrackingInstance,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$oauth$2f$google$2f$dist$2f$index$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["GoogleOAuthProvider"], {
            clientId: "570302890760-nlkgd99b71q4d61am4lpqdhen1penddt.apps.googleusercontent.com",
            children: children
        }, void 0, false, {
            fileName: "[project]/components/Providers/Providers.tsx",
            lineNumber: 13,
            columnNumber: 13
        }, this)
    }, void 0, false, {
        fileName: "[project]/components/Providers/Providers.tsx",
        lineNumber: 12,
        columnNumber: 9
    }, this);
}
_c = Providers;
var _c;
__turbopack_context__.k.register(_c, "Providers");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/OfflineBanner/OfflineBanner.module.css [app-client] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "offlineBanner": "OfflineBanner-module__7FQ3SW__offlineBanner",
  "slideFromTop": "OfflineBanner-module__7FQ3SW__slideFromTop",
});
}}),
"[project]/components/OfflineBanner/OfflineBanner.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "OfflineBanner": (()=>OfflineBanner)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$OfflineBanner$2f$OfflineBanner$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/components/OfflineBanner/OfflineBanner.module.css [app-client] (css module)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
function OfflineBanner(props) {
    _s();
    let [isOnline, setIsOnline] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "OfflineBanner.useEffect": ()=>{
            window.addEventListener('online', setOnlineState);
            window.addEventListener('offline', setOnlineState);
            return ({
                "OfflineBanner.useEffect": ()=>{
                    window.removeEventListener('online', setOnlineState);
                    window.removeEventListener('offline', setOnlineState);
                }
            })["OfflineBanner.useEffect"];
        }
    }["OfflineBanner.useEffect"], []);
    function setOnlineState() {
        setIsOnline(navigator.onLine);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        children: !isOnline ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            id: "offline-banner",
            className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$OfflineBanner$2f$OfflineBanner$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].offlineBanner,
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                style: {
                    color: 'white'
                },
                children: "No connection"
            }, void 0, false, {
                fileName: "[project]/components/OfflineBanner/OfflineBanner.tsx",
                lineNumber: 26,
                columnNumber: 21
            }, this)
        }, void 0, false, {
            fileName: "[project]/components/OfflineBanner/OfflineBanner.tsx",
            lineNumber: 25,
            columnNumber: 17
        }, this) : ''
    }, void 0, false, {
        fileName: "[project]/components/OfflineBanner/OfflineBanner.tsx",
        lineNumber: 23,
        columnNumber: 9
    }, this);
}
_s(OfflineBanner, "mRBquyBAMh60D2Q5WI/A8/L/7j4=");
_c = OfflineBanner;
var _c;
__turbopack_context__.k.register(_c, "OfflineBanner");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/utils/Base64Utils.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "atobUnicode": (()=>atobUnicode),
    "btoaUnicode": (()=>btoaUnicode)
});
function btoaUnicode(str) {
    return btoa(encodeURIComponent(str).replace(/%([0-9A-F]{2})/g, function(_match, p1) {
        return String.fromCharCode(parseInt(p1, 16));
    }));
}
function atobUnicode(str) {
    return decodeURIComponent(Array.prototype.map.call(atob(str), function(c) {
        return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
    }).join(''));
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/api/ApiTypes.d.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "CUSTOM_EVENTS": (()=>CUSTOM_EVENTS),
    "RequestType": (()=>RequestType),
    "SubscriptionType": (()=>SubscriptionType)
});
var RequestType = /*#__PURE__*/ function(RequestType) {
    RequestType["SEARCH"] = "search";
    RequestType["PLAYER_DETAIL"] = "playerDetails";
    RequestType["ITEM_PRICES"] = "pricerdicer";
    RequestType["BAZAAR_PRICES"] = "bazaarPrices";
    RequestType["AUCTION_DETAILS"] = "auction";
    RequestType["ITEM_DETAILS"] = "itemDetails";
    RequestType["PLAYER_AUCTION"] = "playerAuctions";
    RequestType["PLAYER_BIDS"] = "playerBids";
    RequestType["ALL_ENCHANTMENTS"] = "getEnchantments";
    RequestType["TRACK_SEARCH"] = "trackSearch";
    RequestType["PLAYER_NAME"] = "playerName";
    RequestType["PLAYER_NAMES"] = "playerNames";
    RequestType["SET_CONNECTION_ID"] = "setConId";
    RequestType["GET_VERSION"] = "version";
    RequestType["SUBSCRIBE"] = "subscribe";
    RequestType["UNSUBSCRIBE"] = "unsubscribe";
    RequestType["GET_SUBSCRIPTIONS"] = "subscriptions";
    RequestType["LOGIN_WITH_TOKEN"] = "loginWithToken";
    RequestType["STRIPE_PAYMENT_SESSION"] = "topup/stripe";
    RequestType["GET_PRODUCTS"] = "topup/options";
    RequestType["PREMIUM_EXPIRATION"] = "premiumExpiration";
    RequestType["FCM_TOKEN"] = "token";
    RequestType["GET_STRIPE_PRODUCTS"] = "getProducts";
    RequestType["GET_STRIPE_PRICES"] = "getPrices";
    RequestType["VALIDATE_PAYMENT_TOKEN"] = "gPurchase";
    RequestType["RECENT_AUCTIONS"] = "recentAuctions";
    RequestType["SUBSCRIBE_FLIPS"] = "subFlip";
    RequestType["UNSUBSCRIBE_FLIPS"] = "unsubFlip";
    RequestType["GET_FLIPS"] = "getFlips";
    RequestType["GET_FILTER"] = "getFilter";
    RequestType["NEW_AUCTIONS"] = "newAuctions";
    RequestType["NEW_PLAYERS"] = "newPlayers";
    RequestType["NEW_ITEMS"] = "newItems";
    RequestType["POPULAR_SEARCHES"] = "popularSearches";
    RequestType["ENDED_AUCTIONS"] = "endedAuctions";
    RequestType["GET_FLIP_BASED_AUCTIONS"] = "flipBased";
    RequestType["PAYPAL_PAYMENT"] = "topup/paypal";
    RequestType["LEMONSQUEEZY_PAYMENT"] = "topup/lemonsqueezy";
    RequestType["GET_REF_INFO"] = "referral/info";
    RequestType["SET_REF"] = "referral/referred/by";
    RequestType["ACTIVE_AUCTIONS"] = "activeAuctions";
    RequestType["FLIP_FILTERS"] = "flipFilters";
    RequestType["CONNECT_MINECRAFT_ACCOUNT"] = "conMc";
    RequestType["GET_ACCOUNT_INFO"] = "accountInfo";
    RequestType["ITEM_SEARCH"] = "item/search";
    RequestType["AUTHENTICATE_MOD_CONNECTION"] = "authCon";
    RequestType["FLIP_UPDATE_TIME"] = "flip/update/when";
    RequestType["PLAYER_SEARCH"] = "search/player";
    RequestType["GET_PROFITABLE_CRAFTS"] = "craft/profit";
    RequestType["GET_LOW_SUPPLY_ITEMS"] = "auctions/supply/low";
    RequestType["SEND_FEEDBACK"] = "sendFeedback";
    RequestType["TRIGGER_PLAYER_NAME_CHECK"] = "triggerNameCheck";
    RequestType["GET_PLAYER_PROFILES"] = "profile";
    RequestType["GET_CRAFTING_RECIPE"] = "craft/recipe";
    RequestType["GET_LOWEST_BIN"] = "lowestBin";
    RequestType["GET_BAZAAR_TAGS"] = "items/bazaar/tags";
    RequestType["ITEM_PRICE_SUMMARY"] = "item/price";
    RequestType["GET_KAT_FLIPS"] = "kat/profit";
    RequestType["GET_TRACKED_FLIPS_FOR_PLAYER"] = "flip/stats/player";
    RequestType["PURCHASE_WITH_COFLCOiNS"] = "service/purchase";
    RequestType["SUBSCRIBE_EVENTS"] = "subEvents";
    RequestType["GET_COFLCOIN_BALANCE"] = "getCoflBalance";
    RequestType["GET_FLIP_SETTINGS"] = "getFlipSettings";
    RequestType["SET_FLIP_SETTING"] = "setFlipSetting";
    RequestType["TRASFER_COFLCOINS"] = "transferCofl";
    RequestType["GET_BAZAAR_SNAPSHOT"] = "getBazaarSnapshot";
    RequestType["SUBSCRIBE_FLIPS_ANONYM"] = "subFlipAnonym";
    RequestType["GET_PRIVACY_SETTINGS"] = "getPrivacySettings";
    RequestType["SET_PRIVACY_SETTINGS"] = "setPrivacySettings";
    RequestType["CHECK_FOR_RAT"] = "checkForRat";
    RequestType["GET_PREMIUM_PRODUCTS"] = "premium/user/owns";
    RequestType["UNSUBSCRIBE_ALL"] = "unsubscribeAll";
    RequestType["GET_ITEM_NAMES"] = "items/names";
    RequestType["RELATED_ITEMS"] = "realtedItems";
    RequestType["CHECK_FILTER"] = "checkFilter";
    RequestType["OWNER_HISOTRY"] = "ownerHistory";
    RequestType["MAYOR_DATA"] = "mayorData";
    RequestType["INVENTORY_DATA"] = "inventoryData";
    RequestType["CREATE_TRADE_OFFER"] = "createTradeOffer";
    RequestType["DELETE_TRADE_OFFER"] = "deleteTradeOffer";
    RequestType["GET_TRADES"] = "getTrades";
    RequestType["GET_TRANSACTIONS"] = "getTransactions";
    RequestType["GET_NOTIFICATION_TARGETS"] = "getNotificationTargets";
    RequestType["ADD_NOTIFICATION_TARGETS"] = "addNotificationTargets";
    RequestType["DELETE_NOTIFICATION_TARGETS"] = "deleteNotificationTargets";
    RequestType["UPDATE_NOTIFICATION_TARGET"] = "updateNotificationTarget";
    RequestType["SEND_TEST_NOTIFICATION"] = "sendTestNotification";
    RequestType["GET_NOTIFICATION_SUBSCRIPTION"] = "getNotificationSubscription";
    RequestType["ADD_NOTIFICATION_SUBSCRIPTION"] = "addNotificationSubscription";
    RequestType["DELETE_NOTIFICATION_SUBSCRIPTION"] = "deleteNotificationSubscription";
    RequestType["GET_PUBLISHED_CONFIGS"] = "publishedConfigs";
    RequestType["LOAD_CONFIG"] = "loadConfig";
    RequestType["UPDATE_CONFIG"] = "updateConfig";
    RequestType["ARCHIVED_AUCTIONS"] = "archivedAuctions";
    RequestType["EXPORT_ARCHIVED_AUCTIONS"] = "exportArchivedAuctions";
    RequestType["GET_LINKVERTISE_LINK"] = "getLinkvertiseLink";
    RequestType["CREATE_PREMIUM_SUBSCRIPTION"] = "createPremiumSubscription";
    RequestType["DELETE_PREMIUM_SUBSCRIPTION"] = "deletePremiumSubscription";
    RequestType["PURCHASE_PREMIUM_SUBSCRIPTION"] = "purchasePremiumSubscription";
    RequestType["GET_CRAFTING_INSTRUCTIONS"] = "craft/instructions";
    return RequestType;
}({});
var SubscriptionType = /*#__PURE__*/ function(SubscriptionType) {
    SubscriptionType[SubscriptionType["NONE"] = 0] = "NONE";
    SubscriptionType[SubscriptionType["PRICE_LOWER_THAN"] = 1] = "PRICE_LOWER_THAN";
    SubscriptionType[SubscriptionType["PRICE_HIGHER_THAN"] = 2] = "PRICE_HIGHER_THAN";
    SubscriptionType[SubscriptionType["OUTBID"] = 4] = "OUTBID";
    SubscriptionType[SubscriptionType["SOLD"] = 8] = "SOLD";
    SubscriptionType[SubscriptionType["BIN"] = 16] = "BIN";
    SubscriptionType[SubscriptionType["USE_SELL_NOT_BUY"] = 32] = "USE_SELL_NOT_BUY";
    SubscriptionType[SubscriptionType["AUCTION"] = 64] = "AUCTION";
    SubscriptionType[SubscriptionType["PLAYER_CREATES_AUCTION"] = 128] = "PLAYER_CREATES_AUCTION";
    SubscriptionType[SubscriptionType["BOUGHT_ANY_AUCTION"] = 1024] = "BOUGHT_ANY_AUCTION";
    return SubscriptionType;
}({});
let CUSTOM_EVENTS = {
    FLIP_SETTINGS_CHANGE: 'flipSettingsChange',
    COFLCOIN_UPDATE: 'coflCoinRefresh',
    GOOGLE_LOGIN: 'googleLogin',
    BAZAAR_SNAPSHOT_UPDATE: 'bazaarSnapshotUpdate'
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/FilterElement/FilterType.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "FilterType": (()=>FilterType),
    "hasFlag": (()=>hasFlag)
});
var FilterType = /*#__PURE__*/ function(FilterType) {
    FilterType[FilterType["EQUAL"] = 1] = "EQUAL";
    FilterType[FilterType["HIGHER"] = 2] = "HIGHER";
    FilterType[FilterType["LOWER"] = 4] = "LOWER";
    FilterType[FilterType["DATE"] = 8] = "DATE";
    FilterType[FilterType["NUMERICAL"] = 16] = "NUMERICAL";
    FilterType[FilterType["RANGE"] = 32] = "RANGE";
    FilterType[FilterType["PLAYER"] = 64] = "PLAYER";
    FilterType[FilterType["SIMPLE"] = 128] = "SIMPLE";
    FilterType[FilterType["BOOLEAN"] = 256] = "BOOLEAN";
    FilterType[FilterType["PLAYER_WITH_RANK"] = 512] = "PLAYER_WITH_RANK";
    FilterType[FilterType["SHOW_ICON"] = 1024] = "SHOW_ICON";
    return FilterType;
}({});
function hasFlag(full, flag) {
    let result = full && flag && (full & flag) === flag;
    return result === null ? false : result;
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/utils/SSRUtils.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "getHeadMetadata": (()=>getHeadMetadata),
    "isClientSideRendering": (()=>isClientSideRendering)
});
function isClientSideRendering() {
    return "object" !== 'undefined';
}
function getHeadMetadata(title = 'Skyblock Auction House History | Hypixel SkyBlock AH history', description = 'Browse over 600 million auctions, and the bazaar of Hypixel SkyBlock.', imageUrl = 'https://sky.coflnet.com/logo192.png', keywords = [], embedTitle = 'Skyblock Auction House History | Hypixel SkyBlock AH history') {
    return {
        title: title,
        description: description,
        manifest: '/manifest.json',
        openGraph: {
            title: embedTitle,
            description: description,
            images: {
                url: imageUrl,
                height: 64,
                width: 64
            }
        },
        keywords: [
            ...keywords,
            'hypixel',
            'skyblock',
            'auction',
            'history',
            'bazaar',
            'tracker'
        ].join(',')
    };
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/utils/Formatter.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "camelCaseToSentenceCase": (()=>camelCaseToSentenceCase),
    "convertTagToName": (()=>convertTagToName),
    "enchantmentAndReforgeCompare": (()=>enchantmentAndReforgeCompare),
    "formatAsCoins": (()=>formatAsCoins),
    "formatDungeonStarsInString": (()=>formatDungeonStarsInString),
    "formatToPriceToShorten": (()=>formatToPriceToShorten),
    "getDecimalSeparator": (()=>getDecimalSeparator),
    "getLocalDateAndTime": (()=>getLocalDateAndTime),
    "getMinecraftColorCodedElement": (()=>getMinecraftColorCodedElement),
    "getNumberFromShortenString": (()=>getNumberFromShortenString),
    "getStyleForTier": (()=>getStyleForTier),
    "getThousandSeparator": (()=>getThousandSeparator),
    "numberWithThousandsSeparators": (()=>numberWithThousandsSeparators),
    "removeMinecraftColorCoding": (()=>removeMinecraftColorCoding)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SSRUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/SSRUtils.tsx [app-client] (ecmascript)");
;
;
;
function numberWithThousandsSeparators(number, thousandSeperator, decimalSeperator) {
    if (!number) {
        return '0';
    }
    return number.toLocaleString();
}
function convertTagToName(itemTag) {
    if (!itemTag) {
        return '';
    }
    // special case for PET_SKIN to avoid confusion
    if (itemTag === 'PET_SKIN') {
        return 'Pet Skin (unapplied)';
    }
    // words that should remain lowercase
    const exceptions = [
        'of',
        'the'
    ];
    function capitalizeWords(text) {
        return text.replace(/\w\S*/g, function(txt) {
            if (exceptions.findIndex((a)=>a === txt) > -1) {
                return txt;
            }
            return txt.charAt(0).toUpperCase() + txt.slice(1).toLowerCase();
        });
    }
    let formatted = itemTag.toString().replace(new RegExp('_', 'g'), ' ').toLowerCase();
    formatted = capitalizeWords(formatted);
    // special per item Formating
    formatted = formatted?.replace('Pet Item', '');
    if (formatted?.startsWith('Pet')) {
        formatted = formatted?.replace('Pet', '') + ' Pet';
    }
    if (formatted?.startsWith('Ring')) {
        formatted = formatted?.replace('Ring ', '') + ' Ring';
    }
    return formatted;
}
function camelCaseToSentenceCase(camelCase) {
    const exceptions = [
        'UId'
    ];
    if (exceptions.findIndex((a)=>a === camelCase) > -1) {
        return camelCase;
    }
    var result = camelCase.replace(/([A-Z])/g, ' $1');
    var finalResult = result.split(' ');
    var isFirstWord = true;
    finalResult.forEach((word, i)=>{
        if (word !== '' && isFirstWord) {
            isFirstWord = false;
            return;
        }
        finalResult[i] = word.toLowerCase();
    });
    return finalResult.join(' ');
}
function getStyleForTier(tier) {
    let tierColors = [
        {
            type: 'UNKNOWN',
            colourCode: 'black'
        },
        {
            type: 'COMMON',
            colourCode: 'black'
        },
        {
            type: 'UNCOMMON',
            colourCode: '#55ff55'
        },
        {
            type: 'RARE',
            colourCode: '#5555ff'
        },
        {
            type: 'EPIC',
            colourCode: '#aa00aa'
        },
        {
            type: 'LEGENDARY',
            colourCode: '#ffaa00'
        },
        {
            type: 'SPECIAL',
            colourCode: '#FF5555'
        },
        {
            type: 'VERY_SPECIAL',
            colourCode: '#FF5555'
        },
        {
            type: 'MYTHIC',
            colourCode: '#ff55ff'
        },
        {
            type: 'SUPREME',
            colourCode: '#AA0000'
        }
    ];
    let color;
    if (tier) {
        //!tier ? DEFAULT_COLOR : (TIER_COLORS[tier.toString().toUpperCase()] ||
        if (!isNaN(Number(tier))) {
            color = tierColors[tier];
        } else {
            color = tierColors.find((color)=>{
                return color.type === tier.toString().toUpperCase();
            });
        }
    }
    return {
        color: color ? color.colourCode : undefined,
        fontFamily: 'monospace',
        fontWeight: 'bold'
    };
}
function enchantmentAndReforgeCompare(a, b) {
    let aName = a.name ? a.name.toLowerCase() : '';
    let bName = b.name ? b.name.toLowerCase() : '';
    if (aName === 'any' || aName === 'none' && bName !== 'any') {
        return -1;
    }
    if (bName === 'any' || bName === 'none') {
        return 1;
    }
    return aName.localeCompare(bName);
}
function formatToPriceToShorten(num, decimals = 0) {
    let multMap = [
        {
            mult: 1e12,
            suffix: 'T'
        },
        {
            mult: 1e9,
            suffix: 'B'
        },
        {
            mult: 1e6,
            suffix: 'M'
        },
        {
            mult: 1e3,
            suffix: 'k'
        },
        {
            mult: 1,
            suffix: ''
        }
    ];
    let multIndex = multMap.findIndex((m)=>num >= m.mult);
    if (multIndex === -1) {
        multIndex = multMap.length - 1;
    }
    if (multIndex !== 0) {
        if (Math.round(num / multMap[multIndex].mult) === 1000) {
            multIndex -= 1;
        }
    }
    let mult = multMap[multIndex];
    return (num / mult.mult).toFixed(decimals) + mult.suffix;
}
function getThousandSeparator() {
    let langTag = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SSRUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isClientSideRendering"])() ? navigator.language : 'en';
    if (langTag.startsWith('en')) {
        return ',';
    } else {
        return '.';
    }
}
function getDecimalSeparator() {
    let langTag = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SSRUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isClientSideRendering"])() ? navigator.language : 'en';
    if (langTag.startsWith('en')) {
        return '.';
    } else {
        return ',';
    }
}
function getNumberFromShortenString(shortString) {
    if (!shortString) {
        return;
    }
    let val = [
        {
            value: 1e12,
            suffix: 'T'
        },
        {
            value: 1e12,
            suffix: 't'
        },
        {
            value: 1e9,
            suffix: 'B'
        },
        {
            value: 1e9,
            suffix: 'b'
        },
        {
            value: 1e6,
            suffix: 'M'
        },
        {
            value: 1e6,
            suffix: 'm'
        },
        {
            value: 1e3,
            suffix: 'K'
        },
        {
            value: 1e3,
            suffix: 'k'
        },
        {
            value: 1,
            suffix: ''
        }
    ].find((val)=>shortString.includes(val.suffix)) || {
        value: 1,
        suffix: ''
    };
    return parseFloat(shortString.at(-1) == val.suffix ? shortString.slice(0, -1) : shortString) * val.value;
}
function getLocalDateAndTime(d) {
    if (!d) {
        return '';
    }
    return d.toLocaleDateString() + ', ' + d.toLocaleTimeString();
}
function formatAsCoins(number) {
    if (typeof number === 'string') {
        try {
            number = parseInt(number);
        } catch  {
            return '';
        }
    }
    return `${numberWithThousandsSeparators(number)} Coins`;
}
function formatDungeonStarsInString(stringWithStars = '', style = {}, dungeonItemLevelString) {
    let yellowStarStyle = {
        color: '#ffaa00',
        fontWeight: 'normal',
        height: '100%'
    };
    let redStarStyle = {
        color: 'red',
        fontWeight: 'normal',
        height: '100%'
    };
    let itemNameStyle = {
        height: '32px',
        marginRight: '-5px'
    };
    let stars = stringWithStars?.match(/✪.*/gm);
    let numberOfMasterstars = 0;
    if (dungeonItemLevelString) {
        try {
            numberOfMasterstars = Math.max(parseInt(dungeonItemLevelString) - 5, 0);
        } catch  {}
    }
    if (!stars || stars.length === 0) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
            style: style,
            children: stringWithStars
        }, void 0, false, {
            fileName: "[project]/utils/Formatter.tsx",
            lineNumber: 233,
            columnNumber: 16
        }, this);
    }
    let starsString = stars[0];
    let itemName = stringWithStars.split(stars[0])[0];
    let starsLastChar = starsString.charAt(starsString.length - 1);
    let starWithNumber = starsLastChar === '✪' ? undefined : starsLastChar;
    let normalStarElement = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
        style: yellowStarStyle,
        children: '✪'.repeat(starsString.length - numberOfMasterstars)
    }, void 0, false, {
        fileName: "[project]/utils/Formatter.tsx",
        lineNumber: 241,
        columnNumber: 29
    }, this);
    if (starWithNumber) {
        normalStarElement = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
            style: yellowStarStyle,
            children: starsString.substring(0, starsString.length - 1)
        }, void 0, false, {
            fileName: "[project]/utils/Formatter.tsx",
            lineNumber: 243,
            columnNumber: 29
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
        style: style,
        children: [
            itemName ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                style: itemNameStyle,
                children: itemName
            }, void 0, false, {
                fileName: "[project]/utils/Formatter.tsx",
                lineNumber: 248,
                columnNumber: 25
            }, this) : null,
            " ",
            normalStarElement,
            starWithNumber || numberOfMasterstars ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                style: redStarStyle,
                children: starWithNumber ? starWithNumber : '✪'.repeat(numberOfMasterstars)
            }, void 0, false, {
                fileName: "[project]/utils/Formatter.tsx",
                lineNumber: 250,
                columnNumber: 17
            }, this) : null
        ]
    }, void 0, true, {
        fileName: "[project]/utils/Formatter.tsx",
        lineNumber: 247,
        columnNumber: 9
    }, this);
}
function getMinecraftColorCodedElement(text = '', autoFormat = true) {
    let styleMap = {
        '0': {
            color: '#000000'
        },
        '1': {
            color: '#0000aa'
        },
        '2': {
            color: '#00aa00'
        },
        '3': {
            color: '#00aaaa'
        },
        '4': {
            color: '#aa0000'
        },
        '5': {
            color: '#aa00aa'
        },
        '6': {
            color: '#ffaa00'
        },
        '7': {
            color: '#aaaaaa'
        },
        '8': {
            color: '#555555'
        },
        '9': {
            color: '#5555ff'
        },
        a: {
            color: '#55ff55'
        },
        b: {
            color: '#55ffff'
        },
        c: {
            color: '#FF5555'
        },
        d: {
            color: '#FF55FF'
        },
        e: {
            color: '#FFFF55'
        },
        f: {
            color: '#FFFFFF'
        },
        l: {
            fontWeight: 'bold'
        },
        n: {
            textDecorationLine: 'underline',
            textDecorationSkip: 'spaces'
        },
        o: {
            fontStyle: 'italic'
        },
        m: {
            textDecoration: 'line-through',
            textDecorationSkip: 'spaces'
        },
        r: {
            textDecoration: 'none',
            textDecorationLine: 'none',
            textDecorationSkip: 'none',
            fontWeight: 'normal',
            fontStyle: 'normal',
            color: '#FFFFFF'
        }
    };
    let splits = text.split('§');
    let elements = [];
    let currentStyle = {};
    splits.forEach((split, i)=>{
        if (i === 0) {
            if (split !== '') {
                elements.push(/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                    children: split
                }, i, false, {
                    fileName: "[project]/utils/Formatter.tsx",
                    lineNumber: 288,
                    columnNumber: 31
                }, this));
            }
            return;
        }
        let code = split.substring(0, 1);
        let text = autoFormat ? convertTagToName(split.substring(1, split.length)) : split.substring(1, split.length);
        // get new style. Use reset if a unknown color code is used
        let newStyle = styleMap[code] || styleMap['r'];
        currentStyle = {
            ...currentStyle,
            ...newStyle
        };
        elements.push(/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
            style: currentStyle,
            children: text
        }, i, false, {
            fileName: "[project]/utils/Formatter.tsx",
            lineNumber: 300,
            columnNumber: 13
        }, this));
    });
    function textContent(elem) {
        if (!elem) {
            return '';
        }
        if (typeof elem === 'string') {
            return elem;
        }
        const children = elem.props && elem.props.children;
        if (children instanceof Array) {
            return children.map(textContent).join('');
        }
        return textContent(children);
    }
    function addBreaks(elements) {
        const updatedElements = elements.map((element)=>{
            if (element.type === 'span' && element.props.children) {
                let text = textContent(element);
                if (text.includes('\n')) {
                    const parts = text.split('\n');
                    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cloneElement"])(element, element.props, /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: parts[0]
                            }, void 0, false, {
                                fileName: "[project]/utils/Formatter.tsx",
                                lineNumber: 330,
                                columnNumber: 29
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("br", {}, void 0, false, {
                                fileName: "[project]/utils/Formatter.tsx",
                                lineNumber: 331,
                                columnNumber: 29
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: parts[1]
                            }, void 0, false, {
                                fileName: "[project]/utils/Formatter.tsx",
                                lineNumber: 332,
                                columnNumber: 29
                            }, this)
                        ]
                    }, void 0, true));
                }
            }
            return element;
        });
        return updatedElements;
    }
    elements = addBreaks(elements);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
        children: elements
    }, void 0, false, {
        fileName: "[project]/utils/Formatter.tsx",
        lineNumber: 344,
        columnNumber: 12
    }, this);
}
function removeMinecraftColorCoding(text = '') {
    return text.replace(/§[0-9a-fk-or]/gi, '');
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/utils/Parser/ParseBMConfig.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "parseBMConfig": (()=>parseBMConfig),
    "parseBMName": (()=>parseBMName)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$FlipUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/FlipUtils.tsx [app-client] (ecmascript)");
;
// probably a better way to standardize this
const enchants = new Set([
    'cleave',
    'life_steal',
    'smite',
    'sharpness',
    'titan_killer',
    'giant_killer',
    'growth',
    'protection',
    'overload',
    'power'
]);
// bm doenst match on stuff like bustling
const reforges = new Set([
    'mossy'
]);
const attributes = new Set([
    'mana_pool',
    'arachno_resistance',
    'blazing_resistance',
    'breeze',
    'ender_resistance',
    'experience',
    'fortitude',
    'lifeline',
    'magic_find',
    'blazing_resistance',
    'life_regeneration',
    'mana_regeneration',
    'mending',
    'speed',
    'undead_resistance',
    'veteran',
    'dominance'
]);
// return the filter:{...} portion
const parseModifiers = (name, modifiers)=>{
    const filter = {};
    if (modifiers.length == 0) {
        return filter;
    }
    const modifiersList = modifiers.split('&');
    for (const modifier of modifiersList){
        const tokens = modifier.split(':');
        // weird case
        if (tokens[0] == 'ultimate_reiterate') {
            tokens[0] = 'ultimate_duplex';
        }
        if (tokens[0] == 'nil') {
            filter['Clean'] = 'yes';
        } else if (tokens.length == 1 && !reforges.has(tokens[0])) {
            filter[tokens[0]] = '>0';
        } else {
            if (tokens.length != 2) throw new Error(`malformed bm config at current modifier ${modifier} at ${modifiers} on ${name}`);
            const [arg, val] = tokens;
            if (arg == 'rarity_upgraded') {
                filter['Recombobulated'] = val;
            } else if (arg == 'stars') {
                // can only identify one level at a time in bm
                filter['Stars'] = `${val}-${val}`;
            } else if (arg.startsWith('ultimate_')) {
                filter[arg] = `${val}-${val}`;
            } else if (arg == 'candyUsed') {
                if (val == 'false') {
                    filter['Candy'] = '0';
                } else {
                    filter['Candy'] = '1-10';
                }
            } else if (arg == 'rounded_level') {
                filter['PetLevel'] = `<=${val}`;
            } else if (arg == 'heldItem') {
                filter['PetItem'] = val;
            } else if (enchants.has(arg) || attributes.has(arg)) {
                filter[arg] = `${val}-${val}`;
            } else if (reforges.has(arg)) {
                filter['Reforge'] = arg;
            } else if (arg == 'winning_bid_value') {
                const maxBid = name == 'MIDAS_STAFF' ? 100 : 50;
                let winningBid;
                if (val == 'high') {
                    winningBid = `>=${maxBid}m`;
                } else if (val == 'medium') {
                    winningBid = `${(maxBid * 2 / 3).toFixed(4)}m-${maxBid}m`;
                } else {
                    winningBid = `0-${(maxBid * 2 / 3).toFixed(4)}m`;
                }
                filter['WinningBid'] = winningBid;
            } else if (arg !== 'global') {
                throw new Error(`error parsing token ${modifier} at ${modifiers} on ${name}`);
            }
        }
    }
    return filter;
};
const petRarities = new Set([
    'COMMON',
    'UNCOMMON',
    'RARE',
    'EPIC',
    'LEGENDARY'
]);
const parseBMName = (entry)=>{
    let [name, modifiers] = entry.includes('=') ? entry.split('=') : [
        entry,
        ''
    ];
    const filter = {};
    // handle pets
    if (name.startsWith('PET_') && !name.startsWith('PET_ITEM_') && petRarities.has(name.substring(name.lastIndexOf('_') + 1))) {
        const i = name.lastIndexOf('_');
        const rarity = name.substring(i + 1);
        name = name.substring(0, i);
        filter['Rarity'] = rarity;
    }
    if (/^.*_RUNE_\d$/.test(name)) {
        const i = name.lastIndexOf('_');
        const level = name.substring(i + 1);
        name = name.substring(0, i);
        const incorrectIDReplacements = {
            PESTILENCE_RUNE: 'ZOMBIE_SLAYER_RUNE',
            MAGICAL_RUNE: 'MAGIC_RUNE'
        };
        // mistake in common filters
        if (name in incorrectIDReplacements) {
            name = incorrectIDReplacements[name];
        }
        const suffixIndex = name.indexOf('_RUNE');
        let shortenedName = name.substring(0, suffixIndex);
        name = `RUNE_${shortenedName}`;
        // end rune is common error, blood rune item id coded incorrect, rest dont have levels yet
        const exceptions = new Set([
            'RUNE_END',
            'RUNE_BLOOD',
            'RUNE_HEARTS',
            'RUNE_ICE',
            'RUNE_SNOW',
            'RUNE_MAGIC',
            'RUNE_ZOMBIE_SLAYER'
        ]);
        if (exceptions.has(name)) {
            return;
        }
        const levelNameReplacements = {
            RUNE_DRAGON: 'END',
            RUNE_HEARTS: 'COEURS',
            RUNE_ICE: 'GLACE',
            RUNE_SNOW: 'NEVE',
            ZOMBIE_SLAYER: 'PESTILENCE',
            RUNE_MAGIC: 'MAGICAL'
        };
        if (name in levelNameReplacements) {
            shortenedName = levelNameReplacements[name];
        }
        const runeLevel = `${shortenedName}_RUNE`.toLowerCase().replace(/(_.)|^./g, (group)=>group.toUpperCase().replace('_', ''));
        filter[runeLevel] = level;
    }
    if (name.startsWith('POTION_')) {
        const i = name.indexOf('_');
        name = `POTION_${name.substring(i + 1, name.length)}`;
    }
    // common error in popular filter
    if (name.startsWith('STARRED_FROZEN_BLAZE')) {
        const i = name.indexOf('_');
        name = name.substring(i + 1, name.length);
    }
    // random exceptions
    const replacements = {
        PIONEER_PICKAXE: 'ALPHA_PICK',
        FLAKE_THE_FISH: 'SNOWFLAKE_THE_FISH',
        SPIRIT_SCEPTRE: 'BAT_WAND',
        // a common filter has these typo so i will fix it here
        POCKET_ESPRESSO_MACINE: 'POCKET_ESPRESSO_MACHINE',
        ADVENT_CALENDER_DISPLAY: 'ADVENT_CALENDAR_DISPLAY'
    };
    if (name in replacements) name = replacements[name];
    // handle modifiers
    const modifierFilter = parseModifiers(name, modifiers);
    return {
        item: {
            tag: name
        },
        itemFilter: {
            ...filter,
            ...modifierFilter
        }
    };
};
const parseBMConfig = (input)=>{
    const output = {
        filter: __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$FlipUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DEFAULT_FLIP_SETTINGS"].FILTER,
        flipCustomizeSettings: __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$FlipUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DEFAULT_FLIP_SETTINGS"].FLIP_CUSTOMIZE,
        restrictions: __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$FlipUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DEFAULT_FLIP_SETTINGS"].RESTRICTIONS
    };
    // true_blacklist -> ForceBlacklist
    for (const TBLEntry of input.true_blacklist){
        output.restrictions.push({
            type: 'blacklist',
            item: {
                tag: TBLEntry
            },
            itemFilter: {
                ForceBlacklist: 'true'
            }
        });
    }
    // regular blacklist
    for (const BLEntry of input.blacklist){
        const parsed = parseBMName(BLEntry);
        if (parsed) output.restrictions.push({
            type: 'blacklist',
            ...parsed
        });
    }
    for (const [WLEntry, { profit, profit_percentage }] of Object.entries(input.whitelist)){
        const parsed = parseBMName(WLEntry);
        if (parsed) {
            const entry = {
                type: 'whitelist',
                ...parsed
            };
            entry['itemFilter']['MinProfit'] = `${profit}`;
            entry['itemFilter']['MinProfitPercentage'] = `${profit_percentage}`;
            output.restrictions.push(entry);
        }
    }
    output.filter.minProfit = input.global.profit;
    output.filter.minProfitPercent = input.global.profit_percentage;
    // probably more elegant way to do this
    output.filter.maxCost = 10 ** 10;
    return output;
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/utils/SettingsUtils.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "AUCTION_GRAPH_LEGEND_SELECTION": (()=>AUCTION_GRAPH_LEGEND_SELECTION),
    "AUTO_REDIRECT_FROM_LINKVERTISE_EXPLANATION": (()=>AUTO_REDIRECT_FROM_LINKVERTISE_EXPLANATION),
    "BAZAAR_GRAPH_LEGEND_SELECTION": (()=>BAZAAR_GRAPH_LEGEND_SELECTION),
    "BAZAAR_GRAPH_TYPE": (()=>BAZAAR_GRAPH_TYPE),
    "CANCELLATION_RIGHT_CONFIRMED": (()=>CANCELLATION_RIGHT_CONFIRMED),
    "CURRENTLY_USED_TAGS": (()=>CURRENTLY_USED_TAGS),
    "FLIPPER_FILTER_KEY": (()=>FLIPPER_FILTER_KEY),
    "FLIP_CUSTOMIZING_KEY": (()=>FLIP_CUSTOMIZING_KEY),
    "GOOGLE_EMAIL": (()=>GOOGLE_EMAIL),
    "GOOGLE_NAME": (()=>GOOGLE_NAME),
    "GOOGLE_PROFILE_PICTURE_URL": (()=>GOOGLE_PROFILE_PICTURE_URL),
    "HIDE_RELATED_ITEMS": (()=>HIDE_RELATED_ITEMS),
    "IGNORE_FLIP_TRACKING_PROFIT": (()=>IGNORE_FLIP_TRACKING_PROFIT),
    "ITEM_FILER_SHOW_ADVANCED": (()=>ITEM_FILER_SHOW_ADVANCED),
    "ITEM_FILTER_USE_COUNT": (()=>ITEM_FILTER_USE_COUNT),
    "ITEM_ICON_TYPE": (()=>ITEM_ICON_TYPE),
    "LAST_PREMIUM_PRODUCTS": (()=>LAST_PREMIUM_PRODUCTS),
    "LAST_USED_FILTER": (()=>LAST_USED_FILTER),
    "PREMIUM_EXPIRATION_NOFIFY_DATE_KEY": (()=>PREMIUM_EXPIRATION_NOFIFY_DATE_KEY),
    "RECENT_AUCTIONS_FETCH_TYPE_KEY": (()=>RECENT_AUCTIONS_FETCH_TYPE_KEY),
    "RESTRICTIONS_SETTINGS_KEY": (()=>RESTRICTIONS_SETTINGS_KEY),
    "USER_COUNTRY_CODE": (()=>USER_COUNTRY_CODE),
    "getCleanRestrictionsForApi": (()=>getCleanRestrictionsForApi),
    "getSetting": (()=>getSetting),
    "getSettingsObject": (()=>getSettingsObject),
    "handleSettingsImport": (()=>handleSettingsImport),
    "mapRestrictionsToApiFormat": (()=>mapRestrictionsToApiFormat),
    "mapSettingsToApiFormat": (()=>mapSettingsToApiFormat),
    "setSetting": (()=>setSetting),
    "setSettingsFromServerSide": (()=>setSettingsFromServerSide),
    "sleep": (()=>sleep),
    "storeUsedTagsInLocalStorage": (()=>storeUsedTagsInLocalStorage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-toastify/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/api/ApiHelper.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/api/ApiTypes.d.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterType$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/FilterElement/FilterType.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$FlipUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/FlipUtils.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SSRUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/SSRUtils.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Formatter$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/Formatter.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Parser$2f$ParseBMConfig$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/Parser/ParseBMConfig.tsx [app-client] (ecmascript)");
;
;
;
;
;
;
;
;
const LOCAL_STORAGE_SETTINGS_KEY = 'userSettings';
let settings = getInitUserSettings();
function getInitUserSettings() {
    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SSRUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isClientSideRendering"])()) {
        return {};
    }
    let item = localStorage.getItem(LOCAL_STORAGE_SETTINGS_KEY);
    // item === "\"{}\"" is a wrong state and has to be reset (fix for users that still have this in the local storage)
    if (!item || item === '"{}"') {
        item = '{}';
        localStorage.setItem(LOCAL_STORAGE_SETTINGS_KEY, item);
    }
    try {
        return JSON.parse(item);
    } catch  {
        return {};
    }
}
function getSetting(key, defaultValue = '') {
    return settings[key] || defaultValue;
}
function getSettingsObject(key, defaultValue) {
    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SSRUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isClientSideRendering"])()) {
        return defaultValue;
    }
    let object = settings[key] || JSON.stringify(defaultValue);
    let parsed;
    try {
        parsed = JSON.parse(object);
    } catch  {
        parsed = defaultValue;
    }
    return parsed;
}
function setSetting(key, value) {
    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SSRUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isClientSideRendering"])()) {
        return;
    }
    settings[key] = value;
    localStorage.setItem(LOCAL_STORAGE_SETTINGS_KEY, JSON.stringify(settings));
}
function setSettingsFromServerSide(settings, updateLocalSettings = true) {
    return new Promise((resolve)=>{
        settings.visibility = settings.visibility || {};
        settings.mod = settings.mod || {};
        settings.filters = settings.filters || {};
        let flipCustomizing = {
            hideCost: !settings.visibility.cost,
            hideEstimatedProfit: !settings.visibility.estProfit,
            hideLowestBin: !settings.visibility.lbin,
            hideSecondLowestBin: !settings.visibility.slbin,
            hideMedianPrice: !settings.visibility.medPrice,
            hideSeller: !settings.visibility.seller,
            hideVolume: !settings.visibility.volume,
            maxExtraInfoFields: settings.visibility.extraFields,
            hideProfitPercent: !settings.visibility.profitPercent,
            hideSellerOpenBtn: !settings.visibility.sellerOpenBtn,
            hideLore: !settings.visibility.lore,
            useLowestBinForProfit: settings.lbin,
            shortNumbers: settings.mod.shortNumbers,
            soundOnFlip: settings.mod.soundOnFlip,
            justProfit: settings.mod.justProfit,
            blockTenSecMsg: settings.mod.blockTenSecMsg,
            hideModChat: !settings.mod.chat,
            modFormat: settings.mod.format,
            modCountdown: settings.mod.countdown,
            disableLinks: !settings.visibility.links,
            hideCopySuccessMessage: !settings.visibility.copySuccessMessage,
            finders: __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$FlipUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FLIP_FINDERS"].filter((finder)=>{
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterType$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["hasFlag"])(parseInt(settings.finders), parseInt(finder.value));
            }).map((finder)=>parseInt(finder.value)),
            blockExport: settings.blockExport
        };
        let filter = {
            maxCost: settings.maxCost,
            minProfit: settings.minProfit,
            minProfitPercent: settings.minProfitPercent,
            minVolume: settings.minVolume,
            onlyBin: settings.onlyBin,
            onlyUnsold: settings.visibility.hideSold
        };
        let restrictions = getSettingsObject(RESTRICTIONS_SETTINGS_KEY, []);
        let itemMap = {};
        restrictions.forEach((restriction)=>{
            if (restriction.item?.tag) {
                itemMap[restriction.item.tag] = restriction.item?.name;
            }
        });
        let _addListToRestrictions = async function(list, type) {
            return new Promise((resolve, reject)=>{
                if (list) {
                    let newRestrictions = [];
                    let tagsToFindNamesFor = new Set();
                    let restrictionsToLoadNamesFor = [];
                    list.forEach((item)=>{
                        let itemName = item.displayName || itemMap[item.tag];
                        if (!item.tag) {
                            newRestrictions.push({
                                type: type,
                                itemFilter: item.filter,
                                tags: item.tags,
                                disabled: item.disabled
                            });
                        } else if (itemName && item.tag) {
                            newRestrictions.push({
                                type: type,
                                item: {
                                    tag: item.tag,
                                    name: itemName,
                                    iconUrl: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getItemImageUrl(item)
                                },
                                itemFilter: item.filter,
                                tags: item.tags,
                                disabled: item.disabled
                            });
                        } else {
                            tagsToFindNamesFor.add(item.tag);
                            restrictionsToLoadNamesFor.push({
                                type: type,
                                item: {
                                    tag: item.tag,
                                    name: '',
                                    iconUrl: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getItemImageUrl(item)
                                },
                                itemFilter: item.filter,
                                tags: item.tags,
                                disabled: item.disabled
                            });
                        }
                    });
                    if (tagsToFindNamesFor.size > 0) {
                        let tags = Array.from(tagsToFindNamesFor);
                        __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getItemNames(tags.map((tag)=>{
                            return {
                                tag: tag
                            };
                        })).then((nameMap)=>{
                            restrictionsToLoadNamesFor.forEach((newRestriction)=>{
                                newRestriction.item.name = nameMap[newRestriction.item.tag];
                                newRestrictions.push(newRestriction);
                            });
                            resolve(newRestrictions);
                        });
                    } else {
                        resolve(newRestrictions);
                    }
                } else {
                    resolve([]);
                }
            });
        };
        if (updateLocalSettings) {
            setSetting(FLIP_CUSTOMIZING_KEY, JSON.stringify(flipCustomizing));
            setSetting(FLIPPER_FILTER_KEY, JSON.stringify(filter));
        }
        Promise.all([
            _addListToRestrictions(settings.whitelist, 'whitelist'),
            _addListToRestrictions(settings.blacklist, 'blacklist')
        ]).then((results)=>{
            let newRestrictions = getCleanRestrictionsForApi(results[0].concat(results[1]));
            if (updateLocalSettings) {
                let newRestrictionsString = JSON.stringify(newRestrictions);
                let oldRestrictionsString = getSetting(RESTRICTIONS_SETTINGS_KEY, '[]');
                setSetting(RESTRICTIONS_SETTINGS_KEY, JSON.stringify(newRestrictions));
                if (newRestrictionsString !== oldRestrictionsString) {
                    document.dispatchEvent(new CustomEvent(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CUSTOM_EVENTS"].FLIP_SETTINGS_CHANGE, {
                        detail: {
                            apiUpdate: true
                        }
                    }));
                }
            }
            resolve({
                filter,
                flipCustomizing,
                restrictions: newRestrictions
            });
        });
    });
}
async function handleSettingsImport(importString) {
    let filter = __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$FlipUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DEFAULT_FLIP_SETTINGS"].FILTER;
    let flipCustomizeSettings = __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$FlipUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DEFAULT_FLIP_SETTINGS"].FLIP_CUSTOMIZE;
    let restrictions = __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$FlipUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DEFAULT_FLIP_SETTINGS"].RESTRICTIONS;
    let promises = [];
    try {
        let importObject = JSON.parse(importString);
        // Check for global field (BM format)
        if (importObject.global !== undefined) {
            const converted = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Parser$2f$ParseBMConfig$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseBMConfig"])(importObject);
            filter = converted.filter;
            flipCustomizeSettings = converted.flipCustomizeSettings;
            restrictions = converted.restrictions;
        } else if (importObject.whitelist !== undefined) {
            // Handle import in server-side format
            let settings = await setSettingsFromServerSide(importObject, false);
            filter = settings.filter;
            flipCustomizeSettings = settings.flipCustomizing;
            restrictions = settings.restrictions;
        } else {
            // Handle import in client-side format
            filter = importObject[FLIPPER_FILTER_KEY] ? JSON.parse(importObject[FLIPPER_FILTER_KEY]) : {};
            flipCustomizeSettings = importObject[FLIP_CUSTOMIZING_KEY] ? JSON.parse(importObject[FLIP_CUSTOMIZING_KEY]) : {};
            restrictions = importObject[RESTRICTIONS_SETTINGS_KEY] ? JSON.parse(importObject[RESTRICTIONS_SETTINGS_KEY]) : [];
        }
    } catch (e) {
        // Handle toml settings import
        try {
            var json = (await __turbopack_context__.r("[project]/node_modules/toml/index.js [app-client] (ecmascript, async loader)")(__turbopack_context__.i)).parse(importString);
            if (json.thresholds.blacklist.blacklist_bypass_percent) {
                restrictions.push({
                    type: 'whitelist',
                    itemFilter: {
                        MinProfitPercentage: (json.thresholds.blacklist.blacklist_bypass_percent * 100).toString()
                    }
                });
            }
            if (json.thresholds.blacklist.blacklist_bypass_profit) {
                restrictions.push({
                    type: 'whitelist',
                    itemFilter: {
                        MinProfit: json.thresholds.blacklist.blacklist_bypass_profit.toString()
                    }
                });
            }
            if (json.thresholds.blacklist.blacklist_bypass_volume) {
                restrictions.push({
                    type: 'whitelist',
                    itemFilter: {
                        Volume: json.thresholds.blacklist.blacklist_bypass_volume.toString()
                    }
                });
            }
            if (json.thresholds.blacklist.user_blacklist) {
                json.thresholds.blacklist.user_blacklist.split(',').forEach((user)=>{
                    restrictions.push({
                        type: 'blacklist',
                        itemFilter: {
                            Seller: user
                        }
                    });
                });
            }
            json.thresholds.blacklist.enchant_blacklist.split(',').forEach((item)=>{
                let restriction = {
                    type: 'blacklist'
                };
                let split = item.split('-');
                if (split[0].length > 0) {
                    restriction.itemFilter = {
                        Enchantment: split[0]
                    };
                    if (split[1] && split[1].length > 0) {
                        restriction.itemFilter.EnchantLvl = split[1];
                    }
                }
                restrictions.push(restriction);
            });
            let tagsToLoad = new Set();
            let entriesToLoadNamesFor = [];
            json.flipping.others.blacklist.split(',').forEach((item)=>{
                let restriction = {
                    type: 'blacklist'
                };
                let split = item.split('_+_');
                if (split[0].length > 0) {
                    let split2 = split[0].split('==');
                    if (split2[1] && split2[1].length > 0) {
                        restriction.itemFilter = {
                            Stars: split2[1].split('_STARRED_')[0]
                        };
                    }
                    restriction.item = {
                        tag: split2[0]
                    };
                }
                if (split[1] && split[1].length > 0) {
                    if (!restriction.itemFilter) {
                        restriction.itemFilter = {};
                    }
                    restriction.itemFilter.Rarity = split[1];
                }
                if (restriction.item?.tag) {
                    tagsToLoad.add(restriction.item?.tag);
                    entriesToLoadNamesFor.push(restriction);
                } else {
                    restrictions.push(restriction);
                }
            });
            let nameMap = await __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getItemNames(Array.from(tagsToLoad).map((tag)=>{
                return {
                    tag: tag
                };
            }));
            entriesToLoadNamesFor.forEach((entry)=>{
                entry.item.name = nameMap[entry.item.tag];
                entry.item.iconUrl = __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getItemImageUrl({
                    tag: entry.item.tag
                });
                restrictions.push(entry);
            });
            flipCustomizeSettings.soundOnFlip = !!json.flipping.others.enable_sounds;
            flipCustomizeSettings.hideModChat = !json.flipping.others.enable_chat;
            filter.minProfit = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Formatter$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getNumberFromShortenString"])(json.thresholds.threshold.threshold);
            filter.minProfitPercent = json.thresholds.threshold.threshold_percentage * 100;
            filter.maxCost = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Formatter$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getNumberFromShortenString"])(json.thresholds.threshold.max_cost);
        } catch  {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('The import of the filter settings failed. Please make sure this is a valid filter file.');
            return;
        }
    }
    if (flipCustomizeSettings.finders && localStorage.getItem('disableRiskyFinderImportProtection') !== 'true') {
        // These finders are considered "safe". All others are removed when importing a config
        // "1" = "Flipper", "2" = "Sniper", "4" = "Sniper (Median)"
        const safe_finders = [
            '1',
            '2',
            '4'
        ];
        let removed = [];
        let newFinders = flipCustomizeSettings.finders.filter((finder)=>{
            let isSafe = safe_finders.includes(finder.toString());
            if (!isSafe) {
                removed.push(__TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$FlipUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FLIP_FINDERS"].find((f)=>f.value === finder.toString()).label);
            }
            return isSafe;
        });
        if (removed.length > 0) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].warn(`Removed potentially dangerous finder${removed.length > 1 ? 's' : ''} (${removed.toString()}). Re-add them if you know what you are doing.`);
            await sleep(5000);
        }
        flipCustomizeSettings.finders = newFinders;
    }
    await Promise.allSettled(promises);
    if (restrictions.length > 1000) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"])('You are importing a large config! This may take a while...', {
            type: 'info',
            autoClose: false
        });
    }
    let toastId = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"])('Uploading config...', {
        type: 'info',
        autoClose: false
    });
    __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].subscribeFlips(restrictions || [], filter, flipCustomizeSettings, undefined, undefined, undefined, ()=>{
        setSetting(FLIPPER_FILTER_KEY, JSON.stringify(filter));
        setSetting(FLIP_CUSTOMIZING_KEY, JSON.stringify(flipCustomizeSettings));
        setSetting(RESTRICTIONS_SETTINGS_KEY, JSON.stringify(getCleanRestrictionsForApi(restrictions)));
        window.location.reload();
    }, ()=>{
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].dismiss(toastId);
    }, true);
}
function sleep(ms) {
    return new Promise((resolve)=>{
        setTimeout(()=>{
            resolve();
        }, ms);
    });
}
function mapSettingsToApiFormat(filter, flipSettings, restrictions) {
    return {
        whitelist: mapRestrictionsToApiFormat(restrictions.filter((restriction)=>restriction.type === 'whitelist')),
        blacklist: mapRestrictionsToApiFormat(restrictions.filter((restriction)=>restriction.type === 'blacklist')),
        minProfit: filter.minProfit || 0,
        minProfitPercent: filter.minProfitPercent || 0,
        minVolume: filter.minVolume || 0,
        maxCost: filter.maxCost || 0,
        onlyBin: filter.onlyBin,
        lbin: flipSettings.useLowestBinForProfit,
        mod: {
            justProfit: flipSettings.justProfit,
            soundOnFlip: flipSettings.soundOnFlip,
            shortNumbers: flipSettings.shortNumbers,
            blockTenSecMsg: flipSettings.blockTenSecMsg,
            format: flipSettings.modFormat,
            chat: !flipSettings.hideModChat,
            countdown: flipSettings.modCountdown
        },
        visibility: {
            cost: !flipSettings.hideCost,
            estProfit: !flipSettings.hideEstimatedProfit,
            lbin: !flipSettings.hideLowestBin,
            slbin: !flipSettings.hideSecondLowestBin,
            medPrice: !flipSettings.hideMedianPrice,
            seller: !flipSettings.hideSeller,
            volume: !flipSettings.hideVolume,
            extraFields: flipSettings.maxExtraInfoFields || 0,
            profitPercent: !flipSettings.hideProfitPercent,
            sellerOpenBtn: !flipSettings.hideSellerOpenBtn,
            lore: !flipSettings.hideLore,
            copySuccessMessage: !flipSettings.hideCopySuccessMessage,
            links: !flipSettings.disableLinks
        },
        blockExport: flipSettings.blockExport,
        finders: flipSettings.finders?.reduce((a, b)=>+a + +b, 0),
        changer: window.sessionStorage.getItem('sessionId')
    };
}
function mapRestrictionsToApiFormat(restrictions) {
    return restrictions.map((restriction)=>{
        return {
            tag: restriction.item?.tag,
            filter: restriction.itemFilter,
            displayName: restriction.item?.name,
            tags: restriction.tags,
            disabled: restriction.disabled
        };
    });
}
function storeUsedTagsInLocalStorage(restrictions) {
    let tags = new Set();
    restrictions.forEach((restriction)=>{
        if (restriction.tags) {
            restriction.tags.forEach((tag)=>tags.add(tag));
        }
    });
    localStorage.setItem(CURRENTLY_USED_TAGS, tags.size > 0 ? JSON.stringify(Array.from(tags)) : '[]');
}
function getCleanRestrictionsForApi(restrictions) {
    return restrictions.map((restriction)=>{
        let newRestriction = {
            type: restriction.type,
            tags: restriction.tags,
            disabled: restriction.disabled
        };
        if (restriction.item) {
            newRestriction.item = {
                tag: restriction.item?.tag,
                name: restriction.item?.name
            };
        }
        if (restriction.itemFilter) {
            newRestriction.itemFilter = {};
            Object.keys(restriction.itemFilter).forEach((key)=>{
                if (!key.startsWith('_')) {
                    newRestriction.itemFilter[key] = restriction.itemFilter[key];
                }
            });
        }
        return newRestriction;
    });
}
const FLIP_CUSTOMIZING_KEY = 'flipCustomizing';
const RESTRICTIONS_SETTINGS_KEY = 'flipRestrictions';
const FLIPPER_FILTER_KEY = 'flipperFilters';
const PREMIUM_EXPIRATION_NOFIFY_DATE_KEY = 'premiumExpirationNotifyDate';
const BAZAAR_GRAPH_TYPE = 'bazaarGraphType';
const BAZAAR_GRAPH_LEGEND_SELECTION = 'bazaarGraphLegendSelection';
const AUCTION_GRAPH_LEGEND_SELECTION = 'auctionGraphLegendSelection';
const RECENT_AUCTIONS_FETCH_TYPE_KEY = 'recentAuctionsFetchType';
const CANCELLATION_RIGHT_CONFIRMED = 'cancellationRightConfirmed';
const LAST_USED_FILTER = 'lastUsedFilter';
const IGNORE_FLIP_TRACKING_PROFIT = 'ignoreFlipTrackingProfit';
const LAST_PREMIUM_PRODUCTS = 'lastPremiumProducts';
const CURRENTLY_USED_TAGS = 'currentlyUsedTags';
const HIDE_RELATED_ITEMS = 'hideRelatedItems';
const GOOGLE_PROFILE_PICTURE_URL = 'googleProfilePictureUrl';
const GOOGLE_EMAIL = 'googleEmail';
const GOOGLE_NAME = 'googleName';
const USER_COUNTRY_CODE = 'userCountryCode';
const ITEM_FILTER_USE_COUNT = 'itemFilterUseCount';
const ITEM_FILER_SHOW_ADVANCED = 'itemFilterShowAdvanced';
const AUTO_REDIRECT_FROM_LINKVERTISE_EXPLANATION = 'autoRedirectFromLinkvertiseExplanation';
const ITEM_ICON_TYPE = 'itemIconType';
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/utils/FlipUtils.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "DEFAULT_FLIP_SETTINGS": (()=>DEFAULT_FLIP_SETTINGS),
    "DEFAULT_MOD_FORMAT": (()=>DEFAULT_MOD_FORMAT),
    "DEMO_FLIP": (()=>DEMO_FLIP),
    "FLIP_FINDERS": (()=>FLIP_FINDERS),
    "getCurrentProfitCalculationState": (()=>getCurrentProfitCalculationState),
    "getFlipCustomizeSettings": (()=>getFlipCustomizeSettings),
    "getFlipFinders": (()=>getFlipFinders)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SettingsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/SettingsUtils.tsx [app-client] (ecmascript)");
;
;
const DEMO_FLIP = {
    bin: true,
    cost: 45000000,
    item: {
        category: 'WEAPON',
        name: "Sharp Midas' Sword",
        tag: 'MIDAS_SWORD',
        tier: 'LEGENDARY',
        iconUrl: 'https://sky.coflnet.com/static/icon/MIDAS_SWORD'
    },
    profit: 5000000,
    lowestBin: 46000000,
    secondLowestBin: 47000000,
    median: 50000000,
    sellerName: 'Testuser',
    showLink: true,
    uuid: 'e4723502450544c8a3711a0a5b1e8cd0',
    volume: 5.874998615,
    sold: true,
    finder: 1,
    props: [
        'Top Bid: 50.000.000',
        'Recombobulated',
        'Hot Potato Book: 2',
        'Ultimate Wise 1',
        'Sharpness 6',
        'Thunderlord 6',
        'Vampirism 6',
        'Critical 6',
        'Luck 6',
        'Giant Killer 6',
        'Smite 6',
        'Ender Slayer 6',
        '...',
        '...',
        '...',
        '...',
        '...',
        '...',
        '...',
        '...',
        '...',
        '...',
        '...',
        '...',
        '...',
        '...',
        '...',
        '...',
        '...',
        '...'
    ]
};
const DEFAULT_MOD_FORMAT = '{0}: {1}{2} {3}{4} -> {5} (+{6} {7}) Med: {8} Lbin: {9} Volume: {10}';
function getFlipCustomizeSettings() {
    let settings;
    try {
        settings = JSON.parse((0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SettingsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getSetting"])(__TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SettingsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FLIP_CUSTOMIZING_KEY"]));
        // Fields that have special default values
        if (settings.hideSecondLowestBin !== false) {
            settings.hideSecondLowestBin = true;
        }
        if (settings.soundOnFlip !== false) {
            settings.soundOnFlip = true;
        }
        if (!settings.finders) {
            settings.finders = FLIP_FINDERS.map((finder)=>+finder.value);
        }
    } catch  {
        settings = DEFAULT_FLIP_SETTINGS.FLIP_CUSTOMIZE;
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SettingsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setSetting"])(__TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SettingsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FLIP_CUSTOMIZING_KEY"], JSON.stringify(DEFAULT_FLIP_SETTINGS.FLIP_CUSTOMIZE));
    }
    return settings;
}
const FLIP_FINDERS = [
    {
        value: '1',
        label: 'Flipper',
        shortLabel: 'FLIP',
        default: true,
        description: 'Is the classical flip finding algorithm using the Skyblock AH history database. It searches the history for similar items but searching for references takes time thus this is relatively slow.',
        selectable: true
    },
    {
        value: '2',
        label: 'Sniper',
        shortLabel: 'SNIPE',
        default: true,
        description: 'Is a classical sniping algorithm that stores prices in a dictionary grouped by any relevant modifiers. It only outputs flips that are below lbin and median for a combination of relevant modifiers. Its faster by about 3000x but may not find as many flips as the flipper.',
        selectable: true
    },
    {
        value: '4',
        label: 'Sniper (Median)',
        shortLabel: 'MSNIPE',
        default: true,
        description: "Uses the same algorithm as Sniper but doesn't require the item to be below lowest bin and only 5% below the median sell value.",
        selectable: true
    },
    {
        value: '8',
        label: 'AI',
        shortLabel: 'AI',
        default: false,
        description: '',
        selectable: false
    },
    {
        value: '16',
        label: 'User (whitelists)',
        shortLabel: 'User (whitelisted by you)',
        default: false,
        description: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
            children: [
                "Forwards all new auctions with a target value set to the starting bid (0 profit)",
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("br", {}, void 0, false, {
                    fileName: "[project]/utils/FlipUtils.tsx",
                    lineNumber: 118,
                    columnNumber: 17
                }, this),
                " You can use this together with whitelist/blacklist of ",
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("b", {
                    children: "Starting Bid"
                }, void 0, false, {
                    fileName: "[project]/utils/FlipUtils.tsx",
                    lineNumber: 118,
                    columnNumber: 78
                }, this),
                " and other filters to create your own flip rules.",
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("br", {}, void 0, false, {
                    fileName: "[project]/utils/FlipUtils.tsx",
                    lineNumber: 119,
                    columnNumber: 17
                }, this),
                " Different to the other finders this one won't pre-filter auctions its all up to you.\","
            ]
        }, void 0, true, {
            fileName: "[project]/utils/FlipUtils.tsx",
            lineNumber: 116,
            columnNumber: 13
        }, this),
        selectable: true
    },
    {
        value: '32',
        label: 'TFM',
        shortLabel: 'TFM',
        default: false,
        description: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
            children: [
                "These are flips from TFM (TheFlippingMod)",
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("br", {}, void 0, false, {
                    fileName: "[project]/utils/FlipUtils.tsx",
                    lineNumber: 132,
                    columnNumber: 17
                }, this),
                'The integration is currently under development.",'
            ]
        }, void 0, true, {
            fileName: "[project]/utils/FlipUtils.tsx",
            lineNumber: 130,
            columnNumber: 13
        }, this),
        selectable: true
    },
    {
        value: '64',
        label: 'Stonks',
        shortLabel: 'Stonks',
        default: false,
        description: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
            children: [
                "Experimental finder trying to predict the value of an item without references ",
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("br", {}, void 0, false, {
                    fileName: "[project]/utils/FlipUtils.tsx",
                    lineNumber: 145,
                    columnNumber: 95
                }, this),
                'This is under active development and will occasionally overvalue flips, use with caution.",'
            ]
        }, void 0, true, {
            fileName: "[project]/utils/FlipUtils.tsx",
            lineNumber: 144,
            columnNumber: 13
        }, this),
        selectable: true
    },
    {
        value: '128',
        label: 'External',
        shortLabel: 'External',
        default: false,
        description: '',
        selectable: false
    },
    {
        value: '1024',
        label: 'CraftCost',
        shortLabel: 'CraftCost',
        default: false,
        description: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
            children: [
                "Displays any auction that would be at least 5% more expensive to craft. ",
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("br", {}, void 0, false, {
                    fileName: "[project]/utils/FlipUtils.tsx",
                    lineNumber: 159,
                    columnNumber: 89
                }, this),
                "Sums up clean+modifier cost. ",
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("br", {}, void 0, false, {
                    fileName: "[project]/utils/FlipUtils.tsx",
                    lineNumber: 160,
                    columnNumber: 46
                }, this),
                "You can adjust weights of every attribute with the ",
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("b", {
                    children: "CraftCostWeight"
                }, void 0, false, {
                    fileName: "[project]/utils/FlipUtils.tsx",
                    lineNumber: 161,
                    columnNumber: 68
                }, this),
                " filter.",
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("br", {}, void 0, false, {
                    fileName: "[project]/utils/FlipUtils.tsx",
                    lineNumber: 161,
                    columnNumber: 98
                }, this),
                "Note that this does not indicate that the item will sell for that price."
            ]
        }, void 0, true, {
            fileName: "[project]/utils/FlipUtils.tsx",
            lineNumber: 158,
            columnNumber: 13
        }, this),
        selectable: true
    }
];
function getFlipFinders(finderValues) {
    let finders = FLIP_FINDERS.filter((option)=>finderValues.some((finder)=>finder.toString() === option.value));
    let notFoundFinder = {
        value: '',
        label: 'Unknown',
        shortLabel: 'Unknown',
        default: false,
        description: '',
        selectable: false
    };
    return finders.length > 0 ? finders : [
        notFoundFinder
    ];
}
const DEFAULT_FLIP_SETTINGS = {
    FLIP_CUSTOMIZE: {
        hideCost: false,
        hideEstimatedProfit: false,
        hideLowestBin: true,
        hideMedianPrice: false,
        hideSeller: true,
        hideVolume: false,
        maxExtraInfoFields: 3,
        hideCopySuccessMessage: false,
        hideSecondLowestBin: true,
        useLowestBinForProfit: false,
        disableLinks: false,
        justProfit: false,
        soundOnFlip: true,
        shortNumbers: false,
        hideProfitPercent: false,
        blockTenSecMsg: false,
        finders: FLIP_FINDERS.filter((finder)=>finder.default).map((finder)=>+finder.value),
        hideLore: true,
        hideModChat: false,
        hideSellerOpenBtn: false,
        modFormat: '',
        modCountdown: false
    },
    RESTRICTIONS: [],
    FILTER: {
        onlyBin: false,
        maxCost: 10000000000,
        minProfit: 0,
        minProfitPercent: 0,
        minVolume: 0,
        onlyUnsold: false,
        restrictions: []
    }
};
function getCurrentProfitCalculationState(flipCustomizeSettings) {
    if (flipCustomizeSettings.useLowestBinForProfit) {
        return 'lbin';
    }
    if (flipCustomizeSettings.finders?.length === 1 && flipCustomizeSettings.finders[0].toString() === '2') {
        return 'lbin';
    }
    if (flipCustomizeSettings.finders?.length === 1 && flipCustomizeSettings.finders[0].toString() === '1') {
        return 'median';
    }
    if (flipCustomizeSettings.finders?.length === 1 && flipCustomizeSettings.finders[0].toString() === '4') {
        return 'median';
    }
    if (flipCustomizeSettings.finders?.length === 2 && flipCustomizeSettings.finders.find((f)=>f.toString() === '1') && flipCustomizeSettings.finders.find((f)=>f.toString() === '4')) {
        return 'median';
    }
    return 'custom';
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/utils/Parser/APIResponseParser.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "parseAccountInfo": (()=>parseAccountInfo),
    "parseArchivedAuctions": (()=>parseArchivedAuctions),
    "parseAuction": (()=>parseAuction),
    "parseAuctionDetails": (()=>parseAuctionDetails),
    "parseBazaarOrder": (()=>parseBazaarOrder),
    "parseBazaarPrice": (()=>parseBazaarPrice),
    "parseBazaarSnapshot": (()=>parseBazaarSnapshot),
    "parseCraftingInstructions": (()=>parseCraftingInstructions),
    "parseCraftingRecipe": (()=>parseCraftingRecipe),
    "parseCraftingRecipeSlot": (()=>parseCraftingRecipeSlot),
    "parseDate": (()=>parseDate),
    "parseEnchantment": (()=>parseEnchantment),
    "parseFilterOption": (()=>parseFilterOption),
    "parseFlipAuction": (()=>parseFlipAuction),
    "parseFlipTrackingFlip": (()=>parseFlipTrackingFlip),
    "parseFlipTrackingResponse": (()=>parseFlipTrackingResponse),
    "parseInventoryData": (()=>parseInventoryData),
    "parseItem": (()=>parseItem),
    "parseItemBid": (()=>parseItemBid),
    "parseItemBidForList": (()=>parseItemBidForList),
    "parseItemPrice": (()=>parseItemPrice),
    "parseItemSummary": (()=>parseItemSummary),
    "parseKatFlip": (()=>parseKatFlip),
    "parseLowSupplyItem": (()=>parseLowSupplyItem),
    "parseMayorData": (()=>parseMayorData),
    "parseMinecraftConnectionInfo": (()=>parseMinecraftConnectionInfo),
    "parseOwnerHistory": (()=>parseOwnerHistory),
    "parsePaymentResponse": (()=>parsePaymentResponse),
    "parsePlayer": (()=>parsePlayer),
    "parsePlayerDetails": (()=>parsePlayerDetails),
    "parsePopularSearch": (()=>parsePopularSearch),
    "parsePremiumProducts": (()=>parsePremiumProducts),
    "parsePremiumSubscription": (()=>parsePremiumSubscription),
    "parsePrivacySettings": (()=>parsePrivacySettings),
    "parseProducts": (()=>parseProducts),
    "parseProfitableCrafts": (()=>parseProfitableCrafts),
    "parseRecentAuction": (()=>parseRecentAuction),
    "parseRefInfo": (()=>parseRefInfo),
    "parseSearchResultItem": (()=>parseSearchResultItem),
    "parseSkyblockProfile": (()=>parseSkyblockProfile),
    "parseSubscription": (()=>parseSubscription),
    "parseSubscriptionTypes": (()=>parseSubscriptionTypes),
    "parseTradeObject": (()=>parseTradeObject),
    "parseTransaction": (()=>parseTransaction)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/api/ApiHelper.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/api/ApiTypes.d.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterType$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/FilterElement/FilterType.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$FlipUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/FlipUtils.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Formatter$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/Formatter.tsx [app-client] (ecmascript)");
;
;
;
;
;
function parseItemBidForList(bid) {
    return {
        uuid: bid.uuid || bid.auctionId,
        end: parseDate(bid.end),
        item: {
            name: bid.itemName,
            tag: bid.tag
        },
        highestBid: bid.highestBid,
        highestOwn: bid.highestOwnBid,
        bin: bid.bin
    };
}
function parseItemBid(bid) {
    return {
        auctionId: bid.auctionId,
        amount: bid.amount,
        bidder: parsePlayer(bid.bidder),
        timestamp: parseDate(bid.timestamp),
        profileId: bid.profileId,
        bin: bid.bin
    };
}
function parseAuction(auction) {
    let parsedAuction = {
        uuid: auction.uuid || auction.auctionId,
        end: parseDate(auction.end),
        item: {
            tag: auction.tag,
            name: auction.itemName || auction.name
        },
        startingBid: auction.startingBid,
        highestBid: auction.highestBid,
        bin: auction.bin
    };
    parsedAuction.item.iconUrl = __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getItemImageUrl(parsedAuction.item);
    return parsedAuction;
}
function parsePlayerDetails(playerDetails) {
    return {
        bids: playerDetails.bids.map((bid)=>{
            return {
                uuid: bid.uuid,
                highestOwn: bid.highestOwn,
                end: parseDate(bid.end),
                highestBid: bid.highestBid,
                item: {
                    tag: bid.tag,
                    name: bid.itemName
                }
            };
        }),
        auctions: playerDetails.auctions.map((auction)=>{
            return {
                uuid: auction.auctionId,
                highestBid: auction.highestBid,
                end: parseDate(auction.end),
                item: {
                    tag: auction.tag,
                    name: auction.itemName
                },
                bin: auction.bin
            };
        })
    };
}
function parseItemPrice(priceData) {
    return {
        time: parseDate(priceData.time),
        avg: priceData.avg,
        max: priceData.max,
        min: priceData.min,
        volume: priceData.volume
    };
}
function parseItem(item) {
    const CRAB_HAT_IMAGES = {
        red: 'https://mc-heads.net/head/56b61f826dd6bfc1e3191a8369aeb0435c5a5335563431a538585ad039da1e0c',
        orange: 'https://mc-heads.net/head/ae38a15704089676a24e9eeccf4c290644d352c7d8f2b4135fa3538625107db',
        yellow: 'https://mc-heads.net/head/6b92684647051bd27ee04adb4098ee9bccca45a726a7cbf38e98b7e75cb889f4',
        lime: 'https://mc-heads.net/head/993c60fd0dd130695e378eef010a7d3c5dfde77f6b82b20b8124cfb830017ff',
        green: 'https://mc-heads.net/head/98d99983ab5986921251a29bba96c8e734f5de084a296cb627bcd64fd0fba593',
        aqua: 'https://mc-heads.net/head/2e1556958b1df4fd6228c9dcbd8c053f5d8902a41c4a59376bd0df1c60be8369',
        purple: 'https://mc-heads.net/head/2e3af5824014b57f4a4a84d4bb7fb88cac9e4ac75d00c7adb09dfe5ab737e224',
        pink: 'https://mc-heads.net/head/effaf0dc89da58bd1ed08f917407853e58d7bcbf5e6b5f33586389eb863a5bbd',
        black: 'https://mc-heads.net/head/cb85828267e59e83edc3bef235102e43fb70922622ccc3809a326a8c5632199a'
    };
    const BALLOON_HAT_IMAGES = {
        red: 'https://mc-heads.net/head/567f93963ad7ac3abdc49d02c9861fa2d45d00da1a7b31193ceb246313d39bc5',
        orange: 'https://mc-heads.net/head/841c5e8e0637acee09b68fab743db66c6714938c73792bc95acb1f393478144b',
        yellow: 'https://mc-heads.net/head/a90d9adef4732e5832448973e3557fce8a2e9ec8129ed32143eb666b4fa88ab2',
        lime: 'https://mc-heads.net/head/fb0209c346e04c3253f492ab904ef6e472faa617c681706f9153a116aa6481c2',
        green: 'https://mc-heads.net/head/8ba968d27dd90f67434e818646b2b0042946b67a14c594b31698dae309cb52a4',
        aqua: 'https://mc-heads.net/head/6432dda190146d4ad7a8569b389aea777684c44fa2624dcf74016467765e9693',
        purple: 'https://mc-heads.net/head/98b97944067281fa1361aa43ac522fdf190e50d65479f89a35e8cf87154e005c',
        pink: 'https://mc-heads.net/head/2ad4c874756ab9672aec51d53b9b94e6c9b5c6e7e0a2d44928df2186f4791e96',
        black: 'https://mc-heads.net/head/63bdb1e21a9fdf98aff67f0ff4e7dfada05f340210dd70b688c09c3cf50f6410'
    };
    let parsed = {
        tag: item.tag,
        name: item.altNames && item.altNames[0] && item.altNames[0].Name ? item.altNames[0].Name : item.itemName || item.name,
        category: item.category,
        iconUrl: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getItemImageUrl(item),
        tier: item.tier,
        bazaar: (0, __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterType$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["hasFlag"])(item.flags, 1)
    };
    if (item.flatNbt && item.flatNbt['party_hat_color'] && item.tag === 'PARTY_HAT_CRAB') {
        parsed.iconUrl = CRAB_HAT_IMAGES[item.flatNbt['party_hat_color']] || parsed.iconUrl;
    }
    if (item.flatNbt && item.flatNbt['party_hat_color'] && item.tag === 'BALLOON_HAT_2024') {
        parsed.iconUrl = BALLOON_HAT_IMAGES[item.flatNbt['party_hat_color']] || parsed.iconUrl;
    }
    return parsed;
}
function parseEnchantment(enchantment) {
    console.log(enchantment);
    return {
        id: enchantment.id,
        level: enchantment.level,
        name: enchantment.type ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Formatter$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["convertTagToName"])(enchantment.type) : '',
        color: enchantment.color
    };
}
function parseSearchResultItem(item) {
    let _getRoute = ()=>{
        switch(item.type){
            case 'filter':
                return '/item/' + item.id.split('?')[0];
            case 'item':
                return '/item/' + item.id;
            case 'player':
                return '/player/' + item.id;
            case 'auction':
                return '/auction/' + item.id;
        }
        return '';
    };
    return {
        dataItem: {
            name: item.name,
            iconUrl: item.img ? 'data:image/png;base64,' + item.img : item.type === 'item' ? __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getItemImageUrl(item) : __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getItemImageUrl(item) + '?size=8'
        },
        type: item.type,
        route: _getRoute(),
        urlSearchParams: item.type === 'filter' ? new URLSearchParams(item.id.split('?')[1] + '&apply=true') : undefined,
        id: item.id,
        tier: item.tier
    };
}
function parsePlayer(player) {
    if (typeof player === 'string') {
        player = {
            uuid: player,
            name: player
        };
    }
    return {
        name: player.name,
        uuid: player.uuid,
        iconUrl: player.iconUrl ? player.iconUrl + '?size=8' : 'https://crafatar.com/avatars/' + player.uuid + '?size=8'
    };
}
function parseAuctionDetails(auctionDetails) {
    return {
        auction: {
            uuid: auctionDetails.uuid,
            end: parseDate(auctionDetails.end),
            highestBid: auctionDetails.highestBidAmount,
            startingBid: auctionDetails.startingBid,
            item: parseItem(auctionDetails),
            bin: auctionDetails.bin
        },
        start: parseDate(auctionDetails.start),
        anvilUses: auctionDetails.anvilUses,
        auctioneer: parsePlayer(auctionDetails.auctioneer),
        bids: auctionDetails.bids.map((bid)=>{
            return parseItemBid(bid);
        }),
        claimed: auctionDetails.claimed,
        coop: auctionDetails.coop,
        count: auctionDetails.count,
        enchantments: auctionDetails.enchantments.map((enchantment)=>{
            return parseEnchantment(enchantment);
        }),
        profileId: auctionDetails.profileId,
        reforge: auctionDetails.reforge,
        nbtData: auctionDetails.flatNbt ? auctionDetails.flatNbt : undefined,
        itemCreatedAt: parseDate(auctionDetails.itemCreatedAt),
        uuid: auctionDetails.uuid
    };
}
function parseSubscriptionTypes(typeInNumeric) {
    let keys = Object.keys(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SubscriptionType"]);
    let subTypes = [];
    for(let i = keys.length; i >= 0; i--){
        let enumValue = __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SubscriptionType"][keys[i]];
        if (typeof __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SubscriptionType"][enumValue] === 'number') {
            let number = parseInt(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SubscriptionType"][enumValue]);
            if (number <= typeInNumeric && number > 0) {
                typeInNumeric -= number;
                subTypes.push(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SubscriptionType"][number.toString()]);
            }
        }
    }
    return subTypes;
}
function _getTypeFromSubTypes(subTypes, itemFilter) {
    let type;
    switch(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SubscriptionType"][subTypes[0].toString()]){
        case __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SubscriptionType"].BIN:
        case __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SubscriptionType"].PRICE_HIGHER_THAN:
        case __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SubscriptionType"].PRICE_LOWER_THAN:
        case __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SubscriptionType"].USE_SELL_NOT_BUY:
            if (!itemFilter) {
                type = 'bazaar';
            } else {
                type = 'item';
            }
            break;
        case __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SubscriptionType"].OUTBID:
        case __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SubscriptionType"].SOLD:
        case __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SubscriptionType"].PLAYER_CREATES_AUCTION:
        case __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SubscriptionType"].BOUGHT_ANY_AUCTION:
            type = 'player';
            break;
        case __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SubscriptionType"].AUCTION:
            type = 'auction';
            break;
    }
    return type;
}
function parseSubscription(subscription) {
    return {
        id: subscription.id,
        price: subscription.price,
        topicId: subscription.topicId,
        types: parseSubscriptionTypes(subscription.type),
        type: _getTypeFromSubTypes(parseSubscriptionTypes(subscription.type), subscription.filter),
        filter: subscription.filter ? JSON.parse(subscription.filter) : undefined
    };
}
function parseProducts(products) {
    return products.map((product)=>{
        return {
            productId: product.slug,
            description: product.description,
            title: product.title,
            ownershipSeconds: product.ownershipSeconds,
            cost: product.cost
        };
    });
}
function parseRecentAuction(auction) {
    return {
        end: parseDate(auction.end),
        playerName: auction.playerName,
        price: auction.price,
        seller: parsePlayer(auction.seller),
        uuid: auction.uuid
    };
}
function parseFlipAuction(flip) {
    let parsedFlip = {
        showLink: true,
        median: flip.median,
        cost: flip.cost,
        uuid: flip.uuid,
        volume: flip.volume,
        bin: flip.bin,
        item: {
            tag: flip.tag,
            name: flip.name,
            tier: flip.tier
        },
        secondLowestBin: flip.secondLowestBin,
        sold: flip.sold,
        sellerName: flip.sellerName,
        lowestBin: flip.lowestBin,
        props: flip.prop,
        finder: flip.finder,
        profit: flip.Profit
    };
    parsedFlip.item.iconUrl = __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getItemImageUrl(parsedFlip.item);
    return parsedFlip;
}
function parsePopularSearch(search) {
    return {
        title: search.title,
        url: search.url,
        img: search.img
    };
}
function parseRefInfo(refInfo) {
    return {
        oldInfo: {
            refId: refInfo.oldInfo.refId,
            count: refInfo.oldInfo.count,
            receivedHours: refInfo.oldInfo.receivedHours,
            receivedTime: refInfo.oldInfo.receivedTime,
            bougthPremium: refInfo.oldInfo.bougthPremium
        },
        purchasedCoins: refInfo.purchasedCoins,
        referedCount: refInfo.referedCount,
        validatedMinecraft: refInfo.validatedMinecraft,
        referredBy: refInfo.referredBy
    };
}
function parseFilterOption(filterOption) {
    return {
        name: filterOption.name,
        options: filterOption.options,
        type: filterOption.type,
        description: filterOption.description
    };
}
function parseAccountInfo(accountInfo) {
    return {
        email: accountInfo.email,
        mcId: accountInfo.mcId,
        mcName: accountInfo.mcName,
        token: accountInfo.token
    };
}
function parseMinecraftConnectionInfo(minecraftConnectionInfo) {
    return {
        code: minecraftConnectionInfo.code,
        isConnected: minecraftConnectionInfo.isConnected
    };
}
function parseDate(dateString) {
    if (!dateString) {
        return new Date();
    }
    if (typeof dateString === 'object' && typeof dateString.getTime === 'function') {
        dateString = dateString.toISOString();
    }
    if (dateString.slice(-1) === 'Z') {
        return new Date(dateString);
    }
    return new Date(dateString + 'Z');
}
function parseProfitableCrafts(crafts = []) {
    let parseCraftIngredient = (ingredient)=>{
        let result = {
            cost: ingredient.cost,
            count: ingredient.count,
            type: ingredient.type,
            item: {
                tag: ingredient.itemId
            }
        };
        result.item.name = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Formatter$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["convertTagToName"])(result.item.tag);
        result.item.iconUrl = __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getItemImageUrl(result.item);
        if (result.type === 'craft') {
            let toCraft = crafts.find((craft)=>craft.itemId === result.item.tag);
            if (!toCraft) {
                console.log(`craft not found for ${JSON.stringify(result)}`);
                return result;
            }
            result.ingredients = toCraft.ingredients.map(parseCraftIngredient);
        }
        return result;
    };
    return crafts.map((craft)=>{
        let c = {
            item: {
                tag: craft.itemId,
                name: craft.itemName
            },
            craftCost: craft.craftCost,
            sellPrice: craft.sellPrice,
            ingredients: craft.ingredients.map(parseCraftIngredient),
            median: craft.median,
            volume: craft.volume,
            requiredCollection: craft.reqCollection ? {
                name: craft.reqCollection.name,
                level: craft.reqCollection.level
            } : null,
            requiredSlayer: craft.reqSlayer ? {
                name: craft.reqSlayer.name,
                level: craft.reqSlayer.level
            } : null
        };
        c.item.name = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Formatter$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["convertTagToName"])(c.item.name);
        c.item.iconUrl = __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getItemImageUrl(c.item);
        return c;
    });
}
function parseLowSupplyItem(item) {
    let lowSupplyItem = parseItem(item);
    lowSupplyItem.supply = item.supply;
    lowSupplyItem.medianPrice = item.median;
    lowSupplyItem.volume = item.volume;
    lowSupplyItem.iconUrl = __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getItemImageUrl(item);
    lowSupplyItem.name = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Formatter$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["convertTagToName"])(item.tag);
    return lowSupplyItem;
}
function parseSkyblockProfile(profile) {
    return {
        current: profile.current,
        cuteName: profile.cute_name,
        id: profile.profile_id
    };
}
function parseCraftingRecipe(recipe) {
    return {
        A1: parseCraftingRecipeSlot(recipe.A1),
        A2: parseCraftingRecipeSlot(recipe.A2),
        A3: parseCraftingRecipeSlot(recipe.A3),
        B1: parseCraftingRecipeSlot(recipe.B1),
        B2: parseCraftingRecipeSlot(recipe.B2),
        B3: parseCraftingRecipeSlot(recipe.B3),
        C1: parseCraftingRecipeSlot(recipe.C1),
        C2: parseCraftingRecipeSlot(recipe.C2),
        C3: parseCraftingRecipeSlot(recipe.C3)
    };
}
function parseCraftingRecipeSlot(craftingRecipeSlotString) {
    if (!craftingRecipeSlotString) return undefined;
    let count = parseInt(craftingRecipeSlotString.split(':')[1]);
    return {
        tag: craftingRecipeSlotString.split(':')[0],
        count: count || 0
    };
}
function parseItemSummary(price) {
    return {
        max: price.max,
        mean: price.mean,
        median: price.median,
        min: price.min,
        mode: price.mode,
        volume: price.volume
    };
}
function parsePaymentResponse(payment) {
    return {
        id: payment.id,
        directLink: payment.dirctLink
    };
}
function parseKatFlip(katFlip) {
    let flip = {
        coreData: {
            amount: katFlip.coreData.amount,
            cost: katFlip.coreData.cost,
            hours: katFlip.coreData.hours,
            item: {
                tag: katFlip.coreData.itemTag,
                name: katFlip.coreData.name,
                tier: katFlip.coreData.baseRarity
            },
            material: katFlip.coreData.material
        },
        cost: katFlip.purchaseCost + katFlip.materialCost + katFlip.upgradeCost,
        purchaseCost: katFlip.purchaseCost,
        materialCost: katFlip.materialCost,
        median: katFlip.median,
        originAuctionUUID: katFlip.originAuction,
        profit: katFlip.profit,
        referenceAuctionUUID: katFlip.referenceAuction,
        targetRarity: katFlip.targetRarity,
        upgradeCost: katFlip.upgradeCost,
        volume: katFlip.volume,
        originAuctionName: katFlip.originAuctionName
    };
    flip.coreData.item.iconUrl = __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getItemImageUrl(flip.coreData.item);
    return flip;
}
function parseFlipTrackingFlip(flip) {
    let flipTrackingFlip = {
        item: {
            tag: flip?.itemTag,
            name: flip?.itemName || flip?.itemTag || '---',
            tier: flip?.tier
        },
        originAuction: flip?.originAuction,
        pricePaid: flip?.pricePaid,
        soldAuction: flip?.soldAuction,
        soldFor: flip?.soldFor,
        uId: flip?.uId,
        finder: (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$FlipUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getFlipFinders"])([
            flip.finder || 0
        ])[0],
        sellTime: parseDate(flip?.sellTime),
        buyTime: parseDate(flip?.buyTime),
        profit: flip?.profit,
        propertyChanges: flip.propertyChanges?.map((change)=>{
            return {
                description: change.description,
                effect: change.effect
            };
        }),
        flags: flip.flags ? new Set(flip.flags.split(', ')) : new Set()
    };
    flipTrackingFlip.item.iconUrl = __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getItemImageUrl(flipTrackingFlip?.item);
    return flipTrackingFlip;
}
function parseBazaarOrder(order) {
    return {
        amount: order.amount,
        pricePerUnit: order.pricePerUnit,
        orders: order.orders
    };
}
function parseBazaarSnapshot(snapshot) {
    return {
        item: parseItem({
            tag: snapshot.productId
        }),
        buyData: {
            orderCount: snapshot.buyOrdersCount,
            price: snapshot.buyPrice,
            volume: snapshot.buyVolume,
            moving: snapshot.buyMovingWeek
        },
        sellData: {
            orderCount: snapshot.sellOrdersCount,
            price: snapshot.sellPrice,
            volume: snapshot.sellVolume,
            moving: snapshot.sellMovingWeek
        },
        timeStamp: parseDate(snapshot.timeStamp),
        buyOrders: snapshot.buyOrders.map(parseBazaarOrder),
        sellOrders: snapshot.sellOrders.map(parseBazaarOrder)
    };
}
function parseFlipTrackingResponse(flipTrackingResponse) {
    return {
        flips: flipTrackingResponse?.flips ? flipTrackingResponse.flips.map(parseFlipTrackingFlip) : [],
        totalProfit: flipTrackingResponse?.totalProfit || 0
    };
}
function parseBazaarPrice(bazaarPrice) {
    return {
        buyData: {
            max: bazaarPrice.maxBuy || 0,
            min: bazaarPrice.minBuy || 0,
            price: bazaarPrice.buy || 0,
            volume: bazaarPrice.buyVolume || 0,
            moving: bazaarPrice.buyMovingWeek || 0
        },
        sellData: {
            max: bazaarPrice.maxSell || 0,
            min: bazaarPrice.minSell || 0,
            price: bazaarPrice.sell || 0,
            volume: bazaarPrice.sellVolume || 0,
            moving: bazaarPrice.sellMovingWeek || 0
        },
        timestamp: parseDate(bazaarPrice.timestamp)
    };
}
function parsePrivacySettings(privacySettings) {
    return {
        allowProxy: privacySettings.allowProxy,
        autoStart: privacySettings.autoStart,
        chatRegex: privacySettings.chatRegex,
        collectChat: privacySettings.collectChat,
        collectChatClicks: privacySettings.collectChatClicks,
        collectEntities: privacySettings.collectEntities,
        collectInvClick: privacySettings.collectInvClick,
        collectInventory: privacySettings.collectInventory,
        collectLobbyChanges: privacySettings.collectLobbyChanges,
        collectScoreboard: privacySettings.collectScoreboard,
        collectTab: privacySettings.collectTab,
        commandPrefixes: privacySettings.commandPrefixes,
        extendDescriptions: privacySettings.extendDescriptions
    };
}
function parsePremiumProducts(productsObject) {
    let products = [];
    Object.keys(productsObject).forEach((key)=>{
        products.push({
            productSlug: key,
            expires: parseDate(productsObject[key].expiresAt)
        });
    });
    return products;
}
function parseMayorData(mayorData) {
    return {
        end: parseDate(mayorData.end),
        start: parseDate(mayorData.start),
        winner: mayorData.winner,
        year: mayorData.yearF
    };
}
function parseInventoryData(data) {
    if (data === null || data.itemName === null) {
        return data;
    }
    if (data.enchantments !== null) {
        Object.keys(data?.enchantments).forEach((key)=>{
            data.enchantments[key] = data.enchantments[key].toString();
        });
    }
    return {
        color: data.data,
        count: data.count,
        description: data.description,
        enchantments: data.enchantments || {},
        extraAttributes: data.extraAttributes,
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getItemImageUrl({
            tag: data.tag
        }),
        id: data.id,
        itemName: data.itemName,
        tag: data.tag
    };
}
function parseTradeObject(data) {
    return {
        id: data.id,
        playerUuid: data.playerUuid,
        playerName: data.playerName,
        buyerUuid: data.buyerUuid,
        item: parseInventoryData(data.item),
        wantedItems: data.wantedItems ? data.wantedItems.filter((wantedItem)=>wantedItem.tag !== 'SKYBLOCK_COIN') : [],
        wantedCoins: data.wantedItems ? data.wantedItems.find((wantedItem)=>wantedItem.tag === 'SKYBLOCK_COIN')?.filters?.Count : null,
        timestamp: parseDate(data.timestamp),
        coins: data.coins
    };
}
function parseTransaction(transaction) {
    return {
        productId: transaction.productId,
        reference: transaction.reference,
        amount: transaction.amount,
        timeStamp: parseDate(transaction.timeStamp)
    };
}
function parseOwnerHistory(ownerHistory) {
    return {
        buyer: parsePlayer(ownerHistory.buyer),
        seller: parsePlayer(ownerHistory.seller),
        highestBid: ownerHistory.highestBid,
        itemTag: ownerHistory.itemTag,
        uuid: ownerHistory.uuid,
        timestamp: parseDate(ownerHistory.timestamp)
    };
}
function parseArchivedAuctions(archivedAuctionsResponse) {
    return {
        queryStatus: archivedAuctionsResponse.queryStatus,
        auctions: archivedAuctionsResponse.auctions.map((a)=>{
            return {
                end: parseDate(a.end),
                price: a.price,
                seller: parsePlayer({
                    name: a.playerName,
                    uuid: a.seller
                }),
                uuid: a.uuid
            };
        })
    };
}
function parsePremiumSubscription(subscription) {
    return {
        externalId: subscription.externalId,
        endsAt: subscription.endsAt ? parseDate(subscription.endsAt) : undefined,
        productName: subscription.productName,
        paymentAmount: subscription.paymentAmount,
        renewsAt: parseDate(subscription.renewsAt),
        createdAt: parseDate(subscription.createdAt)
    };
}
function parseCraftingInstructions(craftingInstructions) {
    return {
        itemTag: craftingInstructions.itemTag,
        recipe: parseCraftingRecipe(craftingInstructions.recipe),
        copyCommands: craftingInstructions.copyCommands,
        detailsPath: craftingInstructions.detailsPath
    };
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/utils/PremiumTypeUtils.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "PREMIUM_RANK": (()=>PREMIUM_RANK),
    "PREMIUM_TYPES": (()=>PREMIUM_TYPES),
    "TEST_PREMIUM_DAYS": (()=>TEST_PREMIUM_DAYS),
    "getHighestPriorityPremiumProduct": (()=>getHighestPriorityPremiumProduct),
    "getPremiumLabelForSubscription": (()=>getPremiumLabelForSubscription),
    "getPremiumType": (()=>getPremiumType),
    "hasHighEnoughPremium": (()=>hasHighEnoughPremium)
});
var PREMIUM_RANK = /*#__PURE__*/ function(PREMIUM_RANK) {
    PREMIUM_RANK[PREMIUM_RANK["STARTER"] = 1] = "STARTER";
    PREMIUM_RANK[PREMIUM_RANK["PREMIUM"] = 2] = "PREMIUM";
    PREMIUM_RANK[PREMIUM_RANK["PREMIUM_PLUS"] = 3] = "PREMIUM_PLUS";
    return PREMIUM_RANK;
}({});
const TEST_PREMIUM_DAYS = 3;
const PREMIUM_TYPES = [
    {
        productId: 'premium',
        label: 'Premium',
        durationString: 'month',
        priority: 2,
        options: generateNumberOptionArray(1, 12, 'premium', 1800)
    },
    {
        productId: 'premium_plus',
        label: 'Premium+',
        durationString: '',
        priority: 3,
        options: [
            {
                value: 1,
                label: '1 week for 2700',
                productId: 'premium_plus',
                price: 2700
            },
            {
                value: 2,
                label: '2 weeks',
                productId: 'premium_plus',
                price: 2700
            },
            {
                value: 1,
                label: '4 weeks (2250/week)',
                productId: 'premium_plus-weeks',
                price: 9000
            },
            {
                value: 1,
                label: '11 weeks (1964/w)',
                productId: 'premium_plus-months',
                price: 21600
            },
            {
                value: 1,
                label: '1 hour',
                productId: 'premium_plus-hour',
                price: 200
            },
            {
                value: 1,
                label: '1 day',
                productId: 'premium_plus-day',
                price: 600
            }
        ]
    },
    {
        productId: 'starter_premium',
        label: 'Starter Premium',
        durationString: '',
        priority: 1,
        options: [
            {
                value: 1,
                label: '1 day',
                productId: 'starter_premium-day',
                price: 24
            },
            {
                value: 1,
                label: '1 week',
                productId: 'starter_premium-week',
                price: 120
            },
            {
                value: 4,
                label: '4 weeks',
                productId: 'starter_premium-week',
                price: 120
            },
            {
                value: 1,
                label: '6 months',
                productId: 'starter_premium',
                price: 1800
            },
            {
                value: 2,
                label: '12 months',
                productId: 'starter_premium',
                price: 1800
            }
        ]
    }
];
function generateNumberOptionArray(start, end, productId, priceForOption) {
    return Array(end - start + 1).fill().map((_, idx)=>start + idx).map((number)=>{
        return {
            value: number,
            label: number,
            productId: productId,
            price: priceForOption
        };
    });
}
function getHighestPriorityPremiumProduct(premiumProducts = []) {
    let results = premiumProducts.map((product)=>{
        let type = getPremiumType(product);
        return {
            productSlug: product.productSlug,
            productId: type?.productId,
            priority: type?.priority
        };
    });
    let result = results.sort((a, b)=>b.priority - a.priority)[0];
    return premiumProducts.find((product)=>product.productSlug === result.productSlug && product.expires > new Date());
}
function getPremiumType(product) {
    return [
        ...PREMIUM_TYPES
    ].sort((a, b)=>b.productId.localeCompare(a.productId)).find((type)=>product.productSlug.startsWith(type.productId));
}
function getPremiumLabelForSubscription(subscription) {
    if (!subscription.productName) {
        return 'Unknown';
    }
    if (subscription.productName.includes('premium_plus')) {
        return 'Premium+';
    }
    if (subscription.productName.includes('premium')) {
        return 'Premium';
    }
    return subscription.productName;
}
function hasHighEnoughPremium(products, minPremiumType) {
    let hasHighEnoughPremium = false;
    products.forEach((product)=>{
        let type = getPremiumType(product);
        if (type && type.priority >= minPremiumType && product.expires > new Date()) {
            hasHighEnoughPremium = true;
        }
    });
    return hasHighEnoughPremium;
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/properties.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
let properties = {};
let isSSR = "object" === 'undefined';
// Temporary fallback to production for working demo while local services are being set up
// TODO: Switch back to local once all services are properly configured
const useLocalServices = false; // Set to true once local APIs are working
properties = {
    commandEndpoint: isSSR || window.location.host.startsWith('localhost') || window.location.hostname.includes('pr-env-sky-') ? ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : 'https://sky.coflnet.com/command' : '/command',
    apiEndpoint: isSSR || window.location.host.startsWith('localhost') || window.location.hostname.includes('pr-env-sky-') ? ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : 'https://sky.coflnet.com/api' : '/api',
    websocketEndpoint: isSSR || window.location.host === 'localhost:8008' ? 'ws://localhost:8008/skyblock' : 'wss://sky.coflnet.com/skyblock',
    refLink: ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : 'https://sky.coflnet.com/refed',
    websocketOldEndpoint: 'wss://skyblock-backend.coflnet.com/skyblock',
    feedbackEndpoint: ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : 'https://feedback.coflnet.com/api/',
    isTestRunner: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].env.TEST_RUNNER === 'true' || false
};
const __TURBOPACK__default__export__ = properties;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/utils/PropertiesUtils.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "getProperty": (()=>getProperty)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$properties$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/properties.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SSRUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/SSRUtils.tsx [app-client] (ecmascript)");
;
;
function getProperty(propertyName) {
    // Dynamicly change properties
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SSRUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isClientSideRendering"])() && window.dynamicProps && window.dynamicProps[propertyName]) {
        return window.dynamicProps[propertyName];
    }
    return __TURBOPACK__imported__module__$5b$project$5d2f$properties$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"][propertyName];
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/utils/MessageIdUtils.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "getNextMessageId": (()=>getNextMessageId)
});
let messageId = 0;
function getNextMessageId() {
    return ++messageId;
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/api/HttpHelper.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "initHttpHelper": (()=>initHttpHelper)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$CacheUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/CacheUtils.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$PropertiesUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/PropertiesUtils.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$MessageIdUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/MessageIdUtils.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Base64Utils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/Base64Utils.tsx [app-client] (ecmascript)");
;
;
;
;
function initHttpHelper(customCommandEndpoint, customApiEndpoint) {
    const commandEndpoint = customCommandEndpoint || (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$PropertiesUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getProperty"])('commandEndpoint');
    const apiEndpoint = customApiEndpoint || (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$PropertiesUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getProperty"])('apiEndpoint');
    let requests = [];
    /**
     * @deprecated
     * Sends http-Request to the backend
     * @param request The request-Object
     * @param cacheInvalidationGrouping A number which is appended to the url to be able to invalidate the cache
     * @returns A emty promise (the resolve/reject Method of the request-Object is called)
     */ function sendRequest(request, cacheInvalidationGrouping) {
        request.mId = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$MessageIdUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getNextMessageId"])();
        let requestString = JSON.stringify(request.data);
        var url = `${commandEndpoint}/${request.type}/${(0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Base64Utils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["btoaUnicode"])(requestString)}`;
        if (cacheInvalidationGrouping) {
            url += `/${cacheInvalidationGrouping}`;
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$CacheUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getFromCache(request.type, requestString).then((cacheValue)=>{
            if (cacheValue) {
                request.resolve(cacheValue);
                return;
            }
            try {
                request.data = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Base64Utils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["btoaUnicode"])(requestString);
            } catch (error) {
                throw new Error('couldnt btoa this data: ' + request.data);
            }
            // don't resend in progress requests
            let equals = findForEqualSentRequest(request);
            if (equals.length > 0) {
                requests.push(request);
                return;
            }
            requests.push(request);
            return handleServerRequest(request, url);
        });
    }
    /**
     * Sends API-Request to the backend
     * @param request The request-Object
     * @returns A emty promise (the resolve/reject Method of the request-Object is called)
     */ function sendApiRequest(request, body) {
        request.mId = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$MessageIdUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getNextMessageId"])();
        let requestString = request.data;
        var url = `${apiEndpoint}/${request.type}`;
        if (requestString) {
            url += `/${requestString}`;
        }
        if (request.customRequestURL) {
            url = request.customRequestURL;
        }
        if (url.endsWith('&') || url.endsWith('?')) {
            url = url.substring(0, url.length - 1);
        }
        // don't resend in progress requests
        // ignore requests with a body, as they are currently not stored and therefore not checked
        if (!body) {
            let equals = findForEqualSentRequest(request);
            if (equals.length > 0) {
                requests.push(request);
                return Promise.resolve();
            }
        }
        requests.push(request);
        return handleServerRequest(request, url, body);
    }
    function handleServerRequest(request, url, body) {
        let parsedResponse;
        try {
            return fetch(url, {
                body: body,
                method: request.requestMethod,
                headers: request.requestHeader
            }).then((response)=>{
                if (!response.ok && response.status !== 304) {
                    return Promise.reject(response.text());
                }
                return response.text();
            }).then((responseText)=>{
                parsedResponse = parseResponseText(responseText);
                if (!parsedResponse && parsedResponse !== false || parsedResponse.slug !== undefined) {
                    request.resolve();
                    return;
                }
                request.resolve(parsedResponse);
                let equals = findForEqualSentRequest(request);
                equals.forEach((equal)=>{
                    equal.resolve(parsedResponse);
                });
                // all http answers are valid for 60 sec
                let maxAge = 60;
                let data = request.data;
                try {
                    data = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Base64Utils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["atobUnicode"])(request.data);
                } catch  {}
                __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$CacheUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].setIntoCache(request.customRequestURL || request.type, data, parsedResponse, maxAge);
                removeSentRequests([
                    ...equals,
                    request
                ]);
            }).catch((responseTextPromise)=>{
                if (!responseTextPromise || typeof responseTextPromise.then !== 'function') {
                    request.reject(responseTextPromise || 'no responseTextPromise');
                    return;
                }
                responseTextPromise.then((responseText)=>{
                    request.reject(parseResponseText(responseText) || 'no responseTextPromise after parse');
                });
            }).finally(()=>{
                // when there are still matching request remove them
                let equals = findForEqualSentRequest(request);
                equals.forEach((equal)=>{
                    equal.resolve(parsedResponse);
                });
                removeSentRequests([
                    ...equals,
                    request
                ]);
            });
        } catch (e) {
            console.log('Fetch threw exception...');
            console.log('URL: ' + url);
            console.log('Request: ' + JSON.stringify(request));
            console.log('Body: ' + JSON.stringify(body));
            console.log(JSON.stringify(e));
            console.log('------------------------');
            return request.reject('Fetch threw exception: ' + JSON.stringify(e));
        }
    }
    function sendRequestLimitCache(request, grouping = 1) {
        let group = Math.round(new Date().getMinutes() / grouping);
        return sendRequest(request, group);
    }
    function removeSentRequests(toDelete) {
        requests = requests.filter((request)=>{
            for(let i = 0; i < toDelete.length; i++){
                if (toDelete[i].mId === request.mId) {
                    return false;
                }
            }
            return true;
        });
    }
    function findForEqualSentRequest(request) {
        return requests.filter((r)=>{
            return r.type === request.type && JSON.stringify(r.data) === JSON.stringify(request.data) && r.customRequestURL === request.customRequestURL && r.mId !== request.mId;
        });
    }
    function parseResponseText(responseText) {
        let parsedResponse;
        try {
            if (!isNaN(responseText)) {
                return responseText;
            }
            parsedResponse = JSON.parse(responseText);
        } catch  {
            parsedResponse = responseText;
        }
        return parsedResponse;
    }
    return {
        sendRequest: sendRequest,
        sendLimitedCacheRequest: sendRequestLimitCache,
        sendApiRequest: sendApiRequest
    };
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/api/WebsocketHelper.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "websocketHelper": (()=>websocketHelper)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/api/ApiTypes.d.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$CacheUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/CacheUtils.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/api/ApiHelper.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-toastify/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$PropertiesUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/PropertiesUtils.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$MessageIdUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/MessageIdUtils.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Base64Utils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/Base64Utils.tsx [app-client] (ecmascript)");
;
;
;
;
;
;
;
let requests = [];
let websocket;
let isConnectionIdSet = false;
let apiSubscriptions = [];
function initWebsocket() {
    let onWebsocketClose = ()=>{
        var timeout = Math.random() * (5000 - 0) + 0;
        setTimeout(()=>{
            websocket = getNewWebsocket(true);
        }, timeout);
    };
    // dont show a toast message on websocket errors as this gets spammed when the user for example locks their computer
    let onWebsocketError = (e)=>{
        console.error(e);
    };
    let onOpen = (e, isReconnecting)=>{
        let _reconnect = function() {
            let toReconnect = [
                ...apiSubscriptions
            ];
            apiSubscriptions = [];
            toReconnect.forEach((subscription)=>{
                subscription.resubscribe(subscription);
            });
        };
        // set the connection id first
        __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].setConnectionId().then(()=>{
            isConnectionIdSet = true;
            if (isReconnecting && sessionStorage.getItem('googleId') !== null) {
                __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].loginWithToken(sessionStorage.getItem('googleId')).then((token)=>{
                    sessionStorage.setItem('googleId', token);
                    localStorage.setItem('googleId', token);
                    _reconnect();
                });
            }
        });
    };
    let _handleRequestOnMessage = function(response, request) {
        let equals = findForEqualSentRequest(request);
        if (response.type === 'display') {
            let parsedData = JSON.parse(response.data);
            if (typeof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"][parsedData.type] === 'function') {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"][parsedData.type](parsedData.message);
            } else {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].info(parsedData.message);
            }
            return;
        }
        if (response.type.includes('error')) {
            request.reject(JSON.parse(response.data));
            equals.forEach((equal)=>equal.reject(JSON.parse(response.data)));
        } else {
            if (response.data === '') {
                response.data = '""';
            }
            let parsedResponse = JSON.parse(response.data);
            request.resolve(parsedResponse);
            equals.forEach((equal)=>equal.resolve(parsedResponse));
            // cache the response
            let maxAge = response.maxAge;
            __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$CacheUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].setIntoCache(request.type, (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Base64Utils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["atobUnicode"])(request.data), parsedResponse, maxAge);
        }
        removeSentRequests([
            ...equals,
            request
        ]);
    };
    let _handleSubscriptionOnMessage = function(response, subscription) {
        try {
            response.data = JSON.parse(response.data);
        } catch (e) {}
        if (response.type === 'error') {
            subscription.onError(response.data.message);
        } else {
            subscription.callback(response);
        }
    };
    let onWebsocketMessage = (e)=>{
        let response = JSON.parse(e.data);
        let request = requests.find((e)=>e.mId === response.mId);
        let subscription = apiSubscriptions.find((e)=>e.mId === response.mId);
        if (!request && !subscription) {
            return;
        }
        if (request) {
            _handleRequestOnMessage(response, request);
        }
        if (subscription) {
            _handleSubscriptionOnMessage(response, subscription);
        }
    };
    let getNewWebsocket = (isReconnecting)=>{
        websocket = new WebSocket((0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$PropertiesUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getProperty"])('websocketEndpoint'));
        websocket.onclose = onWebsocketClose;
        websocket.onerror = onWebsocketError;
        websocket.onmessage = onWebsocketMessage;
        websocket.onopen = (e)=>{
            onOpen(e, isReconnecting);
        };
        window.websocket = websocket;
        return websocket;
    };
    websocket = getNewWebsocket(false);
}
function sendRequest(request) {
    if (!websocket) {
        initWebsocket();
    }
    let requestString = JSON.stringify(request.data);
    return __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$CacheUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getFromCache(request.type, requestString).then((cacheValue)=>{
        if (cacheValue) {
            request.resolve(cacheValue);
            return;
        }
        if (_isWebsocketReady(request.type, websocket)) {
            request.mId = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$MessageIdUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getNextMessageId"])();
            let equals = findForEqualSentRequest(request);
            if (equals.length > 0) {
                requests.push(request);
                return;
            }
            requests.push(request);
            prepareDataBeforeSend(request);
            websocket.send(JSON.stringify(request));
        } else {
            setTimeout(()=>{
                sendRequest(request);
            }, 500);
            return;
        }
    });
}
function prepareDataBeforeSend(request) {
    try {
        request.data = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Base64Utils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["btoaUnicode"])(JSON.stringify(request.data));
    } catch (error) {
        throw new Error('couldnt btoa this data: ' + request.data);
    }
}
function removeOldSubscriptionByType(type) {
    for(let i = apiSubscriptions.length - 1; i >= 0; i--){
        let subscription = apiSubscriptions[i];
        if (subscription.type === type) {
            apiSubscriptions.splice(i, 1);
        }
    }
}
function subscribe(subscription) {
    if (!websocket) {
        initWebsocket();
    }
    let requestString = JSON.stringify(subscription.data);
    if (_isWebsocketReady(subscription.type, websocket)) {
        subscription.mId = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$MessageIdUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getNextMessageId"])();
        try {
            subscription.data = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Base64Utils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["btoaUnicode"])(requestString);
        } catch (error) {
            throw new Error('couldnt btoa this data: ' + subscription.data);
        }
        apiSubscriptions.push(subscription);
        websocket.send(JSON.stringify(subscription));
    } else {
        setTimeout(()=>{
            subscribe(subscription);
        }, 500);
    }
}
function findForEqualSentRequest(request) {
    return requests.filter((r)=>{
        return r.type === request.type && r.data === request.data && r.mId !== request.mId;
    });
}
function removeSentRequests(toDelete) {
    requests = requests.filter((request)=>{
        for(let i = 0; i < toDelete.length; i++){
            if (toDelete[i].mId === request.mId) {
                return false;
            }
        }
        return true;
    });
}
function _isWebsocketReady(requestType, websocket) {
    return websocket && websocket.readyState === WebSocket.OPEN && (isConnectionIdSet || requestType === __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].SET_CONNECTION_ID);
}
let websocketHelper = {
    sendRequest: sendRequest,
    subscribe: subscribe,
    removeOldSubscriptionByType: removeOldSubscriptionByType
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/utils/ClipboardUtils.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// DuckDuckgo on Android doesnt support the clipboard API, but the old execCommand
__turbopack_context__.s({
    "canUseClipBoard": (()=>canUseClipBoard),
    "writeToClipboard": (()=>writeToClipboard)
});
function isAndroidDuckDuckGo() {
    return navigator.userAgent.includes('DuckDuckGo') && navigator.userAgent.includes('Android');
}
function canUseClipBoard() {
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    return window.navigator.clipboard || isAndroidDuckDuckGo();
}
function writeToClipboard(text) {
    if (isAndroidDuckDuckGo() || !window.navigator.clipboard) {
        let textarea = document.createElement('textarea');
        textarea.style.position = 'fixed';
        textarea.style.width = '1px';
        textarea.style.height = '1px';
        textarea.style.padding = '0';
        textarea.style.border = 'none';
        textarea.style.outline = 'none';
        textarea.style.boxShadow = 'none';
        textarea.style.background = 'transparent';
        document.body.appendChild(textarea);
        textarea.textContent = text;
        textarea.focus();
        textarea.select();
        document.execCommand('copy');
        textarea.remove();
    } else {
        window.navigator.clipboard.writeText(text);
    }
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/utils/CoflCoinsUtils.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "getCurrentCoflCoins": (()=>getCurrentCoflCoins),
    "initCoflCoinManager": (()=>initCoflCoinManager),
    "subscribeToCoflcoinChange": (()=>subscribeToCoflcoinChange)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/api/ApiHelper.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/api/ApiTypes.d.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SSRUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/SSRUtils.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__ = __turbopack_context__.i("[project]/node_modules/uuid/dist/esm-browser/v4.js [app-client] (ecmascript) <export default as v4>");
;
;
;
;
let registeredCallbacks = [];
let currentCoflCoins = -1;
function subscribeToCoflcoinChange(callback) {
    let uuid = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])();
    registeredCallbacks.push({
        uuid: uuid,
        callback: callback
    });
    return ()=>{
        let index = registeredCallbacks.findIndex((registeredCallback)=>{
            return registeredCallback.uuid === uuid;
        });
        registeredCallbacks.splice(index, 1);
    };
}
function getCurrentCoflCoins() {
    return currentCoflCoins;
}
function initCoflCoinManager() {
    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SSRUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isClientSideRendering"])()) {
        return;
    }
    function notifyAboutCoflCoinUpdate(coflCoins) {
        registeredCallbacks.forEach((registeredCallback)=>{
            registeredCallback.callback(coflCoins);
        });
    }
    function initCoflCoinBalanceAndSubscriptions() {
        __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].subscribeCoflCoinChange();
        __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getCoflcoinBalance().then((coflCoins)=>{
            currentCoflCoins = coflCoins;
            notifyAboutCoflCoinUpdate(coflCoins);
        });
        document.removeEventListener(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CUSTOM_EVENTS"].GOOGLE_LOGIN, initCoflCoinBalanceAndSubscriptions);
    }
    document.addEventListener(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CUSTOM_EVENTS"].GOOGLE_LOGIN, initCoflCoinBalanceAndSubscriptions);
    document.addEventListener(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CUSTOM_EVENTS"].COFLCOIN_UPDATE, (e)=>{
        let coflCoins = e.detail?.coflCoins;
        currentCoflCoins = coflCoins;
        notifyAboutCoflCoinUpdate(coflCoins);
    });
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/api/ApiHelper.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__),
    "initAPI": (()=>initAPI)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-toastify/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__ = __turbopack_context__.i("[project]/node_modules/uuid/dist/esm-browser/v4.js [app-client] (ecmascript) <export default as v4>");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Base64Utils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/Base64Utils.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$CacheUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/CacheUtils.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$FlipUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/FlipUtils.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Formatter$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/Formatter.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Parser$2f$APIResponseParser$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/Parser/APIResponseParser.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$PremiumTypeUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/PremiumTypeUtils.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$PropertiesUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/PropertiesUtils.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SettingsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/SettingsUtils.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SSRUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/SSRUtils.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/api/ApiTypes.d.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$HttpHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/api/HttpHelper.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$WebsocketHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/api/WebsocketHelper.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$ClipboardUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/ClipboardUtils.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$properties$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/properties.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$CoflCoinsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/CoflCoinsUtils.tsx [app-client] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
function getApiEndpoint() {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SSRUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isClientSideRendering"])() ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$PropertiesUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getProperty"])('apiEndpoint') : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].env.API_ENDPOINT || (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$PropertiesUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getProperty"])('apiEndpoint');
}
function initAPI(returnSSRResponse = false) {
    let httpApi;
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SSRUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isClientSideRendering"])()) {
        httpApi = (0, __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$HttpHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["initHttpHelper"])();
    } else {
        let commandEndpoint = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].env.COMMAND_ENDPOINT;
        let apiEndpoint = getApiEndpoint();
        httpApi = (0, __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$HttpHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["initHttpHelper"])(commandEndpoint, apiEndpoint);
    }
    setTimeout(()=>{
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SSRUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isClientSideRendering"])()) {
            __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$CacheUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].checkForCacheClear();
        }
    }, 20000);
    let apiErrorHandler = (requestType, error, requestData = null)=>{
        if (!error || !error.message) {
            return;
        }
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SSRUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isClientSideRendering"])()) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(error.message, {
                onClick: ()=>{
                    if (error.traceId && (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$ClipboardUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["canUseClipBoard"])()) {
                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$ClipboardUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["writeToClipboard"])(error.traceId);
                        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success(/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            children: [
                                "Copied the error trace to the clipboard. Please use this to ask for help on our",
                                ' ',
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                                    target: "_blank",
                                    rel: "noreferrer",
                                    href: "https://discord.gg/wvKXfTgCfb",
                                    children: "Discord"
                                }, void 0, false, {
                                    fileName: "[project]/api/ApiHelper.tsx",
                                    lineNumber: 98,
                                    columnNumber: 33
                                }, this),
                                "."
                            ]
                        }, void 0, true, {
                            fileName: "[project]/api/ApiHelper.tsx",
                            lineNumber: 96,
                            columnNumber: 29
                        }, this));
                    }
                }
            });
        }
        console.log('RequestType: ' + requestType);
        console.log('ErrorMessage: ' + error.message);
        console.log('RequestData: ');
        console.log(requestData);
        console.log('------------------------------\n');
    };
    let search = (searchText)=>{
        return new Promise((resolve, reject)=>{
            httpApi.sendApiRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].SEARCH,
                data: searchText,
                resolve: (items)=>{
                    resolve(!items ? [] : items.map((item)=>{
                        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Parser$2f$APIResponseParser$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseSearchResultItem"])(item);
                    }));
                },
                reject: (error)=>{
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].SEARCH, error, searchText);
                    reject(error);
                }
            });
        });
    };
    let getItemImageUrl = (item)=>{
        let type = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SettingsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getSetting"])(__TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SettingsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ITEM_ICON_TYPE"], 'default');
        let iconURL = item.iconUrl || item.icon;
        if (iconURL) {
            // Ensure all icon URLs use production servers for consistency
            if (iconURL.includes('localhost:8086')) {
                iconURL = iconURL.replace('http://localhost:8086/static/icon', 'https://sky.coflnet.com/static/icon');
            }
            if (type === 'vanilla' && !iconURL.endsWith('/vanilla') && iconURL.includes('sky.coflnet.com/static/icon')) {
                return iconURL + '/vanilla';
            }
            return iconURL;
        }
        // Always use production icons for consistency
        let r = `https://sky.coflnet.com/static/icon/${item.tag}${type === 'vanilla' ? '/vanilla' : ''}`;
        return r;
    };
    let getItemDetails = (itemTag)=>{
        return new Promise((resolve, reject)=>{
            httpApi.sendApiRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].ITEM_DETAILS,
                customRequestURL: `${getApiEndpoint()}/item/${itemTag}/details`,
                data: '',
                resolve: (item)=>{
                    returnSSRResponse ? resolve(item) : resolve((0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Parser$2f$APIResponseParser$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseItem"])(item));
                },
                reject: (error)=>{
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].ITEM_DETAILS, error, itemTag);
                    reject(error);
                }
            });
        });
    };
    let getItemPrices = (itemTag, fetchSpan, itemFilter)=>{
        return new Promise((resolve, reject)=>{
            let params = new URLSearchParams();
            if (itemFilter && Object.keys(itemFilter).length > 0) {
                params = new URLSearchParams(itemFilter);
            }
            httpApi.sendApiRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].ITEM_PRICES,
                data: '',
                customRequestURL: getApiEndpoint() + `/item/price/${itemTag}/history/${fetchSpan}?${params.toString()}`,
                requestMethod: 'GET',
                requestHeader: {
                    'Content-Type': 'application/json'
                },
                resolve: (data)=>{
                    if (returnSSRResponse) {
                        resolve(data);
                        return;
                    }
                    resolve(data ? data.map(__TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Parser$2f$APIResponseParser$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseItemPrice"]).sort((a, b)=>a.time.getTime() - b.time.getTime()) : []);
                },
                reject: (error)=>{
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].ITEM_PRICES, error, {
                        itemTag,
                        fetchSpan,
                        itemFilter
                    });
                    reject(error);
                }
            });
        });
    };
    let getBazaarPrices = (itemTag, fetchSpan)=>{
        return new Promise((resolve, reject)=>{
            httpApi.sendApiRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].BAZAAR_PRICES,
                data: '',
                customRequestURL: (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$PropertiesUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getProperty"])('apiEndpoint') + `/bazaar/${itemTag}/history/${fetchSpan}`,
                requestMethod: 'GET',
                resolve: (data)=>{
                    resolve(data ? data.map(__TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Parser$2f$APIResponseParser$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseBazaarPrice"]).sort((a, b)=>a.timestamp.getTime() - b.timestamp.getTime()) : []);
                },
                reject: (error)=>{
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].BAZAAR_PRICES, error, {
                        itemTag,
                        fetchSpan
                    });
                    reject(error);
                }
            });
        });
    };
    let getBazaarPricesByRange = (itemTag, startDate, endDate)=>{
        return new Promise((resolve, reject)=>{
            let startDateIso = new Date(startDate).toISOString();
            let endDateIso = new Date(endDate).toISOString();
            httpApi.sendApiRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].BAZAAR_PRICES,
                data: '',
                customRequestURL: (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$PropertiesUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getProperty"])('apiEndpoint') + `/bazaar/${itemTag}/history/?start=${startDateIso}&end=${endDateIso}`,
                requestMethod: 'GET',
                resolve: (data)=>{
                    data = data.filter((d)=>d.sell !== undefined && d.buy !== undefined);
                    let buySort = [
                        ...data
                    ].sort((a, b)=>a.buy - b.buy);
                    let sellSort = [
                        ...data
                    ].sort((a, b)=>a.sell - b.sell);
                    let medianBuy = buySort.length > 0 ? buySort[Math.floor(buySort.length / 2)].buy : 0;
                    let medianSell = sellSort.length > 0 ? sellSort[Math.floor(sellSort.length / 2)].sell : 0;
                    let bazaarData = data.map(__TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Parser$2f$APIResponseParser$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseBazaarPrice"]).sort((a, b)=>a.timestamp.getTime() - b.timestamp.getTime());
                    let normalizer = 8;
                    resolve(bazaarData.filter((b)=>b.buyData.max < medianBuy * normalizer && b.sellData.max < medianSell * normalizer && b.buyData.min > medianBuy / normalizer && b.sellData.min > medianSell / normalizer));
                },
                reject: (error)=>{
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].BAZAAR_PRICES, error, {
                        itemTag,
                        startDateIso,
                        endDateIso
                    });
                    reject(error);
                }
            });
        });
    };
    let getAuctions = (uuid, page = 0, itemFilter)=>{
        return new Promise((resolve, reject)=>{
            let params = new URLSearchParams();
            params.append('page', page.toString());
            if (itemFilter && Object.keys(itemFilter).length > 0) {
                Object.keys(itemFilter).forEach((key)=>{
                    params.append(key, itemFilter[key]);
                });
            }
            httpApi.sendApiRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].PLAYER_AUCTION,
                customRequestURL: `${getApiEndpoint()}/player/${uuid}/auctions?${params.toString()}`,
                data: '',
                resolve: (auctions)=>{
                    returnSSRResponse ? resolve(auctions) : resolve(auctions.map((auction)=>{
                        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Parser$2f$APIResponseParser$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseAuction"])(auction);
                    }));
                },
                reject: (error)=>{
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].PLAYER_AUCTION, error, {
                        uuid,
                        page
                    });
                    reject(error);
                }
            });
        });
    };
    let getBids = (uuid, page = 0, itemFilter)=>{
        return new Promise((resolve, reject)=>{
            let params = new URLSearchParams();
            params.append('page', page.toString());
            if (itemFilter && Object.keys(itemFilter).length > 0) {
                Object.keys(itemFilter).forEach((key)=>{
                    params.append(key, itemFilter[key]);
                });
            }
            httpApi.sendApiRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].PLAYER_BIDS,
                customRequestURL: `${getApiEndpoint()}/player/${uuid}/bids?${params.toString()}`,
                data: '',
                resolve: (bids)=>{
                    resolve(bids.map((bid)=>{
                        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Parser$2f$APIResponseParser$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseItemBidForList"])(bid);
                    }));
                },
                reject: (error)=>{
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].PLAYER_BIDS, error, {
                        uuid,
                        page
                    });
                    reject(error);
                }
            });
        });
    };
    let getEnchantments = ()=>{
        return new Promise((resolve, reject)=>{
            httpApi.sendRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].ALL_ENCHANTMENTS,
                data: '',
                resolve: (enchantments)=>{
                    let parsedEnchantments = enchantments.map((enchantment)=>{
                        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Parser$2f$APIResponseParser$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseEnchantment"])({
                            type: enchantment.label,
                            id: enchantment.id
                        });
                    });
                    parsedEnchantments = parsedEnchantments.filter((enchantment)=>{
                        return enchantment.name.toLowerCase() !== 'unknown';
                    }).sort(__TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Formatter$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["enchantmentAndReforgeCompare"]);
                    resolve(parsedEnchantments);
                },
                reject: (error)=>{
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].ALL_ENCHANTMENTS, error, '');
                    reject(error);
                }
            });
        });
    };
    let trackSearch = (fullSearchId, fullSearchType)=>{
        let requestData = {
            id: fullSearchId,
            type: fullSearchType
        };
        __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$WebsocketHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["websocketHelper"].sendRequest({
            type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].TRACK_SEARCH,
            data: requestData,
            resolve: ()=>{},
            reject: (error)=>{
                apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].TRACK_SEARCH, error, requestData);
            }
        });
    };
    let getAuctionDetails = (auctionUUID)=>{
        return new Promise((resolve, reject)=>{
            httpApi.sendApiRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].AUCTION_DETAILS,
                data: auctionUUID,
                resolve: (auctionDetails)=>{
                    if (!auctionDetails) {
                        reject();
                        return;
                    }
                    if (!auctionDetails.auctioneer) {
                        api.getPlayerName(auctionDetails.auctioneerId).then((name)=>{
                            auctionDetails.auctioneer = {
                                name,
                                uuid: auctionDetails.auctioneerId
                            };
                        }).catch((e)=>{
                            console.error(`Error fetching playername for ${auctionDetails.auctioneerId}. ${JSON.stringify(e)}`);
                            auctionDetails.auctioneer = {
                                name: '',
                                uuid: auctionDetails.auctioneerId
                            };
                        }).finally(()=>{
                            resolve({
                                parsed: (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Parser$2f$APIResponseParser$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseAuctionDetails"])(auctionDetails),
                                original: auctionDetails
                            });
                        });
                    } else {
                        resolve({
                            parsed: (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Parser$2f$APIResponseParser$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseAuctionDetails"])(auctionDetails),
                            original: auctionDetails
                        });
                    }
                },
                reject: (error)=>{
                    reject(error);
                }
            });
        });
    };
    let getPlayerName = (uuid)=>{
        // Reduce amount of API calls during test runs
        if (__TURBOPACK__imported__module__$5b$project$5d2f$properties$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].isTestRunner) {
            return Promise.resolve('TestRunnerUser');
        }
        return new Promise((resolve, reject)=>{
            if (!uuid) {
                resolve('');
                return;
            }
            httpApi.sendApiRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].PLAYER_NAME,
                customRequestURL: `${getApiEndpoint()}/player/${uuid}/name`,
                data: '',
                resolve: (name)=>{
                    resolve(name);
                },
                reject: (error)=>{
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].PLAYER_NAME, error, uuid);
                    reject(error);
                }
            });
        });
    };
    let getPlayerNames = (uuids)=>{
        // Reduce amount of API calls during test runs
        if (__TURBOPACK__imported__module__$5b$project$5d2f$properties$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].isTestRunner) {
            let result = {};
            uuids.forEach((uuid)=>{
                result[uuid] = 'TestRunnerUser';
            });
            return Promise.resolve(result);
        }
        return new Promise((resolve, reject)=>{
            httpApi.sendApiRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].PLAYER_NAMES,
                customRequestURL: `${getApiEndpoint()}/player/names`,
                requestMethod: 'POST',
                requestHeader: {
                    'Content-Type': 'application/json'
                },
                data: '',
                resolve: (names)=>{
                    resolve(names);
                },
                reject: (error)=>{
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].PLAYER_NAMES, error, '');
                    reject(error);
                }
            }, JSON.stringify(uuids));
        });
    };
    let connectionId = null;
    let setConnectionId = ()=>{
        return new Promise((resolve, reject)=>{
            connectionId = connectionId || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])();
            __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$WebsocketHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["websocketHelper"].sendRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].SET_CONNECTION_ID,
                data: connectionId,
                resolve: ()=>{
                    resolve();
                },
                reject: (error)=>{
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].SET_CONNECTION_ID, error, connectionId);
                    reject(error);
                }
            });
        });
    };
    let getVersion = ()=>{
        return new Promise((resolve, reject)=>{
            httpApi.sendRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].GET_VERSION,
                data: '',
                resolve: (response)=>{
                    resolve(response.toString());
                },
                reject: (error)=>{
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].GET_VERSION, error, '');
                    reject(error);
                }
            });
        });
    };
    let subscribe = (topic, types, targets, price, filter)=>{
        return new Promise((resolve, reject)=>{
            let googleId = sessionStorage.getItem('googleId');
            if (!googleId) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('You need to be logged in to create a notification listeners');
                reject();
                return;
            }
            let typesToSend = [
                ...types
            ];
            typesToSend.push(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SubscriptionType"].NONE);
            if (filter) {
                filter._hide = undefined;
                filter._sellerName = undefined;
            }
            let requestData = {
                topicId: topic,
                price: price || undefined,
                type: typesToSend.reduce((a, b)=>{
                    let aNum = typeof a === 'number' ? a : parseInt(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SubscriptionType"][a]);
                    let bNum = typeof b === 'number' ? b : parseInt(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SubscriptionType"][b]);
                    return aNum + bNum;
                }),
                filter: filter ? JSON.stringify(filter) : undefined
            };
            httpApi.sendApiRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].SUBSCRIBE,
                customRequestURL: `${getApiEndpoint()}/notifications/listeners`,
                data: '',
                requestMethod: 'POST',
                requestHeader: {
                    GoogleToken: googleId,
                    'Content-Type': 'application/json'
                },
                resolve: (listener)=>{
                    createNotificationSubscription({
                        id: undefined,
                        sourceSubIdRegex: listener.id?.toString() || '',
                        sourceType: 'Subscription',
                        targets: targets.map((t)=>{
                            return {
                                id: t.id || 0,
                                name: t.name || '',
                                isDisabled: false,
                                priority: 0
                            };
                        })
                    }).then(()=>{
                        resolve();
                    }).catch((e)=>{
                        apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].SUBSCRIBE, e);
                        reject(e);
                    });
                },
                reject: (error)=>{
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].SUBSCRIBE, error);
                    reject(error);
                }
            }, JSON.stringify(requestData));
        });
    };
    let unsubscribe = (subscription)=>{
        return new Promise((resolve, reject)=>{
            let googleId = sessionStorage.getItem('googleId');
            if (!googleId) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('You need to be logged in to delete notification listeners');
                reject();
                return;
            }
            let typesToSend = [
                ...subscription.types
            ];
            typesToSend.push(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SubscriptionType"].NONE);
            let filterToSend = {
                ...subscription.filter
            };
            if (subscription.filter) {
                filterToSend._hide = undefined;
                filterToSend._sellerName = undefined;
            }
            let requestData = {
                id: subscription.id,
                topicId: subscription.topicId,
                price: subscription.price || undefined,
                type: typesToSend.reduce((a, b)=>{
                    let aNum = typeof a === 'number' ? a : parseInt(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SubscriptionType"][a]);
                    let bNum = typeof b === 'number' ? b : parseInt(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SubscriptionType"][b]);
                    return aNum + bNum;
                }),
                filter: ("TURBOPACK compile-time truthy", 1) ? JSON.stringify(filterToSend) : ("TURBOPACK unreachable", undefined)
            };
            httpApi.sendApiRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].UNSUBSCRIBE,
                customRequestURL: `${getApiEndpoint()}/notifications/listeners`,
                data: '',
                requestMethod: 'DELETE',
                requestHeader: {
                    GoogleToken: googleId,
                    'Content-Type': 'application/json'
                },
                resolve: ()=>{
                    resolve();
                },
                reject: (error)=>{
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].UNSUBSCRIBE, error);
                    reject(error);
                }
            }, JSON.stringify(requestData));
        });
    };
    let getNotificationListener = ()=>{
        return new Promise((resolve, reject)=>{
            let googleId = sessionStorage.getItem('googleId');
            if (!googleId) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('You need to be logged in to get notification listeners');
                reject();
                return;
            }
            httpApi.sendApiRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].GET_SUBSCRIPTIONS,
                customRequestURL: `${getApiEndpoint()}/notifications/listeners`,
                data: '',
                requestHeader: {
                    GoogleToken: googleId,
                    'Content-Type': 'application/json'
                },
                resolve: (data)=>{
                    resolve(data ? data.map(__TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Parser$2f$APIResponseParser$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseSubscription"]) : []);
                },
                reject: (error)=>{
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].GET_SUBSCRIPTIONS, error);
                    reject(error);
                }
            });
        });
    };
    let loginWithToken = (id)=>{
        return new Promise((resolve, reject)=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$WebsocketHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["websocketHelper"].sendRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].LOGIN_WITH_TOKEN,
                data: id,
                resolve: (token)=>{
                    resolve(token);
                },
                reject: (error)=>{
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].LOGIN_WITH_TOKEN, error);
                    reject(error);
                }
            });
        });
    };
    let setToken = (token)=>{
        return new Promise((resolve, reject)=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$WebsocketHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["websocketHelper"].sendRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].FCM_TOKEN,
                data: {
                    name: '',
                    token: token
                },
                resolve: ()=>{
                    resolve();
                },
                reject: (error)=>{
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].FCM_TOKEN, error, token);
                    reject(error);
                }
            });
        });
    };
    let getRecentAuctions = (itemTag, itemFilter)=>{
        return new Promise((resolve, reject)=>{
            let params = new URLSearchParams();
            if (itemFilter && Object.keys(itemFilter).length > 0) {
                params = new URLSearchParams(itemFilter);
            }
            httpApi.sendApiRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].RECENT_AUCTIONS,
                customRequestURL: getApiEndpoint() + `/auctions/tag/${itemTag}/recent/overview?${params.toString()}`,
                data: '',
                resolve: (data)=>{
                    resolve(data ? data.map((a)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Parser$2f$APIResponseParser$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseRecentAuction"])(a)) : []);
                },
                reject: (error)=>{
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].RECENT_AUCTIONS, error, itemTag);
                    reject(error);
                }
            });
        });
    };
    let getFlips = ()=>{
        return new Promise((resolve, reject)=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$WebsocketHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["websocketHelper"].sendRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].GET_FLIPS,
                data: '',
                resolve: (data)=>{
                    resolve(data.map((a)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Parser$2f$APIResponseParser$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseFlipAuction"])(a)));
                },
                reject: (error)=>{
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].RECENT_AUCTIONS, error, '');
                    reject(error);
                }
            });
        });
    };
    let getPreloadFlips = ()=>{
        return new Promise((resolve, reject)=>{
            httpApi.sendRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].GET_FLIPS,
                data: '',
                resolve: (data)=>{
                    returnSSRResponse ? resolve(data) : resolve(data.map(__TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Parser$2f$APIResponseParser$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseFlipAuction"]));
                },
                reject: (error)=>{
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].GET_FLIPS, error, '');
                }
            });
        });
    };
    let subscribeFlips = (restrictionList, filter, flipSettings, flipCallback, soldCallback, nextUpdateNotificationCallback, onSubscribeSuccessCallback, onErrorCallback, forceSettingsUpdate = false)=>{
        __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$WebsocketHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["websocketHelper"].removeOldSubscriptionByType(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].SUBSCRIBE_FLIPS);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SettingsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["storeUsedTagsInLocalStorage"])(restrictionList);
        let requestData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SettingsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mapSettingsToApiFormat"])(filter, flipSettings, restrictionList);
        __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$WebsocketHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["websocketHelper"].subscribe({
            type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].SUBSCRIBE_FLIPS,
            data: forceSettingsUpdate ? requestData : null,
            callback: function(response) {
                switch(response.type){
                    case 'flip':
                        if (flipCallback) {
                            flipCallback((0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Parser$2f$APIResponseParser$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseFlipAuction"])(response.data));
                        }
                        break;
                    case 'nextUpdate':
                        if (nextUpdateNotificationCallback) {
                            nextUpdateNotificationCallback();
                        }
                        break;
                    case 'sold':
                        if (soldCallback) {
                            soldCallback(response.data);
                        }
                        break;
                    case 'flipSettings':
                        if (!response.data) {
                            api.subscribeFlips(restrictionList, filter, flipSettings, flipCallback, soldCallback, nextUpdateNotificationCallback, undefined, onErrorCallback, true);
                        } else {
                            (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SettingsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setSettingsFromServerSide"])(response.data);
                        }
                        break;
                    case 'settingsUpdate':
                        let data = response.data;
                        if (data.changer === window.sessionStorage.getItem('sessionId')) {
                            return;
                        }
                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SettingsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setSettingsFromServerSide"])(response.data);
                        break;
                    case 'ok':
                        if (onSubscribeSuccessCallback) {
                            onSubscribeSuccessCallback();
                        }
                        break;
                    default:
                        break;
                }
            },
            resubscribe: function(subscription) {
                let filter = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SettingsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getSettingsObject"])(__TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SettingsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FLIPPER_FILTER_KEY"], {});
                let restrictions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SettingsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getSettingsObject"])(__TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SettingsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RESTRICTIONS_SETTINGS_KEY"], []);
                subscribeFlips(restrictions, filter, (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$FlipUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getFlipCustomizeSettings"])(), flipCallback, soldCallback, nextUpdateNotificationCallback, undefined, onErrorCallback, false);
            },
            onError: function(message) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(message);
                if (onErrorCallback) {
                    onErrorCallback();
                }
            }
        });
    };
    const debounceSubFlipAnonymFunction = function() {
        let timerId;
        return (restrictionList, filter, flipSettings, flipCallback, soldCallback, nextUpdateNotificationCallback, onSubscribeSuccessCallback)=>{
            clearTimeout(timerId);
            timerId = setTimeout(()=>{
                __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$WebsocketHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["websocketHelper"].removeOldSubscriptionByType(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].SUBSCRIBE_FLIPS);
                let requestData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SettingsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mapSettingsToApiFormat"])(filter, flipSettings, restrictionList);
                __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$WebsocketHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["websocketHelper"].subscribe({
                    type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].SUBSCRIBE_FLIPS_ANONYM,
                    data: requestData,
                    callback: function(response) {
                        switch(response.type){
                            case 'flip':
                                if (flipCallback) {
                                    flipCallback((0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Parser$2f$APIResponseParser$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseFlipAuction"])(response.data));
                                }
                                break;
                            case 'nextUpdate':
                                if (nextUpdateNotificationCallback) {
                                    nextUpdateNotificationCallback();
                                }
                                break;
                            case 'sold':
                                if (soldCallback) {
                                    soldCallback(response.data);
                                }
                                break;
                            case 'ok':
                                if (onSubscribeSuccessCallback) {
                                    onSubscribeSuccessCallback();
                                }
                                break;
                            default:
                                break;
                        }
                    },
                    resubscribe: function(subscription) {
                        let filter = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SettingsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getSettingsObject"])(__TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SettingsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FLIPPER_FILTER_KEY"], {});
                        let restrictions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SettingsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getSettingsObject"])(__TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SettingsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RESTRICTIONS_SETTINGS_KEY"], []);
                        subscribeFlipsAnonym(restrictions, filter, (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$FlipUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getFlipCustomizeSettings"])(), flipCallback, soldCallback, nextUpdateNotificationCallback, undefined);
                    },
                    onError: function(message) {
                        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(message);
                    }
                });
            }, 2000);
        };
    }();
    let subscribeFlipsAnonym = (restrictionList, filter, flipSettings, flipCallback, soldCallback, nextUpdateNotificationCallback, onSubscribeSuccessCallback)=>{
        debounceSubFlipAnonymFunction(restrictionList, filter, flipSettings, flipCallback, soldCallback, nextUpdateNotificationCallback, onSubscribeSuccessCallback);
    };
    let unsubscribeFlips = ()=>{
        return new Promise((resolve, reject)=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$WebsocketHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["websocketHelper"].sendRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].UNSUBSCRIBE_FLIPS,
                data: '',
                resolve: function(data) {
                    resolve();
                },
                reject: function(error) {
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].ACTIVE_AUCTIONS, error, '');
                    reject(error);
                }
            });
        });
    };
    let getFilters = (tag)=>{
        return new Promise((resolve, reject)=>{
            httpApi.sendApiRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].GET_FILTER,
                customRequestURL: `${getApiEndpoint()}/filter/options?itemTag=${tag}`,
                data: '',
                resolve: (data)=>{
                    resolve(data.map((a)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Parser$2f$APIResponseParser$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseFilterOption"])(a)));
                },
                reject: (error)=>{
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].GET_FILTER, error, tag);
                }
            });
        });
    };
    let getNewPlayers = ()=>{
        return new Promise((resolve, reject)=>{
            httpApi.sendLimitedCacheRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].NEW_PLAYERS,
                data: '',
                resolve: function(data) {
                    returnSSRResponse ? resolve(data) : resolve(data.map((p)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Parser$2f$APIResponseParser$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parsePlayer"])(p)));
                },
                reject: function(error) {
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].NEW_PLAYERS, error, '');
                    reject(error);
                }
            }, 5);
        });
    };
    let getNewItems = ()=>{
        return new Promise((resolve, reject)=>{
            httpApi.sendLimitedCacheRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].NEW_ITEMS,
                data: '',
                resolve: function(data) {
                    returnSSRResponse ? resolve(data) : resolve(data.map((i)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Parser$2f$APIResponseParser$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseItem"])(i)));
                },
                reject: function(error) {
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].NEW_ITEMS, error, '');
                    reject(error);
                }
            }, 15);
        });
    };
    let getPopularSearches = ()=>{
        return new Promise((resolve, reject)=>{
            httpApi.sendLimitedCacheRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].POPULAR_SEARCHES,
                data: '',
                resolve: function(data) {
                    returnSSRResponse ? resolve(data) : resolve(data.map((s)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Parser$2f$APIResponseParser$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parsePopularSearch"])(s)));
                },
                reject: function(error) {
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].POPULAR_SEARCHES, error, '');
                    reject(error);
                }
            }, 5);
        });
    };
    let getEndedAuctions = ()=>{
        return new Promise((resolve, reject)=>{
            httpApi.sendLimitedCacheRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].ENDED_AUCTIONS,
                data: '',
                resolve: function(data) {
                    returnSSRResponse ? resolve(data) : resolve(data.map((a)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Parser$2f$APIResponseParser$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseAuction"])(a)));
                },
                reject: function(error) {
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].ENDED_AUCTIONS, error, '');
                    reject(error);
                }
            }, 1);
        });
    };
    let getNewAuctions = ()=>{
        return new Promise((resolve, reject)=>{
            httpApi.sendLimitedCacheRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].NEW_AUCTIONS,
                data: '',
                resolve: function(data) {
                    returnSSRResponse ? resolve(data) : resolve(data.map((a)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Parser$2f$APIResponseParser$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseAuction"])(a)));
                },
                reject: function(error) {
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].NEW_AUCTIONS, error, '');
                    reject(error);
                }
            }, 1);
        });
    };
    let getFlipBasedAuctions = (flipUUID)=>{
        return new Promise((resolve, reject)=>{
            httpApi.sendRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].GET_FLIP_BASED_AUCTIONS,
                data: flipUUID,
                resolve: (data)=>{
                    resolve(data.map((a)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Parser$2f$APIResponseParser$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseAuction"])(a)));
                },
                reject: (error)=>{
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].GET_FLIP_BASED_AUCTIONS, error, flipUUID);
                    reject(error);
                }
            });
        });
    };
    let stripePurchase = (productId, coinAmount)=>{
        return new Promise((resolve, reject)=>{
            let googleId = sessionStorage.getItem('googleId');
            if (!googleId) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('You need to be logged in to purchase something.');
                reject();
                return;
            }
            let data = {
                userId: googleId,
                productId: productId
            };
            httpApi.sendApiRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].STRIPE_PAYMENT_SESSION,
                requestMethod: 'POST',
                requestHeader: {
                    GoogleToken: data.userId,
                    'Content-Type': 'application/json'
                },
                data: data.productId,
                resolve: (data)=>{
                    resolve((0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Parser$2f$APIResponseParser$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parsePaymentResponse"])(data));
                },
                reject: (error)=>{
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].STRIPE_PAYMENT_SESSION, error, data);
                    reject(error);
                }
            }, JSON.stringify({
                coinAmount
            }));
        });
    };
    let paypalPurchase = (productId, coinAmount)=>{
        return new Promise((resolve, reject)=>{
            let googleId = sessionStorage.getItem('googleId');
            if (!googleId) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('You need to be logged in to purchase something.');
                reject();
                return;
            }
            let data = {
                userId: googleId,
                productId: productId
            };
            httpApi.sendApiRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].PAYPAL_PAYMENT,
                requestMethod: 'POST',
                data: data.productId,
                requestHeader: {
                    GoogleToken: data.userId,
                    'Content-Type': 'application/json'
                },
                resolve: (response)=>{
                    resolve((0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Parser$2f$APIResponseParser$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parsePaymentResponse"])(response));
                },
                reject: (error)=>{
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].PAYPAL_PAYMENT, error, data);
                    reject(error);
                }
            }, JSON.stringify({
                coinAmount
            }));
        });
    };
    let lemonsqueezyPurchase = (productId, coinAmount)=>{
        return new Promise((resolve, reject)=>{
            let googleId = sessionStorage.getItem('googleId');
            if (!googleId) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('You need to be logged in to purchase something.');
                reject();
                return;
            }
            let data = {
                userId: googleId,
                productId: productId
            };
            httpApi.sendApiRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].LEMONSQUEEZY_PAYMENT,
                requestMethod: 'POST',
                data: data.productId,
                requestHeader: {
                    GoogleToken: data.userId,
                    'Content-Type': 'application/json'
                },
                resolve: (response)=>{
                    resolve((0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Parser$2f$APIResponseParser$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parsePaymentResponse"])(response));
                },
                reject: (error)=>{
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].LEMONSQUEEZY_PAYMENT, error, data);
                    reject(error);
                }
            }, JSON.stringify({
                coinAmount
            }));
        });
    };
    let purchaseWithCoflcoins = (productId, googleToken, count)=>{
        return new Promise((resolve, reject)=>{
            let data = {
                userId: googleToken,
                productId: productId
            };
            httpApi.sendApiRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].PURCHASE_WITH_COFLCOiNS,
                data: '',
                requestMethod: 'POST',
                requestHeader: {
                    GoogleToken: data.userId,
                    'Content-Type': 'application/json'
                },
                resolve: function() {
                    resolve();
                },
                reject: function(error) {
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].PURCHASE_WITH_COFLCOiNS, error, data);
                    reject(error);
                }
            }, JSON.stringify({
                count: count,
                slug: productId
            }));
        });
    };
    let subscribeCoflCoinChange = ()=>{
        __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$WebsocketHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["websocketHelper"].subscribe({
            type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].SUBSCRIBE_EVENTS,
            data: '',
            resubscribe: function(subscription) {
                subscribeCoflCoinChange();
            },
            onError: function(message) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(message);
            },
            callback: function(response) {
                if (response.data.sourceType === 'purchase' || response.data.sourceType === 'topup') {
                    // CoflCoins shouldnt change below 0 with a purchase or topup change
                    let newCoflCoinAmount = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$CoflCoinsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getCurrentCoflCoins"])() + Math.round(response.data.data.amount);
                    if (newCoflCoinAmount < 0) {
                        newCoflCoinAmount = 0;
                    }
                    document.dispatchEvent(new CustomEvent(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CUSTOM_EVENTS"].COFLCOIN_UPDATE, {
                        detail: {
                            coflCoins: newCoflCoinAmount
                        }
                    }));
                }
            }
        });
    };
    let getCoflcoinBalance = ()=>{
        return new Promise((resolve, reject)=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$WebsocketHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["websocketHelper"].sendRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].GET_COFLCOIN_BALANCE,
                data: '',
                resolve: function(response) {
                    resolve(parseInt(response));
                },
                reject: function(error) {
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].GET_COFLCOIN_BALANCE, error, '');
                    reject(error);
                }
            });
        });
    };
    let getRefInfo = ()=>{
        return new Promise((resolve, reject)=>{
            let googleId = sessionStorage.getItem('googleId');
            if (!googleId) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('You need to be logged in to use the ref system.');
                reject();
                return;
            }
            httpApi.sendApiRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].GET_REF_INFO,
                data: '',
                requestHeader: {
                    GoogleToken: googleId
                },
                resolve: (response)=>{
                    resolve((0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Parser$2f$APIResponseParser$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseRefInfo"])(response));
                },
                reject: (error)=>{
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].GET_REF_INFO, error, '');
                    reject(error);
                }
            });
        });
    };
    let setRef = (refId)=>{
        return new Promise((resolve, reject)=>{
            let googleId = sessionStorage.getItem('googleId');
            if (!googleId) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('You need to be logged in to use the ref system.');
                reject();
                return;
            }
            httpApi.sendApiRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].SET_REF,
                data: '',
                requestMethod: 'POST',
                requestHeader: {
                    GoogleToken: googleId,
                    'Content-Type': 'application/json'
                },
                resolve: ()=>{
                    resolve();
                },
                reject: (error)=>{
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].SET_REF, error, '');
                    reject(error);
                }
            }, JSON.stringify({
                refCode: refId
            }));
        });
    };
    let getActiveAuctions = (item, order, filter = {})=>{
        return new Promise((resolve, reject)=>{
            let params = {
                orderBy: order
            };
            Object.keys(filter).forEach((key)=>{
                params[key] = filter[key].toString();
            });
            httpApi.sendApiRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].ACTIVE_AUCTIONS,
                customRequestURL: `${getApiEndpoint()}/auctions/tag/${item.tag}/active/overview?${new URLSearchParams(params).toString()}`,
                data: '',
                resolve: function(data) {
                    resolve(data.map((a)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Parser$2f$APIResponseParser$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseRecentAuction"])(a)));
                },
                reject: function(error) {
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].ACTIVE_AUCTIONS, error, {
                        tag: item.tag,
                        filter,
                        order
                    });
                    reject(error);
                }
            });
        });
    };
    let connectMinecraftAccount = (playerUUID)=>{
        return new Promise((resolve, reject)=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$WebsocketHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["websocketHelper"].sendRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].CONNECT_MINECRAFT_ACCOUNT,
                data: playerUUID,
                resolve: function(data) {
                    resolve((0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Parser$2f$APIResponseParser$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseMinecraftConnectionInfo"])(data));
                },
                reject: function(error) {
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].CONNECT_MINECRAFT_ACCOUNT, error, playerUUID);
                    reject(error);
                }
            });
        });
    };
    let accountInfo;
    let getAccountInfo = ()=>{
        return new Promise((resolve, reject)=>{
            if (accountInfo) {
                resolve(accountInfo);
                return;
            }
            __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$WebsocketHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["websocketHelper"].sendRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].GET_ACCOUNT_INFO,
                data: '',
                resolve: function(accountInfo) {
                    let info = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Parser$2f$APIResponseParser$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseAccountInfo"])(accountInfo);
                    accountInfo = info;
                    resolve(info);
                },
                reject: function(error) {
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].GET_ACCOUNT_INFO, error, '');
                }
            });
        });
    };
    let itemSearch = (searchText)=>{
        return new Promise((resolve, reject)=>{
            httpApi.sendApiRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].ITEM_SEARCH,
                data: searchText,
                resolve: function(data) {
                    resolve(data.map((a)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Parser$2f$APIResponseParser$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseSearchResultItem"])(a)));
                },
                reject: function(error) {
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].ITEM_SEARCH, error, searchText);
                    reject(error);
                }
            });
        });
    };
    let authenticateModConnection = async (conId, googleToken)=>{
        let timeout = setTimeout(()=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].warn(/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                children: 'The login seems to take longer that expected. Are you using Kaspersky? If so, the "Secure Browsing" feature seems to interfere with the login'
            }, void 0, false, {
                fileName: "[project]/api/ApiHelper.tsx",
                lineNumber: 1409,
                columnNumber: 17
            }, this));
        }, 10000);
        return new Promise((resolve, reject)=>{
            httpApi.sendApiRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].AUTHENTICATE_MOD_CONNECTION,
                requestMethod: 'POST',
                data: '',
                requestHeader: {
                    GoogleToken: googleToken,
                    'Content-Type': 'application/json'
                },
                customRequestURL: `${getApiEndpoint()}/mod/auth?newId=${encodeURIComponent(conId)}`,
                resolve: function() {
                    clearTimeout(timeout);
                    resolve();
                },
                reject: function(error) {
                    clearTimeout(timeout);
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].AUTHENTICATE_MOD_CONNECTION, error, conId);
                    reject(error);
                }
            });
        });
    };
    let getFlipUpdateTime = ()=>{
        return new Promise((resolve, reject)=>{
            httpApi.sendApiRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].FLIP_UPDATE_TIME,
                data: '',
                resolve: function(data) {
                    resolve(new Date(data));
                },
                reject: function(error) {
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].FLIP_UPDATE_TIME, error, '');
                }
            });
        });
    };
    let playerSearch = (playerName)=>{
        return new Promise((resolve, reject)=>{
            httpApi.sendApiRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].PLAYER_SEARCH,
                data: playerName,
                resolve: function(players) {
                    resolve(players ? players.map(__TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Parser$2f$APIResponseParser$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parsePlayer"]) : []);
                },
                reject: function(error) {
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].PLAYER_SEARCH, error, playerName);
                    reject(error);
                }
            });
        });
    };
    let getLowSupplyItems = ()=>{
        return new Promise((resolve, reject)=>{
            httpApi.sendApiRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].GET_LOW_SUPPLY_ITEMS,
                data: '',
                resolve: function(items) {
                    returnSSRResponse ? resolve(items) : resolve(items.map((item)=>{
                        let lowSupplyItem = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Parser$2f$APIResponseParser$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseLowSupplyItem"])(item);
                        return lowSupplyItem;
                    }));
                },
                reject: function(error) {
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].GET_LOW_SUPPLY_ITEMS, error, '');
                    reject(error);
                }
            });
        });
    };
    let sendFeedback = (feedbackKey, feedback)=>{
        return new Promise((resolve, reject)=>{
            let googleId = sessionStorage.getItem('googleId');
            let user;
            if (googleId) {
                let parts = googleId.split('.');
                if (parts.length > 2) {
                    let obj = JSON.parse((0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Base64Utils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["atobUnicode"])(parts[1]));
                    user = obj.sub;
                }
            }
            let requestData = {
                Context: 'Skyblock',
                User: user || '',
                Feedback: JSON.stringify(feedback),
                FeedbackName: feedbackKey
            };
            httpApi.sendApiRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].SEND_FEEDBACK,
                data: '',
                customRequestURL: (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$PropertiesUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getProperty"])('feedbackEndpoint'),
                requestMethod: 'POST',
                requestHeader: {
                    'Content-Type': 'application/json'
                },
                resolve: function() {
                    resolve();
                },
                reject: function(error) {
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].SEND_FEEDBACK, error, feedback);
                    reject(error);
                }
            }, JSON.stringify(requestData));
        });
    };
    let getProfitableCrafts = ()=>{
        return new Promise((resolve, reject)=>{
            httpApi.sendApiRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].GET_PROFITABLE_CRAFTS,
                customRequestURL: getApiEndpoint() + '/' + __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].GET_PROFITABLE_CRAFTS,
                data: '',
                resolve: function(crafts) {
                    returnSSRResponse ? resolve(crafts) : resolve((0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Parser$2f$APIResponseParser$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseProfitableCrafts"])(crafts));
                },
                reject: function(error) {
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].GET_PROFITABLE_CRAFTS, error, '');
                    reject(error);
                }
            });
        });
    };
    let triggerPlayerNameCheck = (playerUUID)=>{
        return new Promise((resolve, reject)=>{
            httpApi.sendApiRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].TRIGGER_PLAYER_NAME_CHECK,
                data: '',
                customRequestURL: getApiEndpoint() + '/player/' + playerUUID + '/name',
                requestMethod: 'POST',
                resolve: function() {
                    resolve();
                },
                reject: function(error) {
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].TRIGGER_PLAYER_NAME_CHECK, error, '');
                    reject(error);
                }
            });
        });
    };
    let getPlayerProfiles = (playerUUID)=>{
        return new Promise((resolve, reject)=>{
            httpApi.sendApiRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].GET_PLAYER_PROFILES,
                data: playerUUID,
                resolve: function(result) {
                    resolve(Object.keys(result.profiles).map((key)=>{
                        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Parser$2f$APIResponseParser$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseSkyblockProfile"])(result.profiles[key]);
                    }));
                },
                reject: function(error) {
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].TRIGGER_PLAYER_NAME_CHECK, error, playerUUID);
                }
            });
        });
    };
    let getCraftingRecipe = (itemTag)=>{
        return new Promise((resolve, reject)=>{
            httpApi.sendApiRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].GET_CRAFTING_RECIPE,
                data: itemTag,
                resolve: function(data) {
                    resolve((0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Parser$2f$APIResponseParser$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseCraftingRecipe"])(data));
                },
                reject: function(error) {
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].GET_CRAFTING_RECIPE, error, itemTag);
                    reject(error);
                }
            });
        });
    };
    let getLowestBin = (itemTag)=>{
        return new Promise((resolve, reject)=>{
            httpApi.sendApiRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].GET_LOWEST_BIN,
                customRequestURL: 'item/price/' + itemTag + '/bin',
                data: itemTag,
                resolve: function(data) {
                    resolve({
                        lowest: data.lowest,
                        secondLowest: data.secondLowest
                    });
                },
                reject: function(error) {
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].GET_LOWEST_BIN, error, itemTag);
                    reject(error);
                }
            });
        });
    };
    let flipFilters = (tag)=>{
        return new Promise((resolve, reject)=>{
            httpApi.sendLimitedCacheRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].FLIP_FILTERS,
                data: tag,
                resolve: function(data) {
                    resolve(data.map((a)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Parser$2f$APIResponseParser$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseFilterOption"])(a)));
                },
                reject: function(error) {
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].FLIP_FILTERS, error, tag);
                    reject(error);
                }
            }, 1);
        });
    };
    let getBazaarTags = ()=>{
        return new Promise((resolve, reject)=>{
            httpApi.sendApiRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].GET_BAZAAR_TAGS,
                data: '',
                resolve: function(data) {
                    resolve(data);
                },
                reject: function(error) {
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].GET_BAZAAR_TAGS, error, '');
                    reject(error);
                }
            });
        });
    };
    let getItemPriceSummary = (itemTag, filter)=>{
        let getParams = new URLSearchParams(filter).toString();
        return new Promise((resolve, reject)=>{
            httpApi.sendApiRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].ITEM_PRICE_SUMMARY,
                customRequestURL: `${getApiEndpoint()}/${__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].ITEM_PRICE_SUMMARY}/${itemTag}?${getParams}`,
                data: '',
                resolve: function(data) {
                    returnSSRResponse ? resolve(data) : resolve((0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Parser$2f$APIResponseParser$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseItemSummary"])(data));
                },
                reject: function(error) {
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].ITEM_PRICE_SUMMARY, error, '');
                    reject(error);
                }
            });
        });
    };
    let setFlipSetting = (key, value)=>{
        if (sessionStorage.getItem('googleId') === null) {
            return Promise.resolve();
        }
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SettingsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["storeUsedTagsInLocalStorage"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SettingsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getSettingsObject"])(__TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SettingsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RESTRICTIONS_SETTINGS_KEY"], []));
        return new Promise((resolve, reject)=>{
            let data = {
                key,
                value: typeof value === 'object' ? JSON.stringify(value) : value.toString(),
                changer: window.sessionStorage.getItem('sessionId')
            };
            __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$WebsocketHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["websocketHelper"].sendRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].SET_FLIP_SETTING,
                data: data,
                resolve: ()=>{
                    resolve();
                },
                reject: (error)=>{
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].SET_FLIP_SETTING, error, data);
                }
            });
        });
    };
    let getKatFlips = ()=>{
        return new Promise((resolve, reject)=>{
            httpApi.sendApiRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].GET_KAT_FLIPS,
                data: '',
                resolve: function(data) {
                    returnSSRResponse ? resolve(data) : resolve(data.map(__TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Parser$2f$APIResponseParser$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseKatFlip"]));
                },
                reject: function(error) {
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].GET_KAT_FLIPS, error, '');
                }
            });
        });
    };
    let getTrackedFlipsForPlayer = (playerUUID, from, to)=>{
        return new Promise((resolve, reject)=>{
            let params = new URLSearchParams();
            if (from && to) {
                params.set('start', from.toISOString());
                params.set('end', to.toISOString());
            }
            let googleId = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SSRUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isClientSideRendering"])() ? sessionStorage.getItem('googleId') : null;
            let requestHeader = googleId ? {
                GoogleToken: googleId
            } : {};
            httpApi.sendApiRequest({
                customRequestURL: `${getApiEndpoint()}/flip/stats/player/${playerUUID}?${params.toString()}`,
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].GET_TRACKED_FLIPS_FOR_PLAYER,
                requestHeader: requestHeader,
                data: playerUUID,
                resolve: function(data) {
                    returnSSRResponse ? resolve(data) : resolve((0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Parser$2f$APIResponseParser$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseFlipTrackingResponse"])(data));
                },
                reject: function(error) {
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].GET_TRACKED_FLIPS_FOR_PLAYER, error, playerUUID);
                    reject(error);
                }
            });
        });
    };
    let transferCoflCoins = (email, mcId, amount, reference)=>{
        return new Promise((resolve, reject)=>{
            let data = {
                email: email,
                mcId: mcId,
                amount: amount,
                reference: reference
            };
            __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$WebsocketHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["websocketHelper"].sendRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].TRASFER_COFLCOINS,
                data: data,
                resolve: function() {
                    resolve();
                },
                reject: function(error) {
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].TRASFER_COFLCOINS, error, data);
                    reject(error);
                }
            });
        });
    };
    let getBazaarSnapshot = (itemTag, timestamp)=>{
        return new Promise((resolve, reject)=>{
            let isoTimestamp = new Date(timestamp).toISOString();
            httpApi.sendApiRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].GET_BAZAAR_SNAPSHOT,
                customRequestURL: (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$PropertiesUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getProperty"])('apiEndpoint') + `/bazaar/${itemTag}/snapshot${isoTimestamp ? `?timestamp=${isoTimestamp}` : ''}`,
                data: '',
                resolve: function(data) {
                    if (!data) {
                        resolve({
                            item: {
                                tag: ''
                            },
                            buyData: {
                                moving: 0,
                                orderCount: 0,
                                price: 0,
                                volume: 0
                            },
                            sellData: {
                                moving: 0,
                                orderCount: 0,
                                price: 0,
                                volume: 0
                            },
                            sellOrders: [],
                            buyOrders: [],
                            timeStamp: new Date()
                        });
                        return;
                    }
                    resolve((0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Parser$2f$APIResponseParser$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseBazaarSnapshot"])(data));
                },
                reject: function(error) {
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].GET_BAZAAR_SNAPSHOT, error, {
                        itemTag,
                        timestamp: isoTimestamp
                    });
                    reject(error);
                }
            });
        });
    };
    let getPrivacySettings = ()=>{
        return new Promise((resolve, reject)=>{
            let googleId = sessionStorage.getItem('googleId');
            if (!googleId) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('You need to be logged in to configure privacy settings.');
                reject();
                return;
            }
            httpApi.sendApiRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].GET_PRIVACY_SETTINGS,
                data: '',
                customRequestURL: `${getApiEndpoint()}/user/privacy`,
                requestHeader: {
                    GoogleToken: googleId
                },
                resolve: (data)=>{
                    resolve((0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Parser$2f$APIResponseParser$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parsePrivacySettings"])(data));
                },
                reject: (error)=>{
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].GET_PRIVACY_SETTINGS, error, '');
                    reject(error);
                }
            });
        });
    };
    let setPrivacySettings = (settings)=>{
        return new Promise((resolve, reject)=>{
            let googleId = sessionStorage.getItem('googleId');
            if (!googleId) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('You need to be logged in to save privacy settings.');
                reject();
                return;
            }
            httpApi.sendApiRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].SET_PRIVACY_SETTINGS,
                data: '',
                requestMethod: 'POST',
                customRequestURL: `${getApiEndpoint()}/user/privacy`,
                requestHeader: {
                    GoogleToken: googleId,
                    'Content-Type': 'application/json'
                },
                resolve: ()=>{
                    resolve();
                },
                reject: (error)=>{
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].SET_PRIVACY_SETTINGS, error, settings);
                    reject(error);
                }
            }, JSON.stringify(settings));
        });
    };
    let checkRat = (hash)=>{
        return new Promise((resolve, reject)=>{
            httpApi.sendApiRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].CHECK_FOR_RAT,
                data: '',
                customRequestURL: `https://isthisarat.com/api/signature/${hash}`,
                resolve: (data)=>{
                    resolve(data);
                },
                reject: (error)=>{
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].CHECK_FOR_RAT, error, hash);
                    reject(error);
                }
            });
        });
    };
    let getPremiumProducts = ()=>{
        return new Promise((resolve, reject)=>{
            let googleId = sessionStorage.getItem('googleId');
            if (!googleId) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('You need to be logged in to load premium products.');
                reject();
                return;
            }
            httpApi.sendApiRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].GET_PREMIUM_PRODUCTS,
                data: '',
                requestMethod: 'POST',
                customRequestURL: `${getApiEndpoint()}/premium/user/owns`,
                requestHeader: {
                    GoogleToken: googleId,
                    'Content-Type': 'application/json'
                },
                resolve: (products)=>{
                    localStorage.setItem(__TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SettingsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LAST_PREMIUM_PRODUCTS"], JSON.stringify(products));
                    resolve((0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Parser$2f$APIResponseParser$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parsePremiumProducts"])(products));
                },
                reject: (error)=>{
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].GET_PREMIUM_PRODUCTS, error, '');
                    reject(error);
                }
            }, JSON.stringify(__TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$PremiumTypeUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PREMIUM_TYPES"].map((type)=>type.productId)));
        });
    };
    /**
     * Uses the last loaded premium products (if available) to instantly call the callback function
     * The newest premium products are loaded after that and the callback is executed again
     */ let refreshLoadPremiumProducts = (callback, onError)=>{
        let lastPremiumProducts = localStorage.getItem(__TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SettingsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LAST_PREMIUM_PRODUCTS"]);
        if (lastPremiumProducts) {
            try {
                callback((0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Parser$2f$APIResponseParser$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parsePremiumProducts"])(JSON.parse(lastPremiumProducts)));
            } catch  {
                callback([]);
            }
        }
        getPremiumProducts().then((prodcuts)=>{
            callback(prodcuts);
        }).catch(()=>{
            onError();
        });
    };
    let unsubscribeAll = ()=>{
        return new Promise((resolve, reject)=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$WebsocketHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["websocketHelper"].sendRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].UNSUBSCRIBE_ALL,
                data: '',
                resolve: ()=>{
                    resolve();
                },
                reject: (error)=>{
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].UNSUBSCRIBE_ALL, error, '');
                    reject(error);
                }
            });
        });
    };
    let getItemNames = (items)=>{
        return new Promise((resolve, reject)=>{
            httpApi.sendApiRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].GET_ITEM_NAMES,
                requestMethod: 'POST',
                requestHeader: {
                    'Content-Type': 'application/json'
                },
                data: '',
                resolve: (data)=>{
                    resolve(data);
                },
                reject: (error)=>{
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].GET_ITEM_NAMES, error, items);
                    reject(error);
                }
            }, JSON.stringify(items.map((item)=>item.tag)));
        });
    };
    let checkFilter = (auction, filter)=>{
        return new Promise((resolve, reject)=>{
            httpApi.sendApiRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].CHECK_FILTER,
                requestMethod: 'POST',
                customRequestURL: `${getApiEndpoint()}/Filter`,
                requestHeader: {
                    'Content-Type': 'application/json'
                },
                data: '',
                resolve: (data)=>{
                    resolve(data);
                },
                reject: (error)=>{
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].CHECK_FILTER, error, {
                        auction,
                        filter
                    });
                    reject(error);
                }
            }, JSON.stringify({
                filters: filter,
                auction: auction
            }));
        });
    };
    let getRelatedItems = (tag)=>{
        return new Promise((resolve, reject)=>{
            httpApi.sendApiRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].RELATED_ITEMS,
                customRequestURL: `${getApiEndpoint()}/item/${tag}/similar`,
                data: '',
                resolve: (data)=>{
                    resolve(data.map((item)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Parser$2f$APIResponseParser$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseItem"])(item)));
                },
                reject: (error)=>{
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].RELATED_ITEMS, error, tag);
                    reject(error);
                }
            });
        });
    };
    let getOwnerHistory = (uid)=>{
        return new Promise((resolve, reject)=>{
            httpApi.sendApiRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].OWNER_HISOTRY,
                customRequestURL: `${getApiEndpoint()}/auctions/uid/${uid}/sold`,
                data: '',
                resolve: (data)=>{
                    resolve(data.map(__TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Parser$2f$APIResponseParser$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseOwnerHistory"]));
                },
                reject: (error)=>{
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].OWNER_HISOTRY, error, uid);
                    reject(error);
                }
            });
        });
    };
    let getMayorData = (start, end)=>{
        let params = new URLSearchParams();
        params.set('from', start.toISOString());
        params.set('to', end.toISOString());
        return new Promise((resolve, reject)=>{
            httpApi.sendApiRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].MAYOR_DATA,
                customRequestURL: `${getApiEndpoint()}/mayor?${params.toString()}`,
                data: '',
                resolve: (data)=>{
                    resolve(data.map(__TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Parser$2f$APIResponseParser$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseMayorData"]));
                },
                reject: (error)=>{
                    // temporarly don't show mayor errors
                    //apiErrorHandler(RequestType.MAYOR_DATA, error, { start, end })
                    reject(error);
                }
            });
        });
    };
    let getTransactions = ()=>{
        return new Promise((resolve, reject)=>{
            let googleId = sessionStorage.getItem('googleId');
            if (!googleId) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('You need to be logged in to load transactions.');
                reject();
                return;
            }
            httpApi.sendApiRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].GET_TRANSACTIONS,
                requestHeader: {
                    GoogleToken: googleId,
                    'Content-Type': 'application/json'
                },
                customRequestURL: `${getApiEndpoint()}/premium/transactions`,
                data: '',
                resolve: (data)=>{
                    if (!data) {
                        return [];
                    }
                    resolve(data.map(__TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Parser$2f$APIResponseParser$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseTransaction"]));
                },
                reject: (error)=>{
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].STRIPE_PAYMENT_SESSION, error, '');
                    reject(error);
                }
            });
        });
    };
    let getPlayerInventory = ()=>{
        return new Promise((resolve, reject)=>{
            let googleId = sessionStorage.getItem('googleId');
            if (!googleId) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('You need to be logged in to load the inventory.');
                reject();
                return;
            }
            httpApi.sendApiRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].INVENTORY_DATA,
                customRequestURL: `${getApiEndpoint()}/inventory`,
                requestHeader: {
                    GoogleToken: googleId,
                    'Content-Type': 'application/json'
                },
                data: '',
                resolve: (data)=>{
                    resolve(data ? data.slice(Math.max(data.length - 36, 0)).map(__TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Parser$2f$APIResponseParser$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseInventoryData"]) : []);
                },
                reject: (error)=>{
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].INVENTORY_DATA, error);
                    reject(error);
                }
            });
        });
    };
    let createTradeOffer = (playerUUID, offer, wantedItems = [], offeredCoins)=>{
        return new Promise((resolve, reject)=>{
            let googleId = sessionStorage.getItem('googleId');
            if (!googleId) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('You need to be logged in to load the inventory.');
                reject();
                return;
            }
            httpApi.sendApiRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].CREATE_TRADE_OFFER,
                requestMethod: 'POST',
                customRequestURL: `${getApiEndpoint()}/trades`,
                requestHeader: {
                    GoogleToken: googleId,
                    'Content-Type': 'application/json'
                },
                data: '',
                resolve: ()=>{
                    resolve();
                },
                reject: (error)=>{
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].CREATE_TRADE_OFFER, error);
                    reject(error);
                }
            }, JSON.stringify([
                {
                    playerUuid: playerUUID,
                    item: offer,
                    coins: offeredCoins,
                    wantedItems: wantedItems
                }
            ]));
        });
    };
    let deleteTradeOffer = (tradeId)=>{
        return new Promise((resolve, reject)=>{
            let googleId = sessionStorage.getItem('googleId');
            if (!googleId) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('You need to be logged in to delete your trades.');
                reject();
                return;
            }
            httpApi.sendApiRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].DELETE_TRADE_OFFER,
                requestMethod: 'DELETE',
                customRequestURL: `${getApiEndpoint()}/trades/${tradeId}`,
                requestHeader: {
                    GoogleToken: googleId,
                    'Content-Type': 'application/json'
                },
                data: '',
                resolve: ()=>{
                    resolve();
                },
                reject: (error)=>{
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].DELETE_TRADE_OFFER, error, tradeId);
                    reject(error);
                }
            });
        });
    };
    let getTradeOffers = (onlyOwn, filter)=>{
        return new Promise((resolve, reject)=>{
            let googleId = sessionStorage.getItem('googleId');
            if (!googleId) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('You need to be logged in to use the trade feature.');
                reject();
                return;
            }
            let params = new URLSearchParams();
            if (filter) {
                params = new URLSearchParams({
                    filters: JSON.stringify(filter)
                });
            }
            httpApi.sendApiRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].GET_TRADES,
                customRequestURL: `${getApiEndpoint()}/trades${onlyOwn ? '/own' : ''}?${filter ? `${params.toString()}` : ''}`,
                data: '',
                requestHeader: {
                    GoogleToken: googleId,
                    'Content-Type': 'application/json'
                },
                resolve: (data)=>{
                    resolve(data ? data.map(__TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Parser$2f$APIResponseParser$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseTradeObject"]) : []);
                },
                reject: (error)=>{
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].GET_TRADES, error);
                    reject(error);
                }
            });
        });
    };
    let getNotificationTargets = ()=>{
        return new Promise((resolve, reject)=>{
            let googleId = sessionStorage.getItem('googleId');
            if (!googleId) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('You need to be logged in to load notification targets');
                reject();
                return;
            }
            httpApi.sendApiRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].GET_NOTIFICATION_TARGETS,
                customRequestURL: `${getApiEndpoint()}/notifications/targets`,
                data: '',
                requestHeader: {
                    GoogleToken: googleId,
                    'Content-Type': 'application/json'
                },
                resolve: (data)=>{
                    resolve(data ? data : []);
                },
                reject: (error)=>{
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].GET_NOTIFICATION_TARGETS, error);
                    reject(error);
                }
            });
        });
    };
    let addNotificationTarget = (target)=>{
        return new Promise((resolve, reject)=>{
            let googleId = sessionStorage.getItem('googleId');
            if (!googleId) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('You need to be logged in to add a notification targets');
                reject();
                return;
            }
            httpApi.sendApiRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].ADD_NOTIFICATION_TARGETS,
                customRequestURL: `${getApiEndpoint()}/notifications/targets`,
                requestMethod: 'POST',
                data: '',
                requestHeader: {
                    GoogleToken: googleId,
                    'Content-Type': 'application/json'
                },
                resolve: (data)=>{
                    resolve(data ? data : []);
                },
                reject: (error)=>{
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].ADD_NOTIFICATION_TARGETS, error);
                    reject(error);
                }
            }, JSON.stringify(target));
        });
    };
    let deleteNotificationTarget = (target)=>{
        return new Promise((resolve, reject)=>{
            let googleId = sessionStorage.getItem('googleId');
            if (!googleId) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('You need to be logged in to delete a notification targets');
                reject();
                return;
            }
            httpApi.sendApiRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].ADD_NOTIFICATION_TARGETS,
                customRequestURL: `${getApiEndpoint()}/notifications/targets`,
                requestMethod: 'DELETE',
                data: '',
                requestHeader: {
                    GoogleToken: googleId,
                    'Content-Type': 'application/json'
                },
                resolve: (data)=>{
                    resolve(data ? data : []);
                },
                reject: (error)=>{
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].ADD_NOTIFICATION_TARGETS, error);
                    reject(error);
                }
            }, JSON.stringify(target));
        });
    };
    let updateNotificationTarget = (target)=>{
        return new Promise((resolve, reject)=>{
            let googleId = sessionStorage.getItem('googleId');
            if (!googleId) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('You need to be logged in to update a notification targets');
                reject();
                return;
            }
            httpApi.sendApiRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].UPDATE_NOTIFICATION_TARGET,
                customRequestURL: `${getApiEndpoint()}/notifications/targets`,
                requestMethod: 'PUT',
                data: '',
                requestHeader: {
                    GoogleToken: googleId,
                    'Content-Type': 'application/json'
                },
                resolve: (data)=>{
                    resolve(data ? data : []);
                },
                reject: (error)=>{
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].ADD_NOTIFICATION_TARGETS, error);
                    reject(error);
                }
            }, JSON.stringify(target));
        });
    };
    let sendTestNotification = (target)=>{
        return new Promise((resolve, reject)=>{
            let googleId = sessionStorage.getItem('googleId');
            if (!googleId) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('You need to be logged in to send a test notification');
                reject();
                return;
            }
            httpApi.sendApiRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].SEND_TEST_NOTIFICATION,
                customRequestURL: `${getApiEndpoint()}/notifications/targets/test`,
                data: '',
                requestMethod: 'POST',
                requestHeader: {
                    GoogleToken: googleId,
                    'Content-Type': 'application/json'
                },
                resolve: ()=>{
                    resolve();
                },
                reject: (error)=>{
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].SEND_TEST_NOTIFICATION, error);
                    reject(error);
                }
            }, JSON.stringify(target));
        });
    };
    let getNotificationSubscriptions = ()=>{
        return new Promise((resolve, reject)=>{
            let googleId = sessionStorage.getItem('googleId');
            if (!googleId) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('You need to be logged in to load notification subscriptions');
                reject();
                return;
            }
            httpApi.sendApiRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].GET_NOTIFICATION_SUBSCRIPTION,
                customRequestURL: `${getApiEndpoint()}/notifications/subscriptions`,
                requestMethod: 'GET',
                data: '',
                requestHeader: {
                    GoogleToken: googleId,
                    'Content-Type': 'application/json'
                },
                resolve: (data)=>{
                    resolve(data ? data : []);
                },
                reject: (error)=>{
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].GET_NOTIFICATION_SUBSCRIPTION, error);
                    reject(error);
                }
            });
        });
    };
    let createNotificationSubscription = (subscription)=>{
        return new Promise((resolve, reject)=>{
            let googleId = sessionStorage.getItem('googleId');
            if (!googleId) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('You need to be logged in to create a notification subscription');
                reject();
                return;
            }
            httpApi.sendApiRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].ADD_NOTIFICATION_SUBSCRIPTION,
                customRequestURL: `${getApiEndpoint()}/notifications/subscriptions`,
                requestMethod: 'POST',
                data: '',
                requestHeader: {
                    GoogleToken: googleId,
                    'Content-Type': 'application/json'
                },
                resolve: (data)=>{
                    resolve(data);
                },
                reject: (error)=>{
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].ADD_NOTIFICATION_SUBSCRIPTION, error);
                    reject(error);
                }
            }, JSON.stringify(subscription));
        });
    };
    let deleteNotificationSubscription = (subscription)=>{
        return new Promise((resolve, reject)=>{
            let googleId = sessionStorage.getItem('googleId');
            if (!googleId) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('You need to be logged in to delete a notification subscription');
                reject();
                return;
            }
            httpApi.sendApiRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].DELETE_NOTIFICATION_SUBSCRIPTION,
                customRequestURL: `${getApiEndpoint()}/notifications/subscriptions`,
                requestMethod: 'DELETE',
                data: '',
                requestHeader: {
                    GoogleToken: googleId,
                    'Content-Type': 'application/json'
                },
                resolve: ()=>{
                    resolve();
                },
                reject: (error)=>{
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].DELETE_NOTIFICATION_SUBSCRIPTION, error);
                    reject(error);
                }
            }, JSON.stringify(subscription));
        });
    };
    let getPublishedConfigs = ()=>{
        return new Promise((resolve, reject)=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$WebsocketHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["websocketHelper"].sendRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].GET_PUBLISHED_CONFIGS,
                data: '',
                resolve: (configs)=>{
                    resolve(configs);
                },
                reject: (error)=>{
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].GET_PUBLISHED_CONFIGS, error, '');
                    reject(error);
                }
            });
        });
    };
    let loadConfig = (configName)=>{
        return new Promise((resolve, reject)=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$WebsocketHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["websocketHelper"].sendRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].LOAD_CONFIG,
                data: {
                    configName
                },
                resolve: ()=>{
                    resolve();
                },
                reject: (error)=>{
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].LOAD_CONFIG, error, configName);
                    reject(error);
                }
            });
        });
    };
    let updateConfig = (configName, updateNotes = '')=>{
        return new Promise((resolve, reject)=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$WebsocketHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["websocketHelper"].sendRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].UPDATE_CONFIG,
                data: {
                    configName,
                    updateNotes
                },
                resolve: (configs)=>{
                    resolve(configs);
                },
                reject: (error)=>{
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].UPDATE_CONFIG, error, '');
                    reject(error);
                }
            });
        });
    };
    let requestArchivedAuctions = (itemTag, itemFilter)=>{
        return new Promise((resolve, reject)=>{
            let googleId = sessionStorage.getItem('googleId');
            if (!googleId) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('You need to be logged in to request archived auctions.');
                reject();
                return;
            }
            let params = new URLSearchParams();
            if (itemFilter && Object.keys(itemFilter).length > 0) {
                params = new URLSearchParams(itemFilter);
            }
            httpApi.sendApiRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].ARCHIVED_AUCTIONS,
                customRequestURL: `${getApiEndpoint()}/auctions/tag/${itemTag}/archive/overview?${params.toString()}`,
                requestMethod: 'GET',
                data: '',
                requestHeader: {
                    GoogleToken: googleId,
                    'Content-Type': 'application/json'
                },
                resolve: (result)=>{
                    resolve((0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Parser$2f$APIResponseParser$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseArchivedAuctions"])(result));
                },
                reject: (error)=>{
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].ARCHIVED_AUCTIONS, error, {
                        itemTag,
                        itemFilter
                    });
                    reject(error);
                }
            });
        });
    };
    let exportArchivedAuctionsData = (itemTag, itemFilter, discordWebhookUrl, flags)=>{
        return new Promise((resolve, reject)=>{
            let googleId = sessionStorage.getItem('googleId');
            if (!googleId) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('You need to be logged in to export archived auctions.');
                reject();
                return;
            }
            httpApi.sendApiRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].EXPORT_ARCHIVED_AUCTIONS,
                customRequestURL: `${getApiEndpoint()}/auctions/tag/${itemTag}/archive/export`,
                requestMethod: 'POST',
                data: '',
                requestHeader: {
                    GoogleToken: googleId,
                    'Content-Type': 'application/json'
                },
                resolve: ()=>{
                    resolve();
                },
                reject: (error)=>{
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].EXPORT_ARCHIVED_AUCTIONS, error, {
                        itemTag,
                        itemFilter
                    });
                    reject(error);
                }
            }, JSON.stringify({
                filters: itemFilter,
                discordWebhookUrl: discordWebhookUrl,
                flags: flags.length > 0 ? flags.toString() : undefined
            }));
        });
    };
    let getLinkvertiseLink = ()=>{
        return new Promise((resolve, reject)=>{
            let googleId = sessionStorage.getItem('googleId');
            if (!googleId) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('You need to be logged in to do linkvertise tasks.');
                reject();
                return;
            }
            httpApi.sendApiRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].GET_LINKVERTISE_LINK,
                customRequestURL: `${getApiEndpoint()}/linkvertise`,
                requestMethod: 'GET',
                data: '',
                requestHeader: {
                    GoogleToken: googleId,
                    'Content-Type': 'application/json'
                },
                resolve: (link)=>{
                    resolve(link);
                },
                reject: (error)=>{
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].GET_LINKVERTISE_LINK, error);
                    reject(error);
                }
            });
        });
    };
    let purchasePremiumSubscription = (productSlug, googleToken)=>{
        return new Promise((resolve, reject)=>{
            httpApi.sendApiRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].PURCHASE_PREMIUM_SUBSCRIPTION,
                customRequestURL: `${getApiEndpoint()}/premium/subscription/${productSlug}`,
                requestMethod: 'POST',
                data: '',
                requestHeader: {
                    GoogleToken: googleToken,
                    'Content-Type': 'application/json'
                },
                resolve: (data)=>{
                    resolve((0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Parser$2f$APIResponseParser$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parsePaymentResponse"])(data));
                },
                reject: (error)=>{
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].PURCHASE_PREMIUM_SUBSCRIPTION, error);
                    reject(error);
                }
            });
        });
    };
    let getPremiumSubscriptions = ()=>{
        return new Promise((resolve, reject)=>{
            let googleId = sessionStorage.getItem('googleId');
            if (!googleId) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('You need to be logged in to create a premium subscription.');
                reject();
                return;
            }
            httpApi.sendApiRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].CREATE_PREMIUM_SUBSCRIPTION,
                customRequestURL: `${getApiEndpoint()}/premium/subscription`,
                requestMethod: 'GET',
                data: '',
                requestHeader: {
                    GoogleToken: googleId,
                    'Content-Type': 'application/json'
                },
                resolve: (subscriptions = [])=>{
                    resolve(subscriptions.map(__TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Parser$2f$APIResponseParser$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parsePremiumSubscription"]));
                },
                reject: (error)=>{
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].CREATE_PREMIUM_SUBSCRIPTION, error);
                    reject(error);
                }
            });
        });
    };
    let cancelPremiumSubscription = (id)=>{
        return new Promise((resolve, reject)=>{
            let googleId = sessionStorage.getItem('googleId');
            if (!googleId) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('You need to be logged in to cancel a premium subscription.');
                reject();
                return;
            }
            httpApi.sendApiRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].DELETE_PREMIUM_SUBSCRIPTION,
                customRequestURL: `${getApiEndpoint()}/premium/subscription/${id}`,
                requestMethod: 'DELETE',
                data: '',
                requestHeader: {
                    GoogleToken: googleId,
                    'Content-Type': 'application/json'
                },
                resolve: ()=>{
                    resolve();
                },
                reject: (error)=>{
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].DELETE_PREMIUM_SUBSCRIPTION, error);
                    reject(error);
                }
            });
        });
    };
    let getCraftInstructions = (itemTag)=>{
        return new Promise((resolve, reject)=>{
            httpApi.sendApiRequest({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].GET_CRAFTING_INSTRUCTIONS,
                customRequestURL: `${getApiEndpoint()}/craft/${itemTag}/instructions`,
                data: '',
                resolve: (data)=>{
                    resolve((0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Parser$2f$APIResponseParser$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseCraftingInstructions"])(data));
                },
                reject: (error)=>{
                    apiErrorHandler(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RequestType"].GET_CRAFTING_INSTRUCTIONS, error, itemTag);
                    reject(error);
                }
            });
        });
    };
    return {
        search,
        trackSearch,
        getItemDetails,
        getItemPrices,
        getAuctions,
        getBids,
        getEnchantments,
        getAuctionDetails,
        getItemImageUrl,
        getPlayerName,
        getPlayerNames,
        setConnectionId,
        getVersion,
        subscribe,
        unsubscribe,
        getNotificationListener,
        loginWithToken,
        stripePurchase,
        setToken,
        getRecentAuctions,
        getFlips,
        subscribeFlips,
        getFilters,
        getNewPlayers,
        getNewItems,
        getPopularSearches,
        getEndedAuctions,
        getNewAuctions,
        getFlipBasedAuctions,
        paypalPurchase,
        lemonsqueezyPurchase,
        getRefInfo,
        setRef,
        getActiveAuctions,
        connectMinecraftAccount,
        getAccountInfo,
        unsubscribeFlips,
        itemSearch,
        authenticateModConnection,
        getFlipUpdateTime,
        playerSearch,
        getProfitableCrafts,
        getLowSupplyItems,
        sendFeedback,
        triggerPlayerNameCheck,
        getPlayerProfiles,
        getCraftingRecipe,
        getLowestBin,
        flipFilters,
        getBazaarTags,
        getPreloadFlips,
        getItemPriceSummary,
        purchaseWithCoflcoins,
        subscribeCoflCoinChange,
        getCoflcoinBalance,
        setFlipSetting,
        getKatFlips,
        getTrackedFlipsForPlayer,
        transferCoflCoins,
        getBazaarSnapshot,
        getBazaarPrices,
        getBazaarPricesByRange,
        subscribeFlipsAnonym,
        getPrivacySettings,
        setPrivacySettings,
        checkRat,
        getPremiumProducts,
        unsubscribeAll,
        getItemNames,
        checkFilter,
        refreshLoadPremiumProducts,
        getRelatedItems,
        getOwnerHistory,
        getMayorData,
        getPlayerInventory,
        createTradeOffer,
        getTradeOffers,
        deleteTradeOffer,
        getTransactions,
        getNotificationTargets,
        addNotificationTarget,
        deleteNotificationTarget,
        updateNotificationTarget,
        sendTestNotification,
        createNotificationSubscription,
        deleteNotificationSubscription,
        getNotificationSubscriptions,
        getPublishedConfigs,
        loadConfig,
        updateConfig,
        requestArchivedAuctions,
        exportArchivedAuctionsData,
        getLinkvertiseLink,
        getPremiumSubscriptions,
        cancelPremiumSubscription,
        purchasePremiumSubscription,
        getCraftInstructions
    };
}
let api = initAPI();
const __TURBOPACK__default__export__ = api;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/utils/CacheUtils.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__),
    "getCacheControlHeader": (()=>getCacheControlHeader)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$idb$2d$keyval$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/idb-keyval/dist/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/api/ApiHelper.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SSRUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/SSRUtils.tsx [app-client] (ecmascript)");
;
;
;
let cacheUtils = {
    getFromCache: function(type, data) {
        return new Promise((resolve, reject)=>{
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SSRUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isClientSideRendering"])()) {
                resolve(null);
            }
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$idb$2d$keyval$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["get"])(type + data).then((response)=>{
                if (response) {
                    let parsed = JSON.parse(response);
                    if (parsed.expireTimeStamp && new Date().getTime() < parsed.expireTimeStamp) {
                        resolve(parsed.response);
                        return;
                    }
                }
                resolve(null);
            }).catch(()=>{
                resolve(null);
            });
        });
    },
    setIntoCache: function(type, data, response, maxAge = 0) {
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SSRUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isClientSideRendering"])()) {
            return;
        }
        let entry = {
            expireTimeStamp: new Date().getTime() + maxAge * 1000,
            response: response
        };
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$idb$2d$keyval$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["set"])(type + data, JSON.stringify(entry)).catch(()=>{});
    },
    checkForCacheClear: function() {
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SSRUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isClientSideRendering"])()) {
            return;
        }
        __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getVersion().then((version)=>{
            let localVersion = window.localStorage.getItem('version');
            if (window.caches !== undefined && localVersion !== version) {
                // clear workbox caches
                caches.keys().then((keys)=>{
                    keys.forEach((key)=>{
                        caches.delete(key);
                    });
                }).catch(()=>{});
                // clear index db
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$idb$2d$keyval$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["clear"])();
            }
            window.localStorage.setItem('version', version);
        });
    },
    clearAll: function() {
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SSRUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isClientSideRendering"])()) {
            return;
        }
        if (window.caches !== undefined) {
            caches.keys().then((keys)=>{
                keys.forEach((key)=>{
                    caches.delete(key);
                });
            });
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$idb$2d$keyval$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["clear"])();
        }
    }
};
const __TURBOPACK__default__export__ = cacheUtils;
function getCacheControlHeader() {
    return 'public, max-age=60, s-maxage=20';
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/utils/NotificationUtils.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>registerNotificationCallback),
    "getNotficationWhenEnumAsString": (()=>getNotficationWhenEnumAsString),
    "getNotificationTypeAsString": (()=>getNotificationTypeAsString)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-toastify/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$CacheUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/CacheUtils.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$ClipboardUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/ClipboardUtils.tsx [app-client] (ecmascript)");
;
;
;
function registerNotificationCallback(router) {
    let interval = setInterval(function() {
        // wait until messaging is definded
        let messaging = window.messaging;
        if (typeof messaging == 'undefined') return;
        clearInterval(interval);
        messaging.onMessage(function(payload) {
            let notification = payload.notification;
            if (payload.data?.type === 'auction') {
                savePayloadIntoCache(payload);
            }
            displayNotification(notification);
        });
    }, 10);
    function displayNotification(notification) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].info(notification.title + '\n' + notification.body, {
            onClick: ()=>{
                if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$ClipboardUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["canUseClipBoard"])()) {
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$ClipboardUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["writeToClipboard"])('/viewauction ' + notification.click_action.split(/\/auction\//)[1]);
                }
                router.push('/' + notification.click_action.match(/\/\/[^/]+\/([^.]+)/)[1]);
            },
            autoClose: 20000
        });
    }
    function savePayloadIntoCache(payload) {
        let auction = JSON.parse(payload.data.auction);
        __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$CacheUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].setIntoCache('auctionDetails', JSON.stringify(auction.uuid), auction, 60);
    }
}
function getNotificationTypeAsString(type) {
    switch(type){
        case 1:
        case 'WEBHOOK':
            return 'Webhook';
        case 2:
        case 'DISCORD':
            return 'Discord';
        case 3:
        case 'DiscordWebhook':
            return 'Discord Webhook';
        case 4:
        case 'FIREBASE':
            return 'Push-Notification';
        case 5:
        case 'EMAIL':
            return 'E-Mail';
        case 6:
        case 'InGame':
            return 'InGame';
        default:
            return 'Unknown';
    }
}
function getNotficationWhenEnumAsString(when) {
    switch(when){
        case 0:
        case 'NEVER':
            return 'Never';
        case 1:
        case 'AfterFail':
            return 'After fail';
        case 2:
        case 'ALWAYS':
            return 'Always';
        default:
            return 'Never';
    }
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/utils/Parser/URLParser.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "getItemFilterFromUrl": (()=>getItemFilterFromUrl),
    "getURLSearchParam": (()=>getURLSearchParam),
    "parseItemFilter": (()=>parseItemFilter),
    "setFilterIntoUrlParams": (()=>setFilterIntoUrlParams)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Base64Utils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/Base64Utils.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SSRUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/SSRUtils.tsx [app-client] (ecmascript)");
;
;
function parseItemFilter(itemFilterBase64) {
    let itemFilter = JSON.parse((0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Base64Utils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["atobUnicode"])(itemFilterBase64));
    if (!itemFilter) {
        return {};
    }
    return itemFilter;
}
function getItemFilterFromUrl() {
    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SSRUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isClientSideRendering"])()) {
        return {};
    }
    // backwards compatibility for old itemFilter
    let itemFilterBase64 = getURLSearchParam('itemFilter');
    if (itemFilterBase64) {
        return parseItemFilter(itemFilterBase64);
    }
    let nonFilterParams = [
        'range',
        'refId',
        'conId'
    ];
    let searchParams = new URLSearchParams(window.location.search);
    let itemFilter = {};
    searchParams.forEach((value, key)=>{
        if (nonFilterParams.indexOf(key) === -1) {
            itemFilter[key] = value;
        }
    });
    return itemFilter;
}
function getURLSearchParam(key) {
    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SSRUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isClientSideRendering"])()) {
        return null;
    }
    let searchParams = new URLSearchParams(window.location.search);
    return searchParams.get(key);
}
function setFilterIntoUrlParams(router, pathname, itemFilter) {
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SSRUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isClientSideRendering"])()) {
        let searchParams = new URLSearchParams();
        Object.keys(itemFilter).forEach((key)=>{
            searchParams.set(key, itemFilter[key]);
        });
        router.replace(`${pathname}?${searchParams.toString()}`);
        window.history.replaceState(null, '', `${pathname}?${searchParams.toString()}`);
    } else {
        console.error('Tried to update url query "itemFilter" during serverside rendering');
    }
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/Tooltip/Tooltip.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Modal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Modal$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/Modal.js [app-client] (ecmascript) <export default as Modal>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$OverlayTrigger$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__OverlayTrigger$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/OverlayTrigger.js [app-client] (ecmascript) <export default as OverlayTrigger>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Tooltip$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Tooltip$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/Tooltip.js [app-client] (ecmascript) <export default as Tooltip>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__ = __turbopack_context__.i("[project]/node_modules/uuid/dist/esm-browser/v4.js [app-client] (ecmascript) <export default as v4>");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
function Tooltip(props) {
    _s();
    let [showDialog, setShowDialog] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    function getHoverElement() {
        return props.tooltipContent ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$OverlayTrigger$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__OverlayTrigger$3e$__["OverlayTrigger"], {
            overlay: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Tooltip$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Tooltip$3e$__["Tooltip"], {
                id: props.id || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
                className: props.className,
                children: props.tooltipContent
            }, void 0, false, {
                fileName: "[project]/components/Tooltip/Tooltip.tsx",
                lineNumber: 31,
                columnNumber: 21
            }, void 0),
            placement: props.hoverPlacement,
            children: props.content
        }, void 0, false, {
            fileName: "[project]/components/Tooltip/Tooltip.tsx",
            lineNumber: 29,
            columnNumber: 13
        }, this) : props.content;
    }
    function onClick() {
        setShowDialog(true);
        if (props.onClick) {
            props.onClick();
        }
    }
    function getClickElement() {
        return props.tooltipContent || props.tooltipTitle ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
            className: `tooltipWrapper ${props.className}`,
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                    style: {
                        cursor: 'pointer'
                    },
                    onClick: onClick,
                    children: props.content
                }, void 0, false, {
                    fileName: "[project]/components/Tooltip/Tooltip.tsx",
                    lineNumber: 54,
                    columnNumber: 17
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Modal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Modal$3e$__["Modal"], {
                    size: props.size || 'lg',
                    show: showDialog,
                    onHide: ()=>{
                        setShowDialog(false);
                    },
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Modal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Modal$3e$__["Modal"].Header, {
                            closeButton: true,
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Modal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Modal$3e$__["Modal"].Title, {
                                children: props.tooltipTitle
                            }, void 0, false, {
                                fileName: "[project]/components/Tooltip/Tooltip.tsx",
                                lineNumber: 65,
                                columnNumber: 25
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/components/Tooltip/Tooltip.tsx",
                            lineNumber: 64,
                            columnNumber: 21
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Modal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Modal$3e$__["Modal"].Body, {
                            children: props.tooltipContent
                        }, void 0, false, {
                            fileName: "[project]/components/Tooltip/Tooltip.tsx",
                            lineNumber: 67,
                            columnNumber: 21
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/components/Tooltip/Tooltip.tsx",
                    lineNumber: 57,
                    columnNumber: 17
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/components/Tooltip/Tooltip.tsx",
            lineNumber: 53,
            columnNumber: 13
        }, this) : props.content;
    }
    return props.type === 'hover' ? getHoverElement() : getClickElement();
}
_s(Tooltip, "C5PISlkcQlOsRRZI6hhjnbbUJLI=");
_c = Tooltip;
const __TURBOPACK__default__export__ = Tooltip;
var _c;
__turbopack_context__.k.register(_c, "Tooltip");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/ReloadDialog/ReloadDialog.module.css [app-client] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "checkbox": "ReloadDialog-module__HyoM8G__checkbox",
});
}}),
"[project]/components/ReloadDialog/ReloadDialog.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/Form.js [app-client] (ecmascript) <export default as Form>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Button$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/Button.js [app-client] (ecmascript) <export default as Button>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-toastify/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/api/ApiHelper.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Tooltip$2f$Tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/Tooltip/Tooltip.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ReloadDialog$2f$ReloadDialog$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/components/ReloadDialog/ReloadDialog.module.css [app-client] (css module)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$MainApp$2f$MainApp$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/MainApp/MainApp.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
;
;
;
;
;
;
;
function ReloadDialog(props) {
    _s();
    let [feedback, setFeedback] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        loadNewInformation: false,
        otherIssue: false,
        somethingBroke: false,
        additionalInformation: ''
    });
    let [hasUserInput, setHasUserInput] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    let [showMissingAdditionalInformation, setShowMissingAdditionalInformation] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    function onClose() {
        props.onClose();
    }
    function onSubmit() {
        if (!feedback.additionalInformation) {
            setShowMissingAdditionalInformation(true);
            return;
        }
        let feedbackToSend = {
            ...feedback
        };
        feedbackToSend.errorLog = __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$MainApp$2f$MainApp$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["errorLog"];
        feedbackToSend.href = location.href;
        __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].sendFeedback('reload', feedbackToSend).then(()=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success('Thank you for your feedback!');
            props.onClose();
        }).catch(()=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('Feedback could not be sent.');
            props.onClose();
        });
    }
    function onRememberHideDialogChange(e) {
        localStorage.setItem('rememberHideReloadDialog', e.target.checked.toString());
        setHasUserInput(true);
    }
    function onLoadNewInformationChange(e) {
        let newFeedback = {
            ...feedback
        };
        newFeedback.loadNewInformation = e.target.checked;
        setFeedback(newFeedback);
        setHasUserInput(true);
    }
    function onSomethingBrokeChange(e) {
        let newFeedback = {
            ...feedback
        };
        newFeedback.somethingBroke = e.target.checked;
        setFeedback(newFeedback);
        setHasUserInput(true);
    }
    function onOtherIssueChange(e) {
        let newFeedback = {
            ...feedback
        };
        newFeedback.otherIssue = e.target.checked;
        setFeedback(newFeedback);
        setHasUserInput(true);
    }
    function onAdditionalInformationChange(e) {
        let newFeedback = {
            ...feedback
        };
        newFeedback.additionalInformation = e.target.value;
        if (newFeedback.additionalInformation) {
            setShowMissingAdditionalInformation(false);
        }
        setFeedback(newFeedback);
        setHasUserInput(true);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                children: "We noticed that you reloaded the page multiple times. We try to constantly improve our service. To do so we need to know if and what went wrong."
            }, void 0, false, {
                fileName: "[project]/components/ReloadDialog/ReloadDialog.tsx",
                lineNumber: 85,
                columnNumber: 13
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                children: "Please tell us, why you reloaded the page:"
            }, void 0, false, {
                fileName: "[project]/components/ReloadDialog/ReloadDialog.tsx",
                lineNumber: 88,
                columnNumber: 13
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__["Form"], {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("hr", {}, void 0, false, {
                        fileName: "[project]/components/ReloadDialog/ReloadDialog.tsx",
                        lineNumber: 90,
                        columnNumber: 17
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__["Form"].Group, {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__["Form"].Label, {
                                htmlFor: "loaddNewInformation",
                                children: "I tried to load new information"
                            }, void 0, false, {
                                fileName: "[project]/components/ReloadDialog/ReloadDialog.tsx",
                                lineNumber: 92,
                                columnNumber: 21
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__["Form"].Check, {
                                id: "loaddNewInformation",
                                className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ReloadDialog$2f$ReloadDialog$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].checkbox,
                                defaultChecked: feedback.loadNewInformation,
                                onChange: onLoadNewInformationChange
                            }, void 0, false, {
                                fileName: "[project]/components/ReloadDialog/ReloadDialog.tsx",
                                lineNumber: 93,
                                columnNumber: 21
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                style: {
                                    fontStyle: 'italic'
                                },
                                children: "If you need the most recent information on something you could try to create a notification for it. Currently you can create notifications for Items, Auctions and Players. Do you need something else? Please dont hesitate to ask us."
                            }, void 0, false, {
                                fileName: "[project]/components/ReloadDialog/ReloadDialog.tsx",
                                lineNumber: 99,
                                columnNumber: 21
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/ReloadDialog/ReloadDialog.tsx",
                        lineNumber: 91,
                        columnNumber: 17
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("hr", {}, void 0, false, {
                        fileName: "[project]/components/ReloadDialog/ReloadDialog.tsx",
                        lineNumber: 104,
                        columnNumber: 17
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__["Form"].Group, {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__["Form"].Label, {
                                htmlFor: "somethingBroke",
                                children: "Something broke"
                            }, void 0, false, {
                                fileName: "[project]/components/ReloadDialog/ReloadDialog.tsx",
                                lineNumber: 106,
                                columnNumber: 21
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__["Form"].Check, {
                                id: "somethingBroke",
                                className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ReloadDialog$2f$ReloadDialog$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].checkbox,
                                defaultChecked: feedback.somethingBroke,
                                onChange: onSomethingBrokeChange
                            }, void 0, false, {
                                fileName: "[project]/components/ReloadDialog/ReloadDialog.tsx",
                                lineNumber: 107,
                                columnNumber: 21
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                style: {
                                    fontStyle: 'italic'
                                },
                                children: "You found an bug? Please tell us about it so that we can fix it as soon as possible."
                            }, void 0, false, {
                                fileName: "[project]/components/ReloadDialog/ReloadDialog.tsx",
                                lineNumber: 108,
                                columnNumber: 21
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/ReloadDialog/ReloadDialog.tsx",
                        lineNumber: 105,
                        columnNumber: 17
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("hr", {}, void 0, false, {
                        fileName: "[project]/components/ReloadDialog/ReloadDialog.tsx",
                        lineNumber: 111,
                        columnNumber: 17
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__["Form"].Group, {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__["Form"].Label, {
                                htmlFor: "otherIssues",
                                children: "Other issue"
                            }, void 0, false, {
                                fileName: "[project]/components/ReloadDialog/ReloadDialog.tsx",
                                lineNumber: 113,
                                columnNumber: 21
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__["Form"].Check, {
                                id: "otherIssues",
                                className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ReloadDialog$2f$ReloadDialog$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].checkbox,
                                defaultChecked: feedback.otherIssue,
                                onChange: onOtherIssueChange
                            }, void 0, false, {
                                fileName: "[project]/components/ReloadDialog/ReloadDialog.tsx",
                                lineNumber: 114,
                                columnNumber: 21
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                style: {
                                    fontStyle: 'italic'
                                },
                                children: "If you need any kind of help please join our discord or write us an E-Mail. We are happy to help."
                            }, void 0, false, {
                                fileName: "[project]/components/ReloadDialog/ReloadDialog.tsx",
                                lineNumber: 115,
                                columnNumber: 21
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/ReloadDialog/ReloadDialog.tsx",
                        lineNumber: 112,
                        columnNumber: 17
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("hr", {}, void 0, false, {
                        fileName: "[project]/components/ReloadDialog/ReloadDialog.tsx",
                        lineNumber: 118,
                        columnNumber: 17
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__["Form"].Group, {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__["Form"].Label, {
                                htmlFor: "additionalInformations",
                                children: "*Additional information"
                            }, void 0, false, {
                                fileName: "[project]/components/ReloadDialog/ReloadDialog.tsx",
                                lineNumber: 120,
                                columnNumber: 21
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__["Form"].Control, {
                                isInvalid: showMissingAdditionalInformation,
                                style: {
                                    height: '75px',
                                    resize: 'none'
                                },
                                id: "additionalInformations",
                                as: "textarea",
                                onChange: onAdditionalInformationChange
                            }, void 0, false, {
                                fileName: "[project]/components/ReloadDialog/ReloadDialog.tsx",
                                lineNumber: 121,
                                columnNumber: 21
                            }, this),
                            showMissingAdditionalInformation ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    style: {
                                        color: 'red'
                                    },
                                    children: "Please enter some additional information"
                                }, void 0, false, {
                                    fileName: "[project]/components/ReloadDialog/ReloadDialog.tsx",
                                    lineNumber: 130,
                                    columnNumber: 29
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/components/ReloadDialog/ReloadDialog.tsx",
                                lineNumber: 129,
                                columnNumber: 25
                            }, this) : null
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/ReloadDialog/ReloadDialog.tsx",
                        lineNumber: 119,
                        columnNumber: 17
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("hr", {}, void 0, false, {
                        fileName: "[project]/components/ReloadDialog/ReloadDialog.tsx",
                        lineNumber: 135,
                        columnNumber: 17
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__["Form"].Group, {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__["Form"].Label, {
                                htmlFor: "rememberHideDialog",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("b", {
                                    children: "Please dont show this dialog again."
                                }, void 0, false, {
                                    fileName: "[project]/components/ReloadDialog/ReloadDialog.tsx",
                                    lineNumber: 138,
                                    columnNumber: 25
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/components/ReloadDialog/ReloadDialog.tsx",
                                lineNumber: 137,
                                columnNumber: 21
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__["Form"].Check, {
                                id: "rememberHideDialog",
                                className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ReloadDialog$2f$ReloadDialog$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].checkbox,
                                onChange: onRememberHideDialogChange
                            }, void 0, false, {
                                fileName: "[project]/components/ReloadDialog/ReloadDialog.tsx",
                                lineNumber: 140,
                                columnNumber: 21
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/ReloadDialog/ReloadDialog.tsx",
                        lineNumber: 136,
                        columnNumber: 17
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/components/ReloadDialog/ReloadDialog.tsx",
                lineNumber: 89,
                columnNumber: 13
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                style: {
                    display: 'flex',
                    justifyContent: 'flex-end'
                },
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Button$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__["Button"], {
                        variant: "danger",
                        onClick: onClose,
                        children: "Close"
                    }, void 0, false, {
                        fileName: "[project]/components/ReloadDialog/ReloadDialog.tsx",
                        lineNumber: 144,
                        columnNumber: 17
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Tooltip$2f$Tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                        type: 'hover',
                        content: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Button$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__["Button"], {
                                variant: "success",
                                onClick: onSubmit,
                                disabled: !hasUserInput,
                                style: {
                                    marginLeft: '15px'
                                },
                                children: "Submit"
                            }, void 0, false, {
                                fileName: "[project]/components/ReloadDialog/ReloadDialog.tsx",
                                lineNumber: 151,
                                columnNumber: 29
                            }, void 0)
                        }, void 0, false, {
                            fileName: "[project]/components/ReloadDialog/ReloadDialog.tsx",
                            lineNumber: 150,
                            columnNumber: 25
                        }, void 0),
                        tooltipContent: !hasUserInput ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            children: "Please enter some information before submitting feedback"
                        }, void 0, false, {
                            fileName: "[project]/components/ReloadDialog/ReloadDialog.tsx",
                            lineNumber: 156,
                            columnNumber: 53
                        }, void 0) : undefined
                    }, void 0, false, {
                        fileName: "[project]/components/ReloadDialog/ReloadDialog.tsx",
                        lineNumber: 147,
                        columnNumber: 17
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/components/ReloadDialog/ReloadDialog.tsx",
                lineNumber: 143,
                columnNumber: 13
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/components/ReloadDialog/ReloadDialog.tsx",
        lineNumber: 84,
        columnNumber: 9
    }, this);
}
_s(ReloadDialog, "hygeq6pliwbdcaeeiwPR7iE9LC4=");
_c = ReloadDialog;
const __TURBOPACK__default__export__ = ReloadDialog;
var _c;
__turbopack_context__.k.register(_c, "ReloadDialog");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/migrations/BackupFlipSettingsMigration.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "startFlipSettingsBackupMigration": (()=>startFlipSettingsBackupMigration)
});
function startFlipSettingsBackupMigration() {
    let currentSettings = localStorage.getItem('userSettings');
    let backup = localStorage.getItem('backup_userSettings');
    if (currentSettings && !backup) {
        localStorage.setItem('backup_userSettings', currentSettings);
    }
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/migrations/EmptyFlipRestrictionMigration.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "startEmptyFlipRestrictionMigration": (()=>startEmptyFlipRestrictionMigration)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SettingsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/SettingsUtils.tsx [app-client] (ecmascript)");
;
function startEmptyFlipRestrictionMigration() {
    let restrictions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SettingsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getSettingsObject"])(__TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SettingsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RESTRICTIONS_SETTINGS_KEY"], []);
    let validRestrictions = [];
    for(let i = 0; i < restrictions.length; i++){
        const restriction = restrictions[i];
        if (!restriction.item || restriction.item && restriction.item.tag) {
            validRestrictions.push(restriction);
        }
    }
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SettingsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setSetting"])(__TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SettingsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RESTRICTIONS_SETTINGS_KEY"], JSON.stringify(validRestrictions));
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/migrations/MigrationUtils.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "startMigrations": (()=>startMigrations)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$migrations$2f$BackupFlipSettingsMigration$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/migrations/BackupFlipSettingsMigration.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$migrations$2f$EmptyFlipRestrictionMigration$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/migrations/EmptyFlipRestrictionMigration.tsx [app-client] (ecmascript)");
;
;
function startMigrations() {
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$migrations$2f$BackupFlipSettingsMigration$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["startFlipSettingsBackupMigration"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$migrations$2f$EmptyFlipRestrictionMigration$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["startEmptyFlipRestrictionMigration"])();
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/TopLoader/TopLoadingAnimation.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>TopLoadingAnimation)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$nprogress$2f$nprogress$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/nprogress/nprogress.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
function TopLoadingAnimation() {
    _s();
    const color = '#29d';
    const height = 3;
    const styles = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("style", {
        children: `#nprogress{pointer-events:none}#nprogress .bar{background:${color};position:fixed;z-index:1031;top:0;left:0;width:100%;height:${height}px}#nprogress .peg{display:block;position:absolute;right:0;width:100px;height:100%;opacity:1;-webkit-transform:rotate(3deg) translate(0px,-4px);-ms-transform:rotate(3deg) translate(0px,-4px);transform:rotate(3deg) translate(0px,-4px)}#nprogress .spinner{display:block;position:fixed;z-index:1031;top:15px;right:15px}#nprogress .spinner-icon{width:18px;height:18px;box-sizing:border-box;border:2px solid transparent;border-top-color:${color};border-left-color:${color};border-radius:50%;-webkit-animation:nprogress-spinner 400ms linear infinite;animation:nprogress-spinner 400ms linear infinite}.nprogress-custom-parent{overflow:hidden;position:relative}.nprogress-custom-parent #nprogress .bar,.nprogress-custom-parent #nprogress .spinner{position:absolute}@-webkit-keyframes nprogress-spinner{0%{-webkit-transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg)}}@keyframes nprogress-spinner{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}`
    }, void 0, false, {
        fileName: "[project]/components/TopLoader/TopLoadingAnimation.tsx",
        lineNumber: 9,
        columnNumber: 9
    }, this);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "TopLoadingAnimation.useEffect": ()=>{
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$nprogress$2f$nprogress$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["configure"])({
                showSpinner: true,
                trickle: true,
                trickleSpeed: 200,
                minimum: 0.08,
                easing: 'ease',
                speed: 200
            });
            function isNoSufficientNavigation(currentUrl, newUrl) {
                const currentUrlObj = new URL(currentUrl);
                const newUrlObj = new URL(newUrl);
                // Compare hostname, pathname, and search parameters
                return currentUrlObj.hostname === newUrlObj.hostname && currentUrlObj.pathname === newUrlObj.pathname;
            }
            // eslint-disable-next-line no-var
            var npgclass = document.querySelectorAll('html');
            function findClosestAnchor(element) {
                while(element && element.tagName.toLowerCase() !== 'a'){
                    element = element.parentElement;
                }
                return element;
            }
            function handleClick(event) {
                try {
                    const target = event.target;
                    const anchor = findClosestAnchor(target);
                    if (anchor) {
                        const currentUrl = window.location.href;
                        const newUrl = anchor.href;
                        const isExternalLink = anchor.target === '_blank';
                        const isAnchor = isNoSufficientNavigation(currentUrl, newUrl);
                        if (newUrl === currentUrl || isAnchor || isExternalLink) {
                            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$nprogress$2f$nprogress$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["start"])();
                            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$nprogress$2f$nprogress$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["done"])();
                            [].forEach.call(npgclass, {
                                "TopLoadingAnimation.useEffect.handleClick": function(el) {
                                    el.classList.remove('nprogress-busy');
                                }
                            }["TopLoadingAnimation.useEffect.handleClick"]);
                        } else {
                            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$nprogress$2f$nprogress$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["start"])();
                            ({
                                "TopLoadingAnimation.useEffect.handleClick": function(history) {
                                    const pushState = history.pushState;
                                    history.pushState = ({
                                        "TopLoadingAnimation.useEffect.handleClick": function() {
                                            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$nprogress$2f$nprogress$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["done"])();
                                            [].forEach.call(npgclass, {
                                                "TopLoadingAnimation.useEffect.handleClick": function(el) {
                                                    el.classList.remove('nprogress-busy');
                                                }
                                            }["TopLoadingAnimation.useEffect.handleClick"]);
                                            // eslint-disable-next-line prefer-rest-params, @typescript-eslint/no-explicit-any
                                            return pushState.apply(history, arguments);
                                        }
                                    })["TopLoadingAnimation.useEffect.handleClick"];
                                }
                            })["TopLoadingAnimation.useEffect.handleClick"](window.history);
                        }
                    }
                } catch (err) {
                    // Log the error in development only!
                    // console.log('NextTopLoader error: ', err);
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$nprogress$2f$nprogress$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["start"])();
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$nprogress$2f$nprogress$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["done"])();
                }
            }
            // Add the global click event listener
            document.addEventListener('click', handleClick);
            // Clean up the global click event listener when the component is unmounted
            return ({
                "TopLoadingAnimation.useEffect": ()=>{
                    document.removeEventListener('click', handleClick);
                }
            })["TopLoadingAnimation.useEffect"];
        }
    }["TopLoadingAnimation.useEffect"], []);
    return styles;
}
_s(TopLoadingAnimation, "OD7bBpZva5O2jO+Puf00hKivP7c=");
_c = TopLoadingAnimation;
var _c;
__turbopack_context__.k.register(_c, "TopLoadingAnimation");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/MainApp/MainApp.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "MainApp": (()=>MainApp),
    "errorLog": (()=>errorLog)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$jonkoops$2f$matomo$2d$tracker$2d$react$2f$es$2f$useMatomo$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useMatomo$3e$__ = __turbopack_context__.i("[project]/node_modules/@jonkoops/matomo-tracker-react/es/useMatomo.js [app-client] (ecmascript) <export default as useMatomo>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$cookie$2d$consent$2f$dist$2f$react$2d$cookie$2d$consent$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/react-cookie-consent/dist/react-cookie-consent.esm.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$cookie$2d$consent$2f$dist$2f$react$2d$cookie$2d$consent$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/react-cookie-consent/dist/react-cookie-consent.esm.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-toastify/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$OfflineBanner$2f$OfflineBanner$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/OfflineBanner/OfflineBanner.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$NotificationUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/NotificationUtils.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Parser$2f$URLParser$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/Parser/URLParser.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/js-cookie/dist/js.cookie.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Modal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Modal$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/Modal.js [app-client] (ecmascript) <export default as Modal>");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ReloadDialog$2f$ReloadDialog$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ReloadDialog/ReloadDialog.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$migrations$2f$MigrationUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/migrations/MigrationUtils.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__ = __turbopack_context__.i("[project]/node_modules/uuid/dist/esm-browser/v4.js [app-client] (ecmascript) <export default as v4>");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SSRUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/SSRUtils.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$TopLoader$2f$TopLoadingAnimation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/TopLoader/TopLoadingAnimation.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$CoflCoinsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/CoflCoinsUtils.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const errorLog = [];
(0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$CoflCoinsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["initCoflCoinManager"])();
function MainApp(props) {
    _s();
    const [showRefreshFeedbackDialog, setShowRefreshFeedbackDialog] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isReloadTracked, setIsReloadTracked] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const { trackPageView, trackEvent, pushInstruction } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$jonkoops$2f$matomo$2d$tracker$2d$react$2f$es$2f$useMatomo$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useMatomo$3e$__["useMatomo"])();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MainApp.useEffect": ()=>{
            window.addEventListener('error', {
                "MainApp.useEffect": function(event) {
                    errorLog.push({
                        error: event,
                        timestamp: new Date()
                    });
                    if (event.error.name === 'ChunkLoadError') {
                        let chunkErrorLocalStorage = window.localStorage.getItem('chunkErrorReload');
                        if (chunkErrorLocalStorage && parseInt(chunkErrorLocalStorage) + 5000 > new Date().getTime()) {
                            alert('There is something wrong with the website-chunks. Please try Control + F5 to hard refresh the page.');
                            return;
                        }
                        window.localStorage.setItem('chunkErrorReload', new Date().getTime().toString());
                        caches.keys().then({
                            "MainApp.useEffect": (keys)=>{
                                keys.forEach({
                                    "MainApp.useEffect": (key)=>{
                                        caches.delete(key);
                                    }
                                }["MainApp.useEffect"]);
                            }
                        }["MainApp.useEffect"]).catch({
                            "MainApp.useEffect": ()=>{}
                        }["MainApp.useEffect"]);
                        location.reload();
                    }
                }
            }["MainApp.useEffect"]);
        }
    }["MainApp.useEffect"], []);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MainApp.useEffect": ()=>{
            window.sessionStorage.setItem('sessionId', (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])());
            checkForReload();
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$migrations$2f$MigrationUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["startMigrations"])();
        }
    }["MainApp.useEffect"], []);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MainApp.useEffect": ()=>{
            pushInstruction('requireConsent');
            // check for tracking of old users
            let cookie = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get('nonEssentialCookiesAllowed');
            if (cookie === 'true') {
                pushInstruction('rememberConsentGiven');
            }
            let refId = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Parser$2f$URLParser$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getURLSearchParam"])('refId');
            if (refId) {
                ;
                window.refId = refId;
            }
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$NotificationUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(router);
        // eslint-disable-next-line react-hooks/exhaustive-deps
        }
    }["MainApp.useEffect"], [
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SSRUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isClientSideRendering"])() ? location : null
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MainApp.useEffect": ()=>{
            trackPageView({});
        // eslint-disable-next-line react-hooks/exhaustive-deps
        }
    }["MainApp.useEffect"], [
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SSRUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isClientSideRendering"])() ? document.title : null
    ]);
    function checkForReload() {
        let preventReloadDialog = localStorage.getItem('rememberHideReloadDialog') === 'true';
        if (preventReloadDialog || isReloadTracked) {
            return;
        }
        // check if page was reloaded
        if (window.performance.getEntriesByType('navigation').map((nav)=>nav.type).includes('reload')) {
            let lastReloadTime = localStorage.getItem('lastReloadTime');
            // Check if the last reload was less than 30 seconds ago
            if (lastReloadTime && 30_000 > new Date().getTime() - Number(lastReloadTime)) {
                setTimeout(()=>{
                    setShowRefreshFeedbackDialog(true);
                }, 1000);
            } else {
                setIsReloadTracked(true);
                localStorage.setItem('lastReloadTime', new Date().getTime().toString());
            }
        }
    }
    function setTrackingAllowed() {
        pushInstruction('rememberConsentGiven');
        trackPageView({});
    }
    let refreshFeedbackDialog = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Modal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Modal$3e$__["Modal"], {
        size: 'lg',
        show: showRefreshFeedbackDialog,
        onHide: ()=>{
            setShowRefreshFeedbackDialog(false);
        },
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Modal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Modal$3e$__["Modal"].Header, {
                closeButton: true,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Modal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Modal$3e$__["Modal"].Title, {
                    children: "Has an error occured?"
                }, void 0, false, {
                    fileName: "[project]/components/MainApp/MainApp.tsx",
                    lineNumber: 133,
                    columnNumber: 17
                }, this)
            }, void 0, false, {
                fileName: "[project]/components/MainApp/MainApp.tsx",
                lineNumber: 132,
                columnNumber: 13
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Modal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Modal$3e$__["Modal"].Body, {
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ReloadDialog$2f$ReloadDialog$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    onClose: ()=>{
                        setShowRefreshFeedbackDialog(false);
                    }
                }, void 0, false, {
                    fileName: "[project]/components/MainApp/MainApp.tsx",
                    lineNumber: 136,
                    columnNumber: 17
                }, this)
            }, void 0, false, {
                fileName: "[project]/components/MainApp/MainApp.tsx",
                lineNumber: 135,
                columnNumber: 13
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/components/MainApp/MainApp.tsx",
        lineNumber: 125,
        columnNumber: 9
    }, this);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$OfflineBanner$2f$OfflineBanner$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["OfflineBanner"], {}, void 0, false, {
                fileName: "[project]/components/MainApp/MainApp.tsx",
                lineNumber: 147,
                columnNumber: 13
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$TopLoader$2f$TopLoadingAnimation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                fileName: "[project]/components/MainApp/MainApp.tsx",
                lineNumber: 148,
                columnNumber: 13
            }, this),
            props.children,
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$cookie$2d$consent$2f$dist$2f$react$2d$cookie$2d$consent$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"], {
                enableDeclineButton: true,
                declineButtonStyle: {
                    backgroundColor: 'rgb(65, 65, 65)',
                    borderRadius: '10px',
                    color: 'lightgrey',
                    fontSize: '14px'
                },
                buttonStyle: {
                    backgroundColor: 'green',
                    borderRadius: '10px',
                    color: 'white',
                    fontSize: '20px'
                },
                contentStyle: {
                    marginBottom: '0px'
                },
                buttonText: "Yes, I understand",
                declineButtonText: "Decline",
                cookieName: "nonEssentialCookiesAllowed",
                "data-nosnippet": true,
                style: {
                    paddingLeft: '2vw'
                },
                onAccept: ()=>{
                    setTrackingAllowed();
                },
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                    "data-nosnippet": true,
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        style: {
                            margin: '0'
                        },
                        children: [
                            'We use cookies for analytics. By clicking the "Yes, I understand" button, you consent our use of cookies. View our',
                            ' ',
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                                href: "https://coflnet.com/privacy",
                                style: {
                                    backgroundColor: 'white',
                                    textDecoration: 'none',
                                    color: 'black',
                                    borderRadius: '3px'
                                },
                                children: "Privacy Policy ↗️"
                            }, void 0, false, {
                                fileName: "[project]/components/MainApp/MainApp.tsx",
                                lineNumber: 167,
                                columnNumber: 25
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/MainApp/MainApp.tsx",
                        lineNumber: 165,
                        columnNumber: 21
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/components/MainApp/MainApp.tsx",
                    lineNumber: 164,
                    columnNumber: 17
                }, this)
            }, void 0, false, {
                fileName: "[project]/components/MainApp/MainApp.tsx",
                lineNumber: 150,
                columnNumber: 13
            }, this),
            refreshFeedbackDialog,
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ToastContainer"], {
                theme: 'colored',
                stacked: true
            }, void 0, false, {
                fileName: "[project]/components/MainApp/MainApp.tsx",
                lineNumber: 174,
                columnNumber: 13
            }, this)
        ]
    }, void 0, true);
}
_s(MainApp, "k7hj9DojyJkgaucC/fZCxzaAGh0=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$jonkoops$2f$matomo$2d$tracker$2d$react$2f$es$2f$useMatomo$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useMatomo$3e$__["useMatomo"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"]
    ];
});
_c = MainApp;
var _c;
__turbopack_context__.k.register(_c, "MainApp");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=_46bd9f06._.js.map