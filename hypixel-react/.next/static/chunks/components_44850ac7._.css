/* [project]/components/FlipTracking/FlipTracking.module.css [app-client] (css) */
.FlipTracking-module__IBfYNG__list {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  flex-direction: initial;
  width: 100%;
}

.FlipTracking-module__IBfYNG__listGroupItem {
  width: 100%;
  margin-bottom: 15px;
  border-radius: 20px !important;
}

.FlipTracking-module__IBfYNG__noAuctionFound {
  margin-right: auto;
  margin-left: auto;
  width: max-content;
  text-align: center;
  margin-top: 5vh;
}

.FlipTracking-module__IBfYNG__profitNumberCard {
  width: 45%;
  text-align: center;
  cursor: pointer;
  border-color: #0000 !important;
}

.FlipTracking-module__IBfYNG__profitNumberHeader {
  border-radius: 20px !important;
}

.FlipTracking-module__IBfYNG__profitNumberHeader:hover {
  background-color: #3a3a3a;
}

@media (width >= 1200px) {
  .FlipTracking-module__IBfYNG__listGroupItem {
    width: 49%;
  }
}

.FlipTracking-module__IBfYNG__datePicker {
  margin-right: 15px;
}

.FlipTracking-module__IBfYNG__filterContainer {
  display: flex;
  align-items: center;
}

.FlipTracking-module__IBfYNG__rangeFilter {
  width: 200px;
}

.FlipTracking-module__IBfYNG__itemFilterContainer:has(.show.dropdown-menu) {
  z-index: 12;
  position: relative;
  height: 38px;
}

.FlipTracking-module__IBfYNG__multiSearch {
  flex: 1;
}

.FlipTracking-module__IBfYNG__noPremiumInfoText {
  font-size: small;
}

.FlipTracking-module__IBfYNG__topContainer {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.FlipTracking-module__IBfYNG__filterLabel {
  min-width: 100px;
}

.FlipTracking-module__IBfYNG__filterValueField {
  max-width: 200px;
}

@media (width <= 768px) {
  .FlipTracking-module__IBfYNG__noPremiumInfoText {
    float: none;
    text-align: center;
  }

  .FlipTracking-module__IBfYNG__topContainer {
    display: flex;
    flex-direction: column-reverse;
    margin-bottom: 20px;
    gap: 10px;
  }
}


/* [project]/components/GoogleSignIn/GoogleSignIn.module.css [app-client] (css) */
.GoogleSignIn-module__lTSYOa__googleButton {
  width: 250px;
  color-scheme: light;
}


/* [project]/components/ShowMoreText/ShowMoreText.module.css [app-client] (css) */
.ShowMoreText-module__4MH6ba__textContainer {
  position: relative;
  overflow: hidden;
}

.ShowMoreText-module__4MH6ba__textContainer:after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 30px;
  background: linear-gradient(#0000, #252525);
}

.ShowMoreText-module__4MH6ba__textContainer.ShowMoreText-module__4MH6ba__expanded {
  max-height: none;
  overflow: visible;
}

.ShowMoreText-module__4MH6ba__textContainer.ShowMoreText-module__4MH6ba__expanded:after {
  display: none;
}

.ShowMoreText-module__4MH6ba__showMoreContainer {
  width: 100%;
  position: absolute;
  bottom: -4px;
  cursor: pointer;
  z-index: 2;
}

.ShowMoreText-module__4MH6ba__showMoreText {
  display: flex;
  justify-content: center;
  color: #d3d3d3;
}


/* [project]/components/Search/Search.module.css [app-client] (css) */
.Search-module__Lg4wHG__searchResultIcon {
  margin-right: 20px;
}

.Search-module__Lg4wHG__bar {
  display: flex;
}

.Search-module__Lg4wHG__current {
  flex-grow: 100;
  font-size: 1rem;
}

.Search-module__Lg4wHG__search .form-control {
  background-color: #303030;
  color: #fff;
  border-color: #222;
  box-shadow: none;
}

.Search-module__Lg4wHG__search .form-control:focus {
  background-color: #303030;
  color: #fff;
  border-color: #222;
  box-shadow: none;
}

.Search-module__Lg4wHG__searchFormGroup {
  display: flex;
  justify-content: center;
  align-content: center;
  margin-bottom: 0;
  border-bottom-width: 0;
}

.Search-module__Lg4wHG__previousSearch {
  color: #c389f6;
}

.Search-module__Lg4wHG__multiInputfield {
  color: #fff;
  color-scheme: dark;
}

.Search-module__Lg4wHG__multiSearch .rbt-token {
  background-color: #444;
  color: #fff;
}


/* [project]/components/NavBar/NavBar.module.css [app-client] (css) */
.NavBar-module__yBvhsG__navClosing {
  left: -270px;
  transition: all .5s;
}

.NavBar-module__yBvhsG__navOpen {
  transition: left .5s;
  left: 0 !important;
}

.NavBar-module__yBvhsG__hamburgerIcon {
  display: inline;
  width: 36px;
  height: 36px;
  cursor: pointer;
  margin-right: 12px;
}

.NavBar-module__yBvhsG__navBar {
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  z-index: 20;
  font-size: 1rem;
}

.NavBar-module__yBvhsG__logo {
  padding: 24px;
  font-weight: bold;
  font-size: 20px;
  letter-spacing: 1px;
  overflow: hidden;
  white-space: nowrap;
  display: flex;
  align-items: center;
}

.NavBar-module__yBvhsG__navBar #pro-sidebar {
  position: absolute;
  bottom: 0;
  z-index: 100;
  left: 0;
  top: 0;
  min-height: 100vh;
  border: none;
}

.NavBar-module__yBvhsG__navBar .ps-menu-button:hover {
  background-color: #505050 !important;
}

.NavBar-module__yBvhsG__menuItem {
  display: block !important;
}


/* [project]/components/OptionsMenu/OptionsMenu.module.css [app-client] (css) */
.OptionsMenu-module__BxCVkW__optionsMenu a {
  color: #fff;
  margin-left: 5px;
  text-decoration: none !important;
}

.OptionsMenu-module__BxCVkW__optionsMenu {
  display: flex;
  justify-content: flex-end;
}

.OptionsMenu-module__BxCVkW__buttonsWrapper {
  display: inline-block;
}

@media (width <= 768px) {
  .OptionsMenu-module__BxCVkW__dropdown {
    display: block;
  }

  .OptionsMenu-module__BxCVkW__buttonsWrapper {
    display: none;
  }
}

@media (width >= 768px) {
  .OptionsMenu-module__BxCVkW__dropdown {
    display: none;
  }

  .OptionsMenu-module__BxCVkW__buttonsWrapper {
    display: block;
  }
}


/*# sourceMappingURL=components_44850ac7._.css.map*/
