{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/GoogleSignIn/GoogleSignIn.module.css"], "sourcesContent": ["/* Google shows a white box if the color-scheme is black for some reason */\n.googleButton{\n    width: 250px;\n    color-scheme: light;\n}"], "names": [], "mappings": "AACA", "debugId": null}}, {"offset": {"line": 8, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/NavBar/NavBar.module.css"], "sourcesContent": [".navClosing {\n    left: -270px;\n    transition: all 0.5s;\n}\n\n.navOpen {\n    left: 0px !important;\n    transition: left 0.5s;\n}\n\n.hamburgerIcon {\n    display: inline;\n    width: 36px;\n    height: 36px;\n    cursor: pointer;\n    margin-right: 12px;\n}\n\n.navBar {\n    position: fixed;\n    left: 0px;\n    top: 0px;\n    bottom: 0px;\n    z-index: 20;\n    font-size: 1rem;\n}\n\n.logo {\n    padding: 24px;\n    font-weight: bold;\n    font-size: 20px;\n    letter-spacing: 1px;\n    overflow: hidden;\n    white-space: nowrap;\n    display: flex;\n    align-items: center;\n}\n\n.navBar :global(#pro-sidebar) {\n    position: absolute;\n    bottom: 0;\n    z-index: 100;\n    left: 0;\n    top: 0;\n    min-height: 100vh;\n    border: none;\n}\n\n.navBar :global(.ps-menu-button):hover {\n    background-color: #505050 !important;\n}\n\n.menuItem {\n    display: block !important\n}"], "names": [], "mappings": "AAAA;;;;;AAKA;;;;;AAKA;;;;;;;;AAQA;;;;;;;;;AASA;;;;;;;;;;;AAWA;;;;;;;;;;AAUA;;;;AAIA", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/Premium/PremiumFeatures/PremiumFeatures.module.css"], "sourcesContent": [".premiumFeatures .featureCard {\n    margin-bottom: 20px;\n}\n\n.featureColumn {\n    font-size: larger;\n    text-align: left;\n}\n\n.featureColumnHeading {\n    text-align: left;\n}\n\n.premiumFeatures {\n    overflow-x: auto;\n}\n\n.premiumProductHeading {\n    text-align: center;\n}\n\n.premiumProductHeading {\n    text-align: center;\n}\n\n.premiumProductColumn {\n    text-align: center;\n}\n\n#tooltipHoverId :global(.tooltip-inner){\n    max-width: 100%;\n}\n\n.ingamePriceHoverImage {\n    width: 610px;\n    height: 324px;\n}\n\n@media all and (max-width: 992px) {\n    .ingamePriceHoverImage {\n        width: 305px;\n        height: 162px;\n    }\n}"], "names": [], "mappings": "AAAA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;EACI", "debugId": null}}, {"offset": {"line": 113, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/Premium/Premium.module.css"], "sourcesContent": [".premiumPayment {\n    margin-top: '';\n}\n\n.label {\n    width: 300px;\n    float: left;\n    margin: 0;\n}\n\n.premiumProducts {\n    display: flex;\n    justify-content: space-around;\n    align-content: center;\n}\n\n.premiumProduct {\n    width: calc(25% - 10px);\n}\n\n.premiumProductLabel {\n    text-align: center;\n}\n\n.premiumPrice {\n    text-align: center;\n    font-size: x-large;\n    font-weight: bold;\n    margin: 0px;\n}\n\n@media all and (max-width: 1024px) {\n    .premiumPrice {\n        font-size: medium;\n    }\n    .premiumProductLabel {\n        font-size: x-large;\n    }\n    .premiumProduct {\n        width: auto;\n        margin-bottom: 20px;\n    }\n    .premiumProducts {\n        display: block;\n    }\n}\n\n.purchaseCard {\n    margin-left: 50px;\n}\n\n.sendCoflCoinsButton {\n    float: right;\n}\n\n.cancellationRightCheckbox {\n    position: relative !important;\n}\n\n@media all and (max-width: 480px) {\n    .sendCoflCoinsButton {\n        float: none;\n    }\n}\n"], "names": [], "mappings": "AAAA;;;;AAIA;;;;;;AAMA;;;;;;AAMA;;;;AAIA;;;;AAIA;;;;;;;AAOA;EACI;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;AAKJ;;;;AAIA;;;;AAIA;;;;AAIA;EACI", "debugId": null}}, {"offset": {"line": 183, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/CoflCoins/CoflCoinsPurchase.module.css"], "sourcesContent": [".productGrid {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    flex-direction: initial;\n    flex-wrap: wrap;\n}\n\n.premiumPlanCard {\n    width: 49%;\n    margin-bottom: 25px;\n}\n\n@media all and (max-width: 768px) {\n    .premiumPlanCard {\n        width: calc(100%);\n    }\n}\n\n.premiumPrice {\n    font-size: larger;\n}\n\n.paymentOption {\n    display: flex;\n    justify-content: space-evenly;\n    align-items: center;\n    padding-bottom: 1rem;\n}\n\n.discount {\n    color: lime;\n    float: right;\n}\n\n.manualRedirectLink {\n    margin: 0;\n}\n\n.paymentButtonWrapper {\n    width: 40%;\n}\n\n.paymentButton {\n    width: 100%;\n}\n\n.paymentLabel {\n    width: 50%;\n}"], "names": [], "mappings": "AAAA;;;;;;;;AAQA;;;;;AAKA;EACI;;;;;AAKJ;;;;AAIA;;;;;;;AAOA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA", "debugId": null}}, {"offset": {"line": 236, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/CoflCoins/CoflCoinsDisplay.module.css"], "sourcesContent": [".border {\n    padding: 0.5rem;\n    width: 420px;\n    background-color: #505050;\n    border-radius: 0.5rem;;\n}\n\n.border > :first-child {\n    margin-top: 0;\n}\n\n.border > :last-child {\n    margin-bottom: 0;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;AAOA;;;;AAIA", "debugId": null}}, {"offset": {"line": 253, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/Premium/BuyPremium/BuyPremium.module.css"], "sourcesContent": [".label {\n    width: 200px;\n    float: left;\n    margin: 0;\n}\n\n.coinBalance {\n    float: right;\n}\n\n.dropdown {\n    display: inline;\n    width: 110px;\n}\n\n.dropdown::-webkit-scrollbar {\n    background-color: #272b30;\n}\n\n.dropdown::-webkit-scrollbar {\n    width: 20px;\n}\n\n.dropdown::-webkit-scrollbar-track {\n    box-shadow: inset 0 0 5px grey;\n    border-radius: 10px;\n}\n\n.dropdown::-webkit-scrollbar-thumb {\n    background: #3a3f44;\n    border-radius: 10px;\n}\n\n.dropdown::-webkit-scrollbar-thumb:hover {\n    background: #7a8288;\n}\n\n@media all and (max-width: 768px) {\n    .label {\n        width: auto;\n        margin-right: 15px;\n    }\n    .coinBalance {\n        float: none;\n        margin-top: 20px;\n    }\n    .coinBalance :global(b) {\n        font-size: medium !important;\n    }\n}\n\n@media all and (max-width: 350px) {\n    .label {\n        width: 100%;\n    }\n}\n\n/** Show the not selected price options more clearly as not selected **/\n.purchaseCard :global(:not(.btn-check:checked)) + .priceRangeButton{\n    background-color: #2a3644;\n    border-color: #2a3644;\n    filter: grayscale(70%);\n    font-weight: bold;\n    color: lightgray;\n}\n\n/** Show the selected price option more clearly as selected **/\n.purchaseCard :global(.btn-check:checked) + .priceRangeButton{\n    border-color: white;\n    font-weight: bold;\n}"], "names": [], "mappings": "AAAA;;;;;;AAMA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;AAIA;EACI;;;;;EAIA;;;;;EAIA;;;;;AAKJ;EACI;;;;;AAMJ;;;;;;;;AASA", "debugId": null}}, {"offset": {"line": 327, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/Premium/BuyPremiumConfirmationDialog/BuyPremiumConfirmationDialog.module.css"], "sourcesContent": [".label {\n    width: 200px;\n    float: left;\n    margin: 0;\n}"], "names": [], "mappings": "AAAA", "debugId": null}}, {"offset": {"line": 335, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/TransferCoflCoins/TransferCoflCoinsSummary.module.css"], "sourcesContent": [".label {\n    float: left;\n    width: 150px;\n    font-weight: bold;\n}\n\n.returnButton {\n    float: left;\n    width: 40%;\n}\n\n.sendButton {\n    float: right;\n    width: 40%;\n}\n"], "names": [], "mappings": "AAAA;;;;;;AAMA;;;;;AAKA", "debugId": null}}, {"offset": {"line": 353, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/Premium/PremiumStatus/PremiumStatus.module.css"], "sourcesContent": [".premiumStatusLabel {\n    width: 150px;\n    float: left;\n    margin: 0;\n}\n"], "names": [], "mappings": "AAAA", "debugId": null}}, {"offset": {"line": 361, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/Premium/BuySubscription/BuySubscription.module.css"], "sourcesContent": [".purchaseButtonContainer{\n    display: flex;\n    justify-content: center;\n    flex-direction: column;\n    align-items: center;\n    gap: 8px;\n}\n\n.purchaseButton{\n    width: 80%;\n}"], "names": [], "mappings": "AAAA;;;;;;;;AAQA", "debugId": null}}]}