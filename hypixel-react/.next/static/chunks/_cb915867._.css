/* [project]/components/NavBar/NavBar.module.css [app-client] (css) */
.NavBar-module__yBvhsG__navClosing {
  left: -270px;
  transition: all .5s;
}

.NavBar-module__yBvhsG__navOpen {
  transition: left .5s;
  left: 0 !important;
}

.NavBar-module__yBvhsG__hamburgerIcon {
  display: inline;
  width: 36px;
  height: 36px;
  cursor: pointer;
  margin-right: 12px;
}

.NavBar-module__yBvhsG__navBar {
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  z-index: 20;
  font-size: 1rem;
}

.NavBar-module__yBvhsG__logo {
  padding: 24px;
  font-weight: bold;
  font-size: 20px;
  letter-spacing: 1px;
  overflow: hidden;
  white-space: nowrap;
  display: flex;
  align-items: center;
}

.NavBar-module__yBvhsG__navBar #pro-sidebar {
  position: absolute;
  bottom: 0;
  z-index: 100;
  left: 0;
  top: 0;
  min-height: 100vh;
  border: none;
}

.NavBar-module__yBvhsG__navBar .ps-menu-button:hover {
  background-color: #505050 !important;
}

.NavBar-module__yBvhsG__menuItem {
  display: block !important;
}


/* [project]/components/GoogleSignIn/GoogleSignIn.module.css [app-client] (css) */
.GoogleSignIn-module__lTSYOa__googleButton {
  width: 250px;
  color-scheme: light;
}


/* [project]/components/TradeCreate/TradeCreate.module.css [app-client] (css) */
.TradeCreate-module__kyF5Ra__card {
  margin-bottom: 30px;
}


/* [project]/components/PlayerInventory/PlayerInventory.module.css [app-client] (css) */
.PlayerInventory-module__VgUqnq__inventory {
  overflow: hidden;
}

.PlayerInventory-module__VgUqnq__card {
  margin-bottom: 30px;
}

.PlayerInventory-module__VgUqnq__inventory .PlayerInventory-module__VgUqnq__grid {
  width: 540px;
  height: 100%;
}

.PlayerInventory-module__VgUqnq__inventory .PlayerInventory-module__VgUqnq__gridCell {
  width: 60px;
  height: 60px;
  background-color: #8b8b8b;
  border-top: thick double #373737;
  border-left: thick double #373737;
  border-right: double #fff;
  border-bottom: double #fff;
  float: left;
}

.PlayerInventory-module__VgUqnq__inventory .PlayerInventory-module__VgUqnq__image {
  display: block;
  margin: auto;
  padding: 2px;
}

.PlayerInventory-module__VgUqnq__hoverElement li {
  text-align: left;
}

.PlayerInventory-module__VgUqnq__hoverElement {
  min-width: "800px" !important;
}

#PlayerInventory-module__VgUqnq__tooltipHoverId .tooltip-inner {
  max-width: 100%;
}


/* [project]/components/OptionsMenu/OptionsMenu.module.css [app-client] (css) */
.OptionsMenu-module__BxCVkW__optionsMenu a {
  color: #fff;
  margin-left: 5px;
  text-decoration: none !important;
}

.OptionsMenu-module__BxCVkW__optionsMenu {
  display: flex;
  justify-content: flex-end;
}

.OptionsMenu-module__BxCVkW__buttonsWrapper {
  display: inline-block;
}

@media (width <= 768px) {
  .OptionsMenu-module__BxCVkW__dropdown {
    display: block;
  }

  .OptionsMenu-module__BxCVkW__buttonsWrapper {
    display: none;
  }
}

@media (width >= 768px) {
  .OptionsMenu-module__BxCVkW__dropdown {
    display: none;
  }

  .OptionsMenu-module__BxCVkW__buttonsWrapper {
    display: block;
  }
}


/* [project]/components/Search/Search.module.css [app-client] (css) */
.Search-module__Lg4wHG__searchResultIcon {
  margin-right: 20px;
}

.Search-module__Lg4wHG__bar {
  display: flex;
}

.Search-module__Lg4wHG__current {
  flex-grow: 100;
  font-size: 1rem;
}

.Search-module__Lg4wHG__search .form-control {
  background-color: #303030;
  color: #fff;
  border-color: #222;
  box-shadow: none;
}

.Search-module__Lg4wHG__search .form-control:focus {
  background-color: #303030;
  color: #fff;
  border-color: #222;
  box-shadow: none;
}

.Search-module__Lg4wHG__searchFormGroup {
  display: flex;
  justify-content: center;
  align-content: center;
  margin-bottom: 0;
  border-bottom-width: 0;
}

.Search-module__Lg4wHG__previousSearch {
  color: #c389f6;
}

.Search-module__Lg4wHG__multiInputfield {
  color: #fff;
  color-scheme: dark;
}

.Search-module__Lg4wHG__multiSearch .rbt-token {
  background-color: #444;
  color: #fff;
}


/* [project]/components/FilterElement/FilterElements/DateFilterElement.module.css [app-client] (css) */
.DateFilterElement-module__aG3NAG__datePickerPopper {
  z-index: 50;
}

.DateFilterElement-module__aG3NAG__calendarIcon {
  padding-top: 11px !important;
}

.DateFilterElement-module__aG3NAG__dateFilter {
  padding-left: 35px !important;
}


/* [project]/components/FilterElement/FilterElement.module.css [app-client] (css) */
.FilterElement-module__NQNxTa__genericFilter {
  flex-grow: 100;
}

.FilterElement-module__NQNxTa__genericFilter .form-control {
  width: 100%;
}


/* [project]/components/FilterElement/FilterElements/NumberRangeFilterElement.module.css [app-client] (css) */
.NumberRangeFilterElement-module__oxFKZG__slider.rc-slider {
  height: 38px;
  padding-top: 15px;
  margin-right: 10px;
}

.NumberRangeFilterElement-module__oxFKZG__slider .rc-slider-mark {
  margin-top: 5px;
}

.NumberRangeFilterElement-module__oxFKZG__slider .rc-slider-mark-text {
  cursor: default;
}

.NumberRangeFilterElement-module__oxFKZG__container {
  display: flex;
}

.NumberRangeFilterElement-module__oxFKZG__textField.form-control {
  width: 75px;
  margin-right: 15px;
}


/* [project]/node_modules/rc-slider/assets/index.css [app-client] (css) */
.rc-slider {
  position: relative;
  width: 100%;
  height: 14px;
  padding: 5px 0;
  border-radius: 6px;
  touch-action: none;
  box-sizing: border-box;
  -webkit-tap-highlight-color: #0000;
}

.rc-slider * {
  box-sizing: border-box;
  -webkit-tap-highlight-color: #0000;
}

.rc-slider-rail {
  position: absolute;
  width: 100%;
  height: 4px;
  background-color: #e9e9e9;
  border-radius: 6px;
}

.rc-slider-track, .rc-slider-tracks {
  position: absolute;
  height: 4px;
  background-color: #abe2fb;
  border-radius: 6px;
}

.rc-slider-track-draggable {
  z-index: 1;
  box-sizing: content-box;
  background-clip: content-box;
  border-top: 5px solid #0000;
  border-bottom: 5px solid #0000;
  transform: translateY(-5px);
}

.rc-slider-handle {
  position: absolute;
  z-index: 1;
  width: 14px;
  height: 14px;
  margin-top: -5px;
  background-color: #fff;
  border: 2px solid #96dbfa;
  border-radius: 50%;
  cursor: pointer;
  cursor: -webkit-grab;
  cursor: grab;
  opacity: .8;
  touch-action: pan-x;
}

.rc-slider-handle-dragging.rc-slider-handle-dragging.rc-slider-handle-dragging {
  border-color: #57c5f7;
  box-shadow: 0 0 0 5px #96dbfa;
}

.rc-slider-handle:focus {
  outline: none;
  box-shadow: none;
}

.rc-slider-handle:focus-visible {
  border-color: #2db7f5;
  box-shadow: 0 0 0 3px #96dbfa;
}

.rc-slider-handle-click-focused:focus {
  border-color: #96dbfa;
  box-shadow: unset;
}

.rc-slider-handle:hover {
  border-color: #57c5f7;
}

.rc-slider-handle:active {
  border-color: #57c5f7;
  box-shadow: 0 0 5px #57c5f7;
  cursor: -webkit-grabbing;
  cursor: grabbing;
}

.rc-slider-mark {
  position: absolute;
  top: 18px;
  left: 0;
  width: 100%;
  font-size: 12px;
}

.rc-slider-mark-text {
  position: absolute;
  display: inline-block;
  color: #999;
  text-align: center;
  vertical-align: middle;
  cursor: pointer;
}

.rc-slider-mark-text-active {
  color: #666;
}

.rc-slider-step {
  position: absolute;
  width: 100%;
  height: 4px;
  background: none;
}

.rc-slider-dot {
  position: absolute;
  bottom: -2px;
  width: 8px;
  height: 8px;
  vertical-align: middle;
  background-color: #fff;
  border: 2px solid #e9e9e9;
  border-radius: 50%;
  cursor: pointer;
}

.rc-slider-dot-active {
  border-color: #96dbfa;
}

.rc-slider-dot-reverse {
  margin-right: -4px;
}

.rc-slider-disabled {
  background-color: #e9e9e9;
}

.rc-slider-disabled .rc-slider-track {
  background-color: #ccc;
}

.rc-slider-disabled .rc-slider-handle, .rc-slider-disabled .rc-slider-dot {
  background-color: #fff;
  border-color: #ccc;
  box-shadow: none;
  cursor: not-allowed;
}

.rc-slider-disabled .rc-slider-mark-text, .rc-slider-disabled .rc-slider-dot {
  cursor: not-allowed !important;
}

.rc-slider-vertical {
  width: 14px;
  height: 100%;
  padding: 0 5px;
}

.rc-slider-vertical .rc-slider-rail {
  width: 4px;
  height: 100%;
}

.rc-slider-vertical .rc-slider-track {
  bottom: 0;
  left: 5px;
  width: 4px;
}

.rc-slider-vertical .rc-slider-track-draggable {
  border-top: 0;
  border-bottom: 0;
  border-right: 5px solid #0000;
  border-left: 5px solid #0000;
  transform: translateX(-5px);
}

.rc-slider-vertical .rc-slider-handle {
  position: absolute;
  z-index: 1;
  margin-top: 0;
  margin-left: -5px;
  touch-action: pan-y;
}

.rc-slider-vertical .rc-slider-mark {
  top: 0;
  left: 18px;
  height: 100%;
}

.rc-slider-vertical .rc-slider-step {
  width: 4px;
  height: 100%;
}

.rc-slider-vertical .rc-slider-dot {
  margin-left: -2px;
}

.rc-slider-tooltip-zoom-down-enter, .rc-slider-tooltip-zoom-down-appear {
  animation-duration: .3s;
  animation-fill-mode: both;
  animation-play-state: paused;
  display: block !important;
}

.rc-slider-tooltip-zoom-down-leave {
  animation-duration: .3s;
  animation-fill-mode: both;
  animation-play-state: paused;
  display: block !important;
}

.rc-slider-tooltip-zoom-down-enter.rc-slider-tooltip-zoom-down-enter-active, .rc-slider-tooltip-zoom-down-appear.rc-slider-tooltip-zoom-down-appear-active {
  animation-name: rcSliderTooltipZoomDownIn;
  animation-play-state: running;
}

.rc-slider-tooltip-zoom-down-leave.rc-slider-tooltip-zoom-down-leave-active {
  animation-name: rcSliderTooltipZoomDownOut;
  animation-play-state: running;
}

.rc-slider-tooltip-zoom-down-enter, .rc-slider-tooltip-zoom-down-appear {
  transform: scale(0);
  animation-timing-function: cubic-bezier(.23, 1, .32, 1);
}

.rc-slider-tooltip-zoom-down-leave {
  animation-timing-function: cubic-bezier(.755, .05, .855, .06);
}

@keyframes rcSliderTooltipZoomDownIn {
  0% {
    transform: scale(0);
    transform-origin: 50% 100%;
    opacity: 0;
  }

  100% {
    transform: scale(1);
    transform-origin: 50% 100%;
  }
}

@keyframes rcSliderTooltipZoomDownOut {
  0% {
    transform: scale(1);
    transform-origin: 50% 100%;
  }

  100% {
    transform: scale(0);
    transform-origin: 50% 100%;
    opacity: 0;
  }
}

.rc-slider-tooltip {
  position: absolute;
  top: -9999px;
  left: -9999px;
  visibility: visible;
  box-sizing: border-box;
  -webkit-tap-highlight-color: #0000;
}

.rc-slider-tooltip * {
  box-sizing: border-box;
  -webkit-tap-highlight-color: #0000;
}

.rc-slider-tooltip-hidden {
  display: none;
}

.rc-slider-tooltip-placement-top {
  padding: 4px 0 8px;
}

.rc-slider-tooltip-inner {
  min-width: 24px;
  height: 24px;
  padding: 6px 2px;
  color: #fff;
  font-size: 12px;
  line-height: 1;
  text-align: center;
  text-decoration: none;
  background-color: #6c6c6c;
  border-radius: 6px;
  box-shadow: 0 0 4px #d9d9d9;
}

.rc-slider-tooltip-arrow {
  position: absolute;
  width: 0;
  height: 0;
  border-color: #0000;
  border-style: solid;
}

.rc-slider-tooltip-placement-top .rc-slider-tooltip-arrow {
  bottom: 4px;
  left: 50%;
  margin-left: -4px;
  border-width: 4px 4px 0;
  border-top-color: #6c6c6c;
}


/* [project]/components/ItemFilter/ItemFilter.module.css [app-client] (css) */
.ItemFilter-module__EG8HRq__itemFilter {
  margin: 20px 0;
}

.ItemFilter-module__EG8HRq__itemFilter a {
  color: #fff;
  text-decoration: none !important;
}

.ItemFilter-module__EG8HRq__itemFilter .ItemFilter-module__EG8HRq__filterElement {
  display: flex;
  flex-direction: row;
  width: 49%;
}

.ItemFilter-module__EG8HRq__itemFilter .ItemFilter-module__EG8HRq__filterContainer {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: space-between;
}

.ItemFilter-module__EG8HRq__itemFilter .ItemFilter-module__EG8HRq__removeFilter {
  cursor: pointer;
  margin-top: 35px;
}

.ItemFilter-module__EG8HRq__itemFilter .ItemFilter-module__EG8HRq__addFilterSelect {
  width: 100%;
  margin-bottom: 25px;
}

@media (width <= 768px) {
  .ItemFilter-module__EG8HRq__itemFilter .ItemFilter-module__EG8HRq__filterElement {
    width: 100%;
  }
}


/*# sourceMappingURL=_cb915867._.css.map*/
