{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/components/Flipper/FlipBased/FlipBased.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"cardWrapper\": \"FlipBased-module__5Ly2eq__cardWrapper\",\n  \"filterContainer\": \"FlipBased-module__5Ly2eq__filterContainer\",\n  \"filterInput\": \"FlipBased-module__5Ly2eq__filterInput\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 17, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/Flipper/FlipBased/FlipBased.tsx"], "sourcesContent": ["'use client'\nimport moment from 'moment'\nimport Image from 'next/image'\nimport Link from 'next/link'\nimport { useEffect, useState } from 'react'\nimport { Badge, Card, Form } from 'react-bootstrap'\nimport api from '../../../api/ApiHelper'\nimport { useForceUpdate } from '../../../utils/Hooks'\nimport { getLoadingElement } from '../../../utils/LoadingUtils'\nimport Number from '../../Number/Number'\nimport styles from './FlipBased.module.css'\n\ninterface Props {\n    auctionUUID: string\n    item: Item\n}\n\nfunction FlipBased(props: Props) {\n    let [auctions, setAuctions] = useState<Auction[]>([])\n    let [isLoading, setIsLoading] = useState(true)\n    let [hasLoadingFailed, setHasLoadingFailed] = useState(false)\n    let [orderBy, setOrderBy] = useState('time')\n\n    let forceUpdate = useForceUpdate()\n\n    useEffect(() => {\n        api.getFlipBasedAuctions(props.auctionUUID)\n            .then(auctions => {\n                setAuctions(auctions)\n                setIsLoading(false)\n            })\n            .catch(() => {\n                setIsLoading(false)\n                setHasLoadingFailed(true)\n            })\n    }, [props.auctionUUID])\n\n    useEffect(() => {\n        forceUpdate()\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [props.item.iconUrl, props.item.name])\n\n    auctions = orderBy === 'time' ? auctions.sort((a, b) => b.end.getTime() - a.end.getTime()) : auctions.sort((a, b) => b.highestBid - a.highestBid)\n    let auctionsElement = auctions.map(auction => {\n        return (\n            <>\n                <div className={styles.cardWrapper} style={{ display: 'inline-block' }} key={auction.uuid}>\n                    <span className=\"disableLinkStyle\">\n                        <Link href={`/auction/${auction.uuid}`} className=\"disableLinkStyle\">\n                            <Card className=\"card\">\n                                <Card.Header style={{ padding: '10px' }}>\n                                    <p className=\"ellipsis\" style={{ width: '180px' }}>\n                                        <Image\n                                            crossOrigin=\"anonymous\"\n                                            src={api.getItemImageUrl(props.item) || ''}\n                                            height=\"32\"\n                                            width=\"32\"\n                                            alt=\"\"\n                                            style={{ marginRight: '5px' }}\n                                            loading=\"lazy\"\n                                        />\n                                        {auction.item.name}\n                                    </p>\n                                </Card.Header>\n                                <Card.Body>\n                                    <div>\n                                        <ul>\n                                            <li>Ended {moment(auction.end).fromNow()}</li>\n                                            <li>\n                                                <Number number={auction.highestBid || auction.startingBid} /> Coins\n                                            </li>\n                                            {auction.bin ? (\n                                                <li>\n                                                    <Badge style={{ marginLeft: '5px' }} bg=\"success\">\n                                                        BIN\n                                                    </Badge>\n                                                </li>\n                                            ) : (\n                                                ''\n                                            )}\n                                        </ul>\n                                    </div>\n                                </Card.Body>\n                            </Card>\n                        </Link>\n                    </span>\n                </div>\n            </>\n        )\n    })\n\n    return (\n        <div>\n            {isLoading ? getLoadingElement() : null}\n            {!isLoading && !hasLoadingFailed && auctions.length > 0 ? (\n                <>\n                    <div className={styles.filterContainer}>\n                        <Form.Select\n                            className={styles.filterInput}\n                            defaultValue={'time'}\n                            onChange={e => {\n                                setOrderBy(e.target.value)\n                            }}\n                        >\n                            <option key=\"time\" value=\"time\">\n                                Time\n                            </option>\n                            <option key=\"price\" value=\"price\">\n                                Price\n                            </option>\n                        </Form.Select>\n                    </div>\n                    <div style={{ display: 'flex', flexWrap: 'wrap', justifyContent: 'center', alignItems: 'stretch' }}>{auctionsElement}</div>\n                </>\n            ) : null}\n            {!isLoading && !hasLoadingFailed && auctions.length === 0 ? <p>No auctions found</p> : null}\n            {hasLoadingFailed ? <p>An error occured while loading the auctions</p> : null}\n        </div>\n    )\n}\n\nexport default FlipBased\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;;;AAVA;;;;;;;;;;;AAiBA,SAAS,UAAU,KAAY;;IAC3B,IAAI,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACpD,IAAI,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,IAAI,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,IAAI,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,IAAI,cAAc,CAAA,GAAA,kHAAA,CAAA,iBAAc,AAAD;IAE/B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACN,oHAAA,CAAA,UAAG,CAAC,oBAAoB,CAAC,MAAM,WAAW,EACrC,IAAI;uCAAC,CAAA;oBACF,YAAY;oBACZ,aAAa;gBACjB;sCACC,KAAK;uCAAC;oBACH,aAAa;oBACb,oBAAoB;gBACxB;;QACR;8BAAG;QAAC,MAAM,WAAW;KAAC;IAEtB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACN;QACA,uDAAuD;QAC3D;8BAAG;QAAC,MAAM,IAAI,CAAC,OAAO;QAAE,MAAM,IAAI,CAAC,IAAI;KAAC;IAExC,WAAW,YAAY,SAAS,SAAS,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,GAAG,CAAC,OAAO,KAAK,EAAE,GAAG,CAAC,OAAO,MAAM,SAAS,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,UAAU,GAAG,EAAE,UAAU;IAChJ,IAAI,kBAAkB,SAAS,GAAG,CAAC,CAAA;QAC/B,qBACI;sBACI,cAAA,6LAAC;gBAAI,WAAW,8JAAA,CAAA,UAAM,CAAC,WAAW;gBAAE,OAAO;oBAAE,SAAS;gBAAe;0BACjE,cAAA,6LAAC;oBAAK,WAAU;8BACZ,cAAA,6LAAC,+JAAA,CAAA,UAAI;wBAAC,MAAM,CAAC,SAAS,EAAE,QAAQ,IAAI,EAAE;wBAAE,WAAU;kCAC9C,cAAA,6LAAC,uLAAA,CAAA,OAAI;4BAAC,WAAU;;8CACZ,6LAAC,uLAAA,CAAA,OAAI,CAAC,MAAM;oCAAC,OAAO;wCAAE,SAAS;oCAAO;8CAClC,cAAA,6LAAC;wCAAE,WAAU;wCAAW,OAAO;4CAAE,OAAO;wCAAQ;;0DAC5C,6LAAC,gIAAA,CAAA,UAAK;gDACF,aAAY;gDACZ,KAAK,oHAAA,CAAA,UAAG,CAAC,eAAe,CAAC,MAAM,IAAI,KAAK;gDACxC,QAAO;gDACP,OAAM;gDACN,KAAI;gDACJ,OAAO;oDAAE,aAAa;gDAAM;gDAC5B,SAAQ;;;;;;4CAEX,QAAQ,IAAI,CAAC,IAAI;;;;;;;;;;;;8CAG1B,6LAAC,uLAAA,CAAA,OAAI,CAAC,IAAI;8CACN,cAAA,6LAAC;kDACG,cAAA,6LAAC;;8DACG,6LAAC;;wDAAG;wDAAO,CAAA,GAAA,mIAAA,CAAA,UAAM,AAAD,EAAE,QAAQ,GAAG,EAAE,OAAO;;;;;;;8DACtC,6LAAC;;sEACG,6LAAC,kIAAA,CAAA,UAAM;4DAAC,QAAQ,QAAQ,UAAU,IAAI,QAAQ,WAAW;;;;;;wDAAI;;;;;;;gDAEhE,QAAQ,GAAG,iBACR,6LAAC;8DACG,cAAA,6LAAC,yLAAA,CAAA,QAAK;wDAAC,OAAO;4DAAE,YAAY;wDAAM;wDAAG,IAAG;kEAAU;;;;;;;;;;2DAKtD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAhC6C,QAAQ,IAAI;;;;;;IA2CrG;IAEA,qBACI,6LAAC;;YACI,YAAY,CAAA,GAAA,yHAAA,CAAA,oBAAiB,AAAD,MAAM;YAClC,CAAC,aAAa,CAAC,oBAAoB,SAAS,MAAM,GAAG,kBAClD;;kCACI,6LAAC;wBAAI,WAAW,8JAAA,CAAA,UAAM,CAAC,eAAe;kCAClC,cAAA,6LAAC,uLAAA,CAAA,OAAI,CAAC,MAAM;4BACR,WAAW,8JAAA,CAAA,UAAM,CAAC,WAAW;4BAC7B,cAAc;4BACd,UAAU,CAAA;gCACN,WAAW,EAAE,MAAM,CAAC,KAAK;4BAC7B;;8CAEA,6LAAC;oCAAkB,OAAM;8CAAO;mCAApB;;;;;8CAGZ,6LAAC;oCAAmB,OAAM;8CAAQ;mCAAtB;;;;;;;;;;;;;;;;kCAKpB,6LAAC;wBAAI,OAAO;4BAAE,SAAS;4BAAQ,UAAU;4BAAQ,gBAAgB;4BAAU,YAAY;wBAAU;kCAAI;;;;;;;+BAEzG;YACH,CAAC,aAAa,CAAC,oBAAoB,SAAS,MAAM,KAAK,kBAAI,6LAAC;0BAAE;;;;;uBAAwB;YACtF,iCAAmB,6LAAC;0BAAE;;;;;uBAAkD;;;;;;;AAGrF;GAtGS;;QAMa,kHAAA,CAAA,iBAAc;;;KAN3B;uCAwGM", "debugId": null}}, {"offset": {"line": 312, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/components/Flipper/Flip/Flip.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"flex\": \"Flip-module__YhCQqq__flex\",\n  \"flexMax\": \"Flip-module__YhCQqq__flexMax\",\n  \"flipAuctionCard\": \"Flip-module__YhCQqq__flipAuctionCard\",\n  \"flipAuctionCopyButton\": \"Flip-module__YhCQqq__flipAuctionCopyButton\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 324, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/Flipper/Flip/Flip.tsx"], "sourcesContent": ["'use client'\nimport { useMatomo } from '@jonkoops/matomo-tracker-react'\nimport HelpIcon from '@mui/icons-material/Help'\nimport Image from 'next/image'\nimport { useEffect, type JSX } from 'react';\nimport { Bad<PERSON>, Card } from 'react-bootstrap'\nimport { toast } from 'react-toastify'\nimport { CUSTOM_EVENTS } from '../../../api/ApiTypes.d'\nimport { getFlipCustomizeSettings, getFlipFinders } from '../../../utils/FlipUtils'\nimport { formatDungeonStarsInString, formatToPriceToShorten, getMinecraftColorCodedElement, getStyleForTier } from '../../../utils/Formatter'\nimport { useForceUpdate } from '../../../utils/Hooks'\nimport { CopyButton } from '../../CopyButton/CopyButton'\nimport Number from '../../Number/Number'\nimport styles from './Flip.module.css'\nimport { writeToClipboard } from '../../../utils/ClipboardUtils'\nimport api from '../../../api/ApiHelper'\n\ninterface Props {\n    flip: FlipAuction\n    style?: any\n    onBasedAuctionClick?(flip: FlipAuction)\n    onCardClick?(flip: FlipAuction)\n    onCopy?(flip: FlipAuction)\n}\n\nfunction Flip(props: Props) {\n    let settings = getFlipCustomizeSettings()\n    let forceUpdate = useForceUpdate()\n    let { trackEvent } = useMatomo()\n\n    useEffect(() => {\n        document.addEventListener(CUSTOM_EVENTS.FLIP_SETTINGS_CHANGE, forceUpdate)\n\n        return () => {\n            document.removeEventListener(CUSTOM_EVENTS.FLIP_SETTINGS_CHANGE, forceUpdate)\n        }\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [])\n\n    function getLowestBinLink(itemTag: string) {\n        return '/item/' + itemTag + '?range=active&itemFilter=eyJCaW4iOiJ0cnVlIn0%3D'\n    }\n\n    function onCardClick() {\n        if (props.onCardClick) {\n            props.onCardClick(props.flip)\n        }\n    }\n\n    function onBasedAuctionClick(e) {\n        e.preventDefault()\n        if (props.onBasedAuctionClick) {\n            props.onBasedAuctionClick(props.flip)\n        }\n    }\n\n    function onCopy(e) {\n        if (e.defaultPrevented) {\n            return\n        }\n\n        writeToClipboard('/viewauction ' + props.flip.uuid)\n        if (props.onCopy) {\n            props.onCopy(props.flip)\n        }\n        if (!settings.hideCopySuccessMessage) {\n            toast.success(\n                <p>\n                    Copied ingame link <br />\n                    <i>/viewauction {props.flip.uuid}</i>\n                </p>,\n                {\n                    autoClose: 1500,\n                    pauseOnFocusLoss: false\n                }\n            )\n        }\n        trackEvent({\n            category: 'copyButtonClick',\n            action: '/viewauction ' + props.flip.uuid\n        })\n    }\n\n    function getProfitElement(flip: FlipAuction): JSX.Element {\n        let settings = getFlipCustomizeSettings()\n        let profit = flip.profit\n        let preSymbol = profit > 0 ? '+' : ''\n        let profitPercentElement = <span>({Math.round((profit / flip.cost) * 100)}%)</span>\n        return (\n            <b style={{ color: profit > 0 ? 'lime' : 'white' }}>\n                <span>{preSymbol}</span>\n                <span>{formatPrices(profit)}</span>\n                <span> Coins </span>\n                {!settings.hideProfitPercent ? profitPercentElement : null}\n            </b>\n        )\n    }\n\n    function onMouseDownLowestBin(e) {\n        e.preventDefault()\n        window.open(getLowestBinLink(props.flip.item.tag), '_blank')\n    }\n\n    function onMouseDownSeller(e) {\n        e.preventDefault()\n        window.open('/player/' + props.flip.sellerName)\n    }\n\n    function formatPrices(price: number): JSX.Element {\n        // Always use Number component for consistent server/client rendering\n        // The Number component handles SSR/hydration internally\n        return <Number number={price} />;\n    }\n\n    let stars = props.flip.item.name?.match(/✪+/gm)\n    let itemName = stars && props.flip.item.name ? props.flip.item.name.split(stars[0])[0] : props.flip.item.name\n\n    return (\n        <div key={props.flip.uuid} style={props.style}>\n            <Card className={styles.flipAuctionCard} style={{ cursor: 'pointer' }} onMouseDown={onCardClick}>\n                <Card.Header style={{ padding: '10px', display: 'flex', justifyContent: 'space-between' }}>\n                    <div className=\"ellipse\">\n                        <Image\n                            crossOrigin=\"anonymous\"\n                            src={api.getItemImageUrl(props.flip.item) || ''}\n                            height=\"24\"\n                            width=\"24\"\n                            alt=\"\"\n                            style={{ marginRight: '5px' }}\n                            loading=\"lazy\"\n                        />\n                        <span style={getStyleForTier(props.flip.item.tier)}>{itemName}</span>\n                    </div>\n                    {stars ? formatDungeonStarsInString(stars[0]) : null}\n                    {props.flip.bin ? (\n                        <Badge style={{ marginLeft: '5px' }} bg=\"success\">\n                            BIN\n                        </Badge>\n                    ) : (\n                        ''\n                    )}\n                    {props.flip.sold ? (\n                        <Badge style={{ marginLeft: '5px' }} bg=\"danger\">\n                            SOLD\n                        </Badge>\n                    ) : (\n                        ''\n                    )}\n                </Card.Header>\n                <Card.Body style={{ padding: '10px', cursor: 'pointer' }} onMouseDown={onCopy}>\n                    {settings.hideCost ? null : (\n                        <p>\n                            <span>Cost: </span>\n                            <br />\n                            <b suppressHydrationWarning style={{ color: 'red' }}>{formatPrices(props.flip.cost)} Coins</b>\n                        </p>\n                    )}\n                    {settings.hideMedianPrice ? null : (\n                        <p>\n                            <span>Target price: </span>\n                            <br />\n                            <b suppressHydrationWarning>{formatPrices(props.flip.finder === 2 ? props.flip.lowestBin : props.flip.median)} Coins</b>\n                        </p>\n                    )}\n                    {settings.hideEstimatedProfit ? null : (\n                        <p>\n                            <span>Estimated Profit: </span>\n                            <br />\n                            {getProfitElement(props.flip)}\n                            <span style={{ float: 'right' }}>\n                                <span onMouseDown={onBasedAuctionClick}>\n                                    <HelpIcon />\n                                </span>\n                            </span>\n                        </p>\n                    )}\n                    {settings.hideLowestBin && settings.hideSeller && settings.hideSecondLowestBin ? null : <hr />}\n                    {settings.hideLowestBin ? null : (\n                        <p>\n                            <span>Lowest BIN: </span>\n                            <br />\n                            {!settings.disableLinks ? (\n                                <a rel=\"noreferrer\" target=\"_blank\" onMouseDown={onMouseDownLowestBin} href={getLowestBinLink(props.flip.item.tag)}>\n                                    {formatPrices(props.flip.lowestBin)} Coins\n                                </a>\n                            ) : (\n                                <span>{formatPrices(props.flip.lowestBin)} Coins</span>\n                            )}\n                        </p>\n                    )}\n                    {settings.hideSecondLowestBin ? null : (\n                        <p>\n                            <span>Second lowest BIN: </span>\n                            <br />\n                            {!settings.disableLinks ? (\n                                <a rel=\"noreferrer\" target=\"_blank\" onMouseDown={onMouseDownLowestBin} href={getLowestBinLink(props.flip.item.tag)}>\n                                    {formatPrices(props.flip.secondLowestBin)} Coins\n                                </a>\n                            ) : (\n                                <span>{formatPrices(props.flip.secondLowestBin)} Coins</span>\n                            )}\n                        </p>\n                    )}\n                    {settings.hideSeller ? null : (\n                        <p>\n                            <span>Seller: </span>\n                            <br />\n                            {!settings.disableLinks ? (\n                                <a rel=\"noreferrer\" target=\"_blank\" onMouseDown={onMouseDownSeller} href={'/player/' + props.flip.sellerName}>\n                                    <b>{props.flip.sellerName}</b>\n                                </a>\n                            ) : (\n                                <span>\n                                    <b>{props.flip.sellerName}</b>\n                                </span>\n                            )}\n                        </p>\n                    )}\n                    {props.flip.props && props.flip.props?.length > 0 && settings.maxExtraInfoFields! > 0 ? (\n                        <span>\n                            <hr />\n                            <ul>\n                                {props.flip.props?.map((prop, i) => {\n                                    if (i >= settings.maxExtraInfoFields!) {\n                                        return ''\n                                    } else {\n                                        return <li key={i}>{getMinecraftColorCodedElement(prop)}</li>\n                                    }\n                                })}\n                            </ul>\n                        </span>\n                    ) : (\n                        ''\n                    )}\n                    <hr />\n                    <div className={styles.flex}>\n                        {settings.hideVolume ? null : (\n                            <div className={styles.flexMax}>\n                                <span>Volume: </span>\n                                {props.flip.volume > 59 ? '>60' : '~' + Math.round(props.flip.volume * 10) / 10} per day\n                            </div>\n                        )}\n                        {getFlipFinders([props.flip.finder]).map(finder => {\n                            return (\n                                <Badge key={finder.shortLabel} bg=\"dark\">\n                                    {finder.shortLabel}\n                                </Badge>\n                            )\n                        })}\n                    </div>\n                    <CopyButton forceIsCopied={props.flip.isCopied} buttonClass={styles.flipAuctionCopyButton} />\n                </Card.Body>\n            </Card>\n        </div>\n    )\n}\n\nexport default Flip\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAfA;;;;;;;;;;;;;;;;AAyBA,SAAS,KAAK,KAAY;;IACtB,IAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,2BAAwB,AAAD;IACtC,IAAI,cAAc,CAAA,GAAA,kHAAA,CAAA,iBAAc,AAAD;IAC/B,IAAI,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,sNAAA,CAAA,YAAS,AAAD;IAE7B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACN,SAAS,gBAAgB,CAAC,wHAAA,CAAA,gBAAa,CAAC,oBAAoB,EAAE;YAE9D;kCAAO;oBACH,SAAS,mBAAmB,CAAC,wHAAA,CAAA,gBAAa,CAAC,oBAAoB,EAAE;gBACrE;;QACA,uDAAuD;QAC3D;yBAAG,EAAE;IAEL,SAAS,iBAAiB,OAAe;QACrC,OAAO,WAAW,UAAU;IAChC;IAEA,SAAS;QACL,IAAI,MAAM,WAAW,EAAE;YACnB,MAAM,WAAW,CAAC,MAAM,IAAI;QAChC;IACJ;IAEA,SAAS,oBAAoB,CAAC;QAC1B,EAAE,cAAc;QAChB,IAAI,MAAM,mBAAmB,EAAE;YAC3B,MAAM,mBAAmB,CAAC,MAAM,IAAI;QACxC;IACJ;IAEA,SAAS,OAAO,CAAC;QACb,IAAI,EAAE,gBAAgB,EAAE;YACpB;QACJ;QAEA,CAAA,GAAA,2HAAA,CAAA,mBAAgB,AAAD,EAAE,kBAAkB,MAAM,IAAI,CAAC,IAAI;QAClD,IAAI,MAAM,MAAM,EAAE;YACd,MAAM,MAAM,CAAC,MAAM,IAAI;QAC3B;QACA,IAAI,CAAC,SAAS,sBAAsB,EAAE;YAClC,sJAAA,CAAA,QAAK,CAAC,OAAO,eACT,6LAAC;;oBAAE;kCACoB,6LAAC;;;;;kCACpB,6LAAC;;4BAAE;4BAAc,MAAM,IAAI,CAAC,IAAI;;;;;;;;;;;;sBAEpC;gBACI,WAAW;gBACX,kBAAkB;YACtB;QAER;QACA,WAAW;YACP,UAAU;YACV,QAAQ,kBAAkB,MAAM,IAAI,CAAC,IAAI;QAC7C;IACJ;IAEA,SAAS,iBAAiB,IAAiB;QACvC,IAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,2BAAwB,AAAD;QACtC,IAAI,SAAS,KAAK,MAAM;QACxB,IAAI,YAAY,SAAS,IAAI,MAAM;QACnC,IAAI,qCAAuB,6LAAC;;gBAAK;gBAAE,KAAK,KAAK,CAAC,AAAC,SAAS,KAAK,IAAI,GAAI;gBAAK;;;;;;;QAC1E,qBACI,6LAAC;YAAE,OAAO;gBAAE,OAAO,SAAS,IAAI,SAAS;YAAQ;;8BAC7C,6LAAC;8BAAM;;;;;;8BACP,6LAAC;8BAAM,aAAa;;;;;;8BACpB,6LAAC;8BAAK;;;;;;gBACL,CAAC,SAAS,iBAAiB,GAAG,uBAAuB;;;;;;;IAGlE;IAEA,SAAS,qBAAqB,CAAC;QAC3B,EAAE,cAAc;QAChB,OAAO,IAAI,CAAC,iBAAiB,MAAM,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG;IACvD;IAEA,SAAS,kBAAkB,CAAC;QACxB,EAAE,cAAc;QAChB,OAAO,IAAI,CAAC,aAAa,MAAM,IAAI,CAAC,UAAU;IAClD;IAEA,SAAS,aAAa,KAAa;QAC/B,qEAAqE;QACrE,wDAAwD;QACxD,qBAAO,6LAAC,kIAAA,CAAA,UAAM;YAAC,QAAQ;;;;;;IAC3B;IAEA,IAAI,QAAQ,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM;IACxC,IAAI,WAAW,SAAS,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI;IAE7G,qBACI,6LAAC;QAA0B,OAAO,MAAM,KAAK;kBACzC,cAAA,6LAAC,uLAAA,CAAA,OAAI;YAAC,WAAW,oJAAA,CAAA,UAAM,CAAC,eAAe;YAAE,OAAO;gBAAE,QAAQ;YAAU;YAAG,aAAa;;8BAChF,6LAAC,uLAAA,CAAA,OAAI,CAAC,MAAM;oBAAC,OAAO;wBAAE,SAAS;wBAAQ,SAAS;wBAAQ,gBAAgB;oBAAgB;;sCACpF,6LAAC;4BAAI,WAAU;;8CACX,6LAAC,gIAAA,CAAA,UAAK;oCACF,aAAY;oCACZ,KAAK,oHAAA,CAAA,UAAG,CAAC,eAAe,CAAC,MAAM,IAAI,CAAC,IAAI,KAAK;oCAC7C,QAAO;oCACP,OAAM;oCACN,KAAI;oCACJ,OAAO;wCAAE,aAAa;oCAAM;oCAC5B,SAAQ;;;;;;8CAEZ,6LAAC;oCAAK,OAAO,CAAA,GAAA,sHAAA,CAAA,kBAAe,AAAD,EAAE,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI;8CAAI;;;;;;;;;;;;wBAExD,QAAQ,CAAA,GAAA,sHAAA,CAAA,6BAA0B,AAAD,EAAE,KAAK,CAAC,EAAE,IAAI;wBAC/C,MAAM,IAAI,CAAC,GAAG,iBACX,6LAAC,yLAAA,CAAA,QAAK;4BAAC,OAAO;gCAAE,YAAY;4BAAM;4BAAG,IAAG;sCAAU;;;;;mCAIlD;wBAEH,MAAM,IAAI,CAAC,IAAI,iBACZ,6LAAC,yLAAA,CAAA,QAAK;4BAAC,OAAO;gCAAE,YAAY;4BAAM;4BAAG,IAAG;sCAAS;;;;;mCAIjD;;;;;;;8BAGR,6LAAC,uLAAA,CAAA,OAAI,CAAC,IAAI;oBAAC,OAAO;wBAAE,SAAS;wBAAQ,QAAQ;oBAAU;oBAAG,aAAa;;wBAClE,SAAS,QAAQ,GAAG,qBACjB,6LAAC;;8CACG,6LAAC;8CAAK;;;;;;8CACN,6LAAC;;;;;8CACD,6LAAC;oCAAE,wBAAwB;oCAAC,OAAO;wCAAE,OAAO;oCAAM;;wCAAI,aAAa,MAAM,IAAI,CAAC,IAAI;wCAAE;;;;;;;;;;;;;wBAG3F,SAAS,eAAe,GAAG,qBACxB,6LAAC;;8CACG,6LAAC;8CAAK;;;;;;8CACN,6LAAC;;;;;8CACD,6LAAC;oCAAE,wBAAwB;;wCAAE,aAAa,MAAM,IAAI,CAAC,MAAM,KAAK,IAAI,MAAM,IAAI,CAAC,SAAS,GAAG,MAAM,IAAI,CAAC,MAAM;wCAAE;;;;;;;;;;;;;wBAGrH,SAAS,mBAAmB,GAAG,qBAC5B,6LAAC;;8CACG,6LAAC;8CAAK;;;;;;8CACN,6LAAC;;;;;gCACA,iBAAiB,MAAM,IAAI;8CAC5B,6LAAC;oCAAK,OAAO;wCAAE,OAAO;oCAAQ;8CAC1B,cAAA,6LAAC;wCAAK,aAAa;kDACf,cAAA,6LAAC,4JAAA,CAAA,UAAQ;;;;;;;;;;;;;;;;;;;;;wBAKxB,SAAS,aAAa,IAAI,SAAS,UAAU,IAAI,SAAS,mBAAmB,GAAG,qBAAO,6LAAC;;;;;wBACxF,SAAS,aAAa,GAAG,qBACtB,6LAAC;;8CACG,6LAAC;8CAAK;;;;;;8CACN,6LAAC;;;;;gCACA,CAAC,SAAS,YAAY,iBACnB,6LAAC;oCAAE,KAAI;oCAAa,QAAO;oCAAS,aAAa;oCAAsB,MAAM,iBAAiB,MAAM,IAAI,CAAC,IAAI,CAAC,GAAG;;wCAC5G,aAAa,MAAM,IAAI,CAAC,SAAS;wCAAE;;;;;;yDAGxC,6LAAC;;wCAAM,aAAa,MAAM,IAAI,CAAC,SAAS;wCAAE;;;;;;;;;;;;;wBAIrD,SAAS,mBAAmB,GAAG,qBAC5B,6LAAC;;8CACG,6LAAC;8CAAK;;;;;;8CACN,6LAAC;;;;;gCACA,CAAC,SAAS,YAAY,iBACnB,6LAAC;oCAAE,KAAI;oCAAa,QAAO;oCAAS,aAAa;oCAAsB,MAAM,iBAAiB,MAAM,IAAI,CAAC,IAAI,CAAC,GAAG;;wCAC5G,aAAa,MAAM,IAAI,CAAC,eAAe;wCAAE;;;;;;yDAG9C,6LAAC;;wCAAM,aAAa,MAAM,IAAI,CAAC,eAAe;wCAAE;;;;;;;;;;;;;wBAI3D,SAAS,UAAU,GAAG,qBACnB,6LAAC;;8CACG,6LAAC;8CAAK;;;;;;8CACN,6LAAC;;;;;gCACA,CAAC,SAAS,YAAY,iBACnB,6LAAC;oCAAE,KAAI;oCAAa,QAAO;oCAAS,aAAa;oCAAmB,MAAM,aAAa,MAAM,IAAI,CAAC,UAAU;8CACxG,cAAA,6LAAC;kDAAG,MAAM,IAAI,CAAC,UAAU;;;;;;;;;;yDAG7B,6LAAC;8CACG,cAAA,6LAAC;kDAAG,MAAM,IAAI,CAAC,UAAU;;;;;;;;;;;;;;;;;wBAKxC,MAAM,IAAI,CAAC,KAAK,IAAI,MAAM,IAAI,CAAC,KAAK,EAAE,SAAS,KAAK,SAAS,kBAAkB,GAAI,kBAChF,6LAAC;;8CACG,6LAAC;;;;;8CACD,6LAAC;8CACI,MAAM,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM;wCAC1B,IAAI,KAAK,SAAS,kBAAkB,EAAG;4CACnC,OAAO;wCACX,OAAO;4CACH,qBAAO,6LAAC;0DAAY,CAAA,GAAA,sHAAA,CAAA,gCAA6B,AAAD,EAAE;+CAAlC;;;;;wCACpB;oCACJ;;;;;;;;;;;mCAIR;sCAEJ,6LAAC;;;;;sCACD,6LAAC;4BAAI,WAAW,oJAAA,CAAA,UAAM,CAAC,IAAI;;gCACtB,SAAS,UAAU,GAAG,qBACnB,6LAAC;oCAAI,WAAW,oJAAA,CAAA,UAAM,CAAC,OAAO;;sDAC1B,6LAAC;sDAAK;;;;;;wCACL,MAAM,IAAI,CAAC,MAAM,GAAG,KAAK,QAAQ,MAAM,KAAK,KAAK,CAAC,MAAM,IAAI,CAAC,MAAM,GAAG,MAAM;wCAAG;;;;;;;gCAGvF,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE;oCAAC,MAAM,IAAI,CAAC,MAAM;iCAAC,EAAE,GAAG,CAAC,CAAA;oCACrC,qBACI,6LAAC,yLAAA,CAAA,QAAK;wCAAyB,IAAG;kDAC7B,OAAO,UAAU;uCADV,OAAO,UAAU;;;;;gCAIrC;;;;;;;sCAEJ,6LAAC,0IAAA,CAAA,aAAU;4BAAC,eAAe,MAAM,IAAI,CAAC,QAAQ;4BAAE,aAAa,oJAAA,CAAA,UAAM,CAAC,qBAAqB;;;;;;;;;;;;;;;;;;OApI3F,MAAM,IAAI,CAAC,IAAI;;;;;AAyIjC;GAtOS;;QAEa,kHAAA,CAAA,iBAAc;QACX,sNAAA,CAAA,YAAS;;;KAHzB;uCAwOM", "debugId": null}}, {"offset": {"line": 955, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/components/Flipper/Flipper.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"SSRcardsWrapper\": \"Flipper-module__H9tceq__SSRcardsWrapper\",\n  \"cardWrapper\": \"Flipper-module__H9tceq__cardWrapper\",\n  \"flipAuctionCard\": \"Flipper-module__H9tceq__flipAuctionCard\",\n  \"flipCustomizeModal\": \"Flipper-module__H9tceq__flipCustomizeModal\",\n  \"flipper\": \"Flipper-module__H9tceq__flipper\",\n  \"flipperScrollList\": \"Flipper-module__H9tceq__flipperScrollList\",\n  \"flipperSettingsForm\": \"Flipper-module__H9tceq__flipperSettingsForm\",\n  \"flipperSummaryCard\": \"Flipper-module__H9tceq__flipperSummaryCard\",\n  \"flipperSummaryWrapper\": \"Flipper-module__H9tceq__flipperSummaryWrapper\",\n  \"formCheckInput\": \"Flipper-module__H9tceq__formCheckInput\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 973, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/Flipper/FlipperFAQ/FlipperFAQ.tsx"], "sourcesContent": ["'use client'\nimport { useEffect } from 'react'\nimport { Card } from 'react-bootstrap'\n\nfunction FlipperFAQ() {\n    useEffect(() => {\n        if (!location.hash) {\n            return\n        }\n        let element = document.getElementById(location.hash.replace('#', ''))\n        if (element) {\n            setTimeout(() => {\n                window.scrollTo({\n                    top: element!.offsetTop - 50\n                })\n            }, 200)\n        }\n    }, [])\n\n    return (\n        <div id=\"faq\">\n            <Card>\n                <Card.Header>\n                    <Card.Title>\n                        <h2>FAQ</h2>\n                    </Card.Title>\n                </Card.Header>\n                <Card.Body>\n                    <h3>How are profitable flips found?</h3>\n                    <p>\n                        New flips are found by comparing every new auction with the sell price of already finished auctions of the same item with the same or\n                        similar modifiers (e.g. enchantments) and/or comparing to lowest bin.\n                    </p>\n                    <h3>What auctions are new auctions compared with?</h3>\n                    <p>\n                        Reference auctions depend on the individual item, its modifiers, and how often it is sold. The algorithm to determine which auctions can\n                        be used as reference is changing frequently.\n                        <br />\n                        You can see the auctions used for reference by clicking on the (?) next to <code>Estimated Profit</code>.\n                    </p>\n                    <h3>How reliable is the flipper?</h3>\n                    <p>\n                        Statistically very reliable. Still, some flips might not sell as fast as others or at all. If you encounter a flip that can not be sold,\n                        please post a link to it in the skyblock channel on our discord so we can improve the flipper further.\n                    </p>\n                    <h3>Why is there a \"premium\" version?</h3>\n                    <p>\n                        TLDR: Servercosts and compensation for development.\n                        <br />\n                        To run the flipper and the auction explorer we have to pay for servers to run it on. While we work hard to keep the cost down they are\n                        currently up to about 503$ per month. And will increase further the more auctions we save and the the more users are using the site.\n                        Furthermore we collectively spent more than 4000 hours of our spare time developing it and would like to get a some compensation for our\n                        efforts. The best case would be to develop this and similar projects full time if we could.\n                    </p>\n                    <h3>What can the free version do?</h3>\n                    <p>\n                        The free version of the auction flipper can be used if you just got started with ah-flipping. It displays flips with a delay and has\n                        some features deactivated. Other than that, there are no limitations. <b>No cap on profit</b>, no need to do anything. (although we\n                        would appreciate it, if you support us, either with feedback or money) The more users we have the more feedback we can get and the\n                        better the flips will become.\n                    </p>\n                    <h3>What do I get if I buy premium?</h3>\n                    <p>\n                        You get flips as soon as they are found. That allows you to buy up the most profitable flips before anyone else. Furthermore you get\n                        more filter options. Which allow you to only see flips that you are actually interested in. For a full list see{' '}\n                        <a target=\"_blank\" href=\"/premium\" rel=\"noreferrer\">\n                            the premium page\n                        </a>\n                    </p>\n                    <h3>What do these labels mean?</h3>\n                    <h4>Cost</h4>\n                    <p>Cost is the auction price that you would have to pay. </p>\n                    <h4>Median Price</h4>\n                    <p>\n                        Median Price is the median price for that item. Taking into account ultimate enchantments, valuable enchantments (eg. snipe 4), price\n                        paid at dark auctions, Pet level, Pet item, Reforges, Cake years, Kill counts, Rarity and stars. (basically everything that could change\n                        the price)\n                    </p>\n                    <h4>Volume</h4>\n                    <p>Volume is the number of auctions that were sold in a 24 hour window. It is capped at 60 to keep the flipper fast.</p>\n                    <h4>Lowest bin</h4>\n                    <p>\n                        The lowest bin gives you an indication how much this item type is worth. It displays the lowest price for a given item type and ignores\n                        modifiers. You can click it.\n                    </p>\n                    <h3>Should I flip an item with low volume?</h3>\n                    <p>\n                        If you have to ask this question, the answer probably no. Low volume items require some user expertise to determine if the item actually\n                        is a good flip or not. However since its sold so infrequently it may be a niche item that has a higher profit margin.\n                    </p>\n                    <h3>I have another question/ Bug report</h3> Ask via{' '}\n                    <a target=\"_blank\" rel=\"noreferrer\" href=\"https://discord.gg/wvKXfTgCfb\">\n                        discord\n                    </a>{' '}\n                    or{' '}\n                    <a target=\"_blank\" href=\"/feedback\" rel=\"noreferrer\">\n                        feedback site\n                    </a>\n                </Card.Body>\n            </Card>\n        </div>\n    )\n}\n\nexport default FlipperFAQ\n"], "names": [], "mappings": ";;;;AACA;AACA;;;AAFA;;;AAIA,SAAS;;IACL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACN,IAAI,CAAC,SAAS,IAAI,EAAE;gBAChB;YACJ;YACA,IAAI,UAAU,SAAS,cAAc,CAAC,SAAS,IAAI,CAAC,OAAO,CAAC,KAAK;YACjE,IAAI,SAAS;gBACT;4CAAW;wBACP,OAAO,QAAQ,CAAC;4BACZ,KAAK,QAAS,SAAS,GAAG;wBAC9B;oBACJ;2CAAG;YACP;QACJ;+BAAG,EAAE;IAEL,qBACI,6LAAC;QAAI,IAAG;kBACJ,cAAA,6LAAC,uLAAA,CAAA,OAAI;;8BACD,6LAAC,uLAAA,CAAA,OAAI,CAAC,MAAM;8BACR,cAAA,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;kCACP,cAAA,6LAAC;sCAAG;;;;;;;;;;;;;;;;8BAGZ,6LAAC,uLAAA,CAAA,OAAI,CAAC,IAAI;;sCACN,6LAAC;sCAAG;;;;;;sCACJ,6LAAC;sCAAE;;;;;;sCAIH,6LAAC;sCAAG;;;;;;sCACJ,6LAAC;;gCAAE;8CAGC,6LAAC;;;;;gCAAK;8CACqE,6LAAC;8CAAK;;;;;;gCAAuB;;;;;;;sCAE5G,6LAAC;sCAAG;;;;;;sCACJ,6LAAC;sCAAE;;;;;;sCAIH,6LAAC;sCAAG;;;;;;sCACJ,6LAAC;;gCAAE;8CAEC,6LAAC;;;;;gCAAK;;;;;;;sCAMV,6LAAC;sCAAG;;;;;;sCACJ,6LAAC;;gCAAE;8CAEuE,6LAAC;8CAAE;;;;;;gCAAoB;;;;;;;sCAIjG,6LAAC;sCAAG;;;;;;sCACJ,6LAAC;;gCAAE;gCAEiH;8CAChH,6LAAC;oCAAE,QAAO;oCAAS,MAAK;oCAAW,KAAI;8CAAa;;;;;;;;;;;;sCAIxD,6LAAC;sCAAG;;;;;;sCACJ,6LAAC;sCAAG;;;;;;sCACJ,6LAAC;sCAAE;;;;;;sCACH,6LAAC;sCAAG;;;;;;sCACJ,6LAAC;sCAAE;;;;;;sCAKH,6LAAC;sCAAG;;;;;;sCACJ,6LAAC;sCAAE;;;;;;sCACH,6LAAC;sCAAG;;;;;;sCACJ,6LAAC;sCAAE;;;;;;sCAIH,6LAAC;sCAAG;;;;;;sCACJ,6LAAC;sCAAE;;;;;;sCAIH,6LAAC;sCAAG;;;;;;wBAAwC;wBAAS;sCACrD,6LAAC;4BAAE,QAAO;4BAAS,KAAI;4BAAa,MAAK;sCAAgC;;;;;;wBAEpE;wBAAI;wBACN;sCACH,6LAAC;4BAAE,QAAO;4BAAS,MAAK;4BAAY,KAAI;sCAAa;;;;;;;;;;;;;;;;;;;;;;;AAOzE;GAlGS;KAAA;uCAoGM", "debugId": null}}, {"offset": {"line": 1299, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/components/Flipper/FlipCustomize/FormatElement/FormatElement.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"label\": \"FormatElement-module__Rsw40W__label\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA"}}, {"offset": {"line": 1308, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/Flipper/FlipCustomize/FormatElement/FormatElement.tsx"], "sourcesContent": ["'use client'\nimport { useMatomo } from '@jonkoops/matomo-tracker-react'\nimport { ChangeEvent, useRef, useState } from 'react'\nimport { Button, Form } from 'react-bootstrap'\nimport { DEFAULT_MOD_FORMAT } from '../../../../utils/FlipUtils'\nimport Tooltip from '../../../Tooltip/Tooltip'\nimport HelpIcon from '@mui/icons-material/Help'\nimport RefreshIcon from '@mui/icons-material/Refresh'\nimport { useForceUpdate } from '../../../../utils/Hooks'\nimport styles from './FormatElement.module.css'\nimport { getMinecraftColorCodedElement } from '../../../../utils/Formatter'\n\ninterface Props {\n    onChange(value: string)\n    settings: FlipCustomizeSettings\n    labelClass?: string\n}\n\nfunction FormatElement(props: Props) {\n    let formatInputRef = useRef(null)\n    let [isChecked, setIsChecked] = useState<boolean>(!!props.settings.modFormat)\n\n    let { trackEvent } = useMatomo()\n\n    function onChange(value: string) {\n        props.onChange(value)\n    }\n\n    function onModDefaultFormatCheckboxChange(event) {\n        if (event.target.checked) {\n            setDefaultModFormat()\n            setIsChecked(true)\n        } else {\n            setIsChecked(false)\n            onChange('')\n        }\n    }\n\n    function setDefaultModFormat() {\n        if (formatInputRef.current) {\n            ;(formatInputRef.current! as HTMLInputElement).value = DEFAULT_MOD_FORMAT\n        }\n        onChange(DEFAULT_MOD_FORMAT)\n        trackEvent({\n            category: 'customizeFlipStyle',\n            action: 'modFormat: default'\n        })\n    }\n\n    function onInputChange(e: ChangeEvent<HTMLInputElement>) {\n        onChange(e.target.value)\n    }\n\n    function getFormatExampleText() {\n        let settings = props.settings\n        if (!settings.modFormat) {\n            return ''\n        }\n\n        var values = {\n            '{0}': 'FLIP',\n            '{1}': '§2',\n            '{2}': 'Armadillo',\n            '{3}': '§1',\n            '{4}': settings.shortNumbers ? '1.49M' : '1.490.000',\n            '{5}': settings.shortNumbers ? '2M' : '2.000.000',\n            '{6}': settings.shortNumbers ? '470k' : '470.000',\n            '{7}': '31%',\n            '{8}': settings.shortNumbers ? '2M' : '2.000.000',\n            '{9}': settings.shortNumbers ? '1M' : '1.000.000',\n            '{10}': '26',\n            '[menu]': '§f✥',\n            '[sellerbtn]': '§7sellers ah'\n        }\n\n        let result = settings.modFormat\n        Object.keys(values).forEach(key => {\n            result = result.replaceAll(key, values[key])\n        })\n\n        return result\n    }\n\n    const formatHelpTooltip = (\n        <div>\n            <p>The format, the mod displays messages. The followig symbols are replaced by the actual values:</p>\n            <ul>\n                <li>&#123;0&#125; FlipFinder</li>\n                <li>&#123;1&#125; Item Rarity Color</li>\n                <li>&#123;2&#125; Item Name</li>\n                <li>&#123;3&#125; Price color</li>\n                <li>&#123;4&#125; Starting bid</li>\n                <li>&#123;5&#125; Target Price</li>\n                <li>&#123;6&#125; Estimated Profit</li>\n                <li>&#123;7&#125; Profit percentage</li>\n                <li>&#123;8&#125; Median Price</li>\n                <li>&#123;9&#125; Lowest Bin</li>\n                <li>&#123;10&#125; Volume</li>\n            </ul>\n            <p>It uses the default format if unchecked.</p>\n        </div>\n    )\n\n    return (\n        <div>\n            <label htmlFor=\"modFormat\" className={styles.label}>\n                Custom format\n                <Tooltip type=\"hover\" content={<HelpIcon style={{ color: '#007bff', cursor: 'pointer' }} />} tooltipContent={formatHelpTooltip} />\n            </label>\n            <Form.Check onChange={onModDefaultFormatCheckboxChange} defaultChecked={isChecked} id=\"modFormat\" style={{ display: 'inline' }} type=\"checkbox\" />\n            {isChecked ? (\n                <div>\n                    <div style={{ display: 'flex' }}>\n                        <Form.Control\n                            as=\"textarea\"\n                            ref={formatInputRef}\n                            style={{ width: '100%' }}\n                            onChange={onInputChange}\n                            defaultValue={props.settings.modFormat}\n                        />\n                        <Button style={{ whiteSpace: 'nowrap' }} onClick={setDefaultModFormat}>\n                            <RefreshIcon />\n                            Default\n                        </Button>\n                    </div>\n                    <p>{getMinecraftColorCodedElement(getFormatExampleText(), false)}</p>\n                </div>\n            ) : null}\n        </div>\n    )\n}\n\nexport default FormatElement\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAEA;AACA;;;AAVA;;;;;;;;;;AAkBA,SAAS,cAAc,KAAY;;IAC/B,IAAI,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC5B,IAAI,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW,CAAC,CAAC,MAAM,QAAQ,CAAC,SAAS;IAE5E,IAAI,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,sNAAA,CAAA,YAAS,AAAD;IAE7B,SAAS,SAAS,KAAa;QAC3B,MAAM,QAAQ,CAAC;IACnB;IAEA,SAAS,iCAAiC,KAAK;QAC3C,IAAI,MAAM,MAAM,CAAC,OAAO,EAAE;YACtB;YACA,aAAa;QACjB,OAAO;YACH,aAAa;YACb,SAAS;QACb;IACJ;IAEA,SAAS;QACL,IAAI,eAAe,OAAO,EAAE;;YACtB,eAAe,OAAO,CAAuB,KAAK,GAAG,sHAAA,CAAA,qBAAkB;QAC7E;QACA,SAAS,sHAAA,CAAA,qBAAkB;QAC3B,WAAW;YACP,UAAU;YACV,QAAQ;QACZ;IACJ;IAEA,SAAS,cAAc,CAAgC;QACnD,SAAS,EAAE,MAAM,CAAC,KAAK;IAC3B;IAEA,SAAS;QACL,IAAI,WAAW,MAAM,QAAQ;QAC7B,IAAI,CAAC,SAAS,SAAS,EAAE;YACrB,OAAO;QACX;QAEA,IAAI,SAAS;YACT,OAAO;YACP,OAAO;YACP,OAAO;YACP,OAAO;YACP,OAAO,SAAS,YAAY,GAAG,UAAU;YACzC,OAAO,SAAS,YAAY,GAAG,OAAO;YACtC,OAAO,SAAS,YAAY,GAAG,SAAS;YACxC,OAAO;YACP,OAAO,SAAS,YAAY,GAAG,OAAO;YACtC,OAAO,SAAS,YAAY,GAAG,OAAO;YACtC,QAAQ;YACR,UAAU;YACV,eAAe;QACnB;QAEA,IAAI,SAAS,SAAS,SAAS;QAC/B,OAAO,IAAI,CAAC,QAAQ,OAAO,CAAC,CAAA;YACxB,SAAS,OAAO,UAAU,CAAC,KAAK,MAAM,CAAC,IAAI;QAC/C;QAEA,OAAO;IACX;IAEA,MAAM,kCACF,6LAAC;;0BACG,6LAAC;0BAAE;;;;;;0BACH,6LAAC;;kCACG,6LAAC;kCAAG;;;;;;kCACJ,6LAAC;kCAAG;;;;;;kCACJ,6LAAC;kCAAG;;;;;;kCACJ,6LAAC;kCAAG;;;;;;kCACJ,6LAAC;kCAAG;;;;;;kCACJ,6LAAC;kCAAG;;;;;;kCACJ,6LAAC;kCAAG;;;;;;kCACJ,6LAAC;kCAAG;;;;;;kCACJ,6LAAC;kCAAG;;;;;;kCACJ,6LAAC;kCAAG;;;;;;kCACJ,6LAAC;kCAAG;;;;;;;;;;;;0BAER,6LAAC;0BAAE;;;;;;;;;;;;IAIX,qBACI,6LAAC;;0BACG,6LAAC;gBAAM,SAAQ;gBAAY,WAAW,uLAAA,CAAA,UAAM,CAAC,KAAK;;oBAAE;kCAEhD,6LAAC,oIAAA,CAAA,UAAO;wBAAC,MAAK;wBAAQ,uBAAS,6LAAC,4JAAA,CAAA,UAAQ;4BAAC,OAAO;gCAAE,OAAO;gCAAW,QAAQ;4BAAU;;;;;;wBAAO,gBAAgB;;;;;;;;;;;;0BAEjH,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;gBAAC,UAAU;gBAAkC,gBAAgB;gBAAW,IAAG;gBAAY,OAAO;oBAAE,SAAS;gBAAS;gBAAG,MAAK;;;;;;YACpI,0BACG,6LAAC;;kCACG,6LAAC;wBAAI,OAAO;4BAAE,SAAS;wBAAO;;0CAC1B,6LAAC,uLAAA,CAAA,OAAI,CAAC,OAAO;gCACT,IAAG;gCACH,KAAK;gCACL,OAAO;oCAAE,OAAO;gCAAO;gCACvB,UAAU;gCACV,cAAc,MAAM,QAAQ,CAAC,SAAS;;;;;;0CAE1C,6LAAC,2LAAA,CAAA,SAAM;gCAAC,OAAO;oCAAE,YAAY;gCAAS;gCAAG,SAAS;;kDAC9C,6LAAC,+JAAA,CAAA,UAAW;;;;;oCAAG;;;;;;;;;;;;;kCAIvB,6LAAC;kCAAG,CAAA,GAAA,sHAAA,CAAA,gCAA6B,AAAD,EAAE,wBAAwB;;;;;;;;;;;uBAE9D;;;;;;;AAGhB;GAhHS;;QAIgB,sNAAA,CAAA,YAAS;;;KAJzB;uCAkHM", "debugId": null}}, {"offset": {"line": 1623, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/components/Flipper/FlipCustomize/FlipCustomize.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"flip-finder-tooltip\": \"FlipCustomize-module__0zR3nq__flip-finder-tooltip\",\n  \"flipCustomize\": \"FlipCustomize-module__0zR3nq__flipCustomize\",\n  \"label\": \"FlipCustomize-module__0zR3nq__label\",\n  \"section\": \"FlipCustomize-module__0zR3nq__section\",\n  \"sectionLeft\": \"FlipCustomize-module__0zR3nq__sectionLeft\",\n  \"sectionRight\": \"FlipCustomize-module__0zR3nq__sectionRight\",\n  \"tooltip-inner\": \"FlipCustomize-module__0zR3nq__tooltip-inner\",\n  \"verticalLine\": \"FlipCustomize-module__0zR3nq__verticalLine\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 1639, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/Flipper/FlipCustomize/PublishedConfigs/PublishedConfigs.tsx"], "sourcesContent": ["import { useEffect, useState } from 'react'\nimport api from '../../../../api/ApiHelper'\nimport { Button, Form, ListGroup, ListGroupItem } from 'react-bootstrap'\nimport { toast } from 'react-toastify'\n\nconst PublishedConfigs = () => {\n    const [configs, setConfigs] = useState<string[]>([])\n    const [changeNotes, setChangeNotes] = useState<{ config: string; changeNotes: string }[]>([])\n\n    useEffect(() => {\n        loadConfigs()\n    }, [])\n\n    async function loadConfigs() {\n        const configs = await api.getPublishedConfigs()\n        setConfigs(configs)\n    }\n\n    async function updateConfig(configName: string, updateNotes: string = '') {\n        try {\n            await api.updateConfig(configName, updateNotes)\n            toast.success(`Updated config`)\n        } catch (e) {\n            toast.error(`Failed to update config`)\n        }\n    }\n\n    async function loadConfig(configName: string) {\n        try {\n            await api.loadConfig(configName)\n            window.location.reload()\n        } catch (e) {\n            toast.error(`Failed to load config`)\n        }\n    }\n\n    if (!configs || configs.length === 0) {\n        return <></>\n    }\n\n    return (\n        <>\n            <h5>Update published configs</h5>\n            <ListGroup>\n                {configs.map((configName, i) => (\n                    <ListGroupItem key={i} style={{ display: 'flex', gap: 15 }}>\n                        <span style={{ flex: 1 }}>{configName}</span>\n                        <Form.Control\n                            style={{ flex: 2 }}\n                            type=\"text\"\n                            placeholder=\"Change notes\"\n                            onChange={event => {\n                                let newChangeNotes = [...changeNotes]\n                                let index = newChangeNotes.findIndex(changeNote => changeNote.config === configName)\n\n                                if (index === -1) {\n                                    newChangeNotes.push({ config: configName, changeNotes: event.target.value })\n                                } else {\n                                    newChangeNotes[index].changeNotes = event.target.value\n                                }\n\n                                setChangeNotes(newChangeNotes)\n                            }}\n                        />\n                        <Button\n                            variant=\"success\"\n                            onClick={() => {\n                                updateConfig(configName, changeNotes.find(changeNote => changeNote.config === configName)?.changeNotes)\n                            }}\n                        >\n                            Update\n                        </Button>\n                        <Button\n                            onClick={() => {\n                                loadConfig(configName)\n                            }}\n                        >\n                            Load config\n                        </Button>\n                    </ListGroupItem>\n                ))}\n            </ListGroup>\n        </>\n    )\n}\n\nexport default PublishedConfigs\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AACA;;;;;;;AAEA,MAAM,mBAAmB;;IACrB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA6C,EAAE;IAE5F,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACN;QACJ;qCAAG,EAAE;IAEL,eAAe;QACX,MAAM,UAAU,MAAM,oHAAA,CAAA,UAAG,CAAC,mBAAmB;QAC7C,WAAW;IACf;IAEA,eAAe,aAAa,UAAkB,EAAE,cAAsB,EAAE;QACpE,IAAI;YACA,MAAM,oHAAA,CAAA,UAAG,CAAC,YAAY,CAAC,YAAY;YACnC,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,cAAc,CAAC;QAClC,EAAE,OAAO,GAAG;YACR,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,uBAAuB,CAAC;QACzC;IACJ;IAEA,eAAe,WAAW,UAAkB;QACxC,IAAI;YACA,MAAM,oHAAA,CAAA,UAAG,CAAC,UAAU,CAAC;YACrB,OAAO,QAAQ,CAAC,MAAM;QAC1B,EAAE,OAAO,GAAG;YACR,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,qBAAqB,CAAC;QACvC;IACJ;IAEA,IAAI,CAAC,WAAW,QAAQ,MAAM,KAAK,GAAG;QAClC,qBAAO;IACX;IAEA,qBACI;;0BACI,6LAAC;0BAAG;;;;;;0BACJ,6LAAC,iMAAA,CAAA,YAAS;0BACL,QAAQ,GAAG,CAAC,CAAC,YAAY,kBACtB,6LAAC,yMAAA,CAAA,gBAAa;wBAAS,OAAO;4BAAE,SAAS;4BAAQ,KAAK;wBAAG;;0CACrD,6LAAC;gCAAK,OAAO;oCAAE,MAAM;gCAAE;0CAAI;;;;;;0CAC3B,6LAAC,uLAAA,CAAA,OAAI,CAAC,OAAO;gCACT,OAAO;oCAAE,MAAM;gCAAE;gCACjB,MAAK;gCACL,aAAY;gCACZ,UAAU,CAAA;oCACN,IAAI,iBAAiB;2CAAI;qCAAY;oCACrC,IAAI,QAAQ,eAAe,SAAS,CAAC,CAAA,aAAc,WAAW,MAAM,KAAK;oCAEzE,IAAI,UAAU,CAAC,GAAG;wCACd,eAAe,IAAI,CAAC;4CAAE,QAAQ;4CAAY,aAAa,MAAM,MAAM,CAAC,KAAK;wCAAC;oCAC9E,OAAO;wCACH,cAAc,CAAC,MAAM,CAAC,WAAW,GAAG,MAAM,MAAM,CAAC,KAAK;oCAC1D;oCAEA,eAAe;gCACnB;;;;;;0CAEJ,6LAAC,2LAAA,CAAA,SAAM;gCACH,SAAQ;gCACR,SAAS;oCACL,aAAa,YAAY,YAAY,IAAI,CAAC,CAAA,aAAc,WAAW,MAAM,KAAK,aAAa;gCAC/F;0CACH;;;;;;0CAGD,6LAAC,2LAAA,CAAA,SAAM;gCACH,SAAS;oCACL,WAAW;gCACf;0CACH;;;;;;;uBA/Be;;;;;;;;;;;;AAuCxC;GA/EM;KAAA;uCAiFS", "debugId": null}}, {"offset": {"line": 1789, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/Flipper/FlipCustomize/FlipCustomize.tsx"], "sourcesContent": ["'use client'\nimport { use<PERSON>ato<PERSON> } from '@jonkoops/matomo-tracker-react'\nimport React, { ChangeEvent, useEffect, useState, type JSX } from 'react';\nimport { But<PERSON>, Form } from 'react-bootstrap'\nimport { DEMO_FLIP, FLIP_FINDERS, getFlipFinders, getFlipCustomizeSettings } from '../../../utils/FlipUtils'\nimport {\n    FLIPPER_FILTER_KEY,\n    FLIP_CUSTOMIZING_KEY,\n    getSettingsObject,\n    handleSettingsImport,\n    mapSettingsToApiFormat,\n    RESTRICTIONS_SETTINGS_KEY,\n    setSetting\n} from '../../../utils/SettingsUtils'\nimport Tooltip from '../../Tooltip/Tooltip'\nimport Flip from '../Flip/Flip'\nimport HelpIcon from '@mui/icons-material/Help'\nimport Select, { components } from 'react-select'\nimport FormatElement from './FormatElement/FormatElement'\nimport styles from './FlipCustomize.module.css'\nimport api from '../../../api/ApiHelper'\nimport { CUSTOM_EVENTS } from '../../../api/ApiTypes.d'\nimport PublishedConfigs from './PublishedConfigs/PublishedConfigs'\n\nconst customSelectStyle = {\n    option: provided => ({\n        ...provided,\n        color: 'black'\n    })\n}\n\nfunction FlipCustomize() {\n    let [flipCustomizeSettings, setFlipCustomizeSettings] = useState<FlipCustomizeSettings>({})\n    let [isExportDisabled, setIsExportDisabled] = useState(false)\n    let { trackEvent } = useMatomo()\n\n    useEffect(() => {\n        let settings = getFlipCustomizeSettings()\n        setIsExportDisabled(settings.blockExport === true)\n        setFlipCustomizeSettings({ ...settings })\n        loadPublishedConfigs()\n    }, [])\n\n    async function loadPublishedConfigs() {\n        let configs = await api.getPublishedConfigs()\n    }\n\n    function setFlipCustomizeSetting(key: string, value: any) {\n        flipCustomizeSettings[key] = value\n        setFlipCustomizeSettings(flipCustomizeSettings)\n        setSetting(FLIP_CUSTOMIZING_KEY, JSON.stringify(flipCustomizeSettings))\n        document.dispatchEvent(new CustomEvent(CUSTOM_EVENTS.FLIP_SETTINGS_CHANGE))\n        setFlipCustomizeSettings({ ...flipCustomizeSettings })\n    }\n\n    function updateApiSetting(key: string, value: boolean) {\n        api.setFlipSetting(key, value)\n        trackChange(key)\n    }\n\n    function onMaxExtraInfoFieldsChange(event: ChangeEvent<HTMLInputElement>) {\n        setFlipCustomizeSetting('maxExtraInfoFields', event.target.valueAsNumber)\n        api.setFlipSetting('showExtraFields', event.target.valueAsNumber || 0)\n        trackChange('maxExtraInfoFields')\n    }\n\n    function onFindersChange(newValue) {\n        setFlipCustomizeSetting(\n            'finders',\n            newValue.map(value => value.value)\n        )\n        api.setFlipSetting(\n            'finders',\n            flipCustomizeSettings.finders?.reduce((a, b) => +a + +b, 0)\n        )\n        trackChange('finders')\n    }\n\n    function onModFormatChange(value: string) {\n        setFlipCustomizeSetting('modFormat', value)\n        api.setFlipSetting('modFormat', value.replaceAll('§', '$'))\n        trackChange('modFormat')\n    }\n\n    function trackChange(property: string) {\n        trackEvent({\n            category: 'customizeFlipStyle',\n            action: property + ': ' + flipCustomizeSettings[property]\n        })\n    }\n\n    function download(filename, text) {\n        var element = document.createElement('a')\n        element.setAttribute('href', 'data:text/plain;charset=utf-8,' + encodeURIComponent(text))\n        element.setAttribute('download', filename)\n\n        element.style.display = 'none'\n        document.body.appendChild(element)\n\n        element.click()\n\n        document.body.removeChild(element)\n    }\n\n    function exportFilter() {\n        let toExport = mapSettingsToApiFormat(\n            getSettingsObject<FlipperFilter>(FLIPPER_FILTER_KEY, {}),\n            getSettingsObject<FlipCustomizeSettings>(FLIP_CUSTOMIZING_KEY, {}),\n            getSettingsObject<FlipRestriction[]>(RESTRICTIONS_SETTINGS_KEY, [])\n        )\n\n        download('filter.json', JSON.stringify(toExport))\n    }\n\n    function readImportFile(e) {\n        var output = '' //placeholder for text output\n        let reader = new FileReader()\n        if (e.target.files && e.target.files[0]) {\n            reader.onload = function (e) {\n                output = e.target!.result!.toString()\n                handleSettingsImport(output)\n            } //end onload()\n            reader.readAsText(e.target.files[0])\n        }\n        return true\n    }\n\n    function getFlipFinderWarningElement(): JSX.Element | null {\n        let warnings: string[] = []\n\n        let sniperFinder = FLIP_FINDERS.find(finder => finder.label === 'Sniper')\n        if (\n            sniperFinder &&\n            flipCustomizeSettings.useLowestBinForProfit &&\n            (!flipCustomizeSettings.finders ||\n                flipCustomizeSettings.finders.length === 0 ||\n                flipCustomizeSettings.finders.length > 1 ||\n                flipCustomizeSettings.finders[0].toString() !== sniperFinder.value)\n        ) {\n            warnings.push(\n                'Only use the \"Sniper\"-Finder with \"Use lbin to calculate profit option\". Using other finders may lead to muliple seconds of delay as this will require additional calculations.'\n            )\n        }\n\n        addWarningForFinder('TFM', warnings, 'The \"TFM\"-Finder is outdated and therefore considered risky. Only use if you know what you are doing.')\n        addWarningForFinder('Stonks', warnings, 'The \"Stonks\"-Finder is work in progress and therefore considered risky. Only use if you know what you are doing.')\n        addWarningForFinder('CraftCost', warnings, 'The \"CraftCost\"-Finder sums up craft cost. It does not mean its estimations are correct, please report any cases where you know they are not.')\n\n        if (warnings.length === 0) {\n            return null\n        }\n        if (warnings.length === 1) {\n            return (\n                <b>\n                    <p style={{ color: 'red' }}>{warnings[0]}</p>\n                </b>\n            )\n        }\n        return (\n            <ul style={{ color: 'red' }}>\n                {warnings.map(warning => (\n                    <li>{warning}</li>\n                ))}\n            </ul>\n        )\n    }\n\n    const useLowestBinHelpElement = (\n        <p>\n            By enabling this setting, the lowest BIN is used as the estimated selling price to calculate your profit. That can lead to profitable flips being\n            estimated way too low (even as a loss). It also add a little time to process the flips. We recommend using the median to calculate the profit.\n        </p>\n    )\n\n    const MultiValueContainer = props => {\n        return (\n            <components.MultiValueContainer {...props}>\n                <Tooltip\n                    type={'hover'}\n                    content={<span style={props.innerProps.css}>{props.children}</span>}\n                    tooltipContent={<span>{props.data.description}</span>}\n                />\n            </components.MultiValueContainer>\n        )\n    }\n\n    if (Object.keys(flipCustomizeSettings).length === 0) {\n        return <></>\n    }\n\n    return (\n        <div className={styles.flipCustomize}>\n            <div className={styles.sectionLeft}>\n                <Form className={styles.section}>\n                    <div>\n                        <Form.Group>\n                            <Form.Label className={styles.label} htmlFor=\"hideCost\">\n                                Cost\n                            </Form.Label>\n                            <Form.Check\n                                onChange={event => {\n                                    updateApiSetting('showCost', event.target.checked)\n                                    setFlipCustomizeSetting('hideCost', !event.target.checked)\n                                }}\n                                defaultChecked={!flipCustomizeSettings.hideCost}\n                                id=\"hideCost\"\n                                style={{ display: 'inline' }}\n                                type=\"checkbox\"\n                            />\n                        </Form.Group>\n                        <Form.Group>\n                            <Form.Label className={styles.label} htmlFor=\"hideEstimatedProfit\">\n                                Estimated Profit\n                            </Form.Label>\n                            <Form.Check\n                                onChange={event => {\n                                    updateApiSetting('showEstProfit', event.target.checked)\n                                    setFlipCustomizeSetting('hideEstimatedProfit', !event.target.checked)\n                                }}\n                                defaultChecked={!flipCustomizeSettings.hideEstimatedProfit}\n                                id=\"hideEstimatedProfit\"\n                                style={{ display: 'inline' }}\n                                type=\"checkbox\"\n                            />\n                        </Form.Group>\n                        <Form.Group>\n                            <Form.Label className={styles.label} htmlFor=\"hideSecondLowestBin\">\n                                Second lowest BIN\n                            </Form.Label>\n                            <Form.Check\n                                onChange={event => {\n                                    updateApiSetting('showSlbin', event.target.checked)\n                                    setFlipCustomizeSetting('hideSecondLowestBin', !event.target.checked)\n                                }}\n                                defaultChecked={!flipCustomizeSettings.hideSecondLowestBin}\n                                id=\"hideSecondLowestBin\"\n                                style={{ display: 'inline' }}\n                                type=\"checkbox\"\n                            />\n                        </Form.Group>\n                        <Form.Group>\n                            <Form.Label className={styles.label} htmlFor=\"hideVolume\">\n                                Volume\n                            </Form.Label>\n                            <Form.Check\n                                onChange={event => {\n                                    updateApiSetting('showVolume', event.target.checked)\n                                    setFlipCustomizeSetting('hideVolume', !event.target.checked)\n                                }}\n                                defaultChecked={!flipCustomizeSettings.hideVolume}\n                                id=\"hideVolume\"\n                                style={{ display: 'inline' }}\n                                type=\"checkbox\"\n                            />\n                        </Form.Group>\n                        <Form.Group>\n                            <Form.Label className={styles.label} htmlFor=\"shortNumbers\">\n                                Shorten numbers?\n                            </Form.Label>\n                            <Form.Check\n                                onChange={event => {\n                                    updateApiSetting('modShortNumbers', event.target.checked)\n                                    setFlipCustomizeSetting('shortNumbers', event.target.checked)\n                                }}\n                                defaultChecked={flipCustomizeSettings.shortNumbers}\n                                id=\"shortNumbers\"\n                                style={{ display: 'inline' }}\n                                type=\"checkbox\"\n                            />\n                        </Form.Group>\n                        <Form.Group>\n                            <Form.Label className={styles.label} htmlFor=\"hideProfitPercent\">\n                                Profit percent?\n                            </Form.Label>\n                            <Form.Check\n                                onChange={event => {\n                                    updateApiSetting('showProfitPercent', event.target.checked)\n                                    setFlipCustomizeSetting('hideProfitPercent', !event.target.checked)\n                                }}\n                                defaultChecked={!flipCustomizeSettings.hideProfitPercent}\n                                id=\"hideProfitPercent\"\n                                style={{ display: 'inline' }}\n                                type=\"checkbox\"\n                            />\n                        </Form.Group>\n                        <Form.Group>\n                            <Form.Label className={styles.label} htmlFor=\"hideMaxExtraInfo\">\n                                Max. extra info fields\n                            </Form.Label>\n                            <Form.Control\n                                min={0}\n                                max={30}\n                                onChange={onMaxExtraInfoFieldsChange}\n                                defaultValue={flipCustomizeSettings.maxExtraInfoFields}\n                                type=\"number\"\n                                id=\"hideMaxExtraInfo\"\n                            />\n                        </Form.Group>\n                    </div>\n                    <div>\n                        <Form.Group>\n                            <Form.Label className={styles.label} htmlFor=\"hideMedianPrice\">\n                                Median price\n                            </Form.Label>\n                            <Form.Check\n                                onChange={event => {\n                                    updateApiSetting('showMedPrice', event.target.checked)\n                                    setFlipCustomizeSetting('hideMedianPrice', !event.target.checked)\n                                }}\n                                defaultChecked={!flipCustomizeSettings.hideMedianPrice}\n                                id=\"hideMedianPrice\"\n                                style={{ display: 'inline' }}\n                                type=\"checkbox\"\n                            />\n                        </Form.Group>\n                        <Form.Group>\n                            <Form.Label className={styles.label} htmlFor=\"hideLowestBin\">\n                                Lowest BIN\n                            </Form.Label>\n                            <Form.Check\n                                onChange={event => {\n                                    updateApiSetting('showLbin', event.target.checked)\n                                    setFlipCustomizeSetting('hideLowestBin', !event.target.checked)\n                                }}\n                                defaultChecked={!flipCustomizeSettings.hideLowestBin}\n                                id=\"hideLowestBin\"\n                                style={{ display: 'inline' }}\n                                type=\"checkbox\"\n                            />\n                        </Form.Group>\n                        <Form.Group>\n                            <Form.Label className={styles.label} htmlFor=\"hideSeller\">\n                                Seller{'  '}\n                                <Tooltip\n                                    type=\"hover\"\n                                    content={<HelpIcon style={{ color: '#007bff', cursor: 'pointer' }} />}\n                                    tooltipContent={\n                                        <span>\n                                            Showing the player name takes additional processing time and therefore may add a bit of a delay for the flips.\n                                        </span>\n                                    }\n                                />\n                            </Form.Label>\n                            <Form.Check\n                                onChange={event => {\n                                    updateApiSetting('showSeller', event.target.checked)\n                                    setFlipCustomizeSetting('hideSeller', !event.target.checked)\n                                }}\n                                defaultChecked={!flipCustomizeSettings.hideSeller}\n                                id=\"hideSeller\"\n                                style={{ display: 'inline' }}\n                                type=\"checkbox\"\n                            />\n                        </Form.Group>\n                        <Form.Group>\n                            <Form.Label className={styles.label} htmlFor=\"hideCopySuccessMessage\">\n                                Show copy message\n                            </Form.Label>\n                            <Form.Check\n                                onChange={event => {\n                                    updateApiSetting('showCopySuccessMessage', event.target.checked)\n                                    setFlipCustomizeSetting('hideCopySuccessMessage', !event.target.checked)\n                                }}\n                                defaultChecked={!flipCustomizeSettings.hideCopySuccessMessage}\n                                id=\"hideCopySuccessMessage\"\n                                style={{ display: 'inline' }}\n                                type=\"checkbox\"\n                            />\n                        </Form.Group>\n                        <Form.Group>\n                            <Form.Label className={styles.label} htmlFor=\"disableLinks\">\n                                Disable links\n                            </Form.Label>\n                            <Form.Check\n                                onChange={event => {\n                                    updateApiSetting('showLinks', !event.target.checked)\n                                    setFlipCustomizeSetting('disableLinks', event.target.checked)\n                                }}\n                                defaultChecked={flipCustomizeSettings.disableLinks}\n                                id=\"disableLinks\"\n                                style={{ display: 'inline' }}\n                                type=\"checkbox\"\n                            />\n                        </Form.Group>\n                        <Form.Group>\n                            <Form.Label className={styles.label} htmlFor=\"useLowestBinForProfit\">\n                                Use lowest BIN <br /> to calc. profit{' '}\n                                <Tooltip\n                                    type=\"hover\"\n                                    content={<HelpIcon style={{ color: '#007bff', cursor: 'pointer' }} />}\n                                    tooltipContent={useLowestBinHelpElement}\n                                />\n                            </Form.Label>\n                            <Form.Check\n                                onChange={event => {\n                                    updateApiSetting('lbin', event.target.checked)\n                                    setFlipCustomizeSetting('useLowestBinForProfit', event.target.checked)\n                                }}\n                                defaultChecked={flipCustomizeSettings.useLowestBinForProfit}\n                                id=\"useLowestBinForProfit\"\n                                style={{ display: 'inline' }}\n                                type=\"checkbox\"\n                            />\n                        </Form.Group>\n                    </div>\n                </Form>\n                <div style={{ marginLeft: '30px', marginRight: '30px' }}>\n                    <label htmlFor=\"finders\" className={styles.label}>\n                        Used Flip-Finders\n                    </label>\n                    <Select\n                        id=\"finders\"\n                        isMulti\n                        options={FLIP_FINDERS.filter(finder => finder.selectable)}\n                        defaultValue={getFlipFinders(flipCustomizeSettings.finders || [])}\n                        styles={customSelectStyle}\n                        onChange={onFindersChange}\n                        closeMenuOnSelect={false}\n                        components={{ MultiValueContainer }}\n                    />\n                    {getFlipFinderWarningElement()}\n                </div>\n                <hr />\n                <div>\n                    <h5>Mod settings</h5>\n                    <Form className={styles.section}>\n                        <div>\n                            <Form.Group>\n                                <Form.Label className={styles.label} htmlFor=\"justProfit\">\n                                    Just show profit\n                                </Form.Label>\n                                <Form.Check\n                                    onChange={event => {\n                                        updateApiSetting('modJustProfit', event.target.checked)\n                                        setFlipCustomizeSetting('justProfit', event.target.checked)\n                                    }}\n                                    defaultChecked={flipCustomizeSettings.justProfit}\n                                    id=\"justProfit\"\n                                    style={{ display: 'inline' }}\n                                    type=\"checkbox\"\n                                />\n                            </Form.Group>\n                            <Form.Group>\n                                <Form.Label className={styles.label} htmlFor=\"blockTenSecMsg\">\n                                    \"Flips in 10 seconds\"\n                                </Form.Label>\n                                <Form.Check\n                                    onChange={event => {\n                                        updateApiSetting('modBlockTenSecMsg', !event.target.checked)\n                                        setFlipCustomizeSetting('blockTenSecMsg', !event.target.checked)\n                                    }}\n                                    defaultChecked={!flipCustomizeSettings.blockTenSecMsg}\n                                    id=\"blockTenSecMsg\"\n                                    style={{ display: 'inline' }}\n                                    type=\"checkbox\"\n                                />\n                            </Form.Group>\n                            <Form.Group>\n                                <Form.Label className={styles.label} htmlFor=\"hideSellerOpenBtn\">\n                                    Seller AH Button\n                                </Form.Label>\n                                <Form.Check\n                                    onChange={event => {\n                                        updateApiSetting('showSellerOpenBtn', event.target.checked)\n                                        setFlipCustomizeSetting('hideSellerOpenBtn', !event.target.checked)\n                                    }}\n                                    defaultChecked={!flipCustomizeSettings.hideSellerOpenBtn}\n                                    id=\"hideSellerOpenBtn\"\n                                    style={{ display: 'inline' }}\n                                    type=\"checkbox\"\n                                />\n                            </Form.Group>\n                            <Form.Group>\n                                <Form.Label className={styles.label} htmlFor=\"modCountdown\">\n                                    Countdown\n                                </Form.Label>\n                                <Form.Check\n                                    onChange={event => {\n                                        updateApiSetting('modCountdown', event.target.checked)\n                                        setFlipCustomizeSetting('modCountdown', event.target.checked)\n                                    }}\n                                    defaultChecked={flipCustomizeSettings.modCountdown}\n                                    id=\"modCountdown\"\n                                    style={{ display: 'inline' }}\n                                    type=\"checkbox\"\n                                />\n                            </Form.Group>\n                        </div>\n                        <div>\n                            <Form.Group>\n                                <Form.Label className={styles.label} htmlFor=\"soundOnFlip\">\n                                    Play flip sound\n                                </Form.Label>\n                                <Form.Check\n                                    onChange={event => {\n                                        updateApiSetting('modSoundOnFlip', event.target.checked)\n                                        setFlipCustomizeSetting('soundOnFlip', event.target.checked)\n                                    }}\n                                    defaultChecked={flipCustomizeSettings.soundOnFlip}\n                                    id=\"soundOnFlip\"\n                                    style={{ display: 'inline' }}\n                                    type=\"checkbox\"\n                                />\n                            </Form.Group>\n                            <Form.Group>\n                                <Form.Label className={styles.label} htmlFor=\"hideModChat\">\n                                    Mod Chat\n                                </Form.Label>\n                                <Form.Check\n                                    onChange={event => {\n                                        updateApiSetting('modChat', event.target.checked)\n                                        setFlipCustomizeSetting('hideModChat', !event.target.checked)\n                                    }}\n                                    defaultChecked={!flipCustomizeSettings.hideModChat}\n                                    id=\"hideModChat\"\n                                    style={{ display: 'inline' }}\n                                    type=\"checkbox\"\n                                />\n                            </Form.Group>\n                            <Form.Group>\n                                <Form.Label className={styles.label} htmlFor=\"hideLore\">\n                                    Item lore (on hover)\n                                </Form.Label>\n                                <Form.Check\n                                    onChange={event => {\n                                        updateApiSetting('showLore', event.target.checked)\n                                        setFlipCustomizeSetting('hideLore', !event.target.checked)\n                                    }}\n                                    defaultChecked={!flipCustomizeSettings.hideLore}\n                                    id=\"hideLore\"\n                                    style={{ display: 'inline' }}\n                                    type=\"checkbox\"\n                                />\n                            </Form.Group>\n                        </div>\n                    </Form>\n                    <div style={{ marginLeft: '30px', marginRight: '30px' }}>\n                        <FormatElement onChange={onModFormatChange} settings={flipCustomizeSettings} labelClass={styles.label} />\n                    </div>\n                </div>\n                <hr />\n                <div style={{ margin: '20px' }}>\n                    <h5>Import/Export</h5>\n                    <p>\n                        You can export your custom flipper settings into a .json file. You use this to send your settings to a friend or to restore them later\n                        yourself by importing them again.\n                    </p>\n                    <p>After importing a settings file, the page will reload to apply the new settings.</p>\n                    <div className={styles.section} style={{ justifyContent: 'space-between' }}>\n                        <Button\n                            onClick={() => {\n                                document.getElementById('fileUpload')?.click()\n                            }}\n                            style={{ width: '40%' }}\n                        >\n                            Import\n                        </Button>\n                        <Tooltip\n                            type=\"hover\"\n                            content={\n                                <div style={{ width: '40%', marginRight: 0 }}>\n                                    <Button onClick={exportFilter} disabled={isExportDisabled} style={{ width: '100%' }}>\n                                        Export\n                                    </Button>\n                                </div>\n                            }\n                            tooltipContent={\n                                isExportDisabled ? <p>Your settings can't be exported, likely because they are based on a config you bought</p> : undefined\n                            }\n                        />\n                        {/* This is the \"true\" upload field. It is called by the \"Import\"-Button */}\n                        <input onChange={readImportFile} style={{ display: 'none' }} type=\"file\" id=\"fileUpload\" />\n                    </div>\n                    <Form.Group style={{ marginTop: 15 }}>\n                        <Form.Check\n                            onChange={() => {\n                                let wasDisabled = localStorage.getItem('disableRiskyFinderImportProtection') === 'true'\n                                localStorage.setItem('disableRiskyFinderImportProtection', (!wasDisabled).toString())\n                            }}\n                            defaultChecked={localStorage.getItem('disableRiskyFinderImportProtection') !== 'true'}\n                            id=\"riskyFinderProtection\"\n                            style={{ display: 'inline', marginRight: 10 }}\n                            type=\"checkbox\"\n                        />\n                        <Form.Label className={styles.label} htmlFor=\"riskyFinderProtection\">\n                            Risky finder protection{' '}\n                            <Tooltip\n                                type=\"hover\"\n                                content={<HelpIcon style={{ color: '#007bff', cursor: 'pointer' }} />}\n                                tooltipContent={\n                                    <span>This setting disables risky finders when importing settings. Only disable this if you know what you are doing!</span>\n                                }\n                            />\n                        </Form.Label>\n                    </Form.Group>\n                </div>\n                <hr />\n                <PublishedConfigs />\n            </div>\n            <div className={styles.verticalLine}></div>\n            <div className={`${styles.sectionRight} ${styles.section}`}>\n                <Flip style={{ width: '300px' }} flip={DEMO_FLIP} />\n            </div>\n        </div>\n    )\n\n    function addWarningForFinder(name: string, warnings: string[], warning: string) {\n        let craftCostFinder = FLIP_FINDERS.find(finder => finder.label === name)\n        if (flipCustomizeSettings?.finders?.find(finder => finder.toString() === craftCostFinder?.value)) {\n            warnings.push(warning)\n        }\n    }\n}\n\nexport default React.memo(FlipCustomize)\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAAA;AACA;AACA;AASA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;;;AAtBA;;;;;;;;;;;;;;;AAwBA,MAAM,oBAAoB;IACtB,QAAQ,CAAA,WAAY,CAAC;YACjB,GAAG,QAAQ;YACX,OAAO;QACX,CAAC;AACL;AAEA,SAAS;;IACL,IAAI,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAyB,CAAC;IACzF,IAAI,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,IAAI,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,sNAAA,CAAA,YAAS,AAAD;IAE7B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACN,IAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,2BAAwB,AAAD;YACtC,oBAAoB,SAAS,WAAW,KAAK;YAC7C,yBAAyB;gBAAE,GAAG,QAAQ;YAAC;YACvC;QACJ;kCAAG,EAAE;IAEL,eAAe;QACX,IAAI,UAAU,MAAM,oHAAA,CAAA,UAAG,CAAC,mBAAmB;IAC/C;IAEA,SAAS,wBAAwB,GAAW,EAAE,KAAU;QACpD,qBAAqB,CAAC,IAAI,GAAG;QAC7B,yBAAyB;QACzB,CAAA,GAAA,0HAAA,CAAA,aAAU,AAAD,EAAE,0HAAA,CAAA,uBAAoB,EAAE,KAAK,SAAS,CAAC;QAChD,SAAS,aAAa,CAAC,IAAI,YAAY,wHAAA,CAAA,gBAAa,CAAC,oBAAoB;QACzE,yBAAyB;YAAE,GAAG,qBAAqB;QAAC;IACxD;IAEA,SAAS,iBAAiB,GAAW,EAAE,KAAc;QACjD,oHAAA,CAAA,UAAG,CAAC,cAAc,CAAC,KAAK;QACxB,YAAY;IAChB;IAEA,SAAS,2BAA2B,KAAoC;QACpE,wBAAwB,sBAAsB,MAAM,MAAM,CAAC,aAAa;QACxE,oHAAA,CAAA,UAAG,CAAC,cAAc,CAAC,mBAAmB,MAAM,MAAM,CAAC,aAAa,IAAI;QACpE,YAAY;IAChB;IAEA,SAAS,gBAAgB,QAAQ;QAC7B,wBACI,WACA,SAAS,GAAG,CAAC,CAAA,QAAS,MAAM,KAAK;QAErC,oHAAA,CAAA,UAAG,CAAC,cAAc,CACd,WACA,sBAAsB,OAAO,EAAE,OAAO,CAAC,GAAG,IAAM,CAAC,IAAI,CAAC,GAAG;QAE7D,YAAY;IAChB;IAEA,SAAS,kBAAkB,KAAa;QACpC,wBAAwB,aAAa;QACrC,oHAAA,CAAA,UAAG,CAAC,cAAc,CAAC,aAAa,MAAM,UAAU,CAAC,KAAK;QACtD,YAAY;IAChB;IAEA,SAAS,YAAY,QAAgB;QACjC,WAAW;YACP,UAAU;YACV,QAAQ,WAAW,OAAO,qBAAqB,CAAC,SAAS;QAC7D;IACJ;IAEA,SAAS,SAAS,QAAQ,EAAE,IAAI;QAC5B,IAAI,UAAU,SAAS,aAAa,CAAC;QACrC,QAAQ,YAAY,CAAC,QAAQ,mCAAmC,mBAAmB;QACnF,QAAQ,YAAY,CAAC,YAAY;QAEjC,QAAQ,KAAK,CAAC,OAAO,GAAG;QACxB,SAAS,IAAI,CAAC,WAAW,CAAC;QAE1B,QAAQ,KAAK;QAEb,SAAS,IAAI,CAAC,WAAW,CAAC;IAC9B;IAEA,SAAS;QACL,IAAI,WAAW,CAAA,GAAA,0HAAA,CAAA,yBAAsB,AAAD,EAChC,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD,EAAiB,0HAAA,CAAA,qBAAkB,EAAE,CAAC,IACtD,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD,EAAyB,0HAAA,CAAA,uBAAoB,EAAE,CAAC,IAChE,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD,EAAqB,0HAAA,CAAA,4BAAyB,EAAE,EAAE;QAGtE,SAAS,eAAe,KAAK,SAAS,CAAC;IAC3C;IAEA,SAAS,eAAe,CAAC;QACrB,IAAI,SAAS,GAAG,6BAA6B;;QAC7C,IAAI,SAAS,IAAI;QACjB,IAAI,EAAE,MAAM,CAAC,KAAK,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE,EAAE;YACrC,OAAO,MAAM,GAAG,SAAU,CAAC;gBACvB,SAAS,EAAE,MAAM,CAAE,MAAM,CAAE,QAAQ;gBACnC,CAAA,GAAA,0HAAA,CAAA,uBAAoB,AAAD,EAAE;YACzB,EAAE,cAAc;;YAChB,OAAO,UAAU,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE;QACvC;QACA,OAAO;IACX;IAEA,SAAS;QACL,IAAI,WAAqB,EAAE;QAE3B,IAAI,eAAe,sHAAA,CAAA,eAAY,CAAC,IAAI,CAAC,CAAA,SAAU,OAAO,KAAK,KAAK;QAChE,IACI,gBACA,sBAAsB,qBAAqB,IAC3C,CAAC,CAAC,sBAAsB,OAAO,IAC3B,sBAAsB,OAAO,CAAC,MAAM,KAAK,KACzC,sBAAsB,OAAO,CAAC,MAAM,GAAG,KACvC,sBAAsB,OAAO,CAAC,EAAE,CAAC,QAAQ,OAAO,aAAa,KAAK,GACxE;YACE,SAAS,IAAI,CACT;QAER;QAEA,oBAAoB,OAAO,UAAU;QACrC,oBAAoB,UAAU,UAAU;QACxC,oBAAoB,aAAa,UAAU;QAE3C,IAAI,SAAS,MAAM,KAAK,GAAG;YACvB,OAAO;QACX;QACA,IAAI,SAAS,MAAM,KAAK,GAAG;YACvB,qBACI,6LAAC;0BACG,cAAA,6LAAC;oBAAE,OAAO;wBAAE,OAAO;oBAAM;8BAAI,QAAQ,CAAC,EAAE;;;;;;;;;;;QAGpD;QACA,qBACI,6LAAC;YAAG,OAAO;gBAAE,OAAO;YAAM;sBACrB,SAAS,GAAG,CAAC,CAAA,wBACV,6LAAC;8BAAI;;;;;;;;;;;IAIrB;IAEA,MAAM,wCACF,6LAAC;kBAAE;;;;;;IAMP,MAAM,sBAAsB,CAAA;QACxB,qBACI,6LAAC,yMAAA,CAAA,aAAU,CAAC,mBAAmB;YAAE,GAAG,KAAK;sBACrC,cAAA,6LAAC,oIAAA,CAAA,UAAO;gBACJ,MAAM;gBACN,uBAAS,6LAAC;oBAAK,OAAO,MAAM,UAAU,CAAC,GAAG;8BAAG,MAAM,QAAQ;;;;;;gBAC3D,8BAAgB,6LAAC;8BAAM,MAAM,IAAI,CAAC,WAAW;;;;;;;;;;;;;;;;IAI7D;IAEA,IAAI,OAAO,IAAI,CAAC,uBAAuB,MAAM,KAAK,GAAG;QACjD,qBAAO;IACX;IAEA,qBACI,6LAAC;QAAI,WAAW,sKAAA,CAAA,UAAM,CAAC,aAAa;;0BAChC,6LAAC;gBAAI,WAAW,sKAAA,CAAA,UAAM,CAAC,WAAW;;kCAC9B,6LAAC,uLAAA,CAAA,OAAI;wBAAC,WAAW,sKAAA,CAAA,UAAM,CAAC,OAAO;;0CAC3B,6LAAC;;kDACG,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;;0DACP,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;gDAAC,WAAW,sKAAA,CAAA,UAAM,CAAC,KAAK;gDAAE,SAAQ;0DAAW;;;;;;0DAGxD,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;gDACP,UAAU,CAAA;oDACN,iBAAiB,YAAY,MAAM,MAAM,CAAC,OAAO;oDACjD,wBAAwB,YAAY,CAAC,MAAM,MAAM,CAAC,OAAO;gDAC7D;gDACA,gBAAgB,CAAC,sBAAsB,QAAQ;gDAC/C,IAAG;gDACH,OAAO;oDAAE,SAAS;gDAAS;gDAC3B,MAAK;;;;;;;;;;;;kDAGb,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;;0DACP,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;gDAAC,WAAW,sKAAA,CAAA,UAAM,CAAC,KAAK;gDAAE,SAAQ;0DAAsB;;;;;;0DAGnE,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;gDACP,UAAU,CAAA;oDACN,iBAAiB,iBAAiB,MAAM,MAAM,CAAC,OAAO;oDACtD,wBAAwB,uBAAuB,CAAC,MAAM,MAAM,CAAC,OAAO;gDACxE;gDACA,gBAAgB,CAAC,sBAAsB,mBAAmB;gDAC1D,IAAG;gDACH,OAAO;oDAAE,SAAS;gDAAS;gDAC3B,MAAK;;;;;;;;;;;;kDAGb,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;;0DACP,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;gDAAC,WAAW,sKAAA,CAAA,UAAM,CAAC,KAAK;gDAAE,SAAQ;0DAAsB;;;;;;0DAGnE,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;gDACP,UAAU,CAAA;oDACN,iBAAiB,aAAa,MAAM,MAAM,CAAC,OAAO;oDAClD,wBAAwB,uBAAuB,CAAC,MAAM,MAAM,CAAC,OAAO;gDACxE;gDACA,gBAAgB,CAAC,sBAAsB,mBAAmB;gDAC1D,IAAG;gDACH,OAAO;oDAAE,SAAS;gDAAS;gDAC3B,MAAK;;;;;;;;;;;;kDAGb,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;;0DACP,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;gDAAC,WAAW,sKAAA,CAAA,UAAM,CAAC,KAAK;gDAAE,SAAQ;0DAAa;;;;;;0DAG1D,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;gDACP,UAAU,CAAA;oDACN,iBAAiB,cAAc,MAAM,MAAM,CAAC,OAAO;oDACnD,wBAAwB,cAAc,CAAC,MAAM,MAAM,CAAC,OAAO;gDAC/D;gDACA,gBAAgB,CAAC,sBAAsB,UAAU;gDACjD,IAAG;gDACH,OAAO;oDAAE,SAAS;gDAAS;gDAC3B,MAAK;;;;;;;;;;;;kDAGb,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;;0DACP,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;gDAAC,WAAW,sKAAA,CAAA,UAAM,CAAC,KAAK;gDAAE,SAAQ;0DAAe;;;;;;0DAG5D,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;gDACP,UAAU,CAAA;oDACN,iBAAiB,mBAAmB,MAAM,MAAM,CAAC,OAAO;oDACxD,wBAAwB,gBAAgB,MAAM,MAAM,CAAC,OAAO;gDAChE;gDACA,gBAAgB,sBAAsB,YAAY;gDAClD,IAAG;gDACH,OAAO;oDAAE,SAAS;gDAAS;gDAC3B,MAAK;;;;;;;;;;;;kDAGb,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;;0DACP,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;gDAAC,WAAW,sKAAA,CAAA,UAAM,CAAC,KAAK;gDAAE,SAAQ;0DAAoB;;;;;;0DAGjE,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;gDACP,UAAU,CAAA;oDACN,iBAAiB,qBAAqB,MAAM,MAAM,CAAC,OAAO;oDAC1D,wBAAwB,qBAAqB,CAAC,MAAM,MAAM,CAAC,OAAO;gDACtE;gDACA,gBAAgB,CAAC,sBAAsB,iBAAiB;gDACxD,IAAG;gDACH,OAAO;oDAAE,SAAS;gDAAS;gDAC3B,MAAK;;;;;;;;;;;;kDAGb,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;;0DACP,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;gDAAC,WAAW,sKAAA,CAAA,UAAM,CAAC,KAAK;gDAAE,SAAQ;0DAAmB;;;;;;0DAGhE,6LAAC,uLAAA,CAAA,OAAI,CAAC,OAAO;gDACT,KAAK;gDACL,KAAK;gDACL,UAAU;gDACV,cAAc,sBAAsB,kBAAkB;gDACtD,MAAK;gDACL,IAAG;;;;;;;;;;;;;;;;;;0CAIf,6LAAC;;kDACG,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;;0DACP,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;gDAAC,WAAW,sKAAA,CAAA,UAAM,CAAC,KAAK;gDAAE,SAAQ;0DAAkB;;;;;;0DAG/D,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;gDACP,UAAU,CAAA;oDACN,iBAAiB,gBAAgB,MAAM,MAAM,CAAC,OAAO;oDACrD,wBAAwB,mBAAmB,CAAC,MAAM,MAAM,CAAC,OAAO;gDACpE;gDACA,gBAAgB,CAAC,sBAAsB,eAAe;gDACtD,IAAG;gDACH,OAAO;oDAAE,SAAS;gDAAS;gDAC3B,MAAK;;;;;;;;;;;;kDAGb,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;;0DACP,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;gDAAC,WAAW,sKAAA,CAAA,UAAM,CAAC,KAAK;gDAAE,SAAQ;0DAAgB;;;;;;0DAG7D,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;gDACP,UAAU,CAAA;oDACN,iBAAiB,YAAY,MAAM,MAAM,CAAC,OAAO;oDACjD,wBAAwB,iBAAiB,CAAC,MAAM,MAAM,CAAC,OAAO;gDAClE;gDACA,gBAAgB,CAAC,sBAAsB,aAAa;gDACpD,IAAG;gDACH,OAAO;oDAAE,SAAS;gDAAS;gDAC3B,MAAK;;;;;;;;;;;;kDAGb,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;;0DACP,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;gDAAC,WAAW,sKAAA,CAAA,UAAM,CAAC,KAAK;gDAAE,SAAQ;;oDAAa;oDAC/C;kEACP,6LAAC,oIAAA,CAAA,UAAO;wDACJ,MAAK;wDACL,uBAAS,6LAAC,4JAAA,CAAA,UAAQ;4DAAC,OAAO;gEAAE,OAAO;gEAAW,QAAQ;4DAAU;;;;;;wDAChE,8BACI,6LAAC;sEAAK;;;;;;;;;;;;;;;;;0DAMlB,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;gDACP,UAAU,CAAA;oDACN,iBAAiB,cAAc,MAAM,MAAM,CAAC,OAAO;oDACnD,wBAAwB,cAAc,CAAC,MAAM,MAAM,CAAC,OAAO;gDAC/D;gDACA,gBAAgB,CAAC,sBAAsB,UAAU;gDACjD,IAAG;gDACH,OAAO;oDAAE,SAAS;gDAAS;gDAC3B,MAAK;;;;;;;;;;;;kDAGb,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;;0DACP,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;gDAAC,WAAW,sKAAA,CAAA,UAAM,CAAC,KAAK;gDAAE,SAAQ;0DAAyB;;;;;;0DAGtE,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;gDACP,UAAU,CAAA;oDACN,iBAAiB,0BAA0B,MAAM,MAAM,CAAC,OAAO;oDAC/D,wBAAwB,0BAA0B,CAAC,MAAM,MAAM,CAAC,OAAO;gDAC3E;gDACA,gBAAgB,CAAC,sBAAsB,sBAAsB;gDAC7D,IAAG;gDACH,OAAO;oDAAE,SAAS;gDAAS;gDAC3B,MAAK;;;;;;;;;;;;kDAGb,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;;0DACP,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;gDAAC,WAAW,sKAAA,CAAA,UAAM,CAAC,KAAK;gDAAE,SAAQ;0DAAe;;;;;;0DAG5D,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;gDACP,UAAU,CAAA;oDACN,iBAAiB,aAAa,CAAC,MAAM,MAAM,CAAC,OAAO;oDACnD,wBAAwB,gBAAgB,MAAM,MAAM,CAAC,OAAO;gDAChE;gDACA,gBAAgB,sBAAsB,YAAY;gDAClD,IAAG;gDACH,OAAO;oDAAE,SAAS;gDAAS;gDAC3B,MAAK;;;;;;;;;;;;kDAGb,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;;0DACP,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;gDAAC,WAAW,sKAAA,CAAA,UAAM,CAAC,KAAK;gDAAE,SAAQ;;oDAAwB;kEAClD,6LAAC;;;;;oDAAK;oDAAiB;kEACtC,6LAAC,oIAAA,CAAA,UAAO;wDACJ,MAAK;wDACL,uBAAS,6LAAC,4JAAA,CAAA,UAAQ;4DAAC,OAAO;gEAAE,OAAO;gEAAW,QAAQ;4DAAU;;;;;;wDAChE,gBAAgB;;;;;;;;;;;;0DAGxB,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;gDACP,UAAU,CAAA;oDACN,iBAAiB,QAAQ,MAAM,MAAM,CAAC,OAAO;oDAC7C,wBAAwB,yBAAyB,MAAM,MAAM,CAAC,OAAO;gDACzE;gDACA,gBAAgB,sBAAsB,qBAAqB;gDAC3D,IAAG;gDACH,OAAO;oDAAE,SAAS;gDAAS;gDAC3B,MAAK;;;;;;;;;;;;;;;;;;;;;;;;kCAKrB,6LAAC;wBAAI,OAAO;4BAAE,YAAY;4BAAQ,aAAa;wBAAO;;0CAClD,6LAAC;gCAAM,SAAQ;gCAAU,WAAW,sKAAA,CAAA,UAAM,CAAC,KAAK;0CAAE;;;;;;0CAGlD,6LAAC,oLAAA,CAAA,UAAM;gCACH,IAAG;gCACH,OAAO;gCACP,SAAS,sHAAA,CAAA,eAAY,CAAC,MAAM,CAAC,CAAA,SAAU,OAAO,UAAU;gCACxD,cAAc,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,sBAAsB,OAAO,IAAI,EAAE;gCAChE,QAAQ;gCACR,UAAU;gCACV,mBAAmB;gCACnB,YAAY;oCAAE;gCAAoB;;;;;;4BAErC;;;;;;;kCAEL,6LAAC;;;;;kCACD,6LAAC;;0CACG,6LAAC;0CAAG;;;;;;0CACJ,6LAAC,uLAAA,CAAA,OAAI;gCAAC,WAAW,sKAAA,CAAA,UAAM,CAAC,OAAO;;kDAC3B,6LAAC;;0DACG,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;;kEACP,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;wDAAC,WAAW,sKAAA,CAAA,UAAM,CAAC,KAAK;wDAAE,SAAQ;kEAAa;;;;;;kEAG1D,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;wDACP,UAAU,CAAA;4DACN,iBAAiB,iBAAiB,MAAM,MAAM,CAAC,OAAO;4DACtD,wBAAwB,cAAc,MAAM,MAAM,CAAC,OAAO;wDAC9D;wDACA,gBAAgB,sBAAsB,UAAU;wDAChD,IAAG;wDACH,OAAO;4DAAE,SAAS;wDAAS;wDAC3B,MAAK;;;;;;;;;;;;0DAGb,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;;kEACP,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;wDAAC,WAAW,sKAAA,CAAA,UAAM,CAAC,KAAK;wDAAE,SAAQ;kEAAiB;;;;;;kEAG9D,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;wDACP,UAAU,CAAA;4DACN,iBAAiB,qBAAqB,CAAC,MAAM,MAAM,CAAC,OAAO;4DAC3D,wBAAwB,kBAAkB,CAAC,MAAM,MAAM,CAAC,OAAO;wDACnE;wDACA,gBAAgB,CAAC,sBAAsB,cAAc;wDACrD,IAAG;wDACH,OAAO;4DAAE,SAAS;wDAAS;wDAC3B,MAAK;;;;;;;;;;;;0DAGb,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;;kEACP,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;wDAAC,WAAW,sKAAA,CAAA,UAAM,CAAC,KAAK;wDAAE,SAAQ;kEAAoB;;;;;;kEAGjE,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;wDACP,UAAU,CAAA;4DACN,iBAAiB,qBAAqB,MAAM,MAAM,CAAC,OAAO;4DAC1D,wBAAwB,qBAAqB,CAAC,MAAM,MAAM,CAAC,OAAO;wDACtE;wDACA,gBAAgB,CAAC,sBAAsB,iBAAiB;wDACxD,IAAG;wDACH,OAAO;4DAAE,SAAS;wDAAS;wDAC3B,MAAK;;;;;;;;;;;;0DAGb,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;;kEACP,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;wDAAC,WAAW,sKAAA,CAAA,UAAM,CAAC,KAAK;wDAAE,SAAQ;kEAAe;;;;;;kEAG5D,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;wDACP,UAAU,CAAA;4DACN,iBAAiB,gBAAgB,MAAM,MAAM,CAAC,OAAO;4DACrD,wBAAwB,gBAAgB,MAAM,MAAM,CAAC,OAAO;wDAChE;wDACA,gBAAgB,sBAAsB,YAAY;wDAClD,IAAG;wDACH,OAAO;4DAAE,SAAS;wDAAS;wDAC3B,MAAK;;;;;;;;;;;;;;;;;;kDAIjB,6LAAC;;0DACG,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;;kEACP,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;wDAAC,WAAW,sKAAA,CAAA,UAAM,CAAC,KAAK;wDAAE,SAAQ;kEAAc;;;;;;kEAG3D,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;wDACP,UAAU,CAAA;4DACN,iBAAiB,kBAAkB,MAAM,MAAM,CAAC,OAAO;4DACvD,wBAAwB,eAAe,MAAM,MAAM,CAAC,OAAO;wDAC/D;wDACA,gBAAgB,sBAAsB,WAAW;wDACjD,IAAG;wDACH,OAAO;4DAAE,SAAS;wDAAS;wDAC3B,MAAK;;;;;;;;;;;;0DAGb,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;;kEACP,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;wDAAC,WAAW,sKAAA,CAAA,UAAM,CAAC,KAAK;wDAAE,SAAQ;kEAAc;;;;;;kEAG3D,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;wDACP,UAAU,CAAA;4DACN,iBAAiB,WAAW,MAAM,MAAM,CAAC,OAAO;4DAChD,wBAAwB,eAAe,CAAC,MAAM,MAAM,CAAC,OAAO;wDAChE;wDACA,gBAAgB,CAAC,sBAAsB,WAAW;wDAClD,IAAG;wDACH,OAAO;4DAAE,SAAS;wDAAS;wDAC3B,MAAK;;;;;;;;;;;;0DAGb,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;;kEACP,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;wDAAC,WAAW,sKAAA,CAAA,UAAM,CAAC,KAAK;wDAAE,SAAQ;kEAAW;;;;;;kEAGxD,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;wDACP,UAAU,CAAA;4DACN,iBAAiB,YAAY,MAAM,MAAM,CAAC,OAAO;4DACjD,wBAAwB,YAAY,CAAC,MAAM,MAAM,CAAC,OAAO;wDAC7D;wDACA,gBAAgB,CAAC,sBAAsB,QAAQ;wDAC/C,IAAG;wDACH,OAAO;4DAAE,SAAS;wDAAS;wDAC3B,MAAK;;;;;;;;;;;;;;;;;;;;;;;;0CAKrB,6LAAC;gCAAI,OAAO;oCAAE,YAAY;oCAAQ,aAAa;gCAAO;0CAClD,cAAA,6LAAC,4KAAA,CAAA,UAAa;oCAAC,UAAU;oCAAmB,UAAU;oCAAuB,YAAY,sKAAA,CAAA,UAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;kCAG7G,6LAAC;;;;;kCACD,6LAAC;wBAAI,OAAO;4BAAE,QAAQ;wBAAO;;0CACzB,6LAAC;0CAAG;;;;;;0CACJ,6LAAC;0CAAE;;;;;;0CAIH,6LAAC;0CAAE;;;;;;0CACH,6LAAC;gCAAI,WAAW,sKAAA,CAAA,UAAM,CAAC,OAAO;gCAAE,OAAO;oCAAE,gBAAgB;gCAAgB;;kDACrE,6LAAC,2LAAA,CAAA,SAAM;wCACH,SAAS;4CACL,SAAS,cAAc,CAAC,eAAe;wCAC3C;wCACA,OAAO;4CAAE,OAAO;wCAAM;kDACzB;;;;;;kDAGD,6LAAC,oIAAA,CAAA,UAAO;wCACJ,MAAK;wCACL,uBACI,6LAAC;4CAAI,OAAO;gDAAE,OAAO;gDAAO,aAAa;4CAAE;sDACvC,cAAA,6LAAC,2LAAA,CAAA,SAAM;gDAAC,SAAS;gDAAc,UAAU;gDAAkB,OAAO;oDAAE,OAAO;gDAAO;0DAAG;;;;;;;;;;;wCAK7F,gBACI,iCAAmB,6LAAC;sDAAE;;;;;qDAA4F;;;;;;kDAI1H,6LAAC;wCAAM,UAAU;wCAAgB,OAAO;4CAAE,SAAS;wCAAO;wCAAG,MAAK;wCAAO,IAAG;;;;;;;;;;;;0CAEhF,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;gCAAC,OAAO;oCAAE,WAAW;gCAAG;;kDAC/B,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;wCACP,UAAU;4CACN,IAAI,cAAc,aAAa,OAAO,CAAC,0CAA0C;4CACjF,aAAa,OAAO,CAAC,sCAAsC,CAAC,CAAC,WAAW,EAAE,QAAQ;wCACtF;wCACA,gBAAgB,aAAa,OAAO,CAAC,0CAA0C;wCAC/E,IAAG;wCACH,OAAO;4CAAE,SAAS;4CAAU,aAAa;wCAAG;wCAC5C,MAAK;;;;;;kDAET,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;wCAAC,WAAW,sKAAA,CAAA,UAAM,CAAC,KAAK;wCAAE,SAAQ;;4CAAwB;4CACzC;0DACxB,6LAAC,oIAAA,CAAA,UAAO;gDACJ,MAAK;gDACL,uBAAS,6LAAC,4JAAA,CAAA,UAAQ;oDAAC,OAAO;wDAAE,OAAO;wDAAW,QAAQ;oDAAU;;;;;;gDAChE,8BACI,6LAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAM1B,6LAAC;;;;;kCACD,6LAAC,kLAAA,CAAA,UAAgB;;;;;;;;;;;0BAErB,6LAAC;gBAAI,WAAW,sKAAA,CAAA,UAAM,CAAC,YAAY;;;;;;0BACnC,6LAAC;gBAAI,WAAW,GAAG,sKAAA,CAAA,UAAM,CAAC,YAAY,CAAC,CAAC,EAAE,sKAAA,CAAA,UAAM,CAAC,OAAO,EAAE;0BACtD,cAAA,6LAAC,yIAAA,CAAA,UAAI;oBAAC,OAAO;wBAAE,OAAO;oBAAQ;oBAAG,MAAM,sHAAA,CAAA,YAAS;;;;;;;;;;;;;;;;;;IAK5D,SAAS,oBAAoB,IAAY,EAAE,QAAkB,EAAE,OAAe;QAC1E,IAAI,kBAAkB,sHAAA,CAAA,eAAY,CAAC,IAAI,CAAC,CAAA,SAAU,OAAO,KAAK,KAAK;QACnE,IAAI,uBAAuB,SAAS,KAAK,CAAA,SAAU,OAAO,QAAQ,OAAO,iBAAiB,QAAQ;YAC9F,SAAS,IAAI,CAAC;QAClB;IACJ;AACJ;GArkBS;;QAGgB,sNAAA,CAAA,YAAS;;;KAHzB;2DAukBM,6JAAA,CAAA,UAAK,CAAC,IAAI,CAAC", "debugId": null}}, {"offset": {"line": 3088, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/components/Flipper/FlipRestrictionList/FlipRestrictionList.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"refreshAndDeleteButtonContainer\": \"FlipRestrictionList-module__yQKonG__refreshAndDeleteButtonContainer\",\n  \"restrictionList\": \"FlipRestrictionList-module__yQKonG__restrictionList\",\n  \"restrictionListContainer\": \"FlipRestrictionList-module__yQKonG__restrictionListContainer\",\n  \"searchBarContainer\": \"FlipRestrictionList-module__yQKonG__searchBarContainer\",\n  \"searchFilter\": \"FlipRestrictionList-module__yQKonG__searchFilter\",\n  \"sortByNameContainer\": \"FlipRestrictionList-module__yQKonG__sortByNameContainer\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 3102, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/Flipper/FlipRestrictionList/TagSelect/TagSelect.tsx"], "sourcesContent": ["import { useState } from 'react'\nimport { components, MultiValue } from 'react-select'\nimport { CURRENTLY_USED_TAGS } from '../../../../utils/SettingsUtils'\nimport Tooltip from '../../../Tooltip/Tooltip'\nimport HelpIcon from '@mui/icons-material/Help'\nimport Creatable from 'react-select/creatable'\n\ninterface Props {\n    defaultTags: string[]\n    onTagsChange(tags: string[])\n}\n\nconst customSelectStyle = {\n    option: provided => ({\n        ...provided,\n        color: 'black'\n    })\n}\n\nconst MultiValueContainer = props => {\n    return (\n        <components.MultiValueContainer {...props}>\n            <Tooltip\n                type={'hover'}\n                content={<span style={props.innerProps.css}>{props.children}</span>}\n                tooltipContent={<span>{props.data.description}</span>}\n            />\n        </components.MultiValueContainer>\n    )\n}\n\nfunction TagSelect(props: Props) {\n    let [tagOptions, setTagOptions] = useState(\n        props.defaultTags\n            ? props.defaultTags.slice().map(value => {\n                  return { value, label: value }\n              })\n            : []\n    )\n\n    function onTagsChange(\n        value: MultiValue<{\n            value: string\n            label: string\n        }>\n    ) {\n        let newTags = value.map(option => option.value)\n        let newTagOptions = [...value]\n        setTagOptions(newTagOptions)\n        props.onTagsChange(newTags)\n    }\n\n    function getAllUsedOptions(): MultiValue<{\n        value: string\n        label: string\n    }> {\n        let items = localStorage.getItem(CURRENTLY_USED_TAGS)\n        if (!items) {\n            return []\n        }\n        return JSON.parse(items).map(item => {\n            return {\n                value: item,\n                label: item\n            }\n        })\n    }\n\n    return (\n        <div>\n            <div style={{ marginBottom: '20px' }}>\n                <label htmlFor=\"finders\">\n                    Tags{' '}\n                    <Tooltip\n                        type=\"hover\"\n                        content={<HelpIcon style={{ color: '#007bff', cursor: 'pointer' }} />}\n                        tooltipContent={\n                            <span>\n                                Tags are used for you to organize your restrictions and to make it easier to search for specific entries. Tags don't influence\n                                what flips are shown.\n                            </span>\n                        }\n                    />\n                </label>\n                <Creatable\n                    isMulti\n                    options={getAllUsedOptions()}\n                    value={tagOptions}\n                    styles={customSelectStyle}\n                    closeMenuOnSelect={false}\n                    components={{ MultiValueContainer }}\n                    onChange={onTagsChange}\n                />\n            </div>\n        </div>\n    )\n}\n\nexport default TagSelect\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;;;;;;;;;AAOA,MAAM,oBAAoB;IACtB,QAAQ,CAAA,WAAY,CAAC;YACjB,GAAG,QAAQ;YACX,OAAO;QACX,CAAC;AACL;AAEA,MAAM,sBAAsB,CAAA;IACxB,qBACI,6LAAC,yMAAA,CAAA,aAAU,CAAC,mBAAmB;QAAE,GAAG,KAAK;kBACrC,cAAA,6LAAC,oIAAA,CAAA,UAAO;YACJ,MAAM;YACN,uBAAS,6LAAC;gBAAK,OAAO,MAAM,UAAU,CAAC,GAAG;0BAAG,MAAM,QAAQ;;;;;;YAC3D,8BAAgB,6LAAC;0BAAM,MAAM,IAAI,CAAC,WAAW;;;;;;;;;;;;;;;;AAI7D;KAVM;AAYN,SAAS,UAAU,KAAY;;IAC3B,IAAI,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EACrC,MAAM,WAAW,GACX,MAAM,WAAW,CAAC,KAAK,GAAG,GAAG;8BAAC,CAAA;YAC1B,OAAO;gBAAE;gBAAO,OAAO;YAAM;QACjC;+BACA,EAAE;IAGZ,SAAS,aACL,KAGE;QAEF,IAAI,UAAU,MAAM,GAAG,CAAC,CAAA,SAAU,OAAO,KAAK;QAC9C,IAAI,gBAAgB;eAAI;SAAM;QAC9B,cAAc;QACd,MAAM,YAAY,CAAC;IACvB;IAEA,SAAS;QAIL,IAAI,QAAQ,aAAa,OAAO,CAAC,0HAAA,CAAA,sBAAmB;QACpD,IAAI,CAAC,OAAO;YACR,OAAO,EAAE;QACb;QACA,OAAO,KAAK,KAAK,CAAC,OAAO,GAAG,CAAC,CAAA;YACzB,OAAO;gBACH,OAAO;gBACP,OAAO;YACX;QACJ;IACJ;IAEA,qBACI,6LAAC;kBACG,cAAA,6LAAC;YAAI,OAAO;gBAAE,cAAc;YAAO;;8BAC/B,6LAAC;oBAAM,SAAQ;;wBAAU;wBAChB;sCACL,6LAAC,oIAAA,CAAA,UAAO;4BACJ,MAAK;4BACL,uBAAS,6LAAC,4JAAA,CAAA,UAAQ;gCAAC,OAAO;oCAAE,OAAO;oCAAW,QAAQ;gCAAU;;;;;;4BAChE,8BACI,6LAAC;0CAAK;;;;;;;;;;;;;;;;;8BAOlB,6LAAC,8MAAA,CAAA,UAAS;oBACN,OAAO;oBACP,SAAS;oBACT,OAAO;oBACP,QAAQ;oBACR,mBAAmB;oBACnB,YAAY;wBAAE;oBAAoB;oBAClC,UAAU;;;;;;;;;;;;;;;;;AAK9B;GAjES;MAAA;uCAmEM", "debugId": null}}, {"offset": {"line": 3271, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/components/Flipper/FlipRestrictionList/EditRestriction/EditRestriction.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"multiSearch\": \"EditRestriction-module__jYrAnW__multiSearch\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA"}}, {"offset": {"line": 3280, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/Flipper/FlipRestrictionList/EditRestriction/EditRestriction.tsx"], "sourcesContent": ["'use client'\nimport React, { useEffect, useState } from 'react'\nimport { Button } from 'react-bootstrap'\nimport ItemFilter from '../../../ItemFilter/ItemFilter'\nimport Tooltip from '../../../Tooltip/Tooltip'\nimport TagSelect from '../TagSelect/TagSelect'\nimport api from '../../../../api/ApiHelper'\nimport styles from './EditRestriction.module.css'\nimport ApiSearch<PERSON>ield from '../../../Search/ApiSearchField'\n\ninterface Props {\n    defaultRestriction: FlipRestriction\n    onAdd(update: UpdateState)\n    onOverride(update: UpdateState)\n    onCancel()\n}\n\nexport interface UpdateState {\n    type: 'blacklist' | 'whitelist'\n    selectedItem?: Item\n    tags?: string[]\n    itemFilter?: ItemFilter\n}\n\nfunction EditRestriction(props: Props) {\n    let [isFilterValid, setIsFilterValid] = useState(true)\n    let [filters, setFilters] = useState<FilterOptions[]>([])\n    let [updateState, setUpdateState] = useState<UpdateState>({\n        ...props.defaultRestriction\n    })\n\n    useEffect(() => {\n        loadFilters()\n    }, [])\n\n    function loadFilters(): Promise<FilterOptions[]> {\n        return Promise.all([api.getFilters(props.defaultRestriction.item?.tag || '*'), api.flipFilters(props.defaultRestriction.item?.tag || '*')]).then(\n            filters => {\n                let result = [...(filters[0] || []), ...(filters[1] || [])]\n                setFilters(result)\n                return result\n            }\n        )\n    }\n\n    return (\n        <div>\n            <ApiSearchField\n                multiple\n                className={styles.multiSearch}\n                onChange={items => {\n                    setUpdateState({\n                        ...updateState,\n                        selectedItem: items[0]\n                            ? {\n                                  tag: items[0].id,\n                                  name: items[0].dataItem.name,\n                                  iconUrl: items[0].dataItem.iconUrl\n                              }\n                            : undefined\n                    })\n                }}\n                searchFunction={api.itemSearch}\n                selected={\n                    updateState.selectedItem\n                        ? [\n                              {\n                                  dataItem: {\n                                      name: updateState.selectedItem.name,\n                                      iconUrl: updateState.selectedItem.iconUrl\n                                  },\n                                  label: updateState.selectedItem.name,\n                                  id: updateState.selectedItem.tag\n                              } as unknown as SearchResultItem\n                          ]\n                        : undefined\n                }\n            />\n            <ItemFilter\n                defaultFilter={props.defaultRestriction.itemFilter}\n                filters={filters}\n                forceOpen={true}\n                onFilterChange={filter => {\n                    setUpdateState({ ...updateState, itemFilter: filter })\n                }}\n                ignoreURL={true}\n                autoSelect={false}\n                disableLastUsedFilter={true}\n                onIsValidChange={setIsFilterValid}\n            />\n            <TagSelect\n                defaultTags={props.defaultRestriction.tags || []}\n                onTagsChange={tags => {\n                    setUpdateState({ ...updateState, tags: tags })\n                }}\n            />\n            <div style={{ display: 'flex', flexWrap: 'wrap', gap: 10 }}>\n                <Tooltip\n                    type=\"hover\"\n                    content={\n                        <Button\n                            variant=\"success\"\n                            onClick={() => {\n                                props.onAdd(updateState)\n                            }}\n                            disabled={!isFilterValid || !!updateState.selectedItem}\n                        >\n                            Add filter(s)\n                        </Button>\n                    }\n                    tooltipContent={\n                        <span>\n                            Adds all selected filter to the selected restriction(s). If a specific filter is already present, it is <b>not</b> overwritten.\n                        </span>\n                    }\n                />\n                <Tooltip\n                    type=\"hover\"\n                    content={\n                        <Button\n                            variant=\"success\"\n                            onClick={() => {\n                                props.onOverride(updateState)\n                            }}\n                            style={{ marginLeft: '20px' }}\n                            disabled={!isFilterValid}\n                        >\n                            Override\n                        </Button>\n                    }\n                    tooltipContent={<span>Overwrites the filter of all the selected restrictions to this.</span>}\n                />\n                <Button variant=\"danger\" onClick={props.onCancel} style={{ marginLeft: '20px' }}>\n                    Cancel\n                </Button>\n            </div>\n        </div>\n    )\n}\n\nexport default EditRestriction\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AARA;;;;;;;;;AAwBA,SAAS,gBAAgB,KAAY;;IACjC,IAAI,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,IAAI,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IACxD,IAAI,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;QACtD,GAAG,MAAM,kBAAkB;IAC/B;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACN;QACJ;oCAAG,EAAE;IAEL,SAAS;QACL,OAAO,QAAQ,GAAG,CAAC;YAAC,oHAAA,CAAA,UAAG,CAAC,UAAU,CAAC,MAAM,kBAAkB,CAAC,IAAI,EAAE,OAAO;YAAM,oHAAA,CAAA,UAAG,CAAC,WAAW,CAAC,MAAM,kBAAkB,CAAC,IAAI,EAAE,OAAO;SAAK,EAAE,IAAI,CAC5I,CAAA;YACI,IAAI,SAAS;mBAAK,OAAO,CAAC,EAAE,IAAI,EAAE;mBAAO,OAAO,CAAC,EAAE,IAAI,EAAE;aAAE;YAC3D,WAAW;YACX,OAAO;QACX;IAER;IAEA,qBACI,6LAAC;;0BACG,6LAAC,0IAAA,CAAA,UAAc;gBACX,QAAQ;gBACR,WAAW,iMAAA,CAAA,UAAM,CAAC,WAAW;gBAC7B,UAAU,CAAA;oBACN,eAAe;wBACX,GAAG,WAAW;wBACd,cAAc,KAAK,CAAC,EAAE,GAChB;4BACI,KAAK,KAAK,CAAC,EAAE,CAAC,EAAE;4BAChB,MAAM,KAAK,CAAC,EAAE,CAAC,QAAQ,CAAC,IAAI;4BAC5B,SAAS,KAAK,CAAC,EAAE,CAAC,QAAQ,CAAC,OAAO;wBACtC,IACA;oBACV;gBACJ;gBACA,gBAAgB,oHAAA,CAAA,UAAG,CAAC,UAAU;gBAC9B,UACI,YAAY,YAAY,GAClB;oBACI;wBACI,UAAU;4BACN,MAAM,YAAY,YAAY,CAAC,IAAI;4BACnC,SAAS,YAAY,YAAY,CAAC,OAAO;wBAC7C;wBACA,OAAO,YAAY,YAAY,CAAC,IAAI;wBACpC,IAAI,YAAY,YAAY,CAAC,GAAG;oBACpC;iBACH,GACD;;;;;;0BAGd,6LAAC,0IAAA,CAAA,UAAU;gBACP,eAAe,MAAM,kBAAkB,CAAC,UAAU;gBAClD,SAAS;gBACT,WAAW;gBACX,gBAAgB,CAAA;oBACZ,eAAe;wBAAE,GAAG,WAAW;wBAAE,YAAY;oBAAO;gBACxD;gBACA,WAAW;gBACX,YAAY;gBACZ,uBAAuB;gBACvB,iBAAiB;;;;;;0BAErB,6LAAC,0KAAA,CAAA,UAAS;gBACN,aAAa,MAAM,kBAAkB,CAAC,IAAI,IAAI,EAAE;gBAChD,cAAc,CAAA;oBACV,eAAe;wBAAE,GAAG,WAAW;wBAAE,MAAM;oBAAK;gBAChD;;;;;;0BAEJ,6LAAC;gBAAI,OAAO;oBAAE,SAAS;oBAAQ,UAAU;oBAAQ,KAAK;gBAAG;;kCACrD,6LAAC,oIAAA,CAAA,UAAO;wBACJ,MAAK;wBACL,uBACI,6LAAC,2LAAA,CAAA,SAAM;4BACH,SAAQ;4BACR,SAAS;gCACL,MAAM,KAAK,CAAC;4BAChB;4BACA,UAAU,CAAC,iBAAiB,CAAC,CAAC,YAAY,YAAY;sCACzD;;;;;;wBAIL,8BACI,6LAAC;;gCAAK;8CACsG,6LAAC;8CAAE;;;;;;gCAAO;;;;;;;;;;;;kCAI9H,6LAAC,oIAAA,CAAA,UAAO;wBACJ,MAAK;wBACL,uBACI,6LAAC,2LAAA,CAAA,SAAM;4BACH,SAAQ;4BACR,SAAS;gCACL,MAAM,UAAU,CAAC;4BACrB;4BACA,OAAO;gCAAE,YAAY;4BAAO;4BAC5B,UAAU,CAAC;sCACd;;;;;;wBAIL,8BAAgB,6LAAC;sCAAK;;;;;;;;;;;kCAE1B,6LAAC,2LAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAS,SAAS,MAAM,QAAQ;wBAAE,OAAO;4BAAE,YAAY;wBAAO;kCAAG;;;;;;;;;;;;;;;;;;AAMjG;GAlHS;KAAA;uCAoHM", "debugId": null}}, {"offset": {"line": 3501, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/components/Flipper/FlipRestrictionList/NewRestriction/NewRestriction.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"copyButtonContainer\": \"NewRestriction-module__RrDo9a__copyButtonContainer\",\n  \"multiSearch\": \"NewRestriction-module__RrDo9a__multiSearch\",\n  \"newRestrictionSearchbarContainer\": \"NewRestriction-module__RrDo9a__newRestrictionSearchbarContainer\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 3512, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/Flipper/FlipRestrictionList/NewRestriction/NewRestriction.tsx"], "sourcesContent": ["'use client'\nimport React, { useEffect, useState } from 'react'\nimport { <PERSON><PERSON>, Toggle<PERSON>utt<PERSON>, ToggleButtonGroup } from 'react-bootstrap'\nimport api from '../../../../api/ApiHelper'\nimport ItemFilter from '../../../ItemFilter/ItemFilter'\nimport TagSelect from '../TagSelect/TagSelect'\nimport styles from './NewRestriction.module.css'\nimport ApiSearchField from '../../../Search/ApiSearchField'\nimport { CopyButton } from '../../../CopyButton/CopyButton'\n\ninterface Props {\n    onSaveRestrictions(restrictions: FlipRestriction[]): void\n    onCancel(): void\n    prefillRestriction?: RestrictionCreateState\n}\n\nexport interface RestrictionCreateState {\n    selectedItems?: Item[]\n    type: 'blacklist' | 'whitelist'\n    itemFilter?: ItemFilter\n    tags?: string[]\n}\n\nfunction NewRestriction(props: Props) {\n    let [createState, setCreateState] = useState<RestrictionCreateState>({\n        type: props.prefillRestriction ? props.prefillRestriction.type : 'blacklist',\n        itemFilter: props.prefillRestriction ? props.prefillRestriction.itemFilter : undefined,\n        selectedItems: props.prefillRestriction ? props.prefillRestriction.selectedItems : undefined\n    })\n    let [isFilterValid, setIsFilterValid] = useState(true)\n    let [filters, setFilters] = useState<FilterOptions[]>([])\n    let [showFillLastUsedItems, setShowFillLastUsedItems] = useState(sessionStorage.getItem('lastUsedItemsForNewRestrictions') !== null)\n\n    useEffect(() => {\n        loadFilters()\n    }, [createState.selectedItems])\n\n    function loadFilters(): Promise<FilterOptions[]> {\n        if (!createState.selectedItems || createState.selectedItems?.length <= 1) {\n            return Promise.all([\n                api.getFilters(createState.selectedItems?.length === 1 ? createState.selectedItems[0].tag : '*'),\n                api.flipFilters(createState.selectedItems?.length === 1 ? createState.selectedItems[0].tag : '*')\n            ]).then(filters => {\n                let result = [...(filters[0] || []), ...(filters[1] || [])]\n                setFilters(result)\n                return result\n            })\n        } else {\n            let promises: Promise<FilterOptions[]>[] = []\n            createState.selectedItems.forEach(item => {\n                promises.push(api.getFilters(item.tag))\n                promises.push(api.flipFilters(item.tag))\n            })\n            return Promise.all(promises).then(filters => {\n                let groupedFilters: FilterOptions[][] = []\n                for (let i = 0; i < filters.length; i += 2) {\n                    groupedFilters.push([...filters[i], ...filters[i + 1]])\n                }\n\n                const intersect2 = (a: FilterOptions[], b: FilterOptions[]) => a.filter(x => b.some(y => y.name === x.name))\n                const intersect = (arrOfArrays: FilterOptions[][]) =>\n                    arrOfArrays[1] === undefined ? arrOfArrays[0] : intersect([intersect2(arrOfArrays[0], arrOfArrays[1]), ...arrOfArrays.slice(2)])\n\n                var intersecting = intersect(groupedFilters)\n                setFilters(intersecting)\n\n                return intersecting\n            })\n        }\n    }\n\n    function fillLastUsedItems() {\n        let lastUsedItems = JSON.parse(sessionStorage.getItem('lastUsedItemsForNewRestrictions') || '[]')\n        setCreateState({ ...createState, selectedItems: lastUsedItems })\n    }\n\n    let getButtonVariant = (range: string): string => {\n        return range === createState.type ? 'primary' : 'secondary'\n    }\n\n    function getEmptyLabel() {\n        if (!createState.selectedItems || createState.selectedItems.length === 0) {\n            return 'No matching filter found.'\n        }\n        if (createState.selectedItems && createState.selectedItems.length === 1) {\n            return 'No matching filter found. Maybe the filter you are looking is not available for your selected item?'\n        }\n        return 'No matching filter found. Maybe the filter you are looking is not available for all your selected items?'\n    }\n\n    return (\n        <div>\n            <ToggleButtonGroup\n                style={{ maxWidth: '200px', marginBottom: '5px' }}\n                type=\"radio\"\n                name=\"options\"\n                value={createState.type}\n                onChange={newType => {\n                    setCreateState({ ...createState, type: newType })\n                }}\n            >\n                <ToggleButton id=\"newRestrictionWhitelistToggleButton\" value={'blacklist'} variant={getButtonVariant('blacklist')} size=\"sm\">\n                    Blacklist\n                </ToggleButton>\n                <ToggleButton id=\"newRestrictionBlacklistToggleButton\" value={'whitelist'} variant={getButtonVariant('whitelist')} size=\"sm\">\n                    Whitelist\n                </ToggleButton>\n            </ToggleButtonGroup>\n            <div className={styles.newRestrictionSearchbarContainer}>\n                <ApiSearchField\n                    multiple\n                    className={styles.multiSearch}\n                    onChange={items => {\n                        setCreateState({\n                            ...createState,\n                            selectedItems: items.map(item => {\n                                return {\n                                    tag: item.id,\n                                    name: item.dataItem.name,\n                                    iconUrl: item.dataItem.iconUrl\n                                }\n                            })\n                        })\n                    }}\n                    searchFunction={api.itemSearch}\n                    selected={createState.selectedItems?.map(item => {\n                        return {\n                            dataItem: {\n                                name: item.name,\n                                iconUrl: item.iconUrl\n                            },\n                            label: item.name,\n                            id: item.tag\n                        } as unknown as SearchResultItem\n                    })}\n                />\n                {showFillLastUsedItems && <Button onClick={fillLastUsedItems}>Last used Items</Button>}\n            </div>\n            <ItemFilter\n                filters={filters}\n                forceOpen={true}\n                onFilterChange={filter => {\n                    setCreateState({ ...createState, itemFilter: filter })\n                }}\n                defaultFilter={createState.itemFilter}\n                emptyLabel={getEmptyLabel()}\n                ignoreURL={true}\n                autoSelect={false}\n                disableLastUsedFilter={true}\n                onIsValidChange={setIsFilterValid}\n            />\n            <TagSelect\n                defaultTags={createState.tags || []}\n                onTagsChange={tags => {\n                    setCreateState({ ...createState, tags: tags })\n                }}\n            />\n            <span style={{ display: 'grid', gridTemplateColumns: 'max(20%,100px) max(10%,80px) auto max(20%,100px)', gap: 10 }}>\n                <Button\n                    variant=\"success\"\n                    onClick={() => {\n                        sessionStorage.setItem('lastUsedItemsForNewRestrictions', JSON.stringify(createState.selectedItems))\n                        props.onSaveRestrictions(\n                            (createState.selectedItems || [null]).map(item => {\n                                return {\n                                    type: createState.type,\n                                    itemFilter: createState.itemFilter,\n                                    item: item,\n                                    tags: createState.tags\n                                }\n                            })\n                        )\n                    }}\n                    disabled={!isFilterValid}\n                >\n                    Save new restriction\n                </Button>\n                <Button variant=\"danger\" onClick={props.onCancel}>\n                    Cancel\n                </Button>\n                <div />\n                <div className={styles.copyButtonContainer}>\n                    <CopyButton\n                        copyValue={true ? window.location.href.split('?')[0] + '?prefillRestriction=' + JSON.stringify(createState) : ''}\n                        successMessage={<span>Copied Restriction Link</span>}\n                    />\n                </div>\n            </span>\n        </div>\n    )\n}\n\nexport default NewRestriction\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;AARA;;;;;;;;;AAuBA,SAAS,eAAe,KAAY;;IAChC,IAAI,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B;QACjE,MAAM,MAAM,kBAAkB,GAAG,MAAM,kBAAkB,CAAC,IAAI,GAAG;QACjE,YAAY,MAAM,kBAAkB,GAAG,MAAM,kBAAkB,CAAC,UAAU,GAAG;QAC7E,eAAe,MAAM,kBAAkB,GAAG,MAAM,kBAAkB,CAAC,aAAa,GAAG;IACvF;IACA,IAAI,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,IAAI,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IACxD,IAAI,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,eAAe,OAAO,CAAC,uCAAuC;IAE/H,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACN;QACJ;mCAAG;QAAC,YAAY,aAAa;KAAC;IAE9B,SAAS;QACL,IAAI,CAAC,YAAY,aAAa,IAAI,YAAY,aAAa,EAAE,UAAU,GAAG;YACtE,OAAO,QAAQ,GAAG,CAAC;gBACf,oHAAA,CAAA,UAAG,CAAC,UAAU,CAAC,YAAY,aAAa,EAAE,WAAW,IAAI,YAAY,aAAa,CAAC,EAAE,CAAC,GAAG,GAAG;gBAC5F,oHAAA,CAAA,UAAG,CAAC,WAAW,CAAC,YAAY,aAAa,EAAE,WAAW,IAAI,YAAY,aAAa,CAAC,EAAE,CAAC,GAAG,GAAG;aAChG,EAAE,IAAI,CAAC,CAAA;gBACJ,IAAI,SAAS;uBAAK,OAAO,CAAC,EAAE,IAAI,EAAE;uBAAO,OAAO,CAAC,EAAE,IAAI,EAAE;iBAAE;gBAC3D,WAAW;gBACX,OAAO;YACX;QACJ,OAAO;YACH,IAAI,WAAuC,EAAE;YAC7C,YAAY,aAAa,CAAC,OAAO,CAAC,CAAA;gBAC9B,SAAS,IAAI,CAAC,oHAAA,CAAA,UAAG,CAAC,UAAU,CAAC,KAAK,GAAG;gBACrC,SAAS,IAAI,CAAC,oHAAA,CAAA,UAAG,CAAC,WAAW,CAAC,KAAK,GAAG;YAC1C;YACA,OAAO,QAAQ,GAAG,CAAC,UAAU,IAAI,CAAC,CAAA;gBAC9B,IAAI,iBAAoC,EAAE;gBAC1C,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,KAAK,EAAG;oBACxC,eAAe,IAAI,CAAC;2BAAI,OAAO,CAAC,EAAE;2BAAK,OAAO,CAAC,IAAI,EAAE;qBAAC;gBAC1D;gBAEA,MAAM,aAAa,CAAC,GAAoB,IAAuB,EAAE,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,EAAE,IAAI;gBAC1G,MAAM,YAAY,CAAC,cACf,WAAW,CAAC,EAAE,KAAK,YAAY,WAAW,CAAC,EAAE,GAAG,UAAU;wBAAC,WAAW,WAAW,CAAC,EAAE,EAAE,WAAW,CAAC,EAAE;2BAAM,YAAY,KAAK,CAAC;qBAAG;gBAEnI,IAAI,eAAe,UAAU;gBAC7B,WAAW;gBAEX,OAAO;YACX;QACJ;IACJ;IAEA,SAAS;QACL,IAAI,gBAAgB,KAAK,KAAK,CAAC,eAAe,OAAO,CAAC,sCAAsC;QAC5F,eAAe;YAAE,GAAG,WAAW;YAAE,eAAe;QAAc;IAClE;IAEA,IAAI,mBAAmB,CAAC;QACpB,OAAO,UAAU,YAAY,IAAI,GAAG,YAAY;IACpD;IAEA,SAAS;QACL,IAAI,CAAC,YAAY,aAAa,IAAI,YAAY,aAAa,CAAC,MAAM,KAAK,GAAG;YACtE,OAAO;QACX;QACA,IAAI,YAAY,aAAa,IAAI,YAAY,aAAa,CAAC,MAAM,KAAK,GAAG;YACrE,OAAO;QACX;QACA,OAAO;IACX;IAEA,qBACI,6LAAC;;0BACG,6LAAC,iNAAA,CAAA,oBAAiB;gBACd,OAAO;oBAAE,UAAU;oBAAS,cAAc;gBAAM;gBAChD,MAAK;gBACL,MAAK;gBACL,OAAO,YAAY,IAAI;gBACvB,UAAU,CAAA;oBACN,eAAe;wBAAE,GAAG,WAAW;wBAAE,MAAM;oBAAQ;gBACnD;;kCAEA,6LAAC,uMAAA,CAAA,eAAY;wBAAC,IAAG;wBAAsC,OAAO;wBAAa,SAAS,iBAAiB;wBAAc,MAAK;kCAAK;;;;;;kCAG7H,6LAAC,uMAAA,CAAA,eAAY;wBAAC,IAAG;wBAAsC,OAAO;wBAAa,SAAS,iBAAiB;wBAAc,MAAK;kCAAK;;;;;;;;;;;;0BAIjI,6LAAC;gBAAI,WAAW,+LAAA,CAAA,UAAM,CAAC,gCAAgC;;kCACnD,6LAAC,0IAAA,CAAA,UAAc;wBACX,QAAQ;wBACR,WAAW,+LAAA,CAAA,UAAM,CAAC,WAAW;wBAC7B,UAAU,CAAA;4BACN,eAAe;gCACX,GAAG,WAAW;gCACd,eAAe,MAAM,GAAG,CAAC,CAAA;oCACrB,OAAO;wCACH,KAAK,KAAK,EAAE;wCACZ,MAAM,KAAK,QAAQ,CAAC,IAAI;wCACxB,SAAS,KAAK,QAAQ,CAAC,OAAO;oCAClC;gCACJ;4BACJ;wBACJ;wBACA,gBAAgB,oHAAA,CAAA,UAAG,CAAC,UAAU;wBAC9B,UAAU,YAAY,aAAa,EAAE,IAAI,CAAA;4BACrC,OAAO;gCACH,UAAU;oCACN,MAAM,KAAK,IAAI;oCACf,SAAS,KAAK,OAAO;gCACzB;gCACA,OAAO,KAAK,IAAI;gCAChB,IAAI,KAAK,GAAG;4BAChB;wBACJ;;;;;;oBAEH,uCAAyB,6LAAC,2LAAA,CAAA,SAAM;wBAAC,SAAS;kCAAmB;;;;;;;;;;;;0BAElE,6LAAC,0IAAA,CAAA,UAAU;gBACP,SAAS;gBACT,WAAW;gBACX,gBAAgB,CAAA;oBACZ,eAAe;wBAAE,GAAG,WAAW;wBAAE,YAAY;oBAAO;gBACxD;gBACA,eAAe,YAAY,UAAU;gBACrC,YAAY;gBACZ,WAAW;gBACX,YAAY;gBACZ,uBAAuB;gBACvB,iBAAiB;;;;;;0BAErB,6LAAC,0KAAA,CAAA,UAAS;gBACN,aAAa,YAAY,IAAI,IAAI,EAAE;gBACnC,cAAc,CAAA;oBACV,eAAe;wBAAE,GAAG,WAAW;wBAAE,MAAM;oBAAK;gBAChD;;;;;;0BAEJ,6LAAC;gBAAK,OAAO;oBAAE,SAAS;oBAAQ,qBAAqB;oBAAoD,KAAK;gBAAG;;kCAC7G,6LAAC,2LAAA,CAAA,SAAM;wBACH,SAAQ;wBACR,SAAS;4BACL,eAAe,OAAO,CAAC,mCAAmC,KAAK,SAAS,CAAC,YAAY,aAAa;4BAClG,MAAM,kBAAkB,CACpB,CAAC,YAAY,aAAa,IAAI;gCAAC;6BAAK,EAAE,GAAG,CAAC,CAAA;gCACtC,OAAO;oCACH,MAAM,YAAY,IAAI;oCACtB,YAAY,YAAY,UAAU;oCAClC,MAAM;oCACN,MAAM,YAAY,IAAI;gCAC1B;4BACJ;wBAER;wBACA,UAAU,CAAC;kCACd;;;;;;kCAGD,6LAAC,2LAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAS,SAAS,MAAM,QAAQ;kCAAE;;;;;;kCAGlD,6LAAC;;;;;kCACD,6LAAC;wBAAI,WAAW,+LAAA,CAAA,UAAM,CAAC,mBAAmB;kCACtC,cAAA,6LAAC,0IAAA,CAAA,aAAU;4BACP,WAAW,uCAAO,OAAO,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG,yBAAyB,KAAK,SAAS,CAAC;4BAC/F,8BAAgB,6LAAC;0CAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM9C;GAvKS;KAAA;uCAyKM", "debugId": null}}, {"offset": {"line": 3827, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/components/Flipper/FlipRestrictionList/RestrictionListEntry/FlipRestrictionEntry.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"cancelEditButton\": \"FlipRestrictionEntry-module__w-Ivaq__cancelEditButton\",\n  \"editButton\": \"FlipRestrictionEntry-module__w-Ivaq__editButton\",\n  \"label\": \"FlipRestrictionEntry-module__w-Ivaq__label\",\n  \"openInNewTab\": \"FlipRestrictionEntry-module__w-Ivaq__openInNewTab\",\n  \"removeFilter\": \"FlipRestrictionEntry-module__w-Ivaq__removeFilter\",\n  \"restriction\": \"FlipRestrictionEntry-module__w-Ivaq__restriction\",\n  \"restrictionContainer\": \"FlipRestrictionEntry-module__w-Ivaq__restrictionContainer\",\n  \"restrictionMarkedAsEdit\": \"FlipRestrictionEntry-module__w-Ivaq__restrictionMarkedAsEdit\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 3843, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/Flipper/FlipRestrictionList/RestrictionListEntry/FlipRestrictionListEntry.tsx"], "sourcesContent": ["import { Bad<PERSON>, Card, Image, ToggleButton, ToggleButtonGroup } from 'react-bootstrap'\nimport styles from './FlipRestrictionEntry.module.css'\nimport ApiSearchField from '../../../Search/ApiSearchField'\nimport { getStyleForTier } from '../../../../utils/Formatter'\nimport Tooltip from '../../../Tooltip/Tooltip'\nimport SaveIcon from '@mui/icons-material/Save'\nimport ItemFilterPropertiesDisplay from '../../../ItemFilter/ItemFilterPropertiesDisplay'\nimport api from '../../../../api/ApiHelper'\nimport EditIcon from '@mui/icons-material/Edit'\nimport DuplicateIcon from '@mui/icons-material/ControlPointDuplicate'\nimport DeleteIcon from '@mui/icons-material/Delete'\nimport { MouseEventHandler } from 'react'\nimport OpenInNewIcon from '@mui/icons-material/OpenInNew'\nimport { btoaUnicode } from '../../../../utils/Base64Utils'\n\ninterface Props {\n    restriction: FlipRestriction\n    isAnyRestrictionInEditMode: boolean\n    onSaveClick(): void\n    onEditClick(): void\n    onCreateDuplicateClick(): void\n    onDeleteClick(): void\n    onRemoveFilterClick(restriction: FlipRestriction): void\n    onRestrictionChange(restriction: FlipRestriction): void\n    onContextMenu: MouseEventHandler<HTMLElement> | undefined\n    style: React.CSSProperties\n}\n\nexport default function FlipRestrictionListEntry(props: Props) {\n    return (\n        <div id={props.restriction.itemKey} className={styles.restrictionContainer} style={props.style} onContextMenu={props.onContextMenu}>\n            <Card className={`${styles.restriction} ${props.restriction.isEdited ? styles.restrictionMarkedAsEdit : ''}`}>\n                <Card.Header\n                    style={{\n                        padding: '10px',\n                        display: 'flex',\n                        justifyContent: 'space-between'\n                    }}\n                >\n                    {props.restriction.isEdited ? (\n                        <ToggleButtonGroup\n                            style={{ maxWidth: '200px', marginBottom: '5px' }}\n                            type=\"radio\"\n                            name=\"options\"\n                            value={props.restriction.type}\n                            onChange={newValue => {\n                                let newRestriction = { ...props.restriction }\n                                newRestriction.type = newValue as 'blacklist' | 'whitelist'\n                                props.onRestrictionChange(newRestriction)\n                            }}\n                        >\n                            <ToggleButton\n                                id={'blacklistToggleButton-' + props.restriction.itemKey}\n                                value={'blacklist'}\n                                variant={props.restriction.type === 'blacklist' ? 'primary' : 'secondary'}\n                                size=\"sm\"\n                            >\n                                Blacklist\n                            </ToggleButton>\n                            <ToggleButton\n                                id={'whitelistToggleButton-' + props.restriction.itemKey}\n                                value={'whitelist'}\n                                variant={props.restriction.type === 'whitelist' ? 'primary' : 'secondary'}\n                                size=\"sm\"\n                            >\n                                Whitelist\n                            </ToggleButton>\n                        </ToggleButtonGroup>\n                    ) : (\n                        <Badge style={{ marginRight: '10px' }} bg={props.restriction.type === 'blacklist' ? 'danger' : 'success'}>\n                            {props.restriction.type.toUpperCase()}\n                        </Badge>\n                    )}\n                    <div\n                        style={{\n                            width: '-webkit-fill-available',\n                            float: 'left'\n                        }}\n                    >\n                        {props.restriction.isEdited ? (\n                            <ApiSearchField\n                                multiple={false}\n                                className={styles.multiSearch}\n                                onChange={(items, text) => {\n                                    // If the user only deletes part of the text, we don't want to remove the item (and therefore rerender the entry)\n                                    if (items.length === 0 && text != '') return\n\n                                    let newItem: Item | undefined =\n                                        items && items.length > 0\n                                            ? {\n                                                  tag: items[0].id,\n                                                  name: items[0].dataItem.name,\n                                                  iconUrl: items[0].dataItem.iconUrl\n                                              }\n                                            : undefined\n                                    let newRestriction = { ...props.restriction }\n                                    newRestriction.item = newItem\n                                    props.onRestrictionChange(newRestriction)\n                                }}\n                                searchFunction={api.itemSearch}\n                                defaultSelected={\n                                    props.restriction.item\n                                        ? [\n                                              {\n                                                  dataItem: {\n                                                      iconUrl: api.getItemImageUrl(props.restriction.item) || '',\n                                                      name: props.restriction.item.name || '-'\n                                                  },\n                                                  id: props.restriction.item.tag || '',\n                                                  label: props.restriction.item.name || '-'\n                                              } as unknown as SearchResultItem\n                                          ]\n                                        : undefined\n                                }\n                            />\n                        ) : (\n                            props.restriction.item && (\n                                <>\n                                    <Image\n                                        crossOrigin=\"anonymous\"\n                                        src={props.restriction.item?.iconUrl || ''}\n                                        height=\"24\"\n                                        width=\"24\"\n                                        alt=\"\"\n                                        style={{\n                                            marginRight: '5px'\n                                        }}\n                                        loading=\"lazy\"\n                                    />\n                                    <span style={getStyleForTier(props.restriction.item?.tier)}>{props.restriction.item?.name}</span>\n                                </>\n                            )\n                        )}\n                    </div>\n                    <div\n                        style={{\n                            display: 'flex'\n                        }}\n                    >\n                        {props.restriction.disabled && <Badge bg=\"danger\">Disabled</Badge>}\n                        {props.restriction.item ? (\n                            <div className={styles.openInNewTab}>\n                                <Tooltip\n                                    type=\"hover\"\n                                    content={\n                                        <a\n                                            href={`/item/${props.restriction.item.tag}${\n                                                props.restriction.itemFilter ? `?itemFilter=${btoaUnicode(JSON.stringify(props.restriction.itemFilter))}` : ''\n                                            }`}\n                                            target=\"_blank\"\n                                            rel=\"noreferrer\"\n                                        >\n                                            <OpenInNewIcon />\n                                        </a>\n                                    }\n                                    tooltipContent={<p>Open in new tab</p>}\n                                />\n                            </div>\n                        ) : null}\n                        {props.restriction.isEdited ? (\n                            <div\n                                className={styles.cancelEditButton}\n                                onClick={() => {\n                                    props.onSaveClick()\n                                }}\n                            >\n                                <Tooltip type=\"hover\" content={<SaveIcon />} tooltipContent={<p>Save</p>} />\n                            </div>\n                        ) : (\n                            <div className={styles.editButton} onClick={props.onEditClick}>\n                                <Tooltip type=\"hover\" content={<EditIcon />} tooltipContent={<p>Edit restriction</p>} />\n                            </div>\n                        )}\n                        {!props.isAnyRestrictionInEditMode ? (\n                            <>\n                                <div className={styles.removeFilter} onClick={props.onCreateDuplicateClick}>\n                                    <Tooltip type=\"hover\" content={<DuplicateIcon />} tooltipContent={<p>Create duplicate</p>} />\n                                </div>\n                                <div className={styles.removeFilter} onClick={props.onDeleteClick}>\n                                    <Tooltip type=\"hover\" content={<DeleteIcon color=\"error\" />} tooltipContent={<p>Remove restriction</p>} />\n                                </div>\n                            </>\n                        ) : null}\n                    </div>\n                </Card.Header>\n                <Card.Body>\n                    {props.restriction.itemFilter && Object.keys(props.restriction.itemFilter).length > 0 ? (\n                        <ItemFilterPropertiesDisplay\n                            key={props.restriction.itemKey}\n                            filter={props.restriction.itemFilter}\n                            isEditable={props.restriction.isEdited}\n                            onAfterEdit={filter => {\n                                let newRestriction = { ...props.restriction }\n                                newRestriction.itemFilter = filter\n                                props.onRestrictionChange(newRestriction)\n                            }}\n                        />\n                    ) : props.restriction.item?.tag.startsWith('STARRED_') ? (\n                        <p>\n                            Note that you will need to add the <code>Fragged</code> filter to select only fragged items. This currently selects both fragged and\n                            not fragged.\n                        </p>\n                    ) : null}\n                    <div className=\"ellipse\">\n                        {props.restriction.tags?.map(tag => (\n                            <Badge\n                                key={tag}\n                                bg=\"dark\"\n                                style={{\n                                    marginRight: '5px'\n                                }}\n                            >\n                                {tag}\n                            </Badge>\n                        ))}\n                    </div>\n                </Card.Body>\n            </Card>\n        </div>\n    )\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;;;;;;;;;;;;;;;AAee,SAAS,yBAAyB,KAAY;IACzD,qBACI,6LAAC;QAAI,IAAI,MAAM,WAAW,CAAC,OAAO;QAAE,WAAW,2MAAA,CAAA,UAAM,CAAC,oBAAoB;QAAE,OAAO,MAAM,KAAK;QAAE,eAAe,MAAM,aAAa;kBAC9H,cAAA,6LAAC,uLAAA,CAAA,OAAI;YAAC,WAAW,GAAG,2MAAA,CAAA,UAAM,CAAC,WAAW,CAAC,CAAC,EAAE,MAAM,WAAW,CAAC,QAAQ,GAAG,2MAAA,CAAA,UAAM,CAAC,uBAAuB,GAAG,IAAI;;8BACxG,6LAAC,uLAAA,CAAA,OAAI,CAAC,MAAM;oBACR,OAAO;wBACH,SAAS;wBACT,SAAS;wBACT,gBAAgB;oBACpB;;wBAEC,MAAM,WAAW,CAAC,QAAQ,iBACvB,6LAAC,iNAAA,CAAA,oBAAiB;4BACd,OAAO;gCAAE,UAAU;gCAAS,cAAc;4BAAM;4BAChD,MAAK;4BACL,MAAK;4BACL,OAAO,MAAM,WAAW,CAAC,IAAI;4BAC7B,UAAU,CAAA;gCACN,IAAI,iBAAiB;oCAAE,GAAG,MAAM,WAAW;gCAAC;gCAC5C,eAAe,IAAI,GAAG;gCACtB,MAAM,mBAAmB,CAAC;4BAC9B;;8CAEA,6LAAC,uMAAA,CAAA,eAAY;oCACT,IAAI,2BAA2B,MAAM,WAAW,CAAC,OAAO;oCACxD,OAAO;oCACP,SAAS,MAAM,WAAW,CAAC,IAAI,KAAK,cAAc,YAAY;oCAC9D,MAAK;8CACR;;;;;;8CAGD,6LAAC,uMAAA,CAAA,eAAY;oCACT,IAAI,2BAA2B,MAAM,WAAW,CAAC,OAAO;oCACxD,OAAO;oCACP,SAAS,MAAM,WAAW,CAAC,IAAI,KAAK,cAAc,YAAY;oCAC9D,MAAK;8CACR;;;;;;;;;;;iDAKL,6LAAC,yLAAA,CAAA,QAAK;4BAAC,OAAO;gCAAE,aAAa;4BAAO;4BAAG,IAAI,MAAM,WAAW,CAAC,IAAI,KAAK,cAAc,WAAW;sCAC1F,MAAM,WAAW,CAAC,IAAI,CAAC,WAAW;;;;;;sCAG3C,6LAAC;4BACG,OAAO;gCACH,OAAO;gCACP,OAAO;4BACX;sCAEC,MAAM,WAAW,CAAC,QAAQ,iBACvB,6LAAC,0IAAA,CAAA,UAAc;gCACX,UAAU;gCACV,WAAW,2MAAA,CAAA,UAAM,CAAC,WAAW;gCAC7B,UAAU,CAAC,OAAO;oCACd,iHAAiH;oCACjH,IAAI,MAAM,MAAM,KAAK,KAAK,QAAQ,IAAI;oCAEtC,IAAI,UACA,SAAS,MAAM,MAAM,GAAG,IAClB;wCACI,KAAK,KAAK,CAAC,EAAE,CAAC,EAAE;wCAChB,MAAM,KAAK,CAAC,EAAE,CAAC,QAAQ,CAAC,IAAI;wCAC5B,SAAS,KAAK,CAAC,EAAE,CAAC,QAAQ,CAAC,OAAO;oCACtC,IACA;oCACV,IAAI,iBAAiB;wCAAE,GAAG,MAAM,WAAW;oCAAC;oCAC5C,eAAe,IAAI,GAAG;oCACtB,MAAM,mBAAmB,CAAC;gCAC9B;gCACA,gBAAgB,oHAAA,CAAA,UAAG,CAAC,UAAU;gCAC9B,iBACI,MAAM,WAAW,CAAC,IAAI,GAChB;oCACI;wCACI,UAAU;4CACN,SAAS,oHAAA,CAAA,UAAG,CAAC,eAAe,CAAC,MAAM,WAAW,CAAC,IAAI,KAAK;4CACxD,MAAM,MAAM,WAAW,CAAC,IAAI,CAAC,IAAI,IAAI;wCACzC;wCACA,IAAI,MAAM,WAAW,CAAC,IAAI,CAAC,GAAG,IAAI;wCAClC,OAAO,MAAM,WAAW,CAAC,IAAI,CAAC,IAAI,IAAI;oCAC1C;iCACH,GACD;;;;;uCAId,MAAM,WAAW,CAAC,IAAI,kBAClB;;kDACI,6LAAC,yLAAA,CAAA,QAAK;wCACF,aAAY;wCACZ,KAAK,MAAM,WAAW,CAAC,IAAI,EAAE,WAAW;wCACxC,QAAO;wCACP,OAAM;wCACN,KAAI;wCACJ,OAAO;4CACH,aAAa;wCACjB;wCACA,SAAQ;;;;;;kDAEZ,6LAAC;wCAAK,OAAO,CAAA,GAAA,sHAAA,CAAA,kBAAe,AAAD,EAAE,MAAM,WAAW,CAAC,IAAI,EAAE;kDAAQ,MAAM,WAAW,CAAC,IAAI,EAAE;;;;;;;;;;;;;sCAKrG,6LAAC;4BACG,OAAO;gCACH,SAAS;4BACb;;gCAEC,MAAM,WAAW,CAAC,QAAQ,kBAAI,6LAAC,yLAAA,CAAA,QAAK;oCAAC,IAAG;8CAAS;;;;;;gCACjD,MAAM,WAAW,CAAC,IAAI,iBACnB,6LAAC;oCAAI,WAAW,2MAAA,CAAA,UAAM,CAAC,YAAY;8CAC/B,cAAA,6LAAC,oIAAA,CAAA,UAAO;wCACJ,MAAK;wCACL,uBACI,6LAAC;4CACG,MAAM,CAAC,MAAM,EAAE,MAAM,WAAW,CAAC,IAAI,CAAC,GAAG,GACrC,MAAM,WAAW,CAAC,UAAU,GAAG,CAAC,YAAY,EAAE,CAAA,GAAA,wHAAA,CAAA,cAAW,AAAD,EAAE,KAAK,SAAS,CAAC,MAAM,WAAW,CAAC,UAAU,IAAI,GAAG,IAC9G;4CACF,QAAO;4CACP,KAAI;sDAEJ,cAAA,6LAAC,iKAAA,CAAA,UAAa;;;;;;;;;;wCAGtB,8BAAgB,6LAAC;sDAAE;;;;;;;;;;;;;;;2CAG3B;gCACH,MAAM,WAAW,CAAC,QAAQ,iBACvB,6LAAC;oCACG,WAAW,2MAAA,CAAA,UAAM,CAAC,gBAAgB;oCAClC,SAAS;wCACL,MAAM,WAAW;oCACrB;8CAEA,cAAA,6LAAC,oIAAA,CAAA,UAAO;wCAAC,MAAK;wCAAQ,uBAAS,6LAAC,4JAAA,CAAA,UAAQ;;;;;wCAAK,8BAAgB,6LAAC;sDAAE;;;;;;;;;;;;;;;yDAGpE,6LAAC;oCAAI,WAAW,2MAAA,CAAA,UAAM,CAAC,UAAU;oCAAE,SAAS,MAAM,WAAW;8CACzD,cAAA,6LAAC,oIAAA,CAAA,UAAO;wCAAC,MAAK;wCAAQ,uBAAS,6LAAC,4JAAA,CAAA,UAAQ;;;;;wCAAK,8BAAgB,6LAAC;sDAAE;;;;;;;;;;;;;;;;gCAGvE,CAAC,MAAM,0BAA0B,iBAC9B;;sDACI,6LAAC;4CAAI,WAAW,2MAAA,CAAA,UAAM,CAAC,YAAY;4CAAE,SAAS,MAAM,sBAAsB;sDACtE,cAAA,6LAAC,oIAAA,CAAA,UAAO;gDAAC,MAAK;gDAAQ,uBAAS,6LAAC,6KAAA,CAAA,UAAa;;;;;gDAAK,8BAAgB,6LAAC;8DAAE;;;;;;;;;;;;;;;;sDAEzE,6LAAC;4CAAI,WAAW,2MAAA,CAAA,UAAM,CAAC,YAAY;4CAAE,SAAS,MAAM,aAAa;sDAC7D,cAAA,6LAAC,oIAAA,CAAA,UAAO;gDAAC,MAAK;gDAAQ,uBAAS,6LAAC,8JAAA,CAAA,UAAU;oDAAC,OAAM;;;;;;gDAAY,8BAAgB,6LAAC;8DAAE;;;;;;;;;;;;;;;;;mDAGxF;;;;;;;;;;;;;8BAGZ,6LAAC,uLAAA,CAAA,OAAI,CAAC,IAAI;;wBACL,MAAM,WAAW,CAAC,UAAU,IAAI,OAAO,IAAI,CAAC,MAAM,WAAW,CAAC,UAAU,EAAE,MAAM,GAAG,kBAChF,6LAAC,2JAAA,CAAA,UAA2B;4BAExB,QAAQ,MAAM,WAAW,CAAC,UAAU;4BACpC,YAAY,MAAM,WAAW,CAAC,QAAQ;4BACtC,aAAa,CAAA;gCACT,IAAI,iBAAiB;oCAAE,GAAG,MAAM,WAAW;gCAAC;gCAC5C,eAAe,UAAU,GAAG;gCAC5B,MAAM,mBAAmB,CAAC;4BAC9B;2BAPK,MAAM,WAAW,CAAC,OAAO;;;;mCASlC,MAAM,WAAW,CAAC,IAAI,EAAE,IAAI,WAAW,4BACvC,6LAAC;;gCAAE;8CACoC,6LAAC;8CAAK;;;;;;gCAAc;;;;;;mCAG3D;sCACJ,6LAAC;4BAAI,WAAU;sCACV,MAAM,WAAW,CAAC,IAAI,EAAE,IAAI,CAAA,oBACzB,6LAAC,yLAAA,CAAA,QAAK;oCAEF,IAAG;oCACH,OAAO;wCACH,aAAa;oCACjB;8CAEC;mCANI;;;;;;;;;;;;;;;;;;;;;;;;;;;AAcrC;KAhMwB", "debugId": null}}, {"offset": {"line": 4273, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/Flipper/FlipRestrictionList/FlipRestrictionList.tsx"], "sourcesContent": ["'use client'\nimport React, { useEffect, useRef, useState } from 'react'\nimport { Button, Form, Modal } from 'react-bootstrap'\nimport api from '../../../api/ApiHelper'\nimport { camelCaseToSentenceCase } from '../../../utils/Formatter'\nimport { getCleanRestrictionsForApi, getSettingsObject, RESTRICTIONS_SETTINGS_KEY, setSetting } from '../../../utils/SettingsUtils'\nimport Refresh from '@mui/icons-material/Refresh'\nimport DeleteIcon from '@mui/icons-material/Delete'\nimport styles from './FlipRestrictionList.module.css'\nimport EditRestriction, { UpdateState } from './EditRestriction/EditRestriction'\nimport NewRestriction, { RestrictionCreateState } from './NewRestriction/NewRestriction'\nimport { CUSTOM_EVENTS } from '../../../api/ApiTypes.d'\nimport { toast } from 'react-toastify'\nimport AutoSizer from 'react-virtualized-auto-sizer'\nimport { VariableSizeGrid as Grid } from 'react-window'\nimport { v4 as generateUUID } from 'uuid'\nimport FlipRestrictionListEntry from './RestrictionListEntry/FlipRestrictionListEntry'\nimport { Item, Menu, useContextMenu } from 'react-contexify'\nimport BlockIcon from '@mui/icons-material/Block'\nimport CheckIcon from '@mui/icons-material/CheckCircle'\nimport ListIcon from '@mui/icons-material/ListAlt'\nimport { Check } from '@mui/icons-material'\n\ninterface Props {\n    onRestrictionsChange(restrictions: FlipRestriction[], type: 'whitelist' | 'blacklist'): void\n    prefillRestriction?: RestrictionCreateState\n}\n\nconst RESTRICTION_CONTEXT_MENU_ID = 'restriction-entry-context-menu'\nconst RESTRICTION_CONTEXT_CURRENT_SHOING_MENU_ID = 'restriction-entry-context-menu-current-showing'\n\nfunction FlipRestrictionList(props: Props) {\n    let [isAddNewFlipperExtended, setIsNewFlipperExtended] = useState(props.prefillRestriction !== undefined)\n    let [restrictions, setRestrictions] = useState<FlipRestriction[]>(() => getInitialFlipRestrictions())\n    let [restrictionInEditMode, setRestrictionsInEditMode] = useState<FlipRestriction[]>([])\n    let [showDeleteRestrictionsDialog, setShowDeleteRestrictionsDialog] = useState(false)\n    let [searchText, setSearchText] = useState('')\n    let [sortByName, setSortByName] = useState(false)\n    let [isSSR, setIsSSR] = useState(true)\n    let searchFieldRef = useRef<HTMLInputElement>(null)\n    let listRef = useRef(null)\n\n    const { show } = useContextMenu({\n        id: RESTRICTION_CONTEXT_MENU_ID\n    })\n    const { show: showEditShowingRestrictionsMenu } = useContextMenu({\n        id: RESTRICTION_CONTEXT_CURRENT_SHOING_MENU_ID\n    })\n\n    useEffect(() => {\n        function onControlF(event: KeyboardEvent) {\n            if (event.ctrlKey && event.key === 'f' && searchFieldRef.current) {\n                event.preventDefault()\n                searchFieldRef.current.focus()\n            }\n        }\n\n        window.addEventListener('keydown', onControlF)\n\n        setIsSSR(false)\n\n        return () => {\n            window.removeEventListener('keydown', onControlF)\n        }\n    }, [])\n\n    function getInitialFlipRestrictions() {\n        let restrictions = getSettingsObject<FlipRestriction[]>(RESTRICTIONS_SETTINGS_KEY, [])\n        restrictions.forEach(restriction => {\n            if (restriction.item && restriction.item.tag) {\n                restriction.item.iconUrl = api.getItemImageUrl(restriction.item)\n            }\n            restriction.itemKey = generateUUID()\n        })\n        return restrictions\n    }\n\n    function addNewRestriction(newRestrictions: FlipRestriction[] = []) {\n        let restrictionCopies = [...restrictions]\n\n        // Placing the new restrictions at the positions they will be after a reload\n        // => first whitelist entries, then blacklist entries. From oldest to newest\n        newRestrictions.forEach(newRestriction => {\n            newRestriction.itemKey = generateUUID()\n            if (newRestriction.type === 'blacklist') {\n                restrictionCopies.push(newRestriction)\n                return\n            }\n            if (newRestriction.type === 'whitelist') {\n                let firstBlacklist = restrictionCopies.findIndex(element => element.type === 'blacklist')\n                if (firstBlacklist === -1) {\n                    restrictionCopies.push(newRestriction)\n                } else {\n                    restrictionCopies.splice(firstBlacklist, 0, newRestriction)\n                }\n            }\n        })\n\n        setRestrictions(restrictionCopies)\n        setIsNewFlipperExtended(false)\n\n        document.dispatchEvent(new CustomEvent(CUSTOM_EVENTS.FLIP_SETTINGS_CHANGE))\n\n        setSetting(RESTRICTIONS_SETTINGS_KEY, JSON.stringify(getCleanRestrictionsForApi(restrictionCopies)))\n\n        if (props.onRestrictionsChange) {\n            props.onRestrictionsChange(getCleanRestrictionsForApi(restrictionCopies), 'blacklist')\n            props.onRestrictionsChange(getCleanRestrictionsForApi(restrictionCopies), 'whitelist')\n        }\n\n        toast.success('New restriction added')\n    }\n\n    function onNewRestrictionCancel() {\n        setIsNewFlipperExtended(false)\n    }\n\n    function onEditRestrictionCancel() {\n        let newRestrictions = [...restrictions]\n        restrictionInEditMode.forEach(restriction => {\n            newRestrictions[restriction.originalIndex!].isEdited = false\n        })\n        setRestrictions(newRestrictions)\n        setRestrictionsInEditMode([])\n    }\n\n    function addEditedFilter(updateState: UpdateState) {\n        let newRestrictions = [...restrictions]\n        restrictionInEditMode.forEach(restriction => {\n            let toUpdate = newRestrictions[restriction.originalIndex!]\n            Object.keys(updateState.itemFilter || {}).forEach(key => {\n                if (toUpdate.itemFilter && updateState.itemFilter && toUpdate.itemFilter[key] === undefined) {\n                    toUpdate.itemFilter[key] = updateState.itemFilter[key]\n                }\n            })\n            if (updateState.tags) {\n                toUpdate.tags = toUpdate.tags ? [...toUpdate.tags, ...updateState.tags] : updateState.tags\n            }\n            toUpdate.isEdited = false\n            toUpdate.type = restriction.type\n        })\n\n        setSetting(RESTRICTIONS_SETTINGS_KEY, JSON.stringify(getCleanRestrictionsForApi(newRestrictions)))\n        if (props.onRestrictionsChange) {\n            props.onRestrictionsChange(getCleanRestrictionsForApi(newRestrictions), 'blacklist')\n            props.onRestrictionsChange(getCleanRestrictionsForApi(newRestrictions), 'whitelist')\n        }\n\n        setRestrictionsInEditMode([])\n        setRestrictions(newRestrictions)\n        toast.success('Restriction updated')\n        recalculateListHeight()\n    }\n\n    function overrideEditedFilter(updateState: UpdateState) {\n        let newRestrictions = [...restrictions]\n        restrictionInEditMode.forEach(restriction => {\n            let index = restriction.originalIndex!\n            newRestrictions[index].itemFilter = { ...updateState.itemFilter }\n            newRestrictions[index].tags = updateState.tags\n            newRestrictions[index].isEdited = false\n            newRestrictions[index].type = restriction.type\n            if (updateState.selectedItem) newRestrictions[index].item = updateState.selectedItem\n        })\n\n        setSetting(RESTRICTIONS_SETTINGS_KEY, JSON.stringify(getCleanRestrictionsForApi(newRestrictions)))\n        if (props.onRestrictionsChange) {\n            props.onRestrictionsChange(getCleanRestrictionsForApi(newRestrictions), 'blacklist')\n            props.onRestrictionsChange(getCleanRestrictionsForApi(newRestrictions), 'whitelist')\n        }\n        setRestrictionsInEditMode([])\n        setRestrictions(newRestrictions)\n        toast.success('Restriction(s) updated')\n        recalculateListHeight()\n    }\n\n    function removeRestrictionByIndex(restrictions: FlipRestriction[], index: number) {\n        let newRestrictions = [...restrictions]\n        let deletedRestriction = newRestrictions.splice(index, 1)\n\n        setSetting(RESTRICTIONS_SETTINGS_KEY, JSON.stringify(getCleanRestrictionsForApi(newRestrictions)))\n\n        document.dispatchEvent(new CustomEvent(CUSTOM_EVENTS.FLIP_SETTINGS_CHANGE))\n\n        if (props.onRestrictionsChange) {\n            props.onRestrictionsChange(getCleanRestrictionsForApi(newRestrictions), deletedRestriction[0].type)\n        }\n        setRestrictions(newRestrictions)\n        toast.success('Restriction removed')\n        recalculateListHeight()\n    }\n\n    function createDuplicate(restrictions: FlipRestriction[], index: number) {\n        let duplicate = { ...restrictions[index] }\n        let newRestrictions = [...restrictions]\n        newRestrictions.splice(index + 1, 0, duplicate)\n\n        setSetting(RESTRICTIONS_SETTINGS_KEY, JSON.stringify(getCleanRestrictionsForApi(newRestrictions)))\n        if (props.onRestrictionsChange) {\n            props.onRestrictionsChange(getCleanRestrictionsForApi(newRestrictions), 'whitelist')\n            props.onRestrictionsChange(getCleanRestrictionsForApi(newRestrictions), 'blacklist')\n        }\n        setRestrictions(newRestrictions)\n        recalculateListHeight()\n    }\n\n    function saveRestrictionEdit(newRestriction: FlipRestriction) {\n        let newRestrictions = [...restrictions]\n        let newIndexArray = [...restrictionInEditMode]\n\n        newRestrictions[newRestriction.originalIndex!] = { ...newRestriction }\n        newRestrictions[newRestriction.originalIndex!].isEdited = false\n\n        newIndexArray.filter(r => r.itemKey !== newRestriction.itemKey)\n\n        setRestrictionsInEditMode(newIndexArray)\n        setRestrictions(newRestrictions)\n        setSetting(RESTRICTIONS_SETTINGS_KEY, JSON.stringify(getCleanRestrictionsForApi(newRestrictions)))\n        if (props.onRestrictionsChange) {\n            props.onRestrictionsChange(getCleanRestrictionsForApi(newRestrictions), 'blacklist')\n            props.onRestrictionsChange(getCleanRestrictionsForApi(newRestrictions), 'whitelist')\n        }\n        recalculateListHeight()\n    }\n\n    function editRestriction(restrictions: FlipRestriction[], index: number) {\n        let newRestrictions = [...restrictions]\n        let restrictionToEdit = newRestrictions[index]\n        let newRestrictionsInEditMode = [...restrictionInEditMode]\n\n        newRestrictionsInEditMode.push(restrictionToEdit)\n\n        let restrictionsInEditMode: FlipRestriction[] = []\n        newRestrictionsInEditMode.forEach(r => {\n            restrictionsInEditMode.push(r)\n        })\n\n        restrictionToEdit.isEdited = true\n        setRestrictions(newRestrictions)\n        setRestrictionsInEditMode(newRestrictionsInEditMode)\n    }\n\n    function clearRestrictions() {\n        let newRestrictions = getRestrictionsFilteredBySearch(restrictions, true)\n        setRestrictions(newRestrictions)\n        setShowDeleteRestrictionsDialog(false)\n\n        document.dispatchEvent(new CustomEvent(CUSTOM_EVENTS.FLIP_SETTINGS_CHANGE))\n\n        setSetting(RESTRICTIONS_SETTINGS_KEY, JSON.stringify(getCleanRestrictionsForApi(newRestrictions)))\n\n        if (props.onRestrictionsChange) {\n            props.onRestrictionsChange(getCleanRestrictionsForApi(newRestrictions), 'whitelist')\n            props.onRestrictionsChange(getCleanRestrictionsForApi(newRestrictions), 'blacklist')\n        }\n    }\n\n    const debounceSearchFunction = (function () {\n        let timerId\n\n        return searchText => {\n            clearTimeout(timerId)\n            timerId = setTimeout(() => {\n                setSearchText(searchText)\n                recalculateListHeight()\n            }, 1000)\n        }\n    })()\n\n    function refreshItemNames() {\n        let newRestrictions = [...restrictions]\n        let items: Item[] = []\n        newRestrictions.forEach(restriction => {\n            if (restriction.item && restriction.item.tag) {\n                items.push(restriction.item)\n            }\n        })\n        api.getItemNames(items)\n            .then(itemNameMap => {\n                newRestrictions.forEach(restriction => {\n                    if (restriction.item && restriction.item.tag) {\n                        restriction.item.name = itemNameMap[restriction.item.tag]\n                    }\n                })\n                toast.success('Reloaded all item names')\n                setRestrictions(newRestrictions)\n                setSetting(RESTRICTIONS_SETTINGS_KEY, JSON.stringify(getCleanRestrictionsForApi(newRestrictions)))\n                if (props.onRestrictionsChange) {\n                    props.onRestrictionsChange(getCleanRestrictionsForApi(newRestrictions), 'blacklist')\n                    props.onRestrictionsChange(getCleanRestrictionsForApi(newRestrictions), 'whitelist')\n                }\n            })\n            .catch(() => {\n                toast.error('Error reloaded item names')\n            })\n    }\n\n    function recalculateListHeight() {\n        ; (listRef.current as any).resetAfterRowIndex(0, false)\n    }\n\n    function handleContextMenuForRestriction(event) {\n        event.preventDefault()\n        show({ event: event, props: { itemKey: event.currentTarget.id } })\n    }\n\n    function getRestrictionsFilteredBySearch(restrictions: FlipRestriction[], invert = false) {\n\n        let lowerCaseSearchText = searchText.toLowerCase()\n        let filterKind: string | null = null;\n        if (lowerCaseSearchText.includes('kind:blacklist')) {\n            filterKind = 'blacklist';\n            lowerCaseSearchText = lowerCaseSearchText.replace('kind:blacklist', '').trim();\n        }\n        if (lowerCaseSearchText.includes('kind:whitelist')) {\n            filterKind = 'whitelist';\n            lowerCaseSearchText = lowerCaseSearchText.replace('kind:whitelist', '').trim();\n        }\n\n        lowerCaseSearchText = lowerCaseSearchText.replace(/_/g, ' ').replace(/'/g, '')\n\n\n        return restrictions.filter(restriction => {\n\n            if (filterKind && restriction.type !== filterKind) {\n                return invert ? true : false\n            }\n            if (filterKind && !searchText) {\n                let valid = restriction.type === filterKind\n                return invert ? !valid : valid\n            }\n\n            let isValid = false\n            if (restriction.item?.name && restriction.item?.name?.replace(/'/g, '').toLowerCase().includes(lowerCaseSearchText)) {\n                isValid = true\n            }\n            if (restriction.itemFilter && !isValid) {\n                Object.keys(restriction.itemFilter).forEach(key => {\n                    if (isValid) {\n                        return\n                    }\n                    let keyWithColon = `${key}:`\n                    let keyWithoutUnderscore = keyWithColon.replace(/_/g, ' ').replace(/'/g, '')\n                    let valueWithoutUnderScore = restriction.itemFilter![key].toString().replace(/_/g, ' ').replace(/'/g, '')\n                    if (\n                        restriction.itemFilter![key].toString().toLowerCase().includes(lowerCaseSearchText) ||\n                        valueWithoutUnderScore.toLowerCase().includes(lowerCaseSearchText) ||\n                        camelCaseToSentenceCase(keyWithColon).toLowerCase().includes(lowerCaseSearchText) ||\n                        camelCaseToSentenceCase(keyWithoutUnderscore).toLowerCase().includes(lowerCaseSearchText)\n                    ) {\n                        isValid = true\n                    }\n                })\n            }\n            if (restriction.tags && restriction.tags.findIndex(tag => tag.toLowerCase()?.replace(/'/g, '').includes(lowerCaseSearchText)) !== -1 && !isValid) {\n                isValid = true\n            }\n            if (invert) {\n                return !isValid\n            }\n            return isValid\n        })\n    }\n\n    function changeRestrictionDisableState(itemKey: string, newValue: boolean) {\n        let index = restrictions.findIndex(r => r.itemKey === itemKey)\n        if (index === -1) return\n        let newRestrictions = [...restrictions]\n        newRestrictions[index] = { ...newRestrictions[index], disabled: newValue }\n        setRestrictions(newRestrictions)\n\n        setSetting(RESTRICTIONS_SETTINGS_KEY, JSON.stringify(getCleanRestrictionsForApi(newRestrictions)))\n        if (props.onRestrictionsChange) {\n            props.onRestrictionsChange(getCleanRestrictionsForApi(newRestrictions), 'blacklist')\n            props.onRestrictionsChange(getCleanRestrictionsForApi(newRestrictions), 'whitelist')\n        }\n    }\n\n    function changeRestrictionDisableStateForAllCurrentShowing(newValue: boolean) {\n        let newRestrictions = [...restrictions]\n        let shownRestrictions = getRestrictionsFilteredBySearch(restrictions)\n        newRestrictions.forEach(r => {\n            if (r.itemKey && shownRestrictions.find(sr => sr.itemKey === r.itemKey)) {\n                r.disabled = newValue\n            }\n        })\n        setRestrictions(newRestrictions)\n\n        setSetting(RESTRICTIONS_SETTINGS_KEY, JSON.stringify(getCleanRestrictionsForApi(newRestrictions)))\n        if (props.onRestrictionsChange) {\n            props.onRestrictionsChange(getCleanRestrictionsForApi(newRestrictions), 'blacklist')\n            props.onRestrictionsChange(getCleanRestrictionsForApi(newRestrictions), 'whitelist')\n        }\n    }\n\n    let clearListDialog = (\n        <Modal\n            show={showDeleteRestrictionsDialog}\n            onHide={() => {\n                setShowDeleteRestrictionsDialog(false)\n            }}\n        >\n            <Modal.Header closeButton>\n                <Modal.Title>Confirmation</Modal.Title>\n            </Modal.Header>\n            <Modal.Body>\n                <p>Are you sure?</p>\n                <p>\n                    <b>This will delete all {getRestrictionsFilteredBySearch(restrictions)?.length || 0} black-/whitelist entries.</b>\n                </p>\n                <div style={{ display: 'flex', justifyContent: 'space-between' }}>\n                    <Button variant=\"danger\" style={{ width: '45%' }} onClick={clearRestrictions}>\n                        Clear <DeleteIcon />\n                    </Button>\n                    <Button\n                        style={{ width: '45%' }}\n                        onClick={() => {\n                            setShowDeleteRestrictionsDialog(false)\n                        }}\n                    >\n                        Cancel\n                    </Button>\n                </div>\n            </Modal.Body>\n        </Modal>\n    )\n\n    let currentItemContextMenuElement = (\n        <div>\n            <Menu id={RESTRICTION_CONTEXT_MENU_ID} theme={'dark'}>\n                <Item\n                    onClick={params => {\n                        changeRestrictionDisableState(params.props.itemKey, true)\n                    }}\n                    hidden={params => {\n                        let index = restrictions.findIndex(r => r.itemKey === params.props.itemKey)\n                        if (index === -1) return true\n                        return restrictions[index].disabled === true\n                    }}\n                >\n                    <BlockIcon color=\"error\" style={{ marginRight: 5 }} /> Disable restriction\n                </Item>\n                <Item\n                    onClick={params => {\n                        changeRestrictionDisableState(params.props.itemKey, false)\n                    }}\n                    hidden={params => {\n                        let index = restrictions.findIndex(r => r.itemKey === params.props.itemKey)\n                        if (index === -1) return true\n                        return restrictions[index].disabled === false || restrictions[index].disabled === undefined\n                    }}\n                >\n                    <CheckIcon color=\"success\" style={{ marginRight: 5 }} />\n                    Enable restriction\n                </Item>\n            </Menu>\n        </div>\n    )\n\n    let currentShowingItemOptionsContextMenuElement = (\n        <div>\n            <Menu id={RESTRICTION_CONTEXT_CURRENT_SHOING_MENU_ID} theme={'dark'}>\n                <Item\n                    onClick={() => {\n                        setShowDeleteRestrictionsDialog(true)\n                    }}\n                >\n                    <DeleteIcon color=\"error\" style={{ marginRight: 5 }} />\n                    Delete all current showing\n                </Item>\n                <Item\n                    onClick={() => {\n                        changeRestrictionDisableStateForAllCurrentShowing(true)\n                    }}\n                >\n                    <BlockIcon color=\"error\" style={{ marginRight: 5 }} />\n                    Disable all current showing\n                </Item>\n                <Item\n                    onClick={() => {\n                        changeRestrictionDisableStateForAllCurrentShowing(false)\n                    }}\n                >\n                    <Check color=\"success\" style={{ marginRight: 5 }} />\n                    Enable all current showing\n                </Item>\n                <Item onClick={refreshItemNames}>\n                    <Refresh style={{ marginRight: 5 }} />\n                    Refresh item names\n                </Item>\n            </Menu>\n        </div>\n    )\n\n    let windowWidth = isSSR ? 1920 : window.innerWidth\n    let singleColumn = windowWidth < 1024\n\n    let addIcon = (\n        <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"currentColor\" className=\"bi bi-plus-circle\" viewBox=\"0 0 16 16\">\n            <path d=\"M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z\" />\n            <path d=\"M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z\" />\n        </svg>\n    )\n\n    let restrictionsToDisplay = [...restrictions]\n\n    // remembering the original place for every restriction so it can correctly be mutated after a potential sorting\n    restrictionsToDisplay.forEach((restriction, i) => {\n        restriction.originalIndex = i\n    })\n\n    if (searchText) {\n        restrictionsToDisplay = getRestrictionsFilteredBySearch(restrictionsToDisplay)\n    }\n\n    if (sortByName) {\n        restrictionsToDisplay = restrictionsToDisplay.sort((a, b) => {\n            if (!a.item) {\n                return 1\n            }\n            if (!b.item) {\n                return -1\n            }\n            return a.item.name?.localeCompare(b.item.name || '') || -1\n        })\n    }\n\n    return (\n        <>\n            <div className={styles.restrictionListContainer}>\n                <div\n                    style={{\n                        backgroundColor: '#303030',\n                        padding: '10px 20px 0 20px',\n                        zIndex: 10,\n                        flexShrink: 0,\n                        width: '100%',\n                        top: 0\n                    }}\n                >\n                    {restrictionInEditMode.length > 0 ? (\n                        <EditRestriction\n                            defaultRestriction={restrictions[restrictionInEditMode[0].originalIndex!]}\n                            onAdd={addEditedFilter}\n                            onOverride={overrideEditedFilter}\n                            onCancel={onEditRestrictionCancel}\n                        />\n                    ) : isAddNewFlipperExtended ? (\n                        <NewRestriction\n                            onCancel={onNewRestrictionCancel}\n                            onSaveRestrictions={addNewRestriction}\n                            prefillRestriction={props.prefillRestriction}\n                        />\n                    ) : (\n                        <div className={styles.refreshAndDeleteButtonContainer}>\n                            <div style={{ cursor: 'pointer', marginBottom: '5px', flex: 1 }} onClick={() => setIsNewFlipperExtended(true)}>\n                                {addIcon}\n                                <span> Add new restriction</span>\n                            </div>\n                            <div\n                                onClick={e => {\n                                    showEditShowingRestrictionsMenu({\n                                        event: e\n                                    })\n                                }}\n                            >\n                                <label style={{ cursor: 'pointer' }}>Act on all {getRestrictionsFilteredBySearch(restrictions)?.length || 0} elements</label>\n                                <ListIcon id=\"currentShowingOptions\" style={{ cursor: 'pointer', marginLeft: 5 }}></ListIcon>\n                            </div>\n                        </div>\n                    )}\n                    <hr />\n                    <div className={styles.searchBarContainer}>\n                        <Form.Control\n                            ref={searchFieldRef}\n                            className={styles.searchFilter}\n                            placeholder=\"Search...\"\n                            onChange={e => debounceSearchFunction(e.target.value)}\n                        />\n                        <div className={styles.sortByNameContainer}>\n                            <Form.Label style={{ width: '200px' }} htmlFor=\"sortByNameCheckbox\">\n                                Sort by name\n                            </Form.Label>\n                            <Form.Check\n                                id=\"sortByNameCheckbox\"\n                                inline\n                                onChange={e => {\n                                    setSortByName(e.target.checked)\n                                    onEditRestrictionCancel()\n                                    recalculateListHeight()\n                                }}\n                            />\n                        </div>\n                    </div>\n                </div>\n                <div className={styles.restrictionList}>\n                    {!isSSR ? (\n                        <AutoSizer>\n                            {({ height, width }) => (\n                                <Grid\n                                    ref={listRef}\n                                    itemKey={({ columnIndex, rowIndex, data }) => {\n                                        let r = restrictionsToDisplay[singleColumn ? rowIndex : rowIndex * 2 + columnIndex]\n                                        if (!r) {\n                                            return null\n                                        }\n                                        return r.itemKey\n                                    }}\n                                    columnCount={singleColumn ? 1 : 2}\n                                    columnWidth={() => (singleColumn ? width : width / 2)}\n                                    height={height}\n                                    rowCount={singleColumn ? restrictionsToDisplay.length : Math.ceil(restrictionsToDisplay.length / 2)}\n                                    rowHeight={index => {\n                                        function getCellHeight(index) {\n                                            let defaultHeight = 81.5\n                                            let margin = 16\n                                            let restriction = restrictionsToDisplay[index]\n                                            if (!restriction) {\n                                                return 0\n                                            }\n                                            let tags = restriction.tags && restriction.tags.length > 0 ? 24 : 0\n                                            let filterCount = 0\n                                            if (restriction.itemFilter) {\n                                                filterCount = Object.keys(restriction.itemFilter).length\n                                            }\n                                            return defaultHeight + margin + tags + filterCount * 40\n                                        }\n                                        return singleColumn ? getCellHeight(index) : Math.max(getCellHeight(index * 2), getCellHeight(index * 2 + 1))\n                                    }}\n                                    width={width}\n                                    style={{ overflowX: 'hidden' }}\n                                >\n                                    {({ columnIndex, rowIndex, style }) => {\n                                        let restriction = restrictionsToDisplay[singleColumn ? rowIndex : rowIndex * 2 + columnIndex]\n                                        if (!restriction) {\n                                            return null\n                                        }\n                                        if (restriction.isEdited) {\n                                            restriction = restrictionInEditMode.find(r => r.itemKey === restriction.itemKey) || restriction\n                                        }\n                                        return (\n                                            <FlipRestrictionListEntry\n                                                key={restriction.itemKey}\n                                                restriction={restriction}\n                                                style={style}\n                                                onSaveClick={() => {\n                                                    saveRestrictionEdit(restriction)\n                                                }}\n                                                onEditClick={() => {\n                                                    editRestriction(restrictions, restriction.originalIndex!)\n                                                }}\n                                                isAnyRestrictionInEditMode={restrictionInEditMode.length > 0}\n                                                onDeleteClick={() => {\n                                                    removeRestrictionByIndex(restrictions, restriction.originalIndex!)\n                                                }}\n                                                onCreateDuplicateClick={() => {\n                                                    createDuplicate(restrictions, restriction.originalIndex!)\n                                                }}\n                                                onRemoveFilterClick={() => {\n                                                    recalculateListHeight()\n                                                }}\n                                                onRestrictionChange={newRestriction => {\n                                                    let newRestrictionsInEditMode = [...restrictionInEditMode]\n                                                    let index = newRestrictionsInEditMode.findIndex(r => r.itemKey === newRestriction.itemKey)\n                                                    newRestrictionsInEditMode[index] = { ...newRestriction }\n                                                    setRestrictionsInEditMode(newRestrictionsInEditMode)\n                                                    recalculateListHeight()\n                                                }}\n                                                onContextMenu={handleContextMenuForRestriction}\n                                            />\n                                        )\n                                    }}\n                                </Grid>\n                            )}\n                        </AutoSizer>\n                    ) : null}\n                </div>\n            </div>\n            {clearListDialog}\n            {currentItemContextMenuElement}\n            {currentShowingItemOptionsContextMenuElement}\n        </>\n    )\n}\n\nexport default React.memo(FlipRestrictionList)\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AArBA;;;;;;;;;;;;;;;;;;;;;;AA4BA,MAAM,8BAA8B;AACpC,MAAM,6CAA6C;AAEnD,SAAS,oBAAoB,KAAY;;IACrC,IAAI,CAAC,yBAAyB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,MAAM,kBAAkB,KAAK;IAC/F,IAAI,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;wCAAqB,IAAM;;IACxE,IAAI,CAAC,uBAAuB,0BAA0B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB,EAAE;IACvF,IAAI,CAAC,8BAA8B,gCAAgC,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/E,IAAI,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,IAAI,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,IAAI,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,IAAI,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAC9C,IAAI,UAAU,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAErB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,uJAAA,CAAA,iBAAc,AAAD,EAAE;QAC5B,IAAI;IACR;IACA,MAAM,EAAE,MAAM,+BAA+B,EAAE,GAAG,CAAA,GAAA,uJAAA,CAAA,iBAAc,AAAD,EAAE;QAC7D,IAAI;IACR;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACN,SAAS,WAAW,KAAoB;gBACpC,IAAI,MAAM,OAAO,IAAI,MAAM,GAAG,KAAK,OAAO,eAAe,OAAO,EAAE;oBAC9D,MAAM,cAAc;oBACpB,eAAe,OAAO,CAAC,KAAK;gBAChC;YACJ;YAEA,OAAO,gBAAgB,CAAC,WAAW;YAEnC,SAAS;YAET;iDAAO;oBACH,OAAO,mBAAmB,CAAC,WAAW;gBAC1C;;QACJ;wCAAG,EAAE;IAEL,SAAS;QACL,IAAI,eAAe,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD,EAAqB,0HAAA,CAAA,4BAAyB,EAAE,EAAE;QACrF,aAAa,OAAO,CAAC,CAAA;YACjB,IAAI,YAAY,IAAI,IAAI,YAAY,IAAI,CAAC,GAAG,EAAE;gBAC1C,YAAY,IAAI,CAAC,OAAO,GAAG,oHAAA,CAAA,UAAG,CAAC,eAAe,CAAC,YAAY,IAAI;YACnE;YACA,YAAY,OAAO,GAAG,CAAA,GAAA,wLAAA,CAAA,KAAY,AAAD;QACrC;QACA,OAAO;IACX;IAEA,SAAS,kBAAkB,kBAAqC,EAAE;QAC9D,IAAI,oBAAoB;eAAI;SAAa;QAEzC,4EAA4E;QAC5E,4EAA4E;QAC5E,gBAAgB,OAAO,CAAC,CAAA;YACpB,eAAe,OAAO,GAAG,CAAA,GAAA,wLAAA,CAAA,KAAY,AAAD;YACpC,IAAI,eAAe,IAAI,KAAK,aAAa;gBACrC,kBAAkB,IAAI,CAAC;gBACvB;YACJ;YACA,IAAI,eAAe,IAAI,KAAK,aAAa;gBACrC,IAAI,iBAAiB,kBAAkB,SAAS,CAAC,CAAA,UAAW,QAAQ,IAAI,KAAK;gBAC7E,IAAI,mBAAmB,CAAC,GAAG;oBACvB,kBAAkB,IAAI,CAAC;gBAC3B,OAAO;oBACH,kBAAkB,MAAM,CAAC,gBAAgB,GAAG;gBAChD;YACJ;QACJ;QAEA,gBAAgB;QAChB,wBAAwB;QAExB,SAAS,aAAa,CAAC,IAAI,YAAY,wHAAA,CAAA,gBAAa,CAAC,oBAAoB;QAEzE,CAAA,GAAA,0HAAA,CAAA,aAAU,AAAD,EAAE,0HAAA,CAAA,4BAAyB,EAAE,KAAK,SAAS,CAAC,CAAA,GAAA,0HAAA,CAAA,6BAA0B,AAAD,EAAE;QAEhF,IAAI,MAAM,oBAAoB,EAAE;YAC5B,MAAM,oBAAoB,CAAC,CAAA,GAAA,0HAAA,CAAA,6BAA0B,AAAD,EAAE,oBAAoB;YAC1E,MAAM,oBAAoB,CAAC,CAAA,GAAA,0HAAA,CAAA,6BAA0B,AAAD,EAAE,oBAAoB;QAC9E;QAEA,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAClB;IAEA,SAAS;QACL,wBAAwB;IAC5B;IAEA,SAAS;QACL,IAAI,kBAAkB;eAAI;SAAa;QACvC,sBAAsB,OAAO,CAAC,CAAA;YAC1B,eAAe,CAAC,YAAY,aAAa,CAAE,CAAC,QAAQ,GAAG;QAC3D;QACA,gBAAgB;QAChB,0BAA0B,EAAE;IAChC;IAEA,SAAS,gBAAgB,WAAwB;QAC7C,IAAI,kBAAkB;eAAI;SAAa;QACvC,sBAAsB,OAAO,CAAC,CAAA;YAC1B,IAAI,WAAW,eAAe,CAAC,YAAY,aAAa,CAAE;YAC1D,OAAO,IAAI,CAAC,YAAY,UAAU,IAAI,CAAC,GAAG,OAAO,CAAC,CAAA;gBAC9C,IAAI,SAAS,UAAU,IAAI,YAAY,UAAU,IAAI,SAAS,UAAU,CAAC,IAAI,KAAK,WAAW;oBACzF,SAAS,UAAU,CAAC,IAAI,GAAG,YAAY,UAAU,CAAC,IAAI;gBAC1D;YACJ;YACA,IAAI,YAAY,IAAI,EAAE;gBAClB,SAAS,IAAI,GAAG,SAAS,IAAI,GAAG;uBAAI,SAAS,IAAI;uBAAK,YAAY,IAAI;iBAAC,GAAG,YAAY,IAAI;YAC9F;YACA,SAAS,QAAQ,GAAG;YACpB,SAAS,IAAI,GAAG,YAAY,IAAI;QACpC;QAEA,CAAA,GAAA,0HAAA,CAAA,aAAU,AAAD,EAAE,0HAAA,CAAA,4BAAyB,EAAE,KAAK,SAAS,CAAC,CAAA,GAAA,0HAAA,CAAA,6BAA0B,AAAD,EAAE;QAChF,IAAI,MAAM,oBAAoB,EAAE;YAC5B,MAAM,oBAAoB,CAAC,CAAA,GAAA,0HAAA,CAAA,6BAA0B,AAAD,EAAE,kBAAkB;YACxE,MAAM,oBAAoB,CAAC,CAAA,GAAA,0HAAA,CAAA,6BAA0B,AAAD,EAAE,kBAAkB;QAC5E;QAEA,0BAA0B,EAAE;QAC5B,gBAAgB;QAChB,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QACd;IACJ;IAEA,SAAS,qBAAqB,WAAwB;QAClD,IAAI,kBAAkB;eAAI;SAAa;QACvC,sBAAsB,OAAO,CAAC,CAAA;YAC1B,IAAI,QAAQ,YAAY,aAAa;YACrC,eAAe,CAAC,MAAM,CAAC,UAAU,GAAG;gBAAE,GAAG,YAAY,UAAU;YAAC;YAChE,eAAe,CAAC,MAAM,CAAC,IAAI,GAAG,YAAY,IAAI;YAC9C,eAAe,CAAC,MAAM,CAAC,QAAQ,GAAG;YAClC,eAAe,CAAC,MAAM,CAAC,IAAI,GAAG,YAAY,IAAI;YAC9C,IAAI,YAAY,YAAY,EAAE,eAAe,CAAC,MAAM,CAAC,IAAI,GAAG,YAAY,YAAY;QACxF;QAEA,CAAA,GAAA,0HAAA,CAAA,aAAU,AAAD,EAAE,0HAAA,CAAA,4BAAyB,EAAE,KAAK,SAAS,CAAC,CAAA,GAAA,0HAAA,CAAA,6BAA0B,AAAD,EAAE;QAChF,IAAI,MAAM,oBAAoB,EAAE;YAC5B,MAAM,oBAAoB,CAAC,CAAA,GAAA,0HAAA,CAAA,6BAA0B,AAAD,EAAE,kBAAkB;YACxE,MAAM,oBAAoB,CAAC,CAAA,GAAA,0HAAA,CAAA,6BAA0B,AAAD,EAAE,kBAAkB;QAC5E;QACA,0BAA0B,EAAE;QAC5B,gBAAgB;QAChB,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QACd;IACJ;IAEA,SAAS,yBAAyB,YAA+B,EAAE,KAAa;QAC5E,IAAI,kBAAkB;eAAI;SAAa;QACvC,IAAI,qBAAqB,gBAAgB,MAAM,CAAC,OAAO;QAEvD,CAAA,GAAA,0HAAA,CAAA,aAAU,AAAD,EAAE,0HAAA,CAAA,4BAAyB,EAAE,KAAK,SAAS,CAAC,CAAA,GAAA,0HAAA,CAAA,6BAA0B,AAAD,EAAE;QAEhF,SAAS,aAAa,CAAC,IAAI,YAAY,wHAAA,CAAA,gBAAa,CAAC,oBAAoB;QAEzE,IAAI,MAAM,oBAAoB,EAAE;YAC5B,MAAM,oBAAoB,CAAC,CAAA,GAAA,0HAAA,CAAA,6BAA0B,AAAD,EAAE,kBAAkB,kBAAkB,CAAC,EAAE,CAAC,IAAI;QACtG;QACA,gBAAgB;QAChB,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QACd;IACJ;IAEA,SAAS,gBAAgB,YAA+B,EAAE,KAAa;QACnE,IAAI,YAAY;YAAE,GAAG,YAAY,CAAC,MAAM;QAAC;QACzC,IAAI,kBAAkB;eAAI;SAAa;QACvC,gBAAgB,MAAM,CAAC,QAAQ,GAAG,GAAG;QAErC,CAAA,GAAA,0HAAA,CAAA,aAAU,AAAD,EAAE,0HAAA,CAAA,4BAAyB,EAAE,KAAK,SAAS,CAAC,CAAA,GAAA,0HAAA,CAAA,6BAA0B,AAAD,EAAE;QAChF,IAAI,MAAM,oBAAoB,EAAE;YAC5B,MAAM,oBAAoB,CAAC,CAAA,GAAA,0HAAA,CAAA,6BAA0B,AAAD,EAAE,kBAAkB;YACxE,MAAM,oBAAoB,CAAC,CAAA,GAAA,0HAAA,CAAA,6BAA0B,AAAD,EAAE,kBAAkB;QAC5E;QACA,gBAAgB;QAChB;IACJ;IAEA,SAAS,oBAAoB,cAA+B;QACxD,IAAI,kBAAkB;eAAI;SAAa;QACvC,IAAI,gBAAgB;eAAI;SAAsB;QAE9C,eAAe,CAAC,eAAe,aAAa,CAAE,GAAG;YAAE,GAAG,cAAc;QAAC;QACrE,eAAe,CAAC,eAAe,aAAa,CAAE,CAAC,QAAQ,GAAG;QAE1D,cAAc,MAAM,CAAC,CAAA,IAAK,EAAE,OAAO,KAAK,eAAe,OAAO;QAE9D,0BAA0B;QAC1B,gBAAgB;QAChB,CAAA,GAAA,0HAAA,CAAA,aAAU,AAAD,EAAE,0HAAA,CAAA,4BAAyB,EAAE,KAAK,SAAS,CAAC,CAAA,GAAA,0HAAA,CAAA,6BAA0B,AAAD,EAAE;QAChF,IAAI,MAAM,oBAAoB,EAAE;YAC5B,MAAM,oBAAoB,CAAC,CAAA,GAAA,0HAAA,CAAA,6BAA0B,AAAD,EAAE,kBAAkB;YACxE,MAAM,oBAAoB,CAAC,CAAA,GAAA,0HAAA,CAAA,6BAA0B,AAAD,EAAE,kBAAkB;QAC5E;QACA;IACJ;IAEA,SAAS,gBAAgB,YAA+B,EAAE,KAAa;QACnE,IAAI,kBAAkB;eAAI;SAAa;QACvC,IAAI,oBAAoB,eAAe,CAAC,MAAM;QAC9C,IAAI,4BAA4B;eAAI;SAAsB;QAE1D,0BAA0B,IAAI,CAAC;QAE/B,IAAI,yBAA4C,EAAE;QAClD,0BAA0B,OAAO,CAAC,CAAA;YAC9B,uBAAuB,IAAI,CAAC;QAChC;QAEA,kBAAkB,QAAQ,GAAG;QAC7B,gBAAgB;QAChB,0BAA0B;IAC9B;IAEA,SAAS;QACL,IAAI,kBAAkB,gCAAgC,cAAc;QACpE,gBAAgB;QAChB,gCAAgC;QAEhC,SAAS,aAAa,CAAC,IAAI,YAAY,wHAAA,CAAA,gBAAa,CAAC,oBAAoB;QAEzE,CAAA,GAAA,0HAAA,CAAA,aAAU,AAAD,EAAE,0HAAA,CAAA,4BAAyB,EAAE,KAAK,SAAS,CAAC,CAAA,GAAA,0HAAA,CAAA,6BAA0B,AAAD,EAAE;QAEhF,IAAI,MAAM,oBAAoB,EAAE;YAC5B,MAAM,oBAAoB,CAAC,CAAA,GAAA,0HAAA,CAAA,6BAA0B,AAAD,EAAE,kBAAkB;YACxE,MAAM,oBAAoB,CAAC,CAAA,GAAA,0HAAA,CAAA,6BAA0B,AAAD,EAAE,kBAAkB;QAC5E;IACJ;IAEA,MAAM,yBAAyB,AAAC;QAC5B,IAAI;QAEJ,OAAO,CAAA;YACH,aAAa;YACb,UAAU,WAAW;gBACjB,cAAc;gBACd;YACJ,GAAG;QACP;IACJ;IAEA,SAAS;QACL,IAAI,kBAAkB;eAAI;SAAa;QACvC,IAAI,QAAgB,EAAE;QACtB,gBAAgB,OAAO,CAAC,CAAA;YACpB,IAAI,YAAY,IAAI,IAAI,YAAY,IAAI,CAAC,GAAG,EAAE;gBAC1C,MAAM,IAAI,CAAC,YAAY,IAAI;YAC/B;QACJ;QACA,oHAAA,CAAA,UAAG,CAAC,YAAY,CAAC,OACZ,IAAI,CAAC,CAAA;YACF,gBAAgB,OAAO,CAAC,CAAA;gBACpB,IAAI,YAAY,IAAI,IAAI,YAAY,IAAI,CAAC,GAAG,EAAE;oBAC1C,YAAY,IAAI,CAAC,IAAI,GAAG,WAAW,CAAC,YAAY,IAAI,CAAC,GAAG,CAAC;gBAC7D;YACJ;YACA,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,gBAAgB;YAChB,CAAA,GAAA,0HAAA,CAAA,aAAU,AAAD,EAAE,0HAAA,CAAA,4BAAyB,EAAE,KAAK,SAAS,CAAC,CAAA,GAAA,0HAAA,CAAA,6BAA0B,AAAD,EAAE;YAChF,IAAI,MAAM,oBAAoB,EAAE;gBAC5B,MAAM,oBAAoB,CAAC,CAAA,GAAA,0HAAA,CAAA,6BAA0B,AAAD,EAAE,kBAAkB;gBACxE,MAAM,oBAAoB,CAAC,CAAA,GAAA,0HAAA,CAAA,6BAA0B,AAAD,EAAE,kBAAkB;YAC5E;QACJ,GACC,KAAK,CAAC;YACH,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QAChB;IACR;IAEA,SAAS;;QACF,QAAQ,OAAO,CAAS,kBAAkB,CAAC,GAAG;IACrD;IAEA,SAAS,gCAAgC,KAAK;QAC1C,MAAM,cAAc;QACpB,KAAK;YAAE,OAAO;YAAO,OAAO;gBAAE,SAAS,MAAM,aAAa,CAAC,EAAE;YAAC;QAAE;IACpE;IAEA,SAAS,gCAAgC,YAA+B,EAAE,SAAS,KAAK;QAEpF,IAAI,sBAAsB,WAAW,WAAW;QAChD,IAAI,aAA4B;QAChC,IAAI,oBAAoB,QAAQ,CAAC,mBAAmB;YAChD,aAAa;YACb,sBAAsB,oBAAoB,OAAO,CAAC,kBAAkB,IAAI,IAAI;QAChF;QACA,IAAI,oBAAoB,QAAQ,CAAC,mBAAmB;YAChD,aAAa;YACb,sBAAsB,oBAAoB,OAAO,CAAC,kBAAkB,IAAI,IAAI;QAChF;QAEA,sBAAsB,oBAAoB,OAAO,CAAC,MAAM,KAAK,OAAO,CAAC,MAAM;QAG3E,OAAO,aAAa,MAAM,CAAC,CAAA;YAEvB,IAAI,cAAc,YAAY,IAAI,KAAK,YAAY;gBAC/C,OAAO,SAAS,OAAO;YAC3B;YACA,IAAI,cAAc,CAAC,YAAY;gBAC3B,IAAI,QAAQ,YAAY,IAAI,KAAK;gBACjC,OAAO,SAAS,CAAC,QAAQ;YAC7B;YAEA,IAAI,UAAU;YACd,IAAI,YAAY,IAAI,EAAE,QAAQ,YAAY,IAAI,EAAE,MAAM,QAAQ,MAAM,IAAI,cAAc,SAAS,sBAAsB;gBACjH,UAAU;YACd;YACA,IAAI,YAAY,UAAU,IAAI,CAAC,SAAS;gBACpC,OAAO,IAAI,CAAC,YAAY,UAAU,EAAE,OAAO,CAAC,CAAA;oBACxC,IAAI,SAAS;wBACT;oBACJ;oBACA,IAAI,eAAe,GAAG,IAAI,CAAC,CAAC;oBAC5B,IAAI,uBAAuB,aAAa,OAAO,CAAC,MAAM,KAAK,OAAO,CAAC,MAAM;oBACzE,IAAI,yBAAyB,YAAY,UAAU,AAAC,CAAC,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,MAAM,KAAK,OAAO,CAAC,MAAM;oBACtG,IACI,YAAY,UAAU,AAAC,CAAC,IAAI,CAAC,QAAQ,GAAG,WAAW,GAAG,QAAQ,CAAC,wBAC/D,uBAAuB,WAAW,GAAG,QAAQ,CAAC,wBAC9C,CAAA,GAAA,sHAAA,CAAA,0BAAuB,AAAD,EAAE,cAAc,WAAW,GAAG,QAAQ,CAAC,wBAC7D,CAAA,GAAA,sHAAA,CAAA,0BAAuB,AAAD,EAAE,sBAAsB,WAAW,GAAG,QAAQ,CAAC,sBACvE;wBACE,UAAU;oBACd;gBACJ;YACJ;YACA,IAAI,YAAY,IAAI,IAAI,YAAY,IAAI,CAAC,SAAS,CAAC,CAAA,MAAO,IAAI,WAAW,IAAI,QAAQ,MAAM,IAAI,SAAS,0BAA0B,CAAC,KAAK,CAAC,SAAS;gBAC9I,UAAU;YACd;YACA,IAAI,QAAQ;gBACR,OAAO,CAAC;YACZ;YACA,OAAO;QACX;IACJ;IAEA,SAAS,8BAA8B,OAAe,EAAE,QAAiB;QACrE,IAAI,QAAQ,aAAa,SAAS,CAAC,CAAA,IAAK,EAAE,OAAO,KAAK;QACtD,IAAI,UAAU,CAAC,GAAG;QAClB,IAAI,kBAAkB;eAAI;SAAa;QACvC,eAAe,CAAC,MAAM,GAAG;YAAE,GAAG,eAAe,CAAC,MAAM;YAAE,UAAU;QAAS;QACzE,gBAAgB;QAEhB,CAAA,GAAA,0HAAA,CAAA,aAAU,AAAD,EAAE,0HAAA,CAAA,4BAAyB,EAAE,KAAK,SAAS,CAAC,CAAA,GAAA,0HAAA,CAAA,6BAA0B,AAAD,EAAE;QAChF,IAAI,MAAM,oBAAoB,EAAE;YAC5B,MAAM,oBAAoB,CAAC,CAAA,GAAA,0HAAA,CAAA,6BAA0B,AAAD,EAAE,kBAAkB;YACxE,MAAM,oBAAoB,CAAC,CAAA,GAAA,0HAAA,CAAA,6BAA0B,AAAD,EAAE,kBAAkB;QAC5E;IACJ;IAEA,SAAS,kDAAkD,QAAiB;QACxE,IAAI,kBAAkB;eAAI;SAAa;QACvC,IAAI,oBAAoB,gCAAgC;QACxD,gBAAgB,OAAO,CAAC,CAAA;YACpB,IAAI,EAAE,OAAO,IAAI,kBAAkB,IAAI,CAAC,CAAA,KAAM,GAAG,OAAO,KAAK,EAAE,OAAO,GAAG;gBACrE,EAAE,QAAQ,GAAG;YACjB;QACJ;QACA,gBAAgB;QAEhB,CAAA,GAAA,0HAAA,CAAA,aAAU,AAAD,EAAE,0HAAA,CAAA,4BAAyB,EAAE,KAAK,SAAS,CAAC,CAAA,GAAA,0HAAA,CAAA,6BAA0B,AAAD,EAAE;QAChF,IAAI,MAAM,oBAAoB,EAAE;YAC5B,MAAM,oBAAoB,CAAC,CAAA,GAAA,0HAAA,CAAA,6BAA0B,AAAD,EAAE,kBAAkB;YACxE,MAAM,oBAAoB,CAAC,CAAA,GAAA,0HAAA,CAAA,6BAA0B,AAAD,EAAE,kBAAkB;QAC5E;IACJ;IAEA,IAAI,gCACA,6LAAC,yLAAA,CAAA,QAAK;QACF,MAAM;QACN,QAAQ;YACJ,gCAAgC;QACpC;;0BAEA,6LAAC,yLAAA,CAAA,QAAK,CAAC,MAAM;gBAAC,WAAW;0BACrB,cAAA,6LAAC,yLAAA,CAAA,QAAK,CAAC,KAAK;8BAAC;;;;;;;;;;;0BAEjB,6LAAC,yLAAA,CAAA,QAAK,CAAC,IAAI;;kCACP,6LAAC;kCAAE;;;;;;kCACH,6LAAC;kCACG,cAAA,6LAAC;;gCAAE;gCAAsB,gCAAgC,eAAe,UAAU;gCAAE;;;;;;;;;;;;kCAExF,6LAAC;wBAAI,OAAO;4BAAE,SAAS;4BAAQ,gBAAgB;wBAAgB;;0CAC3D,6LAAC,2LAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAS,OAAO;oCAAE,OAAO;gCAAM;gCAAG,SAAS;;oCAAmB;kDACpE,6LAAC,8JAAA,CAAA,UAAU;;;;;;;;;;;0CAErB,6LAAC,2LAAA,CAAA,SAAM;gCACH,OAAO;oCAAE,OAAO;gCAAM;gCACtB,SAAS;oCACL,gCAAgC;gCACpC;0CACH;;;;;;;;;;;;;;;;;;;;;;;;IAQjB,IAAI,8CACA,6LAAC;kBACG,cAAA,6LAAC,uJAAA,CAAA,OAAI;YAAC,IAAI;YAA6B,OAAO;;8BAC1C,6LAAC,uJAAA,CAAA,OAAI;oBACD,SAAS,CAAA;wBACL,8BAA8B,OAAO,KAAK,CAAC,OAAO,EAAE;oBACxD;oBACA,QAAQ,CAAA;wBACJ,IAAI,QAAQ,aAAa,SAAS,CAAC,CAAA,IAAK,EAAE,OAAO,KAAK,OAAO,KAAK,CAAC,OAAO;wBAC1E,IAAI,UAAU,CAAC,GAAG,OAAO;wBACzB,OAAO,YAAY,CAAC,MAAM,CAAC,QAAQ,KAAK;oBAC5C;;sCAEA,6LAAC,6JAAA,CAAA,UAAS;4BAAC,OAAM;4BAAQ,OAAO;gCAAE,aAAa;4BAAE;;;;;;wBAAK;;;;;;;8BAE1D,6LAAC,uJAAA,CAAA,OAAI;oBACD,SAAS,CAAA;wBACL,8BAA8B,OAAO,KAAK,CAAC,OAAO,EAAE;oBACxD;oBACA,QAAQ,CAAA;wBACJ,IAAI,QAAQ,aAAa,SAAS,CAAC,CAAA,IAAK,EAAE,OAAO,KAAK,OAAO,KAAK,CAAC,OAAO;wBAC1E,IAAI,UAAU,CAAC,GAAG,OAAO;wBACzB,OAAO,YAAY,CAAC,MAAM,CAAC,QAAQ,KAAK,SAAS,YAAY,CAAC,MAAM,CAAC,QAAQ,KAAK;oBACtF;;sCAEA,6LAAC,mKAAA,CAAA,UAAS;4BAAC,OAAM;4BAAU,OAAO;gCAAE,aAAa;4BAAE;;;;;;wBAAK;;;;;;;;;;;;;;;;;;IAOxE,IAAI,4DACA,6LAAC;kBACG,cAAA,6LAAC,uJAAA,CAAA,OAAI;YAAC,IAAI;YAA4C,OAAO;;8BACzD,6LAAC,uJAAA,CAAA,OAAI;oBACD,SAAS;wBACL,gCAAgC;oBACpC;;sCAEA,6LAAC,8JAAA,CAAA,UAAU;4BAAC,OAAM;4BAAQ,OAAO;gCAAE,aAAa;4BAAE;;;;;;wBAAK;;;;;;;8BAG3D,6LAAC,uJAAA,CAAA,OAAI;oBACD,SAAS;wBACL,kDAAkD;oBACtD;;sCAEA,6LAAC,6JAAA,CAAA,UAAS;4BAAC,OAAM;4BAAQ,OAAO;gCAAE,aAAa;4BAAE;;;;;;wBAAK;;;;;;;8BAG1D,6LAAC,uJAAA,CAAA,OAAI;oBACD,SAAS;wBACL,kDAAkD;oBACtD;;sCAEA,6LAAC,6JAAA,CAAA,UAAK;4BAAC,OAAM;4BAAU,OAAO;gCAAE,aAAa;4BAAE;;;;;;wBAAK;;;;;;;8BAGxD,6LAAC,uJAAA,CAAA,OAAI;oBAAC,SAAS;;sCACX,6LAAC,+JAAA,CAAA,UAAO;4BAAC,OAAO;gCAAE,aAAa;4BAAE;;;;;;wBAAK;;;;;;;;;;;;;;;;;;IAOtD,IAAI,cAAc,QAAQ,OAAO,OAAO,UAAU;IAClD,IAAI,eAAe,cAAc;IAEjC,IAAI,wBACA,6LAAC;QAAI,OAAM;QAA6B,OAAM;QAAK,QAAO;QAAK,MAAK;QAAe,WAAU;QAAoB,SAAQ;;0BACrH,6LAAC;gBAAK,GAAE;;;;;;0BACR,6LAAC;gBAAK,GAAE;;;;;;;;;;;;IAIhB,IAAI,wBAAwB;WAAI;KAAa;IAE7C,gHAAgH;IAChH,sBAAsB,OAAO,CAAC,CAAC,aAAa;QACxC,YAAY,aAAa,GAAG;IAChC;IAEA,IAAI,YAAY;QACZ,wBAAwB,gCAAgC;IAC5D;IAEA,IAAI,YAAY;QACZ,wBAAwB,sBAAsB,IAAI,CAAC,CAAC,GAAG;YACnD,IAAI,CAAC,EAAE,IAAI,EAAE;gBACT,OAAO;YACX;YACA,IAAI,CAAC,EAAE,IAAI,EAAE;gBACT,OAAO,CAAC;YACZ;YACA,OAAO,EAAE,IAAI,CAAC,IAAI,EAAE,cAAc,EAAE,IAAI,CAAC,IAAI,IAAI,OAAO,CAAC;QAC7D;IACJ;IAEA,qBACI;;0BACI,6LAAC;gBAAI,WAAW,kLAAA,CAAA,UAAM,CAAC,wBAAwB;;kCAC3C,6LAAC;wBACG,OAAO;4BACH,iBAAiB;4BACjB,SAAS;4BACT,QAAQ;4BACR,YAAY;4BACZ,OAAO;4BACP,KAAK;wBACT;;4BAEC,sBAAsB,MAAM,GAAG,kBAC5B,6LAAC,sLAAA,CAAA,UAAe;gCACZ,oBAAoB,YAAY,CAAC,qBAAqB,CAAC,EAAE,CAAC,aAAa,CAAE;gCACzE,OAAO;gCACP,YAAY;gCACZ,UAAU;;;;;uCAEd,wCACA,6LAAC,oLAAA,CAAA,UAAc;gCACX,UAAU;gCACV,oBAAoB;gCACpB,oBAAoB,MAAM,kBAAkB;;;;;qDAGhD,6LAAC;gCAAI,WAAW,kLAAA,CAAA,UAAM,CAAC,+BAA+B;;kDAClD,6LAAC;wCAAI,OAAO;4CAAE,QAAQ;4CAAW,cAAc;4CAAO,MAAM;wCAAE;wCAAG,SAAS,IAAM,wBAAwB;;4CACnG;0DACD,6LAAC;0DAAK;;;;;;;;;;;;kDAEV,6LAAC;wCACG,SAAS,CAAA;4CACL,gCAAgC;gDAC5B,OAAO;4CACX;wCACJ;;0DAEA,6LAAC;gDAAM,OAAO;oDAAE,QAAQ;gDAAU;;oDAAG;oDAAY,gCAAgC,eAAe,UAAU;oDAAE;;;;;;;0DAC5G,6LAAC,+JAAA,CAAA,UAAQ;gDAAC,IAAG;gDAAwB,OAAO;oDAAE,QAAQ;oDAAW,YAAY;gDAAE;;;;;;;;;;;;;;;;;;0CAI3F,6LAAC;;;;;0CACD,6LAAC;gCAAI,WAAW,kLAAA,CAAA,UAAM,CAAC,kBAAkB;;kDACrC,6LAAC,uLAAA,CAAA,OAAI,CAAC,OAAO;wCACT,KAAK;wCACL,WAAW,kLAAA,CAAA,UAAM,CAAC,YAAY;wCAC9B,aAAY;wCACZ,UAAU,CAAA,IAAK,uBAAuB,EAAE,MAAM,CAAC,KAAK;;;;;;kDAExD,6LAAC;wCAAI,WAAW,kLAAA,CAAA,UAAM,CAAC,mBAAmB;;0DACtC,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;gDAAC,OAAO;oDAAE,OAAO;gDAAQ;gDAAG,SAAQ;0DAAqB;;;;;;0DAGpE,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;gDACP,IAAG;gDACH,MAAM;gDACN,UAAU,CAAA;oDACN,cAAc,EAAE,MAAM,CAAC,OAAO;oDAC9B;oDACA;gDACJ;;;;;;;;;;;;;;;;;;;;;;;;kCAKhB,6LAAC;wBAAI,WAAW,kLAAA,CAAA,UAAM,CAAC,eAAe;kCACjC,CAAC,sBACE,6LAAC,gNAAA,CAAA,UAAS;sCACL,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,iBACf,6LAAC,0JAAA,CAAA,mBAAI;oCACD,KAAK;oCACL,SAAS,CAAC,EAAE,WAAW,EAAE,QAAQ,EAAE,IAAI,EAAE;wCACrC,IAAI,IAAI,qBAAqB,CAAC,eAAe,WAAW,WAAW,IAAI,YAAY;wCACnF,IAAI,CAAC,GAAG;4CACJ,OAAO;wCACX;wCACA,OAAO,EAAE,OAAO;oCACpB;oCACA,aAAa,eAAe,IAAI;oCAChC,aAAa,IAAO,eAAe,QAAQ,QAAQ;oCACnD,QAAQ;oCACR,UAAU,eAAe,sBAAsB,MAAM,GAAG,KAAK,IAAI,CAAC,sBAAsB,MAAM,GAAG;oCACjG,WAAW,CAAA;wCACP,SAAS,cAAc,KAAK;4CACxB,IAAI,gBAAgB;4CACpB,IAAI,SAAS;4CACb,IAAI,cAAc,qBAAqB,CAAC,MAAM;4CAC9C,IAAI,CAAC,aAAa;gDACd,OAAO;4CACX;4CACA,IAAI,OAAO,YAAY,IAAI,IAAI,YAAY,IAAI,CAAC,MAAM,GAAG,IAAI,KAAK;4CAClE,IAAI,cAAc;4CAClB,IAAI,YAAY,UAAU,EAAE;gDACxB,cAAc,OAAO,IAAI,CAAC,YAAY,UAAU,EAAE,MAAM;4CAC5D;4CACA,OAAO,gBAAgB,SAAS,OAAO,cAAc;wCACzD;wCACA,OAAO,eAAe,cAAc,SAAS,KAAK,GAAG,CAAC,cAAc,QAAQ,IAAI,cAAc,QAAQ,IAAI;oCAC9G;oCACA,OAAO;oCACP,OAAO;wCAAE,WAAW;oCAAS;8CAE5B,CAAC,EAAE,WAAW,EAAE,QAAQ,EAAE,KAAK,EAAE;wCAC9B,IAAI,cAAc,qBAAqB,CAAC,eAAe,WAAW,WAAW,IAAI,YAAY;wCAC7F,IAAI,CAAC,aAAa;4CACd,OAAO;wCACX;wCACA,IAAI,YAAY,QAAQ,EAAE;4CACtB,cAAc,sBAAsB,IAAI,CAAC,CAAA,IAAK,EAAE,OAAO,KAAK,YAAY,OAAO,KAAK;wCACxF;wCACA,qBACI,6LAAC,oMAAA,CAAA,UAAwB;4CAErB,aAAa;4CACb,OAAO;4CACP,aAAa;gDACT,oBAAoB;4CACxB;4CACA,aAAa;gDACT,gBAAgB,cAAc,YAAY,aAAa;4CAC3D;4CACA,4BAA4B,sBAAsB,MAAM,GAAG;4CAC3D,eAAe;gDACX,yBAAyB,cAAc,YAAY,aAAa;4CACpE;4CACA,wBAAwB;gDACpB,gBAAgB,cAAc,YAAY,aAAa;4CAC3D;4CACA,qBAAqB;gDACjB;4CACJ;4CACA,qBAAqB,CAAA;gDACjB,IAAI,4BAA4B;uDAAI;iDAAsB;gDAC1D,IAAI,QAAQ,0BAA0B,SAAS,CAAC,CAAA,IAAK,EAAE,OAAO,KAAK,eAAe,OAAO;gDACzF,yBAAyB,CAAC,MAAM,GAAG;oDAAE,GAAG,cAAc;gDAAC;gDACvD,0BAA0B;gDAC1B;4CACJ;4CACA,eAAe;2CA1BV,YAAY,OAAO;;;;;oCA6BpC;;;;;;;;;;mCAIZ;;;;;;;;;;;;YAGX;YACA;YACA;;;AAGb;GA5oBS;;QAWY,uJAAA,CAAA,iBAAc;QAGmB,uJAAA,CAAA,iBAAc;;;KAd3D;2DA8oBM,6JAAA,CAAA,UAAK,CAAC,IAAI,CAAC", "debugId": null}}, {"offset": {"line": 5295, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/components/Flipper/FlipperFilter/FlipperFilter.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"checkboxLabel\": \"FlipperFilter-module___Wj5DG__checkboxLabel\",\n  \"filterBorder\": \"FlipperFilter-module___Wj5DG__filterBorder\",\n  \"filterCheckbox\": \"FlipperFilter-module___Wj5DG__filterCheckbox\",\n  \"filterTextfield\": \"FlipperFilter-module___Wj5DG__filterTextfield\",\n  \"flipperFilterFormfieldLabel\": \"FlipperFilter-module___Wj5DG__flipperFilterFormfieldLabel\",\n  \"flipperFilterFormfieldText\": \"FlipperFilter-module___Wj5DG__flipperFilterFormfieldText\",\n  \"flipperFilterGroup\": \"FlipperFilter-module___Wj5DG__flipperFilterGroup\",\n  \"flipperFilterGroupAdvanced\": \"FlipperFilter-module___Wj5DG__flipperFilterGroupAdvanced\",\n  \"flipperfilter\": \"FlipperFilter-module___Wj5DG__flipperfilter\",\n  \"restrictionListContent\": \"FlipperFilter-module___Wj5DG__restrictionListContent\",\n  \"restrictionModal\": \"FlipperFilter-module___Wj5DG__restrictionModal\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 5314, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/Flipper/FlipperFilter/FlipperFilter.tsx"], "sourcesContent": ["'use client'\nimport { use<PERSON>ato<PERSON> } from '@jonkoops/matomo-tracker-react'\nimport FilterIcon from '@mui/icons-material/BallotOutlined'\nimport SettingsIcon from '@mui/icons-material/Settings'\nimport React, { useCallback, useEffect, useRef, useState } from 'react'\nimport { Button, Form, Modal } from 'react-bootstrap'\nimport { NumericFormat } from 'react-number-format'\nimport { v4 as generateUUID } from 'uuid'\nimport api from '../../../api/ApiHelper'\nimport { CUSTOM_EVENTS } from '../../../api/ApiTypes.d'\nimport { getFlipCustomizeSettings, getCurrentProfitCalculationState } from '../../../utils/FlipUtils'\nimport { getDecimalSeparator, getThousandSeparator } from '../../../utils/Formatter'\nimport {\n    FLIPPER_FILTER_KEY,\n    FLIP_CUSTOMIZING_KEY,\n    ITEM_FILER_SHOW_ADVANCED,\n    getSetting,\n    getSettingsObject,\n    mapRestrictionsToApiFormat,\n    setSetting\n} from '../../../utils/SettingsUtils'\nimport Tooltip from '../../Tooltip/Tooltip'\nimport FlipCustomize from '../FlipCustomize/FlipCustomize'\nimport FlipRestrictionList from '../FlipRestrictionList/FlipRestrictionList'\nimport styles from './FlipperFilter.module.css'\nimport { getURLSearchParam } from '../../../utils/Parser/URLParser'\nimport { RestrictionCreateState } from '../FlipRestrictionList/NewRestriction/NewRestriction'\nimport { isValidTokenAvailable } from '../../GoogleSignIn/GoogleSignIn'\n\ninterface Props {\n    onChange(filter: FlipperFilter)\n    isLoggedIn?: boolean\n    isPremium?: boolean\n}\n\nfunction FlipperFilter(props: Props) {\n    let [prefillRestriction] = useState<RestrictionCreateState>(\n        getURLSearchParam('prefillRestriction') ? JSON.parse(getURLSearchParam('prefillRestriction')!) : undefined\n    )\n    let [showRestrictionList, setShowRestrictionList] = useState(!!prefillRestriction)\n    let [isAdvanced, setIsAdvanced] = useState(getSetting(ITEM_FILER_SHOW_ADVANCED, 'false') === 'true')\n    let [flipCustomizeSettings, setFlipCustomizeSettings] = useState<FlipCustomizeSettings>({})\n    let [flipperFilter, setFlipperFilter] = useState<FlipperFilter>(getSettingsObject<FlipperFilter>(FLIPPER_FILTER_KEY, {}))\n    let [showCustomizeFlip, setShowCustomizeFlip] = useState(false)\n    let [flipCustomizeKey, setFlipCustomizeKey] = useState<string>(generateUUID())\n    let [isSSR, setIsSSR] = useState(true)\n\n    let disabled = isSSR ? true : !props.isLoggedIn && isValidTokenAvailable(localStorage.getItem('googleId'))\n\n    let { trackEvent } = useMatomo()\n\n    useEffect(() => {\n        setFlipCustomizeSettings(getFlipCustomizeSettings())\n        setFlipperFilter(getSettingsObject<FlipperFilter>(FLIPPER_FILTER_KEY, {}))\n        setIsSSR(false)\n        document.addEventListener(CUSTOM_EVENTS.FLIP_SETTINGS_CHANGE, e => {\n            if ((e as any).detail?.apiUpdate) {\n                setFlipCustomizeKey(generateUUID())\n            }\n            setFlipCustomizeSettings(getFlipCustomizeSettings())\n            setFlipperFilter(getSettingsObject<FlipperFilter>(FLIPPER_FILTER_KEY, {}))\n        })\n    }, [])\n\n    let onlyUnsoldRef = useRef(null)\n\n    function onFilterChange(filter: FlipperFilter) {\n        if (props.isLoggedIn) {\n            let filterToSave = JSON.parse(JSON.stringify(filter))\n            if (!props.isPremium) {\n                filterToSave.onlyBin = undefined\n                filterToSave.onlyUnsold = undefined\n            }\n            setSetting(FLIPPER_FILTER_KEY, JSON.stringify(filter))\n        } else {\n            setSetting(FLIPPER_FILTER_KEY, JSON.stringify(filter))\n        }\n\n        document.dispatchEvent(new CustomEvent(CUSTOM_EVENTS.FLIP_SETTINGS_CHANGE))\n        props.onChange(filter)\n    }\n\n    function onSettingsChange(key: string, value: any, apiKey?: string) {\n        let filter = flipperFilter\n        filter[key] = value\n        api.setFlipSetting(apiKey || key, value)\n        setFlipperFilter(flipperFilter)\n        onFilterChange(filter)\n    }\n\n    let onRestrictionsChange = useCallback((restrictions: FlipRestriction[], type: 'blacklist' | 'whitelist') => {\n        api.setFlipSetting(type, mapRestrictionsToApiFormat(restrictions.filter(restriction => restriction.type === type)))\n    }, [])\n\n    function numberFieldMaxValue(value: number = 0, maxValue: number) {\n        return value <= maxValue\n    }\n\n    function onProfitCalculationButtonClick() {\n        let flipPriceCalculationState = getCurrentProfitCalculationState(flipCustomizeSettings)\n\n        if (flipPriceCalculationState === 'lbin' || flipPriceCalculationState === 'custom') {\n            flipCustomizeSettings.finders = [1, 4]\n            flipCustomizeSettings.useLowestBinForProfit = false\n            api.setFlipSetting('lbin', false)\n            api.setFlipSetting('finders', 5)\n            trackEvent({\n                category: 'customizeFlipStyle',\n                action: 'finders: 1,4'\n            })\n            trackEvent({\n                category: 'customizeFlipStyle',\n                action: 'lbin: false'\n            })\n        } else {\n            flipCustomizeSettings.finders = [2]\n            flipCustomizeSettings.useLowestBinForProfit = true\n            api.setFlipSetting('lbin', true)\n            api.setFlipSetting('finders', 2)\n            trackEvent({\n                category: 'customizeFlipStyle',\n                action: 'finders: 2'\n            })\n            trackEvent({\n                category: 'customizeFlipStyle',\n                action: 'lbin: false'\n            })\n        }\n        setSetting(FLIP_CUSTOMIZING_KEY, JSON.stringify(flipCustomizeSettings))\n        setFlipCustomizeSettings({ ...flipCustomizeSettings })\n        document.dispatchEvent(new CustomEvent(CUSTOM_EVENTS.FLIP_SETTINGS_CHANGE))\n    }\n\n    const debounceMinProfitChangeFunction = (function () {\n        let timerId\n\n        return (minProfit: number) => {\n            clearTimeout(timerId)\n            timerId = setTimeout(() => {\n                onSettingsChange('minProfit', minProfit || 0)\n            }, 1000)\n        }\n    })()\n\n    let restrictionListDialog = (\n        <Modal\n            size={'xl'}\n            show={showRestrictionList && !disabled}\n            onHide={() => {\n                setShowRestrictionList(false)\n            }}\n            scrollable={true}\n            contentClassName={styles.restrictionListContent}\n        >\n            <Modal.Header closeButton>\n                <Modal.Title>Restrict the flip results</Modal.Title>\n            </Modal.Header>\n            <Modal.Body className={styles.restrictionModal}>\n                <FlipRestrictionList onRestrictionsChange={onRestrictionsChange} prefillRestriction={prefillRestriction} />\n            </Modal.Body>\n        </Modal>\n    )\n\n    let customizeFlipDialog = (\n        <Modal\n            size={'xl'}\n            show={showCustomizeFlip && !disabled}\n            onHide={() => {\n                setShowCustomizeFlip(false)\n            }}\n        >\n            <Modal.Header closeButton>\n                <Modal.Title>Customize the style of flips</Modal.Title>\n            </Modal.Header>\n            <Modal.Body>\n                <FlipCustomize key={flipCustomizeKey} />\n            </Modal.Body>\n        </Modal>\n    )\n\n    let flipPriceCalculationState = getCurrentProfitCalculationState(flipCustomizeSettings)\n\n    return (\n        <div className={styles.flipperFilter}>\n            <div className={styles.flipperFilterGroup}>\n                <Form.Group className={styles.filterTextfield}>\n                    <Tooltip\n                        type=\"hover\"\n                        content={\n                            <Form.Label htmlFor=\"min-profit\" className={`${styles.flipperFilterFormfieldLabel}`}>\n                                Min. Profit:\n                            </Form.Label>\n                        }\n                        tooltipContent={\n                            <span>\n                                How much estimated profit do you at least want from each flip. Note that there is naturally more competition on higher profit\n                                flips{' '}\n                            </span>\n                        }\n                    />\n                    <NumericFormat\n                        id=\"min-profit\"\n                        onValueChange={value => {\n                            debounceMinProfitChangeFunction(value.floatValue || 0)\n                        }}\n                        placeholder={!props.isLoggedIn ? 'Please login first' : null}\n                        className={`${styles.flipperFilterFormfield} ${styles.flipperFilterFormfieldText}`}\n                        type=\"text\"\n                        disabled={!props.isLoggedIn}\n                        isAllowed={value => {\n                            return numberFieldMaxValue(value.floatValue, 10000000000)\n                        }}\n                        customInput={Form.Control}\n                        defaultValue={flipperFilter.minProfit}\n                        thousandSeparator={getThousandSeparator()}\n                        decimalSeparator={getDecimalSeparator()}\n                        allowNegative={false}\n                        decimalScale={0}\n                    />\n                </Form.Group>\n                <div\n                    onClick={() => {\n                        if (disabled) return\n                        setShowRestrictionList(true)\n                    }}\n                    className={styles.filterCheckbox}\n                    style={{ cursor: 'pointer' }}\n                >\n                    <span className={styles.filterBorder}>\n                        <Tooltip\n                            type=\"hover\"\n                            content={<span className={`${styles.flipperFilterFormfieldLabel} ${styles.checkboxLabel}`}>Filter Rules</span>}\n                            tooltipContent={<span>Make custom rules which items should show up and which should not</span>}\n                        />\n                        <FilterIcon className={styles.flipperFilterFormfield} style={{ marginLeft: '-4px' }} />\n                    </span>\n                </div>\n                <Form.Group className={styles.filterCheckbox}>\n                    <Tooltip\n                        type=\"hover\"\n                        content={\n                            <Form.Label\n                                htmlFor=\"onlyBinCheckbox\"\n                                className={`${styles.flipperFilterFormfieldLabel} ${styles.checkboxLabel}`}\n                                defaultChecked={flipperFilter.onlyBin}\n                            >\n                                Only BIN Auctions\n                            </Form.Label>\n                        }\n                        tooltipContent={\n                            <span>\n                                {flipperFilter.onlyBin ? 'Do not display' : 'Display'} auction flips that are about to end and could be profited from with the\n                                current bid\n                            </span>\n                        }\n                    />\n                    <Form.Check\n                        id=\"onlyBinCheckbox\"\n                        onChange={e => {\n                            onSettingsChange('onlyBin', e.target.checked)\n                        }}\n                        disabled={disabled}\n                        defaultChecked={flipperFilter.onlyBin}\n                        className={styles.flipperFilterFormfield}\n                        type=\"checkbox\"\n                    />\n                </Form.Group>\n                <Form.Group className={styles.filterTextfield}>\n                    <Tooltip\n                        type=\"hover\"\n                        content={<Form.Label className={`${styles.flipperFilterFormfieldLabel}`}>Profit based on</Form.Label>}\n                        tooltipContent={\n                            flipPriceCalculationState === 'lbin' ? (\n                                <span>\n                                    Profit is currently based off the lowest bin of similar items. Lbin Flips (also called snipes) will not show if there is no\n                                    similar auction on ah. Auctions shown are expected to sell quickly. There is very high competition for these types of\n                                    auctions.\n                                </span>\n                            ) : flipPriceCalculationState === 'median' ? (\n                                <span>\n                                    Profit is currently based off the weighted median sell value of similar items (so called references). This is the\n                                    recommended setting as it makes the most money over all and prices items based on daily and weekly price swings. But items\n                                    may only sell after days.\n                                </span>\n                            ) : undefined\n                        }\n                    />\n                    <Button onClick={onProfitCalculationButtonClick}>\n                        {flipPriceCalculationState === 'lbin' ? 'Lowest BIN' : flipPriceCalculationState === 'median' ? 'Median' : 'Custom'}\n                    </Button>\n                </Form.Group>\n                <Form.Group\n                    onClick={() => {\n                        if (disabled) return\n                        setShowCustomizeFlip(true)\n                    }}\n                    className={styles.filterCheckbox}\n                >\n                    <span className={styles.filterBorder}>\n                        <Tooltip\n                            type=\"hover\"\n                            content={<span style={{ cursor: 'pointer', marginRight: '10px' }}>Settings</span>}\n                            tooltipContent={<span>Edit flip appearance and general settings</span>}\n                        />\n                        <span style={{ cursor: 'pointer' }}>\n                            {' '}\n                            <SettingsIcon />\n                        </span>\n                    </span>\n                </Form.Group>\n                <Form.Group className={styles.filterCheckbox}>\n                    <Tooltip\n                        type=\"hover\"\n                        content={\n                            <Form.Label htmlFor=\"advancedCheckbox\" className={`${styles.flipperFilterFormfieldLabel} ${styles.checkboxLabel}`}>\n                                Advanced\n                            </Form.Label>\n                        }\n                        tooltipContent={<span>Get more advanced config options. (You shouldn't need them by default)</span>}\n                    />\n                    <Form.Check\n                        id=\"advancedCheckbox\"\n                        onChange={e => {\n                            setIsAdvanced(e.target.checked)\n                            setSetting(ITEM_FILER_SHOW_ADVANCED, e.target.checked.toString())\n                        }}\n                        checked={isAdvanced}\n                        disabled={disabled}\n                        className={styles.flipperFilterFormfield}\n                        type=\"checkbox\"\n                    />\n                </Form.Group>\n            </div>\n            {isAdvanced ? (\n                <>\n                    <hr />\n                    <div className={styles.flipperFilterGroupAdvanced}>\n                        <Form.Group className={styles.filterTextfield}>\n                            <Tooltip\n                                type=\"hover\"\n                                content={\n                                    <Form.Label htmlFor=\"min-profit-percent\" className={`${styles.flipperFilterFormfieldLabel} ${styles.checkboxLabel}`}>\n                                        Min. Profit (%):\n                                    </Form.Label>\n                                }\n                                tooltipContent={\n                                    <span>\n                                        A 94m item worth an estimated 100m would have 3m (3%) estimated profit. This includes a total of 3% ah tax and would\n                                        block any flip below the value chosen, using 2% would show the flip\n                                    </span>\n                                }\n                            />\n                            <NumericFormat\n                                id=\"min-profit-percent\"\n                                onValueChange={value => {\n                                    onSettingsChange('minProfitPercent', value.floatValue || 0)\n                                }}\n                                className={`${styles.flipperFilterFormfield} ${styles.flipperFilterFormfieldText}`}\n                                disabled={!props.isLoggedIn}\n                                placeholder={!props.isLoggedIn ? 'Please login first' : null}\n                                isAllowed={value => {\n                                    return numberFieldMaxValue(value.floatValue, 10000000000)\n                                }}\n                                customInput={Form.Control}\n                                defaultValue={flipperFilter.minProfitPercent}\n                                thousandSeparator={getThousandSeparator()}\n                                decimalSeparator={getDecimalSeparator()}\n                                allowNegative={false}\n                                decimalScale={0}\n                            />\n                        </Form.Group>\n                        <Form.Group className={styles.filterTextfield}>\n                            <Tooltip\n                                type=\"hover\"\n                                content={\n                                    <Form.Label htmlFor=\"min-volume\" className={`${styles.flipperFilterFormfieldLabel} ${styles.checkboxLabel}`}>\n                                        Min. Volume:\n                                    </Form.Label>\n                                }\n                                tooltipContent={<span>Minimum average amount of sells in 24 hours</span>}\n                            />\n                            <NumericFormat\n                                id=\"min-volume\"\n                                onValueChange={value => {\n                                    onSettingsChange('minVolume', value.floatValue || 0)\n                                }}\n                                className={`${styles.flipperFilterFormfield} ${styles.flipperFilterFormfieldText}`}\n                                disabled={!props.isLoggedIn}\n                                placeholder={!props.isLoggedIn ? 'Please login first' : null}\n                                isAllowed={value => {\n                                    return numberFieldMaxValue(value.floatValue, 120)\n                                }}\n                                customInput={Form.Control}\n                                defaultValue={flipperFilter.minVolume}\n                                thousandSeparator={getThousandSeparator()}\n                                decimalSeparator={getDecimalSeparator()}\n                                allowNegative={false}\n                                decimalScale={1}\n                            />\n                        </Form.Group>\n                        <Form.Group className={styles.filterTextfield}>\n                            <Form.Label htmlFor=\"max-cost\" className={`${styles.flipperFilterFormfieldLabel} ${styles.checkboxLabel}`}>\n                                Max. Cost:\n                            </Form.Label>\n                            <NumericFormat\n                                id=\"max-cost\"\n                                onValueChange={value => {\n                                    onSettingsChange('maxCost', value.floatValue || 0)\n                                }}\n                                className={`${styles.flipperFilterFormfield} ${styles.flipperFilterFormfieldText}`}\n                                disabled={!props.isLoggedIn}\n                                placeholder={!props.isLoggedIn ? 'Please login first' : null}\n                                isAllowed={value => {\n                                    return numberFieldMaxValue(value.floatValue, 10000000000)\n                                }}\n                                customInput={Form.Control}\n                                defaultValue={flipperFilter.maxCost}\n                                thousandSeparator={getThousandSeparator()}\n                                decimalSeparator={getDecimalSeparator()}\n                                allowNegative={false}\n                                decimalScale={0}\n                            />\n                        </Form.Group>\n                        <Form.Group className={styles.filterCheckbox}>\n                            <Form.Label htmlFor=\"onlyUnsoldCheckbox\" className={`${styles.flipperFilterFormfieldLabel} ${styles.checkboxLabel}`}>\n                                Hide SOLD Auctions\n                            </Form.Label>\n                            <Form.Check\n                                ref={onlyUnsoldRef}\n                                id=\"onlyUnsoldCheckbox\"\n                                onChange={e => {\n                                    onSettingsChange('onlyUnsold', e.target.checked, 'showHideSold')\n                                }}\n                                defaultChecked={flipperFilter.onlyUnsold}\n                                className={styles.flipperFilterFormfield}\n                                type=\"checkbox\"\n                                disabled={disabled}\n                            />\n                        </Form.Group>\n                    </div>\n                </>\n            ) : null}\n            {restrictionListDialog}\n            {customizeFlipDialog}\n        </div>\n    )\n}\n\nexport default React.memo(FlipperFilter)\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AASA;AACA;AACA;AACA;AACA;AAEA;;;AA3BA;;;;;;;;;;;;;;;;;;;AAmCA,SAAS,cAAc,KAAY;;IAC/B,IAAI,CAAC,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAC9B,CAAA,GAAA,gIAAA,CAAA,oBAAiB,AAAD,EAAE,wBAAwB,KAAK,KAAK,CAAC,CAAA,GAAA,gIAAA,CAAA,oBAAiB,AAAD,EAAE,yBAA0B;IAErG,IAAI,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,CAAC;IAC/D,IAAI,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,GAAA,0HAAA,CAAA,aAAU,AAAD,EAAE,0HAAA,CAAA,2BAAwB,EAAE,aAAa;IAC7F,IAAI,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAyB,CAAC;IACzF,IAAI,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD,EAAiB,0HAAA,CAAA,qBAAkB,EAAE,CAAC;IACtH,IAAI,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,IAAI,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,CAAA,GAAA,wLAAA,CAAA,KAAY,AAAD;IAC1E,IAAI,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjC,IAAI,WAAW,QAAQ,OAAO,CAAC,MAAM,UAAU,IAAI,CAAA,GAAA,8IAAA,CAAA,wBAAqB,AAAD,EAAE,aAAa,OAAO,CAAC;IAE9F,IAAI,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,sNAAA,CAAA,YAAS,AAAD;IAE7B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACN,yBAAyB,CAAA,GAAA,sHAAA,CAAA,2BAAwB,AAAD;YAChD,iBAAiB,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD,EAAiB,0HAAA,CAAA,qBAAkB,EAAE,CAAC;YACvE,SAAS;YACT,SAAS,gBAAgB,CAAC,wHAAA,CAAA,gBAAa,CAAC,oBAAoB;2CAAE,CAAA;oBAC1D,IAAI,AAAC,EAAU,MAAM,EAAE,WAAW;wBAC9B,oBAAoB,CAAA,GAAA,wLAAA,CAAA,KAAY,AAAD;oBACnC;oBACA,yBAAyB,CAAA,GAAA,sHAAA,CAAA,2BAAwB,AAAD;oBAChD,iBAAiB,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD,EAAiB,0HAAA,CAAA,qBAAkB,EAAE,CAAC;gBAC3E;;QACJ;kCAAG,EAAE;IAEL,IAAI,gBAAgB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAE3B,SAAS,eAAe,MAAqB;QACzC,IAAI,MAAM,UAAU,EAAE;YAClB,IAAI,eAAe,KAAK,KAAK,CAAC,KAAK,SAAS,CAAC;YAC7C,IAAI,CAAC,MAAM,SAAS,EAAE;gBAClB,aAAa,OAAO,GAAG;gBACvB,aAAa,UAAU,GAAG;YAC9B;YACA,CAAA,GAAA,0HAAA,CAAA,aAAU,AAAD,EAAE,0HAAA,CAAA,qBAAkB,EAAE,KAAK,SAAS,CAAC;QAClD,OAAO;YACH,CAAA,GAAA,0HAAA,CAAA,aAAU,AAAD,EAAE,0HAAA,CAAA,qBAAkB,EAAE,KAAK,SAAS,CAAC;QAClD;QAEA,SAAS,aAAa,CAAC,IAAI,YAAY,wHAAA,CAAA,gBAAa,CAAC,oBAAoB;QACzE,MAAM,QAAQ,CAAC;IACnB;IAEA,SAAS,iBAAiB,GAAW,EAAE,KAAU,EAAE,MAAe;QAC9D,IAAI,SAAS;QACb,MAAM,CAAC,IAAI,GAAG;QACd,oHAAA,CAAA,UAAG,CAAC,cAAc,CAAC,UAAU,KAAK;QAClC,iBAAiB;QACjB,eAAe;IACnB;IAEA,IAAI,uBAAuB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2DAAE,CAAC,cAAiC;YACrE,oHAAA,CAAA,UAAG,CAAC,cAAc,CAAC,MAAM,CAAA,GAAA,0HAAA,CAAA,6BAA0B,AAAD,EAAE,aAAa,MAAM;mEAAC,CAAA,cAAe,YAAY,IAAI,KAAK;;QAChH;0DAAG,EAAE;IAEL,SAAS,oBAAoB,QAAgB,CAAC,EAAE,QAAgB;QAC5D,OAAO,SAAS;IACpB;IAEA,SAAS;QACL,IAAI,4BAA4B,CAAA,GAAA,sHAAA,CAAA,mCAAgC,AAAD,EAAE;QAEjE,IAAI,8BAA8B,UAAU,8BAA8B,UAAU;YAChF,sBAAsB,OAAO,GAAG;gBAAC;gBAAG;aAAE;YACtC,sBAAsB,qBAAqB,GAAG;YAC9C,oHAAA,CAAA,UAAG,CAAC,cAAc,CAAC,QAAQ;YAC3B,oHAAA,CAAA,UAAG,CAAC,cAAc,CAAC,WAAW;YAC9B,WAAW;gBACP,UAAU;gBACV,QAAQ;YACZ;YACA,WAAW;gBACP,UAAU;gBACV,QAAQ;YACZ;QACJ,OAAO;YACH,sBAAsB,OAAO,GAAG;gBAAC;aAAE;YACnC,sBAAsB,qBAAqB,GAAG;YAC9C,oHAAA,CAAA,UAAG,CAAC,cAAc,CAAC,QAAQ;YAC3B,oHAAA,CAAA,UAAG,CAAC,cAAc,CAAC,WAAW;YAC9B,WAAW;gBACP,UAAU;gBACV,QAAQ;YACZ;YACA,WAAW;gBACP,UAAU;gBACV,QAAQ;YACZ;QACJ;QACA,CAAA,GAAA,0HAAA,CAAA,aAAU,AAAD,EAAE,0HAAA,CAAA,uBAAoB,EAAE,KAAK,SAAS,CAAC;QAChD,yBAAyB;YAAE,GAAG,qBAAqB;QAAC;QACpD,SAAS,aAAa,CAAC,IAAI,YAAY,wHAAA,CAAA,gBAAa,CAAC,oBAAoB;IAC7E;IAEA,MAAM,kCAAkC,AAAC;QACrC,IAAI;QAEJ,OAAO,CAAC;YACJ,aAAa;YACb,UAAU,WAAW;gBACjB,iBAAiB,aAAa,aAAa;YAC/C,GAAG;QACP;IACJ;IAEA,IAAI,sCACA,6LAAC,yLAAA,CAAA,QAAK;QACF,MAAM;QACN,MAAM,uBAAuB,CAAC;QAC9B,QAAQ;YACJ,uBAAuB;QAC3B;QACA,YAAY;QACZ,kBAAkB,sKAAA,CAAA,UAAM,CAAC,sBAAsB;;0BAE/C,6LAAC,yLAAA,CAAA,QAAK,CAAC,MAAM;gBAAC,WAAW;0BACrB,cAAA,6LAAC,yLAAA,CAAA,QAAK,CAAC,KAAK;8BAAC;;;;;;;;;;;0BAEjB,6LAAC,yLAAA,CAAA,QAAK,CAAC,IAAI;gBAAC,WAAW,sKAAA,CAAA,UAAM,CAAC,gBAAgB;0BAC1C,cAAA,6LAAC,uKAAA,CAAA,UAAmB;oBAAC,sBAAsB;oBAAsB,oBAAoB;;;;;;;;;;;;;;;;;IAKjG,IAAI,oCACA,6LAAC,yLAAA,CAAA,QAAK;QACF,MAAM;QACN,MAAM,qBAAqB,CAAC;QAC5B,QAAQ;YACJ,qBAAqB;QACzB;;0BAEA,6LAAC,yLAAA,CAAA,QAAK,CAAC,MAAM;gBAAC,WAAW;0BACrB,cAAA,6LAAC,yLAAA,CAAA,QAAK,CAAC,KAAK;8BAAC;;;;;;;;;;;0BAEjB,6LAAC,yLAAA,CAAA,QAAK,CAAC,IAAI;0BACP,cAAA,6LAAC,2JAAA,CAAA,UAAa,MAAM;;;;;;;;;;;;;;;;IAKhC,IAAI,4BAA4B,CAAA,GAAA,sHAAA,CAAA,mCAAgC,AAAD,EAAE;IAEjE,qBACI,6LAAC;QAAI,WAAW,sKAAA,CAAA,UAAM,CAAC,aAAa;;0BAChC,6LAAC;gBAAI,WAAW,sKAAA,CAAA,UAAM,CAAC,kBAAkB;;kCACrC,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;wBAAC,WAAW,sKAAA,CAAA,UAAM,CAAC,eAAe;;0CACzC,6LAAC,oIAAA,CAAA,UAAO;gCACJ,MAAK;gCACL,uBACI,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;oCAAC,SAAQ;oCAAa,WAAW,GAAG,sKAAA,CAAA,UAAM,CAAC,2BAA2B,EAAE;8CAAE;;;;;;gCAIzF,8BACI,6LAAC;;wCAAK;wCAEI;;;;;;;;;;;;0CAIlB,6LAAC,uLAAA,CAAA,gBAAa;gCACV,IAAG;gCACH,eAAe,CAAA;oCACX,gCAAgC,MAAM,UAAU,IAAI;gCACxD;gCACA,aAAa,CAAC,MAAM,UAAU,GAAG,uBAAuB;gCACxD,WAAW,GAAG,sKAAA,CAAA,UAAM,CAAC,sBAAsB,CAAC,CAAC,EAAE,sKAAA,CAAA,UAAM,CAAC,0BAA0B,EAAE;gCAClF,MAAK;gCACL,UAAU,CAAC,MAAM,UAAU;gCAC3B,WAAW,CAAA;oCACP,OAAO,oBAAoB,MAAM,UAAU,EAAE;gCACjD;gCACA,aAAa,uLAAA,CAAA,OAAI,CAAC,OAAO;gCACzB,cAAc,cAAc,SAAS;gCACrC,mBAAmB,CAAA,GAAA,sHAAA,CAAA,uBAAoB,AAAD;gCACtC,kBAAkB,CAAA,GAAA,sHAAA,CAAA,sBAAmB,AAAD;gCACpC,eAAe;gCACf,cAAc;;;;;;;;;;;;kCAGtB,6LAAC;wBACG,SAAS;4BACL,IAAI,UAAU;4BACd,uBAAuB;wBAC3B;wBACA,WAAW,sKAAA,CAAA,UAAM,CAAC,cAAc;wBAChC,OAAO;4BAAE,QAAQ;wBAAU;kCAE3B,cAAA,6LAAC;4BAAK,WAAW,sKAAA,CAAA,UAAM,CAAC,YAAY;;8CAChC,6LAAC,oIAAA,CAAA,UAAO;oCACJ,MAAK;oCACL,uBAAS,6LAAC;wCAAK,WAAW,GAAG,sKAAA,CAAA,UAAM,CAAC,2BAA2B,CAAC,CAAC,EAAE,sKAAA,CAAA,UAAM,CAAC,aAAa,EAAE;kDAAE;;;;;;oCAC3F,8BAAgB,6LAAC;kDAAK;;;;;;;;;;;8CAE1B,6LAAC,sKAAA,CAAA,UAAU;oCAAC,WAAW,sKAAA,CAAA,UAAM,CAAC,sBAAsB;oCAAE,OAAO;wCAAE,YAAY;oCAAO;;;;;;;;;;;;;;;;;kCAG1F,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;wBAAC,WAAW,sKAAA,CAAA,UAAM,CAAC,cAAc;;0CACxC,6LAAC,oIAAA,CAAA,UAAO;gCACJ,MAAK;gCACL,uBACI,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;oCACP,SAAQ;oCACR,WAAW,GAAG,sKAAA,CAAA,UAAM,CAAC,2BAA2B,CAAC,CAAC,EAAE,sKAAA,CAAA,UAAM,CAAC,aAAa,EAAE;oCAC1E,gBAAgB,cAAc,OAAO;8CACxC;;;;;;gCAIL,8BACI,6LAAC;;wCACI,cAAc,OAAO,GAAG,mBAAmB;wCAAU;;;;;;;;;;;;0CAKlE,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;gCACP,IAAG;gCACH,UAAU,CAAA;oCACN,iBAAiB,WAAW,EAAE,MAAM,CAAC,OAAO;gCAChD;gCACA,UAAU;gCACV,gBAAgB,cAAc,OAAO;gCACrC,WAAW,sKAAA,CAAA,UAAM,CAAC,sBAAsB;gCACxC,MAAK;;;;;;;;;;;;kCAGb,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;wBAAC,WAAW,sKAAA,CAAA,UAAM,CAAC,eAAe;;0CACzC,6LAAC,oIAAA,CAAA,UAAO;gCACJ,MAAK;gCACL,uBAAS,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;oCAAC,WAAW,GAAG,sKAAA,CAAA,UAAM,CAAC,2BAA2B,EAAE;8CAAE;;;;;;gCACzE,gBACI,8BAA8B,uBAC1B,6LAAC;8CAAK;;;;;6CAKN,8BAA8B,yBAC9B,6LAAC;8CAAK;;;;;6CAKN;;;;;;0CAGZ,6LAAC,2LAAA,CAAA,SAAM;gCAAC,SAAS;0CACZ,8BAA8B,SAAS,eAAe,8BAA8B,WAAW,WAAW;;;;;;;;;;;;kCAGnH,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;wBACP,SAAS;4BACL,IAAI,UAAU;4BACd,qBAAqB;wBACzB;wBACA,WAAW,sKAAA,CAAA,UAAM,CAAC,cAAc;kCAEhC,cAAA,6LAAC;4BAAK,WAAW,sKAAA,CAAA,UAAM,CAAC,YAAY;;8CAChC,6LAAC,oIAAA,CAAA,UAAO;oCACJ,MAAK;oCACL,uBAAS,6LAAC;wCAAK,OAAO;4CAAE,QAAQ;4CAAW,aAAa;wCAAO;kDAAG;;;;;;oCAClE,8BAAgB,6LAAC;kDAAK;;;;;;;;;;;8CAE1B,6LAAC;oCAAK,OAAO;wCAAE,QAAQ;oCAAU;;wCAC5B;sDACD,6LAAC,gKAAA,CAAA,UAAY;;;;;;;;;;;;;;;;;;;;;;kCAIzB,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;wBAAC,WAAW,sKAAA,CAAA,UAAM,CAAC,cAAc;;0CACxC,6LAAC,oIAAA,CAAA,UAAO;gCACJ,MAAK;gCACL,uBACI,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;oCAAC,SAAQ;oCAAmB,WAAW,GAAG,sKAAA,CAAA,UAAM,CAAC,2BAA2B,CAAC,CAAC,EAAE,sKAAA,CAAA,UAAM,CAAC,aAAa,EAAE;8CAAE;;;;;;gCAIvH,8BAAgB,6LAAC;8CAAK;;;;;;;;;;;0CAE1B,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;gCACP,IAAG;gCACH,UAAU,CAAA;oCACN,cAAc,EAAE,MAAM,CAAC,OAAO;oCAC9B,CAAA,GAAA,0HAAA,CAAA,aAAU,AAAD,EAAE,0HAAA,CAAA,2BAAwB,EAAE,EAAE,MAAM,CAAC,OAAO,CAAC,QAAQ;gCAClE;gCACA,SAAS;gCACT,UAAU;gCACV,WAAW,sKAAA,CAAA,UAAM,CAAC,sBAAsB;gCACxC,MAAK;;;;;;;;;;;;;;;;;;YAIhB,2BACG;;kCACI,6LAAC;;;;;kCACD,6LAAC;wBAAI,WAAW,sKAAA,CAAA,UAAM,CAAC,0BAA0B;;0CAC7C,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;gCAAC,WAAW,sKAAA,CAAA,UAAM,CAAC,eAAe;;kDACzC,6LAAC,oIAAA,CAAA,UAAO;wCACJ,MAAK;wCACL,uBACI,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;4CAAC,SAAQ;4CAAqB,WAAW,GAAG,sKAAA,CAAA,UAAM,CAAC,2BAA2B,CAAC,CAAC,EAAE,sKAAA,CAAA,UAAM,CAAC,aAAa,EAAE;sDAAE;;;;;;wCAIzH,8BACI,6LAAC;sDAAK;;;;;;;;;;;kDAMd,6LAAC,uLAAA,CAAA,gBAAa;wCACV,IAAG;wCACH,eAAe,CAAA;4CACX,iBAAiB,oBAAoB,MAAM,UAAU,IAAI;wCAC7D;wCACA,WAAW,GAAG,sKAAA,CAAA,UAAM,CAAC,sBAAsB,CAAC,CAAC,EAAE,sKAAA,CAAA,UAAM,CAAC,0BAA0B,EAAE;wCAClF,UAAU,CAAC,MAAM,UAAU;wCAC3B,aAAa,CAAC,MAAM,UAAU,GAAG,uBAAuB;wCACxD,WAAW,CAAA;4CACP,OAAO,oBAAoB,MAAM,UAAU,EAAE;wCACjD;wCACA,aAAa,uLAAA,CAAA,OAAI,CAAC,OAAO;wCACzB,cAAc,cAAc,gBAAgB;wCAC5C,mBAAmB,CAAA,GAAA,sHAAA,CAAA,uBAAoB,AAAD;wCACtC,kBAAkB,CAAA,GAAA,sHAAA,CAAA,sBAAmB,AAAD;wCACpC,eAAe;wCACf,cAAc;;;;;;;;;;;;0CAGtB,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;gCAAC,WAAW,sKAAA,CAAA,UAAM,CAAC,eAAe;;kDACzC,6LAAC,oIAAA,CAAA,UAAO;wCACJ,MAAK;wCACL,uBACI,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;4CAAC,SAAQ;4CAAa,WAAW,GAAG,sKAAA,CAAA,UAAM,CAAC,2BAA2B,CAAC,CAAC,EAAE,sKAAA,CAAA,UAAM,CAAC,aAAa,EAAE;sDAAE;;;;;;wCAIjH,8BAAgB,6LAAC;sDAAK;;;;;;;;;;;kDAE1B,6LAAC,uLAAA,CAAA,gBAAa;wCACV,IAAG;wCACH,eAAe,CAAA;4CACX,iBAAiB,aAAa,MAAM,UAAU,IAAI;wCACtD;wCACA,WAAW,GAAG,sKAAA,CAAA,UAAM,CAAC,sBAAsB,CAAC,CAAC,EAAE,sKAAA,CAAA,UAAM,CAAC,0BAA0B,EAAE;wCAClF,UAAU,CAAC,MAAM,UAAU;wCAC3B,aAAa,CAAC,MAAM,UAAU,GAAG,uBAAuB;wCACxD,WAAW,CAAA;4CACP,OAAO,oBAAoB,MAAM,UAAU,EAAE;wCACjD;wCACA,aAAa,uLAAA,CAAA,OAAI,CAAC,OAAO;wCACzB,cAAc,cAAc,SAAS;wCACrC,mBAAmB,CAAA,GAAA,sHAAA,CAAA,uBAAoB,AAAD;wCACtC,kBAAkB,CAAA,GAAA,sHAAA,CAAA,sBAAmB,AAAD;wCACpC,eAAe;wCACf,cAAc;;;;;;;;;;;;0CAGtB,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;gCAAC,WAAW,sKAAA,CAAA,UAAM,CAAC,eAAe;;kDACzC,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;wCAAC,SAAQ;wCAAW,WAAW,GAAG,sKAAA,CAAA,UAAM,CAAC,2BAA2B,CAAC,CAAC,EAAE,sKAAA,CAAA,UAAM,CAAC,aAAa,EAAE;kDAAE;;;;;;kDAG3G,6LAAC,uLAAA,CAAA,gBAAa;wCACV,IAAG;wCACH,eAAe,CAAA;4CACX,iBAAiB,WAAW,MAAM,UAAU,IAAI;wCACpD;wCACA,WAAW,GAAG,sKAAA,CAAA,UAAM,CAAC,sBAAsB,CAAC,CAAC,EAAE,sKAAA,CAAA,UAAM,CAAC,0BAA0B,EAAE;wCAClF,UAAU,CAAC,MAAM,UAAU;wCAC3B,aAAa,CAAC,MAAM,UAAU,GAAG,uBAAuB;wCACxD,WAAW,CAAA;4CACP,OAAO,oBAAoB,MAAM,UAAU,EAAE;wCACjD;wCACA,aAAa,uLAAA,CAAA,OAAI,CAAC,OAAO;wCACzB,cAAc,cAAc,OAAO;wCACnC,mBAAmB,CAAA,GAAA,sHAAA,CAAA,uBAAoB,AAAD;wCACtC,kBAAkB,CAAA,GAAA,sHAAA,CAAA,sBAAmB,AAAD;wCACpC,eAAe;wCACf,cAAc;;;;;;;;;;;;0CAGtB,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;gCAAC,WAAW,sKAAA,CAAA,UAAM,CAAC,cAAc;;kDACxC,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;wCAAC,SAAQ;wCAAqB,WAAW,GAAG,sKAAA,CAAA,UAAM,CAAC,2BAA2B,CAAC,CAAC,EAAE,sKAAA,CAAA,UAAM,CAAC,aAAa,EAAE;kDAAE;;;;;;kDAGrH,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;wCACP,KAAK;wCACL,IAAG;wCACH,UAAU,CAAA;4CACN,iBAAiB,cAAc,EAAE,MAAM,CAAC,OAAO,EAAE;wCACrD;wCACA,gBAAgB,cAAc,UAAU;wCACxC,WAAW,sKAAA,CAAA,UAAM,CAAC,sBAAsB;wCACxC,MAAK;wCACL,UAAU;;;;;;;;;;;;;;;;;;;+BAK1B;YACH;YACA;;;;;;;AAGb;GA3ZS;;QAcgB,sNAAA,CAAA,YAAS;;;KAdzB;2DA6ZM,6JAAA,CAAA,UAAK,CAAC,IAAI,CAAC", "debugId": null}}, {"offset": {"line": 6107, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/Flipper/Flipper.tsx"], "sourcesContent": ["'use client'\nimport DeleteIcon from '@mui/icons-material/Delete'\nimport HelpIcon from '@mui/icons-material/Help'\nimport ArrowRightIcon from '@mui/icons-material/KeyboardTab'\nimport HandIcon from '@mui/icons-material/PanTool'\nimport SearchIcon from '@mui/icons-material/Search'\nimport Link from 'next/link'\nimport { useEffect, useRef, useState } from 'react'\nimport { Button, Card, Form, Modal } from 'react-bootstrap'\nimport { Item, Menu, useContextMenu } from 'react-contexify'\nimport Countdown, { zeroPad } from 'react-countdown'\nimport AutoSizer from 'react-virtualized-auto-sizer'\nimport { FixedSizeList as List } from 'react-window'\nimport { v4 as generateUUID } from 'uuid'\nimport api from '../../api/ApiHelper'\nimport { CUSTOM_EVENTS } from '../../api/ApiTypes.d'\nimport { DEFAULT_FLIP_SETTINGS, DEMO_FLIP, getFlipCustomizeSettings } from '../../utils/FlipUtils'\nimport { useWasAlreadyLoggedIn } from '../../utils/Hooks'\nimport { getLoadingElement } from '../../utils/LoadingUtils'\nimport { getHighestPriorityPremiumProduct, getPremiumType, hasHighEnoughPremium, PREMIUM_RANK } from '../../utils/PremiumTypeUtils'\nimport { FLIPPER_FILTER_KEY, getSetting, getSettingsObject, handleSettingsImport, RESTRICTIONS_SETTINGS_KEY, setSetting } from '../../utils/SettingsUtils'\nimport AuctionDetails from '../AuctionDetails/AuctionDetails'\nimport { CopyButton } from '../CopyButton/CopyButton'\nimport GoogleSignIn from '../GoogleSignIn/GoogleSignIn'\nimport Number from '../Number/Number'\nimport Tooltip from '../Tooltip/Tooltip'\nimport Flip from './Flip/Flip'\nimport FlipBased from './FlipBased/FlipBased'\nimport styles from './Flipper.module.css'\nimport FlipperFAQ from './FlipperFAQ/FlipperFAQ'\nimport FlipperFilter from './FlipperFilter/FlipperFilter'\nimport { useRouter } from 'next/navigation'\nimport { parseFlipAuction } from '../../utils/Parser/APIResponseParser'\n\n// Not a state\n// Update should not trigger a rerender for performance reasons\nlet missedInfo: FreeFlipperMissInformation = {\n    estimatedProfitCopiedAuctions: 0,\n    missedEstimatedProfit: 0,\n    missedFlipsCount: 0,\n    totalFlips: 0,\n    totalProfit: 0\n}\n\nlet mounted = true\n\nconst FLIP_CONEXT_MENU_ID = 'flip-context-menu'\n\ninterface Props {\n    flips?: any[]\n}\n\nfunction Flipper(props: Props) {\n    let [flipperFilter, setFlipperFilter] = useState<FlipperFilter>(getSettingsObject<FlipperFilter>(FLIPPER_FILTER_KEY, {}))\n    let [flips, setFlips] = useState<FlipAuction[]>(\n        props.flips\n            ? props.flips.map(parseFlipAuction).filter(flip => {\n                return flipperFilter.onlyUnsold ? !flip.sold : true\n            })\n            : []\n    )\n    let [isLoggedIn, setIsLoggedIn] = useState(false)\n    let [autoscroll, setAutoscroll] = useState(false)\n    let [hasPremium, setHasPremium] = useState(false)\n    let [activePremiumProduct, setActivePremiumProduct] = useState<PremiumProduct>()\n    let [enabledScroll, setEnabledScroll] = useState(false)\n    let [isLoading, setIsLoading] = useState(false)\n    let [refInfo, setRefInfo] = useState<RefInfo>()\n    let [basedOnAuction, setBasedOnAuction] = useState<FlipAuction | null>(null)\n    let [lastFlipFetchTimeSeconds, setLastFlipFetchTimeSeconds] = useState<number>()\n    let [lastFlipFetchTimeLoading, setLastFlipFetchTimeLoading] = useState<boolean>(false)\n    let [countdownDateObject, setCountdownDateObject] = useState<Date>()\n    let [isSmall, setIsSmall] = useState(false)\n    let [selectedAuctionUUID, setSelectedAuctionUUID] = useState('')\n    let [isSSR, setIsSSR] = useState(true)\n    let [showResetToDefaultDialog, setShowResetToDefaultDialog] = useState(false)\n    let wasAlreadyLoggedIn = useWasAlreadyLoggedIn()\n\n    let [flipperFilterKey, setFlipperFilterKey] = useState<string>(generateUUID())\n\n    let router = useRouter()\n\n    const { show } = useContextMenu({\n        id: FLIP_CONEXT_MENU_ID\n    })\n\n    const listRef = useRef(null)\n\n    const autoscrollRef = useRef(autoscroll)\n    autoscrollRef.current = autoscroll\n\n    const flipLookup = {}\n\n    useEffect(() => {\n        setIsSSR(false)\n\n        mounted = true\n        _setAutoScroll(true)\n        attachScrollEvent()\n        isSSR = false\n        api.subscribeFlipsAnonym(\n            getSettingsObject(RESTRICTIONS_SETTINGS_KEY, []),\n            getSettingsObject(FLIPPER_FILTER_KEY, {}),\n            getFlipCustomizeSettings(),\n            onNewFlip,\n            onAuctionSold,\n            onNextFlipNotification\n        )\n        getLastFlipFetchTime()\n\n        const debounceSubFlipAnonymFunction = (function () {\n            let timerId\n\n            return () => {\n                clearTimeout(timerId)\n                timerId = setTimeout(() => {\n                    api.subscribeFlipsAnonym(\n                        getSettingsObject(RESTRICTIONS_SETTINGS_KEY, []) || [],\n                        getSettingsObject(FLIPPER_FILTER_KEY, {}),\n                        getFlipCustomizeSettings(),\n                        onNewFlip,\n                        onAuctionSold,\n                        onNextFlipNotification\n                    )\n                }, 1000)\n            }\n        })()\n\n        let onFlipSettingsChange = e => {\n            if ((e as any).detail?.apiUpdate) {\n                setFlipperFilterKey(generateUUID())\n            }\n            if (sessionStorage.getItem('googleId') === null) {\n                debounceSubFlipAnonymFunction()\n            }\n        }\n        document.addEventListener(CUSTOM_EVENTS.FLIP_SETTINGS_CHANGE, onFlipSettingsChange)\n\n        setIsSmall(document.body.clientWidth < 1000)\n\n        return () => {\n            mounted = false\n            document.removeEventListener(CUSTOM_EVENTS.FLIP_SETTINGS_CHANGE, onFlipSettingsChange)\n            api.unsubscribeFlips()\n        }\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [])\n\n    useEffect(() => {\n        if (sessionStorage.getItem('googleId') !== null && !isLoggedIn) {\n            setFlips([])\n            setIsLoading(true)\n        }\n    }, [wasAlreadyLoggedIn, isLoggedIn])\n\n    function handleFlipContextMenu(event, flip: FlipAuction) {\n        event.preventDefault()\n        show({\n            event: event,\n            props: {\n                flip: flip\n            }\n        })\n    }\n\n    let loadHasPremium = () => {\n\n        let onAfterPremiumProductsLoaded = (products: PremiumProduct[]) => {\n            setHasPremium(hasHighEnoughPremium(products, PREMIUM_RANK.STARTER))\n            setActivePremiumProduct(getHighestPriorityPremiumProduct(products))\n            // subscribe to the premium flips\n            api.subscribeFlips(\n                getSettingsObject(RESTRICTIONS_SETTINGS_KEY, []) || [],\n                flipperFilter,\n                getFlipCustomizeSettings(),\n                onNewFlip,\n                onAuctionSold,\n                onNextFlipNotification\n            )\n            setIsLoading(false)\n        }\n\n        api.getPremiumProducts().then(products => {\n            onAfterPremiumProductsLoaded(products)\n        }).catch(() => {\n            onAfterPremiumProductsLoaded([{\n                expires: new Date(Date.now() + 1000 * 60 * 60 * 24 * 365),\n                productSlug: 'premium',\n            }])\n        })\n    }\n\n    function onLogin() {\n        setFlips([])\n        setIsLoggedIn(true)\n        setIsLoading(true)\n        loadHasPremium()\n        api.getRefInfo().then(refInfo => {\n            setRefInfo(refInfo)\n        })\n    }\n\n    function onLoginFail() {\n        setIsLoading(false)\n    }\n\n    function onArrowRightClick() {\n        if (listRef.current) {\n            ; (listRef.current as any).scrollToItem(flips.length - 1)\n        }\n    }\n\n    function _setAutoScroll(value: boolean) {\n        if (value === true) {\n            onArrowRightClick()\n        }\n        autoscroll = value\n        setAutoscroll(value)\n    }\n\n    function attachScrollEvent(scrollContainer: Element | null = null) {\n        if (isSSR || enabledScroll) {\n            return\n        }\n        if (!scrollContainer)\n            scrollContainer =\n                document.getElementsByClassName(styles.flipperScrollList).length > 0 ? document.getElementsByClassName(styles.flipperScrollList).item(0) : null\n        if (scrollContainer) {\n            scrollContainer.addEventListener('wheel', evt => {\n                evt.preventDefault()\n                let scrollAmount = 0\n                var slideTimer = setInterval(() => {\n                    scrollContainer!.scrollLeft += (evt as WheelEvent).deltaY / 10\n                    scrollAmount += Math.abs((evt as WheelEvent).deltaY) / 10\n                    if (scrollAmount >= Math.abs((evt as WheelEvent).deltaY)) {\n                        clearInterval(slideTimer)\n                    }\n                }, 25)\n            })\n            setEnabledScroll(true)\n            enabledScroll = true\n        }\n    }\n\n    function clearFlips() {\n        setFlips([])\n    }\n\n    function onAuctionSold(uuid: string) {\n        if (!mounted) {\n            return\n        }\n        setFlips(flips => {\n            let index = flips.findIndex(a => a.uuid === uuid)\n            if (index === -1) {\n                return flips\n            }\n\n            flips[index].sold = true\n            if (flips[index] && flipperFilter.onlyUnsold) {\n                flips.splice(index, 1)\n                return flips\n            }\n            return flips\n        })\n    }\n\n    function getLastFlipFetchTime() {\n        setLastFlipFetchTimeLoading(true)\n        api.getFlipUpdateTime().then(date => {\n            setLastFlipFetchTimeSeconds(date.getSeconds() % 60)\n            setLastFlipFetchTimeLoading(false)\n        })\n    }\n\n    function onNextFlipNotification() {\n        setTimeout(() => {\n            let d = new Date()\n            d = new Date(d.getTime() + 10_000)\n            setLastFlipFetchTimeSeconds(d.getSeconds())\n        }, 200)\n    }\n\n    function getCountdownDateObject(): Date {\n        let d = new Date()\n        if (countdownDateObject && countdownDateObject.getTime() > new Date().getTime() && countdownDateObject.getSeconds() === lastFlipFetchTimeSeconds) {\n            return countdownDateObject\n        }\n        d.setSeconds(lastFlipFetchTimeSeconds!)\n        if (d.getSeconds() < new Date().getSeconds()) {\n            d.setMinutes(new Date().getMinutes() + 1)\n        } else {\n            d.setMinutes(new Date().getMinutes())\n        }\n        setLastFlipFetchTimeLoading(true)\n        setTimeout(() => {\n            setCountdownDateObject(d)\n            setLastFlipFetchTimeLoading(false)\n        }, 200)\n        return d\n    }\n\n    function onCountdownComplete() {\n        setLastFlipFetchTimeLoading(true)\n        setTimeout(() => {\n            setLastFlipFetchTimeLoading(false)\n        }, 200)\n    }\n\n    function onNewFlip(newFlipAuction: FlipAuction) {\n        if (flipLookup[newFlipAuction.uuid] || !mounted) {\n            return\n        }\n\n        if (flipperFilter.onlyUnsold && newFlipAuction.sold) {\n            return\n        }\n\n        flipLookup[newFlipAuction.uuid] = newFlipAuction\n\n        newFlipAuction.item.iconUrl = api.getItemImageUrl(newFlipAuction.item)\n        newFlipAuction.showLink = true\n\n        missedInfo = {\n            estimatedProfitCopiedAuctions: missedInfo.estimatedProfitCopiedAuctions,\n            missedEstimatedProfit: newFlipAuction.sold ? missedInfo.missedEstimatedProfit + newFlipAuction.profit : missedInfo.missedEstimatedProfit,\n            missedFlipsCount: newFlipAuction.sold ? missedInfo.missedFlipsCount + 1 : missedInfo.missedFlipsCount,\n            totalFlips: missedInfo.totalFlips + 1,\n            totalProfit: missedInfo.totalProfit + newFlipAuction.profit\n        }\n\n        setFlips(flips => {\n            let newFlips = [...flips, newFlipAuction]\n            if (autoscrollRef.current && listRef.current) {\n                setTimeout(() => {\n                    let element =\n                        document.getElementsByClassName(styles.flipperScrollList).length > 0\n                            ? document.getElementsByClassName(styles.flipperScrollList).item(0)\n                            : null\n                    if (element) {\n                        element.scrollBy({ left: 16000, behavior: 'smooth' })\n                        attachScrollEvent(element)\n                    }\n                }, 200)\n            }\n            return newFlips\n        })\n    }\n\n    function onFilterChange(newFilter) {\n        setFlipperFilter(newFilter)\n        setFlips([])\n        if (listRef.current) {\n            ; (listRef.current as any)?.scrollToItem(flips.length - 1)\n        }\n    }\n\n    function onCopyFlip(flip: FlipAuction) {\n        let settings = getFlipCustomizeSettings()\n        let currentMissedInfo = missedInfo\n        currentMissedInfo.estimatedProfitCopiedAuctions += flip.profit\n        flip.isCopied = true\n        setFlips(flips)\n    }\n\n    function getFlipForList(listData) {\n        let { data, index, style } = listData\n        let { flips } = data\n\n        return (\n            <div key={'flip-' + index} id={'flip-' + index}>\n                {getFlipElement(flips[index], style)}\n            </div>\n        )\n    }\n\n    function addItemToBlacklist(flip: FlipAuction) {\n        let restrictions = getSetting(RESTRICTIONS_SETTINGS_KEY, '[]')\n        let parsed: FlipRestriction[]\n        try {\n            parsed = JSON.parse(restrictions)\n        } catch {\n            parsed = []\n        }\n        parsed.push({\n            type: 'blacklist',\n            item: {\n                tag: flip.item.tag,\n                name: flip.item.name\n            },\n            itemFilter: {}\n        })\n\n        setSetting(RESTRICTIONS_SETTINGS_KEY, JSON.stringify(parsed))\n        onFilterChange(flipperFilter)\n    }\n\n    function redirectToSeller(sellerName: string) {\n        router.push('/player/' + sellerName)\n    }\n\n    function resetSettingsToDefault() {\n        api.subscribeFlips(\n            DEFAULT_FLIP_SETTINGS.RESTRICTIONS,\n            DEFAULT_FLIP_SETTINGS.FILTER,\n            DEFAULT_FLIP_SETTINGS.FLIP_CUSTOMIZE,\n            undefined,\n            undefined,\n            undefined,\n            () => {\n                window.location.reload()\n            },\n            () => { },\n            true\n        )\n        localStorage.removeItem('userSettings')\n    }\n\n    let getFlipElement = (flipAuction: FlipAuction, style) => {\n        if (!flipAuction) {\n            return <div />\n        }\n        return (\n            <div onContextMenu={e => handleFlipContextMenu(e, flipAuction)}>\n                <Flip\n                    flip={flipAuction}\n                    style={{\n                        ...style,\n                        padding: '10px'\n                    }}\n                    onCopy={onCopyFlip}\n                    onCardClick={flip => setSelectedAuctionUUID(flip.uuid)}\n                    onBasedAuctionClick={flip => {\n                        setBasedOnAuction(flip)\n                    }}\n                />\n            </div>\n        )\n    }\n\n    function getCardTitleElement() {\n        if (isLoading) {\n            return getLoadingElement(<p>Logging in with Google...</p>)\n        }\n        if (!isLoggedIn) {\n            return <h2>Free Auction Flipper</h2>\n        }\n        if (hasPremium) {\n            let type = getPremiumType(activePremiumProduct!)\n            if (!type) {\n                return null\n            }\n            return (\n                <span>\n                    You are using{' '}\n                    <Link href=\"/premium\">\n                        {\n                            {\n                                1: 'Starter Premium',\n                                2: 'Premium',\n                                3: 'Premium+'\n                            }[type.priority]\n                        }\n                    </Link>\n                </span>\n            )\n        }\n        return (\n            <span>\n                These auctions are delayed by about one minute. Please purchase{' '}\n                <a target=\"_blank\" rel=\"noreferrer\" href=\"/premium\">\n                    premium\n                </a>{' '}\n                if you want real time flips.\n            </span>\n        )\n    }\n\n    function onDrop(e) {\n        e.preventDefault()\n        var output = '' //placeholder for text output\n        let reader = new FileReader()\n        let file = e.dataTransfer.items[0].getAsFile()\n        if (file) {\n            reader.onload = function (e) {\n                output = e.target!.result!.toString()\n                handleSettingsImport(output)\n            }\n            reader.readAsText(file)\n        }\n        return true\n    }\n\n    function onDragOver(e) {\n        e.preventDefault()\n    }\n\n    let basedOnDialog =\n        basedOnAuction === null ? null : (\n            <Modal\n                size={'xl'}\n                show={basedOnAuction !== null}\n                onHide={() => {\n                    setBasedOnAuction(null)\n                }}\n            >\n                <Modal.Header closeButton>\n                    <Modal.Title>Auctions used for calculating the median price</Modal.Title>\n                </Modal.Header>\n                <Modal.Body>\n                    <FlipBased auctionUUID={basedOnAuction.uuid} item={basedOnAuction.item} />\n                </Modal.Body>\n            </Modal>\n        )\n\n    let flipContextMenu = (\n        <div>\n            <Menu id={FLIP_CONEXT_MENU_ID} theme={'dark'}>\n                <Item\n                    onClick={params => {\n                        addItemToBlacklist(params.props.flip as FlipAuction)\n                    }}\n                >\n                    <HandIcon style={{ color: 'red', marginRight: '5px' }} /> Add Item to Blacklist\n                </Item>\n                <Item\n                    onClick={params => {\n                        redirectToSeller((params.props.flip as FlipAuction).sellerName)\n                    }}\n                >\n                    <SearchIcon style={{ marginRight: '5px' }} />\n                    Open seller auction history\n                </Item>\n            </Menu>\n        </div>\n    )\n\n    let resetSettingsElement = (\n        <span>\n            Reset Flipper settings back to default\n            <Button\n                style={{ marginLeft: '5px' }}\n                onClick={() => {\n                    setShowResetToDefaultDialog(true)\n                }}\n            >\n                Reset\n            </Button>\n            <Modal\n                show={showResetToDefaultDialog}\n                onHide={() => {\n                    setShowResetToDefaultDialog(false)\n                }}\n            >\n                <Modal.Header closeButton>\n                    <Modal.Title>Confirmation</Modal.Title>\n                </Modal.Header>\n                <Modal.Body>\n                    <p>Are you sure you want to reset all the flipper settings?</p>\n                    <p>\n                        <b>This will delete all your filter, settings and black-/whitelist.</b>\n                    </p>\n                    <div style={{ display: 'flex', justifyContent: 'space-between' }}>\n                        <Button variant=\"danger\" style={{ width: '45%' }} onClick={resetSettingsToDefault}>\n                            RESET <DeleteIcon />\n                        </Button>\n                        <Button\n                            style={{ width: '45%' }}\n                            onClick={() => {\n                                setShowResetToDefaultDialog(false)\n                            }}\n                        >\n                            Cancel\n                        </Button>\n                    </div>\n                </Modal.Body>\n            </Modal>\n        </span>\n    )\n\n    return (\n        <div className={styles.flipper} onDragOver={onDragOver} onDrop={onDrop}>\n            <Card>\n                <Card.Header>\n                    <Card.Title>\n                        {getCardTitleElement()}\n                        <GoogleSignIn onAfterLogin={onLogin} onLoginFail={onLoginFail} />\n                    </Card.Title>\n                    {!isLoading && !hasPremium ? (\n                        <Card.Subtitle>\n                            You need to be logged and have Premium to have all <Link href=\"/premium\">features</Link> unlocked.\n                        </Card.Subtitle>\n                    ) : null}\n                </Card.Header>\n                <Card.Body>\n                    <div id=\"flipper-card-body\">\n                        <FlipperFilter key={flipperFilterKey} onChange={onFilterChange} isLoggedIn={isLoggedIn} isPremium={hasPremium} />\n                        <hr />\n                        <Form className={styles.flipperSettingsForm}>\n                            <Form.Group>\n                                <Form.Label htmlFor=\"autoScrollCheckbox\" style={{ marginRight: '10px' }}>\n                                    Auto-Scroll?\n                                </Form.Label>\n                                <Form.Check\n                                    style={{ display: 'inline' }}\n                                    id=\"autoScrollCheckbox\"\n                                    checked={autoscroll}\n                                    onChange={e => {\n                                        _setAutoScroll(e.target.checked)\n                                    }}\n                                    type=\"checkbox\"\n                                />\n                            </Form.Group>\n                            <Form.Group>\n                                <div style={{ display: 'contents', cursor: 'pointer', marginRight: '10px' }} onClick={clearFlips}>\n                                    <Form.Label>Clear flips!</Form.Label>\n                                    <DeleteIcon color=\"error\" />\n                                </div>\n                            </Form.Group>\n                            {hasPremium ? (\n                                <Tooltip\n                                    type=\"hover\"\n                                    content={\n                                        <span>\n                                            Next update:{' '}\n                                            {lastFlipFetchTimeSeconds !== undefined && !lastFlipFetchTimeLoading ? (\n                                                <Countdown\n                                                    date={getCountdownDateObject()}\n                                                    onComplete={onCountdownComplete}\n                                                    renderer={({ seconds }) => <span>{zeroPad(seconds)}</span>}\n                                                />\n                                            ) : (\n                                                '...'\n                                            )}\n                                        </span>\n                                    }\n                                    tooltipContent={\n                                        <p>\n                                            The Hypixel API updates once a minute. This is the estimated time (in seconds) until the next batch of flips will be\n                                            shown.\n                                        </p>\n                                    }\n                                />\n                            ) : null}\n                            {!autoscroll ? (\n                                <Form.Group onClick={onArrowRightClick}>\n                                    <Form.Label style={{ cursor: 'pointer', marginRight: '10px' }}>To newest flip</Form.Label>\n                                    <span style={{ cursor: 'pointer' }}>\n                                        {' '}\n                                        <ArrowRightIcon />\n                                    </span>\n                                </Form.Group>\n                            ) : null}\n                        </Form>\n                        <hr />\n                        {!isSSR ? (\n                            <div\n                                id=\"flipper-scroll-list-wrapper\"\n                                style={{ height: document.getElementById('maxHeightDummyFlip')?.offsetHeight, width: '100%' }}\n                            >\n                                <AutoSizer>\n                                    {({ height, width }) => (\n                                        <List\n                                            ref={listRef}\n                                            className={styles.flipperScrollList}\n                                            height={height}\n                                            itemCount={flips.length}\n                                            itemData={{ flips: flips }}\n                                            itemSize={isSmall ? 300 : 330}\n                                            layout=\"horizontal\"\n                                            width={width}\n                                        >\n                                            {getFlipForList}\n                                        </List>\n                                    )}\n                                </AutoSizer>\n                            </div>\n                        ) : (\n                            <div className={`${styles.SSRcardsWrapper} ${styles.flipperScrollList}`}>\n                                {flips.map((flip, index) => {\n                                    return <span key={'flip' + index}>{getFlipElement(flip, { width: '300px', height: '100%' })}</span>\n                                })}\n                            </div>\n                        )}\n                    </div>\n                </Card.Body>\n                <Card.Footer>\n                    For support and suggestions, join our{' '}\n                    <a target=\"_blank\" rel=\"noreferrer\" href=\"https://discord.gg/wvKXfTgCfb\">\n                        Discord\n                    </a>\n                    .\n                    <hr />\n                    {isLoggedIn ? (\n                        ''\n                    ) : (\n                        <div>\n                            <span>\n                                These are flips that were previously found (~5 min ago). Anyone can use these and there is no cap on estimated profit. Keep in\n                                mind that these are delayed to protect our paying supporters. If you want more recent flips purchase our{' '}\n                                <a target=\"_blank\" rel=\"noreferrer\" href=\"/premium\">\n                                    premium plan.\n                                </a>\n                            </span>\n                            <hr />\n                        </div>\n                    )}\n                    {resetSettingsElement}\n                </Card.Footer>\n            </Card>\n            {selectedAuctionUUID ? (\n                <div>\n                    <hr />\n                    <Card className=\"card\">\n                        <Card.Header>\n                            <Card.Title>Auction Details</Card.Title>\n                        </Card.Header>\n                        <Card.Body>\n                            <AuctionDetails auctionUUID={selectedAuctionUUID} retryCounter={5} copyButtonValue=\"web\" />\n                        </Card.Body>\n                    </Card>\n                </div>\n            ) : null}\n            <div>\n                <hr />\n                <Card>\n                    <Card.Header>\n                        <Card.Title>Flipper Summary</Card.Title>\n                    </Card.Header>\n                    <Card.Body>\n                        <div className={styles.flipperSummaryWrapper}>\n                            <Card className={styles.flipperSummaryCard}>\n                                <Card.Header>\n                                    <Card.Title>You got:</Card.Title>\n                                </Card.Header>\n                                <Card.Body>\n                                    <ul>\n                                        <li>\n                                            Total flips received: <Number number={missedInfo.totalFlips} />\n                                        </li>\n                                        <li>\n                                            Total found profit: <Number number={missedInfo.totalProfit} /> Coins\n                                        </li>\n                                        <li>\n                                            Profit of copied flips: <Number number={missedInfo.estimatedProfitCopiedAuctions} /> Coins\n                                        </li>\n                                    </ul>\n                                </Card.Body>\n                            </Card>\n                            {!isLoading && isLoggedIn && !hasPremium ? (\n                                <Card className={styles.flipperSummaryCard}>\n                                    <Card.Header>\n                                        <Card.Title>You don't have Premium</Card.Title>\n                                    </Card.Header>\n                                    <Card.Body>\n                                        <ul>\n                                            <li>\n                                                <span style={{ marginRight: '10px' }}>\n                                                    Missed Profit: <Number number={missedInfo.missedEstimatedProfit} /> Coins\n                                                </span>\n                                                <Tooltip\n                                                    type=\"hover\"\n                                                    content={<HelpIcon />}\n                                                    tooltipContent={\n                                                        <span>\n                                                            This is the sum of the field 'Estimated profit' of the flips that were already sold when you\n                                                            received them. It represents the extra coins you could earn if you purchased our premium plan\n                                                        </span>\n                                                    }\n                                                />\n                                            </li>\n                                            <li>\n                                                Missed Flips: <Number number={missedInfo.missedFlipsCount} />\n                                            </li>\n                                        </ul>\n                                    </Card.Body>\n                                </Card>\n                            ) : null}\n                            {isLoggedIn ? (\n                                <Card style={{ flexGrow: 2 }} className={styles.flipperSummaryCard}>\n                                    <Card.Header>\n                                        {!isLoading && isLoggedIn && hasPremium ? (\n                                            <Card.Title>How to get extra premium time for free</Card.Title>\n                                        ) : (\n                                            <Card.Title>How to get premium for free</Card.Title>\n                                        )}\n                                    </Card.Header>\n                                    <Card.Body>\n                                        <p>\n                                            Get free premium time by inviting other people to our website. For further information check out our{' '}\n                                            <Link href=\"/ref\">Referral Program</Link>.\n                                        </p>\n                                        <p>\n                                            Your Link to invite people:{' '}\n                                            <span style={{ fontStyle: 'italic', color: 'skyblue' }}>\n                                                {!isSSR ? window.location.href.split('?')[0] + '?refId=' + refInfo?.oldInfo.refId : ''}\n                                            </span>{' '}\n                                            <CopyButton\n                                                copyValue={!isSSR ? window.location.href.split('?')[0] + '?refId=' + refInfo?.oldInfo.refId : ''}\n                                                successMessage={<span>Copied Referral Link</span>}\n                                            />\n                                        </p>\n                                    </Card.Body>\n                                </Card>\n                            ) : null}\n                        </div>\n                    </Card.Body>\n                </Card>\n            </div>\n            <hr />\n            <FlipperFAQ />\n            <div id=\"maxHeightDummyFlip\" style={{ position: 'absolute', top: -1000, padding: '20px', zIndex: -1 }}>\n                <Flip flip={DEMO_FLIP} />\n            </div>\n            {basedOnDialog}\n            {flipContextMenu}\n        </div>\n    )\n}\n\nexport default Flipper\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAhCA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkCA,cAAc;AACd,+DAA+D;AAC/D,IAAI,aAAyC;IACzC,+BAA+B;IAC/B,uBAAuB;IACvB,kBAAkB;IAClB,YAAY;IACZ,aAAa;AACjB;AAEA,IAAI,UAAU;AAEd,MAAM,sBAAsB;AAM5B,SAAS,QAAQ,KAAY;;IACzB,IAAI,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD,EAAiB,0HAAA,CAAA,qBAAkB,EAAE,CAAC;IACtH,IAAI,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAC3B,MAAM,KAAK,GACL,MAAM,KAAK,CAAC,GAAG,CAAC,wIAAA,CAAA,mBAAgB,EAAE,MAAM;4BAAC,CAAA;YACvC,OAAO,cAAc,UAAU,GAAG,CAAC,KAAK,IAAI,GAAG;QACnD;6BACE,EAAE;IAEZ,IAAI,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,IAAI,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,IAAI,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,IAAI,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;IAC7D,IAAI,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,IAAI,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,IAAI,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;IACnC,IAAI,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;IACvE,IAAI,CAAC,0BAA0B,4BAA4B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;IACrE,IAAI,CAAC,0BAA0B,4BAA4B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAChF,IAAI,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;IAC3D,IAAI,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,IAAI,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,IAAI,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,IAAI,CAAC,0BAA0B,4BAA4B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvE,IAAI,qBAAqB,CAAA,GAAA,kHAAA,CAAA,wBAAqB,AAAD;IAE7C,IAAI,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,CAAA,GAAA,wLAAA,CAAA,KAAY,AAAD;IAE1E,IAAI,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAErB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,uJAAA,CAAA,iBAAc,AAAD,EAAE;QAC5B,IAAI;IACR;IAEA,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAEvB,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC7B,cAAc,OAAO,GAAG;IAExB,MAAM,aAAa,CAAC;IAEpB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACN,SAAS;YAET,UAAU;YACV,eAAe;YACf;YACA,QAAQ;YACR,oHAAA,CAAA,UAAG,CAAC,oBAAoB,CACpB,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD,EAAE,0HAAA,CAAA,4BAAyB,EAAE,EAAE,GAC/C,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD,EAAE,0HAAA,CAAA,qBAAkB,EAAE,CAAC,IACvC,CAAA,GAAA,sHAAA,CAAA,2BAAwB,AAAD,KACvB,WACA,eACA;YAEJ;YAEA,MAAM,gCAAgC;mEAAC;oBACnC,IAAI;oBAEJ,OAHkC;2EAG3B;4BACH,aAAa;4BACb,UAAU;mFAAW;oCACjB,oHAAA,CAAA,UAAG,CAAC,oBAAoB,CACpB,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD,EAAE,0HAAA,CAAA,4BAAyB,EAAE,EAAE,KAAK,EAAE,EACtD,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD,EAAE,0HAAA,CAAA,qBAAkB,EAAE,CAAC,IACvC,CAAA,GAAA,sHAAA,CAAA,2BAAwB,AAAD,KACvB,WACA,eACA;gCAER;kFAAG;wBACP;qBACH;gBAAD;;YAEA,IAAI;0DAAuB,CAAA;oBACvB,IAAI,AAAC,EAAU,MAAM,EAAE,WAAW;wBAC9B,oBAAoB,CAAA,GAAA,wLAAA,CAAA,KAAY,AAAD;oBACnC;oBACA,IAAI,eAAe,OAAO,CAAC,gBAAgB,MAAM;wBAC7C;oBACJ;gBACJ;;YACA,SAAS,gBAAgB,CAAC,wHAAA,CAAA,gBAAa,CAAC,oBAAoB,EAAE;YAE9D,WAAW,SAAS,IAAI,CAAC,WAAW,GAAG;YAEvC;qCAAO;oBACH,UAAU;oBACV,SAAS,mBAAmB,CAAC,wHAAA,CAAA,gBAAa,CAAC,oBAAoB,EAAE;oBACjE,oHAAA,CAAA,UAAG,CAAC,gBAAgB;gBACxB;;QACA,uDAAuD;QAC3D;4BAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACN,IAAI,eAAe,OAAO,CAAC,gBAAgB,QAAQ,CAAC,YAAY;gBAC5D,SAAS,EAAE;gBACX,aAAa;YACjB;QACJ;4BAAG;QAAC;QAAoB;KAAW;IAEnC,SAAS,sBAAsB,KAAK,EAAE,IAAiB;QACnD,MAAM,cAAc;QACpB,KAAK;YACD,OAAO;YACP,OAAO;gBACH,MAAM;YACV;QACJ;IACJ;IAEA,IAAI,iBAAiB;QAEjB,IAAI,+BAA+B,CAAC;YAChC,cAAc,CAAA,GAAA,6HAAA,CAAA,uBAAoB,AAAD,EAAE,UAAU,6HAAA,CAAA,eAAY,CAAC,OAAO;YACjE,wBAAwB,CAAA,GAAA,6HAAA,CAAA,mCAAgC,AAAD,EAAE;YACzD,iCAAiC;YACjC,oHAAA,CAAA,UAAG,CAAC,cAAc,CACd,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD,EAAE,0HAAA,CAAA,4BAAyB,EAAE,EAAE,KAAK,EAAE,EACtD,eACA,CAAA,GAAA,sHAAA,CAAA,2BAAwB,AAAD,KACvB,WACA,eACA;YAEJ,aAAa;QACjB;QAEA,oHAAA,CAAA,UAAG,CAAC,kBAAkB,GAAG,IAAI,CAAC,CAAA;YAC1B,6BAA6B;QACjC,GAAG,KAAK,CAAC;YACL,6BAA6B;gBAAC;oBAC1B,SAAS,IAAI,KAAK,KAAK,GAAG,KAAK,OAAO,KAAK,KAAK,KAAK;oBACrD,aAAa;gBACjB;aAAE;QACN;IACJ;IAEA,SAAS;QACL,SAAS,EAAE;QACX,cAAc;QACd,aAAa;QACb;QACA,oHAAA,CAAA,UAAG,CAAC,UAAU,GAAG,IAAI,CAAC,CAAA;YAClB,WAAW;QACf;IACJ;IAEA,SAAS;QACL,aAAa;IACjB;IAEA,SAAS;QACL,IAAI,QAAQ,OAAO,EAAE;;YACd,QAAQ,OAAO,CAAS,YAAY,CAAC,MAAM,MAAM,GAAG;QAC3D;IACJ;IAEA,SAAS,eAAe,KAAc;QAClC,IAAI,UAAU,MAAM;YAChB;QACJ;QACA,aAAa;QACb,cAAc;IAClB;IAEA,SAAS,kBAAkB,kBAAkC,IAAI;QAC7D,IAAI,SAAS,eAAe;YACxB;QACJ;QACA,IAAI,CAAC,iBACD,kBACI,SAAS,sBAAsB,CAAC,+IAAA,CAAA,UAAM,CAAC,iBAAiB,EAAE,MAAM,GAAG,IAAI,SAAS,sBAAsB,CAAC,+IAAA,CAAA,UAAM,CAAC,iBAAiB,EAAE,IAAI,CAAC,KAAK;QACnJ,IAAI,iBAAiB;YACjB,gBAAgB,gBAAgB,CAAC,SAAS,CAAA;gBACtC,IAAI,cAAc;gBAClB,IAAI,eAAe;gBACnB,IAAI,aAAa,YAAY;oBACzB,gBAAiB,UAAU,IAAI,AAAC,IAAmB,MAAM,GAAG;oBAC5D,gBAAgB,KAAK,GAAG,CAAC,AAAC,IAAmB,MAAM,IAAI;oBACvD,IAAI,gBAAgB,KAAK,GAAG,CAAC,AAAC,IAAmB,MAAM,GAAG;wBACtD,cAAc;oBAClB;gBACJ,GAAG;YACP;YACA,iBAAiB;YACjB,gBAAgB;QACpB;IACJ;IAEA,SAAS;QACL,SAAS,EAAE;IACf;IAEA,SAAS,cAAc,IAAY;QAC/B,IAAI,CAAC,SAAS;YACV;QACJ;QACA,SAAS,CAAA;YACL,IAAI,QAAQ,MAAM,SAAS,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK;YAC5C,IAAI,UAAU,CAAC,GAAG;gBACd,OAAO;YACX;YAEA,KAAK,CAAC,MAAM,CAAC,IAAI,GAAG;YACpB,IAAI,KAAK,CAAC,MAAM,IAAI,cAAc,UAAU,EAAE;gBAC1C,MAAM,MAAM,CAAC,OAAO;gBACpB,OAAO;YACX;YACA,OAAO;QACX;IACJ;IAEA,SAAS;QACL,4BAA4B;QAC5B,oHAAA,CAAA,UAAG,CAAC,iBAAiB,GAAG,IAAI,CAAC,CAAA;YACzB,4BAA4B,KAAK,UAAU,KAAK;YAChD,4BAA4B;QAChC;IACJ;IAEA,SAAS;QACL,WAAW;YACP,IAAI,IAAI,IAAI;YACZ,IAAI,IAAI,KAAK,EAAE,OAAO,KAAK;YAC3B,4BAA4B,EAAE,UAAU;QAC5C,GAAG;IACP;IAEA,SAAS;QACL,IAAI,IAAI,IAAI;QACZ,IAAI,uBAAuB,oBAAoB,OAAO,KAAK,IAAI,OAAO,OAAO,MAAM,oBAAoB,UAAU,OAAO,0BAA0B;YAC9I,OAAO;QACX;QACA,EAAE,UAAU,CAAC;QACb,IAAI,EAAE,UAAU,KAAK,IAAI,OAAO,UAAU,IAAI;YAC1C,EAAE,UAAU,CAAC,IAAI,OAAO,UAAU,KAAK;QAC3C,OAAO;YACH,EAAE,UAAU,CAAC,IAAI,OAAO,UAAU;QACtC;QACA,4BAA4B;QAC5B,WAAW;YACP,uBAAuB;YACvB,4BAA4B;QAChC,GAAG;QACH,OAAO;IACX;IAEA,SAAS;QACL,4BAA4B;QAC5B,WAAW;YACP,4BAA4B;QAChC,GAAG;IACP;IAEA,SAAS,UAAU,cAA2B;QAC1C,IAAI,UAAU,CAAC,eAAe,IAAI,CAAC,IAAI,CAAC,SAAS;YAC7C;QACJ;QAEA,IAAI,cAAc,UAAU,IAAI,eAAe,IAAI,EAAE;YACjD;QACJ;QAEA,UAAU,CAAC,eAAe,IAAI,CAAC,GAAG;QAElC,eAAe,IAAI,CAAC,OAAO,GAAG,oHAAA,CAAA,UAAG,CAAC,eAAe,CAAC,eAAe,IAAI;QACrE,eAAe,QAAQ,GAAG;QAE1B,aAAa;YACT,+BAA+B,WAAW,6BAA6B;YACvE,uBAAuB,eAAe,IAAI,GAAG,WAAW,qBAAqB,GAAG,eAAe,MAAM,GAAG,WAAW,qBAAqB;YACxI,kBAAkB,eAAe,IAAI,GAAG,WAAW,gBAAgB,GAAG,IAAI,WAAW,gBAAgB;YACrG,YAAY,WAAW,UAAU,GAAG;YACpC,aAAa,WAAW,WAAW,GAAG,eAAe,MAAM;QAC/D;QAEA,SAAS,CAAA;YACL,IAAI,WAAW;mBAAI;gBAAO;aAAe;YACzC,IAAI,cAAc,OAAO,IAAI,QAAQ,OAAO,EAAE;gBAC1C,WAAW;oBACP,IAAI,UACA,SAAS,sBAAsB,CAAC,+IAAA,CAAA,UAAM,CAAC,iBAAiB,EAAE,MAAM,GAAG,IAC7D,SAAS,sBAAsB,CAAC,+IAAA,CAAA,UAAM,CAAC,iBAAiB,EAAE,IAAI,CAAC,KAC/D;oBACV,IAAI,SAAS;wBACT,QAAQ,QAAQ,CAAC;4BAAE,MAAM;4BAAO,UAAU;wBAAS;wBACnD,kBAAkB;oBACtB;gBACJ,GAAG;YACP;YACA,OAAO;QACX;IACJ;IAEA,SAAS,eAAe,SAAS;QAC7B,iBAAiB;QACjB,SAAS,EAAE;QACX,IAAI,QAAQ,OAAO,EAAE;;YACd,QAAQ,OAAO,EAAU,aAAa,MAAM,MAAM,GAAG;QAC5D;IACJ;IAEA,SAAS,WAAW,IAAiB;QACjC,IAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,2BAAwB,AAAD;QACtC,IAAI,oBAAoB;QACxB,kBAAkB,6BAA6B,IAAI,KAAK,MAAM;QAC9D,KAAK,QAAQ,GAAG;QAChB,SAAS;IACb;IAEA,SAAS,eAAe,QAAQ;QAC5B,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG;QAC7B,IAAI,EAAE,KAAK,EAAE,GAAG;QAEhB,qBACI,6LAAC;YAA0B,IAAI,UAAU;sBACpC,eAAe,KAAK,CAAC,MAAM,EAAE;WADxB,UAAU;;;;;IAI5B;IAEA,SAAS,mBAAmB,IAAiB;QACzC,IAAI,eAAe,CAAA,GAAA,0HAAA,CAAA,aAAU,AAAD,EAAE,0HAAA,CAAA,4BAAyB,EAAE;QACzD,IAAI;QACJ,IAAI;YACA,SAAS,KAAK,KAAK,CAAC;QACxB,EAAE,OAAM;YACJ,SAAS,EAAE;QACf;QACA,OAAO,IAAI,CAAC;YACR,MAAM;YACN,MAAM;gBACF,KAAK,KAAK,IAAI,CAAC,GAAG;gBAClB,MAAM,KAAK,IAAI,CAAC,IAAI;YACxB;YACA,YAAY,CAAC;QACjB;QAEA,CAAA,GAAA,0HAAA,CAAA,aAAU,AAAD,EAAE,0HAAA,CAAA,4BAAyB,EAAE,KAAK,SAAS,CAAC;QACrD,eAAe;IACnB;IAEA,SAAS,iBAAiB,UAAkB;QACxC,OAAO,IAAI,CAAC,aAAa;IAC7B;IAEA,SAAS;QACL,oHAAA,CAAA,UAAG,CAAC,cAAc,CACd,sHAAA,CAAA,wBAAqB,CAAC,YAAY,EAClC,sHAAA,CAAA,wBAAqB,CAAC,MAAM,EAC5B,sHAAA,CAAA,wBAAqB,CAAC,cAAc,EACpC,WACA,WACA,WACA;YACI,OAAO,QAAQ,CAAC,MAAM;QAC1B,GACA,KAAQ,GACR;QAEJ,aAAa,UAAU,CAAC;IAC5B;IAEA,IAAI,iBAAiB,CAAC,aAA0B;QAC5C,IAAI,CAAC,aAAa;YACd,qBAAO,6LAAC;;;;;QACZ;QACA,qBACI,6LAAC;YAAI,eAAe,CAAA,IAAK,sBAAsB,GAAG;sBAC9C,cAAA,6LAAC,yIAAA,CAAA,UAAI;gBACD,MAAM;gBACN,OAAO;oBACH,GAAG,KAAK;oBACR,SAAS;gBACb;gBACA,QAAQ;gBACR,aAAa,CAAA,OAAQ,uBAAuB,KAAK,IAAI;gBACrD,qBAAqB,CAAA;oBACjB,kBAAkB;gBACtB;;;;;;;;;;;IAIhB;IAEA,SAAS;QACL,IAAI,WAAW;YACX,OAAO,CAAA,GAAA,yHAAA,CAAA,oBAAiB,AAAD,gBAAE,6LAAC;0BAAE;;;;;;QAChC;QACA,IAAI,CAAC,YAAY;YACb,qBAAO,6LAAC;0BAAG;;;;;;QACf;QACA,IAAI,YAAY;YACZ,IAAI,OAAO,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;YAC1B,IAAI,CAAC,MAAM;gBACP,OAAO;YACX;YACA,qBACI,6LAAC;;oBAAK;oBACY;kCACd,6LAAC,+JAAA,CAAA,UAAI;wBAAC,MAAK;kCAEH;4BACI,GAAG;4BACH,GAAG;4BACH,GAAG;wBACP,CAAC,CAAC,KAAK,QAAQ,CAAC;;;;;;;;;;;;QAKpC;QACA,qBACI,6LAAC;;gBAAK;gBAC8D;8BAChE,6LAAC;oBAAE,QAAO;oBAAS,KAAI;oBAAa,MAAK;8BAAW;;;;;;gBAE/C;gBAAI;;;;;;;IAIrB;IAEA,SAAS,OAAO,CAAC;QACb,EAAE,cAAc;QAChB,IAAI,SAAS,GAAG,6BAA6B;;QAC7C,IAAI,SAAS,IAAI;QACjB,IAAI,OAAO,EAAE,YAAY,CAAC,KAAK,CAAC,EAAE,CAAC,SAAS;QAC5C,IAAI,MAAM;YACN,OAAO,MAAM,GAAG,SAAU,CAAC;gBACvB,SAAS,EAAE,MAAM,CAAE,MAAM,CAAE,QAAQ;gBACnC,CAAA,GAAA,0HAAA,CAAA,uBAAoB,AAAD,EAAE;YACzB;YACA,OAAO,UAAU,CAAC;QACtB;QACA,OAAO;IACX;IAEA,SAAS,WAAW,CAAC;QACjB,EAAE,cAAc;IACpB;IAEA,IAAI,gBACA,mBAAmB,OAAO,qBACtB,6LAAC,yLAAA,CAAA,QAAK;QACF,MAAM;QACN,MAAM,mBAAmB;QACzB,QAAQ;YACJ,kBAAkB;QACtB;;0BAEA,6LAAC,yLAAA,CAAA,QAAK,CAAC,MAAM;gBAAC,WAAW;0BACrB,cAAA,6LAAC,yLAAA,CAAA,QAAK,CAAC,KAAK;8BAAC;;;;;;;;;;;0BAEjB,6LAAC,yLAAA,CAAA,QAAK,CAAC,IAAI;0BACP,cAAA,6LAAC,mJAAA,CAAA,UAAS;oBAAC,aAAa,eAAe,IAAI;oBAAE,MAAM,eAAe,IAAI;;;;;;;;;;;;;;;;;IAKtF,IAAI,gCACA,6LAAC;kBACG,cAAA,6LAAC,uJAAA,CAAA,OAAI;YAAC,IAAI;YAAqB,OAAO;;8BAClC,6LAAC,uJAAA,CAAA,OAAI;oBACD,SAAS,CAAA;wBACL,mBAAmB,OAAO,KAAK,CAAC,IAAI;oBACxC;;sCAEA,6LAAC,+JAAA,CAAA,UAAQ;4BAAC,OAAO;gCAAE,OAAO;gCAAO,aAAa;4BAAM;;;;;;wBAAK;;;;;;;8BAE7D,6LAAC,uJAAA,CAAA,OAAI;oBACD,SAAS,CAAA;wBACL,iBAAiB,AAAC,OAAO,KAAK,CAAC,IAAI,CAAiB,UAAU;oBAClE;;sCAEA,6LAAC,8JAAA,CAAA,UAAU;4BAAC,OAAO;gCAAE,aAAa;4BAAM;;;;;;wBAAK;;;;;;;;;;;;;;;;;;IAO7D,IAAI,qCACA,6LAAC;;YAAK;0BAEF,6LAAC,2LAAA,CAAA,SAAM;gBACH,OAAO;oBAAE,YAAY;gBAAM;gBAC3B,SAAS;oBACL,4BAA4B;gBAChC;0BACH;;;;;;0BAGD,6LAAC,yLAAA,CAAA,QAAK;gBACF,MAAM;gBACN,QAAQ;oBACJ,4BAA4B;gBAChC;;kCAEA,6LAAC,yLAAA,CAAA,QAAK,CAAC,MAAM;wBAAC,WAAW;kCACrB,cAAA,6LAAC,yLAAA,CAAA,QAAK,CAAC,KAAK;sCAAC;;;;;;;;;;;kCAEjB,6LAAC,yLAAA,CAAA,QAAK,CAAC,IAAI;;0CACP,6LAAC;0CAAE;;;;;;0CACH,6LAAC;0CACG,cAAA,6LAAC;8CAAE;;;;;;;;;;;0CAEP,6LAAC;gCAAI,OAAO;oCAAE,SAAS;oCAAQ,gBAAgB;gCAAgB;;kDAC3D,6LAAC,2LAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAS,OAAO;4CAAE,OAAO;wCAAM;wCAAG,SAAS;;4CAAwB;0DACzE,6LAAC,8JAAA,CAAA,UAAU;;;;;;;;;;;kDAErB,6LAAC,2LAAA,CAAA,SAAM;wCACH,OAAO;4CAAE,OAAO;wCAAM;wCACtB,SAAS;4CACL,4BAA4B;wCAChC;kDACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IASrB,qBACI,6LAAC;QAAI,WAAW,+IAAA,CAAA,UAAM,CAAC,OAAO;QAAE,YAAY;QAAY,QAAQ;;0BAC5D,6LAAC,uLAAA,CAAA,OAAI;;kCACD,6LAAC,uLAAA,CAAA,OAAI,CAAC,MAAM;;0CACR,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;;oCACN;kDACD,6LAAC,8IAAA,CAAA,UAAY;wCAAC,cAAc;wCAAS,aAAa;;;;;;;;;;;;4BAErD,CAAC,aAAa,CAAC,2BACZ,6LAAC,uLAAA,CAAA,OAAI,CAAC,QAAQ;;oCAAC;kDACwC,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;kDAAW;;;;;;oCAAe;;;;;;uCAE5F;;;;;;;kCAER,6LAAC,uLAAA,CAAA,OAAI,CAAC,IAAI;kCACN,cAAA,6LAAC;4BAAI,IAAG;;8CACJ,6LAAC,2JAAA,CAAA,UAAa;oCAAwB,UAAU;oCAAgB,YAAY;oCAAY,WAAW;mCAA/E;;;;;8CACpB,6LAAC;;;;;8CACD,6LAAC,uLAAA,CAAA,OAAI;oCAAC,WAAW,+IAAA,CAAA,UAAM,CAAC,mBAAmB;;sDACvC,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;;8DACP,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;oDAAC,SAAQ;oDAAqB,OAAO;wDAAE,aAAa;oDAAO;8DAAG;;;;;;8DAGzE,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;oDACP,OAAO;wDAAE,SAAS;oDAAS;oDAC3B,IAAG;oDACH,SAAS;oDACT,UAAU,CAAA;wDACN,eAAe,EAAE,MAAM,CAAC,OAAO;oDACnC;oDACA,MAAK;;;;;;;;;;;;sDAGb,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;sDACP,cAAA,6LAAC;gDAAI,OAAO;oDAAE,SAAS;oDAAY,QAAQ;oDAAW,aAAa;gDAAO;gDAAG,SAAS;;kEAClF,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;kEAAC;;;;;;kEACZ,6LAAC,8JAAA,CAAA,UAAU;wDAAC,OAAM;;;;;;;;;;;;;;;;;wCAGzB,2BACG,6LAAC,oIAAA,CAAA,UAAO;4CACJ,MAAK;4CACL,uBACI,6LAAC;;oDAAK;oDACW;oDACZ,6BAA6B,aAAa,CAAC,yCACxC,6LAAC,4JAAA,CAAA,UAAS;wDACN,MAAM;wDACN,YAAY;wDACZ,UAAU,CAAC,EAAE,OAAO,EAAE,iBAAK,6LAAC;0EAAM,CAAA,GAAA,4JAAA,CAAA,UAAO,AAAD,EAAE;;;;;;;;;;iEAG9C;;;;;;;4CAIZ,8BACI,6LAAC;0DAAE;;;;;;;;;;mDAMX;wCACH,CAAC,2BACE,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;4CAAC,SAAS;;8DACjB,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;oDAAC,OAAO;wDAAE,QAAQ;wDAAW,aAAa;oDAAO;8DAAG;;;;;;8DAC/D,6LAAC;oDAAK,OAAO;wDAAE,QAAQ;oDAAU;;wDAC5B;sEACD,6LAAC,mKAAA,CAAA,UAAc;;;;;;;;;;;;;;;;mDAGvB;;;;;;;8CAER,6LAAC;;;;;gCACA,CAAC,sBACE,6LAAC;oCACG,IAAG;oCACH,OAAO;wCAAE,QAAQ,SAAS,cAAc,CAAC,uBAAuB;wCAAc,OAAO;oCAAO;8CAE5F,cAAA,6LAAC,gNAAA,CAAA,UAAS;kDACL,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,iBACf,6LAAC,0JAAA,CAAA,gBAAI;gDACD,KAAK;gDACL,WAAW,+IAAA,CAAA,UAAM,CAAC,iBAAiB;gDACnC,QAAQ;gDACR,WAAW,MAAM,MAAM;gDACvB,UAAU;oDAAE,OAAO;gDAAM;gDACzB,UAAU,UAAU,MAAM;gDAC1B,QAAO;gDACP,OAAO;0DAEN;;;;;;;;;;;;;;;yDAMjB,6LAAC;oCAAI,WAAW,GAAG,+IAAA,CAAA,UAAM,CAAC,eAAe,CAAC,CAAC,EAAE,+IAAA,CAAA,UAAM,CAAC,iBAAiB,EAAE;8CAClE,MAAM,GAAG,CAAC,CAAC,MAAM;wCACd,qBAAO,6LAAC;sDAA2B,eAAe,MAAM;gDAAE,OAAO;gDAAS,QAAQ;4CAAO;2CAAvE,SAAS;;;;;oCAC/B;;;;;;;;;;;;;;;;;kCAKhB,6LAAC,uLAAA,CAAA,OAAI,CAAC,MAAM;;4BAAC;4BAC6B;0CACtC,6LAAC;gCAAE,QAAO;gCAAS,KAAI;gCAAa,MAAK;0CAAgC;;;;;;4BAErE;0CAEJ,6LAAC;;;;;4BACA,aACG,mBAEA,6LAAC;;kDACG,6LAAC;;4CAAK;4CAEuG;0DACzG,6LAAC;gDAAE,QAAO;gDAAS,KAAI;gDAAa,MAAK;0DAAW;;;;;;;;;;;;kDAIxD,6LAAC;;;;;;;;;;;4BAGR;;;;;;;;;;;;;YAGR,oCACG,6LAAC;;kCACG,6LAAC;;;;;kCACD,6LAAC,uLAAA,CAAA,OAAI;wBAAC,WAAU;;0CACZ,6LAAC,uLAAA,CAAA,OAAI,CAAC,MAAM;0CACR,cAAA,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;8CAAC;;;;;;;;;;;0CAEhB,6LAAC,uLAAA,CAAA,OAAI,CAAC,IAAI;0CACN,cAAA,6LAAC,kJAAA,CAAA,UAAc;oCAAC,aAAa;oCAAqB,cAAc;oCAAG,iBAAgB;;;;;;;;;;;;;;;;;;;;;;uBAI/F;0BACJ,6LAAC;;kCACG,6LAAC;;;;;kCACD,6LAAC,uLAAA,CAAA,OAAI;;0CACD,6LAAC,uLAAA,CAAA,OAAI,CAAC,MAAM;0CACR,cAAA,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;8CAAC;;;;;;;;;;;0CAEhB,6LAAC,uLAAA,CAAA,OAAI,CAAC,IAAI;0CACN,cAAA,6LAAC;oCAAI,WAAW,+IAAA,CAAA,UAAM,CAAC,qBAAqB;;sDACxC,6LAAC,uLAAA,CAAA,OAAI;4CAAC,WAAW,+IAAA,CAAA,UAAM,CAAC,kBAAkB;;8DACtC,6LAAC,uLAAA,CAAA,OAAI,CAAC,MAAM;8DACR,cAAA,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;kEAAC;;;;;;;;;;;8DAEhB,6LAAC,uLAAA,CAAA,OAAI,CAAC,IAAI;8DACN,cAAA,6LAAC;;0EACG,6LAAC;;oEAAG;kFACsB,6LAAC,kIAAA,CAAA,UAAM;wEAAC,QAAQ,WAAW,UAAU;;;;;;;;;;;;0EAE/D,6LAAC;;oEAAG;kFACoB,6LAAC,kIAAA,CAAA,UAAM;wEAAC,QAAQ,WAAW,WAAW;;;;;;oEAAI;;;;;;;0EAElE,6LAAC;;oEAAG;kFACwB,6LAAC,kIAAA,CAAA,UAAM;wEAAC,QAAQ,WAAW,6BAA6B;;;;;;oEAAI;;;;;;;;;;;;;;;;;;;;;;;;wCAKnG,CAAC,aAAa,cAAc,CAAC,2BAC1B,6LAAC,uLAAA,CAAA,OAAI;4CAAC,WAAW,+IAAA,CAAA,UAAM,CAAC,kBAAkB;;8DACtC,6LAAC,uLAAA,CAAA,OAAI,CAAC,MAAM;8DACR,cAAA,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;kEAAC;;;;;;;;;;;8DAEhB,6LAAC,uLAAA,CAAA,OAAI,CAAC,IAAI;8DACN,cAAA,6LAAC;;0EACG,6LAAC;;kFACG,6LAAC;wEAAK,OAAO;4EAAE,aAAa;wEAAO;;4EAAG;0FACnB,6LAAC,kIAAA,CAAA,UAAM;gFAAC,QAAQ,WAAW,qBAAqB;;;;;;4EAAI;;;;;;;kFAEvE,6LAAC,oIAAA,CAAA,UAAO;wEACJ,MAAK;wEACL,uBAAS,6LAAC,4JAAA,CAAA,UAAQ;;;;;wEAClB,8BACI,6LAAC;sFAAK;;;;;;;;;;;;;;;;;0EAOlB,6LAAC;;oEAAG;kFACc,6LAAC,kIAAA,CAAA,UAAM;wEAAC,QAAQ,WAAW,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;mDAKzE;wCACH,2BACG,6LAAC,uLAAA,CAAA,OAAI;4CAAC,OAAO;gDAAE,UAAU;4CAAE;4CAAG,WAAW,+IAAA,CAAA,UAAM,CAAC,kBAAkB;;8DAC9D,6LAAC,uLAAA,CAAA,OAAI,CAAC,MAAM;8DACP,CAAC,aAAa,cAAc,2BACzB,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;kEAAC;;;;;6EAEZ,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;kEAAC;;;;;;;;;;;8DAGpB,6LAAC,uLAAA,CAAA,OAAI,CAAC,IAAI;;sEACN,6LAAC;;gEAAE;gEACsG;8EACrG,6LAAC,+JAAA,CAAA,UAAI;oEAAC,MAAK;8EAAO;;;;;;gEAAuB;;;;;;;sEAE7C,6LAAC;;gEAAE;gEAC6B;8EAC5B,6LAAC;oEAAK,OAAO;wEAAE,WAAW;wEAAU,OAAO;oEAAU;8EAChD,CAAC,QAAQ,OAAO,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG,YAAY,SAAS,QAAQ,QAAQ;;;;;;gEAChF;8EACR,6LAAC,0IAAA,CAAA,aAAU;oEACP,WAAW,CAAC,QAAQ,OAAO,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG,YAAY,SAAS,QAAQ,QAAQ;oEAC9F,8BAAgB,6LAAC;kFAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;mDAKtC;;;;;;;;;;;;;;;;;;;;;;;;0BAKpB,6LAAC;;;;;0BACD,6LAAC,qJAAA,CAAA,UAAU;;;;;0BACX,6LAAC;gBAAI,IAAG;gBAAqB,OAAO;oBAAE,UAAU;oBAAY,KAAK,CAAC;oBAAM,SAAS;oBAAQ,QAAQ,CAAC;gBAAE;0BAChG,cAAA,6LAAC,yIAAA,CAAA,UAAI;oBAAC,MAAM,sHAAA,CAAA,YAAS;;;;;;;;;;;YAExB;YACA;;;;;;;AAGb;GA9vBS;;QAwBoB,kHAAA,CAAA,wBAAqB;QAIjC,qIAAA,CAAA,YAAS;QAEL,uJAAA,CAAA,iBAAc;;;KA9B1B;uCAgwBM", "debugId": null}}]}