(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["static/chunks/_ee4d4bbe._.js", {

"[project]/utils/Hooks.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "useCoflCoins": (()=>useCoflCoins),
    "useDebounce": (()=>useDebounce),
    "useForceUpdate": (()=>useForceUpdate),
    "useIsMobile": (()=>useIsMobile),
    "useQueryParamState": (()=>useQueryParamState),
    "useStateWithRef": (()=>useStateWithRef),
    "useSwipe": (()=>useSwipe),
    "useWasAlreadyLoggedIn": (()=>useWasAlreadyLoggedIn)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$CoflCoinsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/CoflCoinsUtils.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SSRUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/SSRUtils.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Parser$2f$URLParser$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/Parser/URLParser.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature(), _s3 = __turbopack_context__.k.signature(), _s4 = __turbopack_context__.k.signature(), _s5 = __turbopack_context__.k.signature(), _s6 = __turbopack_context__.k.signature();
;
;
;
;
;
function useForceUpdate() {
    _s();
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const [update, setUpdate] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(0);
    return ()=>setUpdate((update)=>update + 1);
}
_s(useForceUpdate, "U5iovX6txcuyzahsHyptxdF1Nws=");
function useSwipe(onSwipeUp, onSwipeRight, onSwipeDown, onSwipeLeft) {
    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SSRUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isClientSideRendering"])()) {
        return;
    }
    document.addEventListener('touchstart', handleTouchStart, false);
    document.addEventListener('touchmove', handleTouchMove, false);
    var xDown = null;
    var yDown = null;
    function getTouches(evt) {
        return evt.touches || // browser API
        evt.originalEvent.touches; // jQuery
    }
    function handleTouchStart(evt) {
        const firstTouch = getTouches(evt)[0];
        xDown = firstTouch.clientX;
        yDown = firstTouch.clientY;
    }
    function handleTouchMove(evt) {
        if (xDown === null || yDown === null) {
            return;
        }
        var xUp = evt.touches[0].clientX;
        var yUp = evt.touches[0].clientY;
        var xDiff = xDown - xUp;
        var yDiff = yDown - yUp;
        if (Math.abs(xDiff) > Math.abs(yDiff)) {
            /*most significant*/ if (xDiff > 0) {
                if (onSwipeLeft) {
                    onSwipeLeft();
                }
            } else {
                if (onSwipeRight) {
                    onSwipeRight();
                }
            }
        } else {
            if (yDiff > 0) {
                if (onSwipeUp) {
                    onSwipeUp();
                }
            } else {
                if (onSwipeDown) {
                    onSwipeDown();
                }
            }
        }
        /* reset values */ xDown = null;
        yDown = null;
    }
    return ()=>{
        document.removeEventListener('touchstart', handleTouchStart, false);
        document.removeEventListener('touchmove', handleTouchMove, false);
    };
}
function useCoflCoins() {
    _s1();
    const [coflCoins, setCoflCoins] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$CoflCoinsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getCurrentCoflCoins"])());
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useCoflCoins.useEffect": ()=>{
            let unsubscribe = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$CoflCoinsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["subscribeToCoflcoinChange"])(setCoflCoins);
            return ({
                "useCoflCoins.useEffect": ()=>{
                    unsubscribe();
                }
            })["useCoflCoins.useEffect"];
        }
    }["useCoflCoins.useEffect"], []);
    return coflCoins;
}
_s1(useCoflCoins, "C+enwg4fK/g34CESb/vw33ukaqE=");
function useWasAlreadyLoggedIn() {
    _s2();
    const [wasAlreadyLoggedIn, setWasAlreadyLoggedIn] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useWasAlreadyLoggedIn.useEffect": ()=>{
            setWasAlreadyLoggedIn(localStorage.getItem('googleId') !== null);
        }
    }["useWasAlreadyLoggedIn.useEffect"], []);
    return wasAlreadyLoggedIn;
}
_s2(useWasAlreadyLoggedIn, "1f04SSxs2Qtmf783W7lV2PTlbiw=");
function useDebounce(value, delay) {
    _s3();
    const [debouncedValue, setDebouncedValue] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(value);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useDebounce.useEffect": ()=>{
            const handler = setTimeout({
                "useDebounce.useEffect.handler": ()=>{
                    setDebouncedValue(value);
                }
            }["useDebounce.useEffect.handler"], delay);
            return ({
                "useDebounce.useEffect": ()=>{
                    clearTimeout(handler);
                }
            })["useDebounce.useEffect"];
        }
    }["useDebounce.useEffect"], [
        value,
        delay
    ]);
    return debouncedValue;
}
_s3(useDebounce, "KDuPAtDOgxm8PU6legVJOb3oOmA=");
function useStateWithRef(defaultValue) {
    _s4();
    const [state, _setState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(defaultValue);
    let stateRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(state);
    const setState = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useStateWithRef.useCallback[setState]": (newState)=>{
            stateRef.current = newState;
            _setState(newState);
        }
    }["useStateWithRef.useCallback[setState]"], []);
    return [
        state,
        setState,
        stateRef
    ];
}
_s4(useStateWithRef, "2zUIfANzaBXnh0dIWyeU0rgp5no=");
function useQueryParamState(key, defaultValue) {
    _s5();
    const [state, setState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(getDefaultValue() || defaultValue);
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    function getDefaultValue() {
        let param = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Parser$2f$URLParser$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getURLSearchParam"])(key);
        if (!param) {
            return undefined;
        }
        return JSON.parse(decodeURIComponent(param));
    }
    function _setState(newState) {
        setState(newState);
        let urlparams = new URLSearchParams(window.location.search);
        if (!newState) {
            urlparams.delete(key);
        } else {
            urlparams.set(key, encodeURIComponent(JSON.stringify(newState)));
        }
        router.replace(`${window.location.pathname}?${urlparams.toString()}`);
    }
    return [
        state,
        _setState
    ];
}
_s5(useQueryParamState, "gFWI+omlRaxP5wHoiPXmgkzW71U=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"]
    ];
});
function useIsMobile() {
    _s6();
    let [isMobile, setIsMobile] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useIsMobile.useEffect": ()=>{
            setIsMobile(isMobileCheck());
        }
    }["useIsMobile.useEffect"], []);
    function isMobileCheck() {
        let check = false;
        (function(a) {
            if (/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i.test(a) || /1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i.test(a.substr(0, 4))) check = true;
        })(navigator.userAgent || navigator.vendor || window.opera);
        return check;
    }
    return isMobile;
}
_s6(useIsMobile, "0VTTNJATKABQPGLm9RVT0tKGUgU=");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/NavBar/NavBar.module.css [app-client] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "hamburgerIcon": "NavBar-module__yBvhsG__hamburgerIcon",
  "logo": "NavBar-module__yBvhsG__logo",
  "menuItem": "NavBar-module__yBvhsG__menuItem",
  "navBar": "NavBar-module__yBvhsG__navBar",
  "navClosing": "NavBar-module__yBvhsG__navClosing",
  "navOpen": "NavBar-module__yBvhsG__navOpen",
});
}}),
"[project]/components/NavBar/NavBar.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$AccountBalance$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/icons-material/esm/AccountBalance.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$AccountCircle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/icons-material/esm/AccountCircle.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Build$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/icons-material/esm/Build.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Chat$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/icons-material/esm/Chat.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Download$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/icons-material/esm/Download.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Home$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/icons-material/esm/Home.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Menu$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/icons-material/esm/Menu.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$NotificationsOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/icons-material/esm/NotificationsOutlined.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$PetsOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/icons-material/esm/PetsOutlined.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Policy$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/icons-material/esm/Policy.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$ShareOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/icons-material/esm/ShareOutlined.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Storefront$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/icons-material/esm/Storefront.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$CurrencyExchange$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/icons-material/esm/CurrencyExchange.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pro$2d$sidebar$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-pro-sidebar/dist/index.es.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Hooks$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/Hooks.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$NavBar$2f$NavBar$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/components/NavBar/NavBar.module.css [app-client] (css module)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
let resizePromise = null;
function NavBar(props) {
    _s();
    let [isWideOpen, setIsWideOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    let [isHovering, setIsHovering] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    let [isSmall, setIsSmall] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    let [collapsed, setCollapsed] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    let forceUpdate = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Hooks$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForceUpdate"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "NavBar.useEffect": ()=>{
            setIsSmall(document.body.clientWidth < 1500);
            window.addEventListener('resize', resizeHandler);
            return ({
                "NavBar.useEffect": ()=>{
                    window.removeEventListener('resize', resizeHandler);
                }
            })["NavBar.useEffect"];
        }
    }["NavBar.useEffect"], []);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "NavBar.useEffect": ()=>{
            if (isWideOpen) {
                document.addEventListener('click', outsideClickHandler, true);
            } else {
                document.removeEventListener('click', outsideClickHandler, true);
            }
            return ({
                "NavBar.useEffect": ()=>{
                    document.removeEventListener('click', outsideClickHandler, true);
                }
            })["NavBar.useEffect"];
        // eslint-disable-next-line react-hooks/exhaustive-deps
        }
    }["NavBar.useEffect"], [
        isWideOpen
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "NavBar.useEffect": ()=>{
            setCollapsed(isCollapsed());
        }
    }["NavBar.useEffect"], [
        isSmall,
        isWideOpen,
        isHovering
    ]);
    function isCollapsed() {
        if (isSmall) {
            return false;
        }
        return !isWideOpen && !isHovering;
    }
    function outsideClickHandler(evt) {
        const flyoutEl = document.getElementById('navBar');
        const hamburgerEl = document.getElementById('hamburgerIcon');
        let targetEl = evt.target;
        do {
            if (targetEl === flyoutEl || targetEl === hamburgerEl) {
                return;
            }
            targetEl = targetEl.parentNode;
        }while (targetEl)
        if (isWideOpen) {
            if (isSmall) {
                let el = document.getElementById('pro-sidebar');
                el?.classList.add(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$NavBar$2f$NavBar$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].navClosing);
                el?.classList.remove(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$NavBar$2f$NavBar$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].navOpen);
                setTimeout(()=>{
                    setIsWideOpen(false);
                    el?.classList.remove(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$NavBar$2f$NavBar$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].navClosing);
                }, 500);
            } else {
                setIsWideOpen(false);
            }
        }
    }
    function onMouseMove() {
        setIsHovering(true);
    }
    function onMouseOut() {
        setIsHovering(false);
    }
    function resizeHandler() {
        if (resizePromise) {
            return;
        }
        resizePromise = setTimeout(()=>{
            setIsWideOpen(false);
            setIsSmall(document.body.clientWidth < 1500);
            forceUpdate();
            resizePromise = null;
            let el = document.getElementById('pro-sidebar');
            if (el) {
                el.style.left = '0px';
            }
        }, 500);
    }
    function onHamburgerClick() {
        if (isSmall && !isWideOpen) {
            let el = document.getElementById('pro-sidebar');
            if (el) {
                el.hidden = false;
                el.style.left = '-270px';
                setTimeout(()=>{
                    if (el) {
                        el.classList.add(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$NavBar$2f$NavBar$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].navOpen);
                    }
                });
                setTimeout(()=>{
                    setIsWideOpen(true);
                }, 500);
            }
        } else {
            setIsWideOpen(!isWideOpen);
        }
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("aside", {
                className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$NavBar$2f$NavBar$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].navBar,
                id: "navBar",
                onMouseEnter: onMouseMove,
                onMouseLeave: onMouseOut,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pro$2d$sidebar$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Sidebar"], {
                    id: "pro-sidebar",
                    hidden: isSmall && !isWideOpen,
                    backgroundColor: "#1d1d1d",
                    collapsed: collapsed,
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        style: {
                            height: '100%',
                            display: 'flex',
                            flexDirection: 'column'
                        },
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$NavBar$2f$NavBar$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].logo,
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            src: "/logo512.png",
                                            alt: "Logo",
                                            width: 40,
                                            height: 40,
                                            style: {
                                                translate: '-5px'
                                            }
                                        }, void 0, false, {
                                            fileName: "[project]/components/NavBar/NavBar.tsx",
                                            lineNumber: 147,
                                            columnNumber: 33
                                        }, this),
                                        " ",
                                        !isCollapsed() ? 'Coflnet' : ''
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/components/NavBar/NavBar.tsx",
                                    lineNumber: 146,
                                    columnNumber: 29
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/components/NavBar/NavBar.tsx",
                                lineNumber: 145,
                                columnNumber: 25
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("hr", {}, void 0, false, {
                                fileName: "[project]/components/NavBar/NavBar.tsx",
                                lineNumber: 150,
                                columnNumber: 25
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pro$2d$sidebar$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Menu"], {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pro$2d$sidebar$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MenuItem"], {
                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$NavBar$2f$NavBar$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].menuItem,
                                        component: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            href: '/'
                                        }, void 0, false, {
                                            fileName: "[project]/components/NavBar/NavBar.tsx",
                                            lineNumber: 152,
                                            columnNumber: 78
                                        }, void 0),
                                        icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Home$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                            fileName: "[project]/components/NavBar/NavBar.tsx",
                                            lineNumber: 152,
                                            columnNumber: 105
                                        }, void 0),
                                        children: "Home"
                                    }, void 0, false, {
                                        fileName: "[project]/components/NavBar/NavBar.tsx",
                                        lineNumber: 152,
                                        columnNumber: 29
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pro$2d$sidebar$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MenuItem"], {
                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$NavBar$2f$NavBar$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].menuItem,
                                        component: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            href: '/flipper'
                                        }, void 0, false, {
                                            fileName: "[project]/components/NavBar/NavBar.tsx",
                                            lineNumber: 155,
                                            columnNumber: 78
                                        }, void 0),
                                        icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Storefront$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                            fileName: "[project]/components/NavBar/NavBar.tsx",
                                            lineNumber: 155,
                                            columnNumber: 112
                                        }, void 0),
                                        children: "Item Flipper"
                                    }, void 0, false, {
                                        fileName: "[project]/components/NavBar/NavBar.tsx",
                                        lineNumber: 155,
                                        columnNumber: 29
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pro$2d$sidebar$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MenuItem"], {
                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$NavBar$2f$NavBar$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].menuItem,
                                        component: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            href: '/account'
                                        }, void 0, false, {
                                            fileName: "[project]/components/NavBar/NavBar.tsx",
                                            lineNumber: 158,
                                            columnNumber: 78
                                        }, void 0),
                                        icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$AccountCircle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                            fileName: "[project]/components/NavBar/NavBar.tsx",
                                            lineNumber: 158,
                                            columnNumber: 112
                                        }, void 0),
                                        children: "Account"
                                    }, void 0, false, {
                                        fileName: "[project]/components/NavBar/NavBar.tsx",
                                        lineNumber: 158,
                                        columnNumber: 29
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pro$2d$sidebar$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MenuItem"], {
                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$NavBar$2f$NavBar$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].menuItem,
                                        component: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            href: '/subscriptions'
                                        }, void 0, false, {
                                            fileName: "[project]/components/NavBar/NavBar.tsx",
                                            lineNumber: 161,
                                            columnNumber: 78
                                        }, void 0),
                                        icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$NotificationsOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                            fileName: "[project]/components/NavBar/NavBar.tsx",
                                            lineNumber: 161,
                                            columnNumber: 118
                                        }, void 0),
                                        children: "Notifier"
                                    }, void 0, false, {
                                        fileName: "[project]/components/NavBar/NavBar.tsx",
                                        lineNumber: 161,
                                        columnNumber: 29
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pro$2d$sidebar$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MenuItem"], {
                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$NavBar$2f$NavBar$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].menuItem,
                                        component: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            href: '/crafts'
                                        }, void 0, false, {
                                            fileName: "[project]/components/NavBar/NavBar.tsx",
                                            lineNumber: 164,
                                            columnNumber: 78
                                        }, void 0),
                                        icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Build$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                            fileName: "[project]/components/NavBar/NavBar.tsx",
                                            lineNumber: 164,
                                            columnNumber: 111
                                        }, void 0),
                                        children: "Profitable Crafts"
                                    }, void 0, false, {
                                        fileName: "[project]/components/NavBar/NavBar.tsx",
                                        lineNumber: 164,
                                        columnNumber: 29
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pro$2d$sidebar$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MenuItem"], {
                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$NavBar$2f$NavBar$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].menuItem,
                                        component: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            href: '/premium'
                                        }, void 0, false, {
                                            fileName: "[project]/components/NavBar/NavBar.tsx",
                                            lineNumber: 167,
                                            columnNumber: 78
                                        }, void 0),
                                        icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$AccountBalance$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                            fileName: "[project]/components/NavBar/NavBar.tsx",
                                            lineNumber: 167,
                                            columnNumber: 112
                                        }, void 0),
                                        children: "Premium / Shop"
                                    }, void 0, false, {
                                        fileName: "[project]/components/NavBar/NavBar.tsx",
                                        lineNumber: 167,
                                        columnNumber: 29
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pro$2d$sidebar$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MenuItem"], {
                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$NavBar$2f$NavBar$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].menuItem,
                                        component: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            href: '/trade'
                                        }, void 0, false, {
                                            fileName: "[project]/components/NavBar/NavBar.tsx",
                                            lineNumber: 170,
                                            columnNumber: 78
                                        }, void 0),
                                        icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$CurrencyExchange$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                            fileName: "[project]/components/NavBar/NavBar.tsx",
                                            lineNumber: 170,
                                            columnNumber: 110
                                        }, void 0),
                                        children: "Trading"
                                    }, void 0, false, {
                                        fileName: "[project]/components/NavBar/NavBar.tsx",
                                        lineNumber: 170,
                                        columnNumber: 29
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pro$2d$sidebar$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MenuItem"], {
                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$NavBar$2f$NavBar$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].menuItem,
                                        component: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            href: '/kat'
                                        }, void 0, false, {
                                            fileName: "[project]/components/NavBar/NavBar.tsx",
                                            lineNumber: 173,
                                            columnNumber: 78
                                        }, void 0),
                                        icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$PetsOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                            fileName: "[project]/components/NavBar/NavBar.tsx",
                                            lineNumber: 173,
                                            columnNumber: 108
                                        }, void 0),
                                        children: "Kat Flips"
                                    }, void 0, false, {
                                        fileName: "[project]/components/NavBar/NavBar.tsx",
                                        lineNumber: 173,
                                        columnNumber: 29
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pro$2d$sidebar$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MenuItem"], {
                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$NavBar$2f$NavBar$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].menuItem,
                                        component: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            href: '/mod'
                                        }, void 0, false, {
                                            fileName: "[project]/components/NavBar/NavBar.tsx",
                                            lineNumber: 176,
                                            columnNumber: 78
                                        }, void 0),
                                        icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Download$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                            fileName: "[project]/components/NavBar/NavBar.tsx",
                                            lineNumber: 176,
                                            columnNumber: 108
                                        }, void 0),
                                        children: "Mod"
                                    }, void 0, false, {
                                        fileName: "[project]/components/NavBar/NavBar.tsx",
                                        lineNumber: 176,
                                        columnNumber: 29
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pro$2d$sidebar$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MenuItem"], {
                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$NavBar$2f$NavBar$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].menuItem,
                                        component: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            href: '/ref'
                                        }, void 0, false, {
                                            fileName: "[project]/components/NavBar/NavBar.tsx",
                                            lineNumber: 179,
                                            columnNumber: 78
                                        }, void 0),
                                        icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$ShareOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                            fileName: "[project]/components/NavBar/NavBar.tsx",
                                            lineNumber: 179,
                                            columnNumber: 108
                                        }, void 0),
                                        children: "Referral"
                                    }, void 0, false, {
                                        fileName: "[project]/components/NavBar/NavBar.tsx",
                                        lineNumber: 179,
                                        columnNumber: 29
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pro$2d$sidebar$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MenuItem"], {
                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$NavBar$2f$NavBar$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].menuItem,
                                        component: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            href: '/about'
                                        }, void 0, false, {
                                            fileName: "[project]/components/NavBar/NavBar.tsx",
                                            lineNumber: 182,
                                            columnNumber: 78
                                        }, void 0),
                                        icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Policy$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                            fileName: "[project]/components/NavBar/NavBar.tsx",
                                            lineNumber: 182,
                                            columnNumber: 110
                                        }, void 0),
                                        children: "Links / Legal"
                                    }, void 0, false, {
                                        fileName: "[project]/components/NavBar/NavBar.tsx",
                                        lineNumber: 182,
                                        columnNumber: 29
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pro$2d$sidebar$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MenuItem"], {
                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$NavBar$2f$NavBar$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].menuItem,
                                        component: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            href: '/feedback'
                                        }, void 0, false, {
                                            fileName: "[project]/components/NavBar/NavBar.tsx",
                                            lineNumber: 185,
                                            columnNumber: 78
                                        }, void 0),
                                        icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Chat$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                            fileName: "[project]/components/NavBar/NavBar.tsx",
                                            lineNumber: 185,
                                            columnNumber: 113
                                        }, void 0),
                                        children: "Feedback"
                                    }, void 0, false, {
                                        fileName: "[project]/components/NavBar/NavBar.tsx",
                                        lineNumber: 185,
                                        columnNumber: 29
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pro$2d$sidebar$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MenuItem"], {
                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$NavBar$2f$NavBar$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].menuItem,
                                        component: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            href: 'https://discord.gg/wvKXfTgCfb',
                                            target: "_blank"
                                        }, void 0, false, {
                                            fileName: "[project]/components/NavBar/NavBar.tsx",
                                            lineNumber: 190,
                                            columnNumber: 44
                                        }, void 0),
                                        rel: "noreferrer",
                                        icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            src: "/discord_icon.svg",
                                            alt: "Discord icon",
                                            height: 24,
                                            width: 32
                                        }, void 0, false, {
                                            fileName: "[project]/components/NavBar/NavBar.tsx",
                                            lineNumber: 192,
                                            columnNumber: 39
                                        }, void 0),
                                        children: "Discord"
                                    }, void 0, false, {
                                        fileName: "[project]/components/NavBar/NavBar.tsx",
                                        lineNumber: 188,
                                        columnNumber: 29
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/components/NavBar/NavBar.tsx",
                                lineNumber: 151,
                                columnNumber: 25
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/NavBar/NavBar.tsx",
                        lineNumber: 144,
                        columnNumber: 21
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/components/NavBar/NavBar.tsx",
                    lineNumber: 143,
                    columnNumber: 17
                }, this)
            }, void 0, false, {
                fileName: "[project]/components/NavBar/NavBar.tsx",
                lineNumber: 142,
                columnNumber: 13
            }, this),
            isSmall ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                onClick: onHamburgerClick,
                className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$NavBar$2f$NavBar$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].hamburgerIcon,
                id: "hamburgerIcon",
                style: props.hamburgerIconStyle,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Menu$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    fontSize: "large"
                }, void 0, false, {
                    fileName: "[project]/components/NavBar/NavBar.tsx",
                    lineNumber: 202,
                    columnNumber: 21
                }, this)
            }, void 0, false, {
                fileName: "[project]/components/NavBar/NavBar.tsx",
                lineNumber: 201,
                columnNumber: 17
            }, this) : ''
        ]
    }, void 0, true, {
        fileName: "[project]/components/NavBar/NavBar.tsx",
        lineNumber: 141,
        columnNumber: 9
    }, this);
}
_s(NavBar, "ejfVnX45tW8OIsOm1ZQEJxnncxQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Hooks$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForceUpdate"]
    ];
});
_c = NavBar;
const __TURBOPACK__default__export__ = NavBar;
var _c;
__turbopack_context__.k.register(_c, "NavBar");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/utils/LoadingUtils.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "getInitialLoadingElement": (()=>getInitialLoadingElement),
    "getLoadingElement": (()=>getLoadingElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Spinner$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Spinner$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/Spinner.js [app-client] (ecmascript) <export default as Spinner>");
;
;
;
function getLoadingElement(text) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        style: {
            textAlign: 'center'
        },
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Spinner$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Spinner$3e$__["Spinner"], {
                        animation: "grow",
                        variant: "primary"
                    }, void 0, false, {
                        fileName: "[project]/utils/LoadingUtils.tsx",
                        lineNumber: 9,
                        columnNumber: 17
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Spinner$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Spinner$3e$__["Spinner"], {
                        animation: "grow",
                        variant: "primary"
                    }, void 0, false, {
                        fileName: "[project]/utils/LoadingUtils.tsx",
                        lineNumber: 10,
                        columnNumber: 17
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Spinner$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Spinner$3e$__["Spinner"], {
                        animation: "grow",
                        variant: "primary"
                    }, void 0, false, {
                        fileName: "[project]/utils/LoadingUtils.tsx",
                        lineNumber: 11,
                        columnNumber: 17
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/utils/LoadingUtils.tsx",
                lineNumber: 8,
                columnNumber: 13
            }, this),
            text ? text : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                children: "Loading Data..."
            }, void 0, false, {
                fileName: "[project]/utils/LoadingUtils.tsx",
                lineNumber: 13,
                columnNumber: 28
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/utils/LoadingUtils.tsx",
        lineNumber: 7,
        columnNumber: 9
    }, this);
}
function getInitialLoadingElement() {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "main-loading",
        style: {
            height: '500px'
        },
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    src: "/logo192.png",
                    height: "192",
                    width: "192",
                    alt: "auction house logo"
                }, void 0, false, {
                    fileName: "[project]/utils/LoadingUtils.tsx",
                    lineNumber: 22,
                    columnNumber: 17
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "main-loading",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        children: "Loading App..."
                    }, void 0, false, {
                        fileName: "[project]/utils/LoadingUtils.tsx",
                        lineNumber: 24,
                        columnNumber: 21
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/utils/LoadingUtils.tsx",
                    lineNumber: 23,
                    columnNumber: 17
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/utils/LoadingUtils.tsx",
            lineNumber: 21,
            columnNumber: 13
        }, this)
    }, void 0, false, {
        fileName: "[project]/utils/LoadingUtils.tsx",
        lineNumber: 20,
        columnNumber: 9
    }, this);
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/GoogleSignIn/GoogleSignIn.module.css [app-client] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "googleButton": "GoogleSignIn-module__lTSYOa__googleButton",
});
}}),
"[project]/components/GoogleSignIn/GoogleSignIn.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__),
    "isValidTokenAvailable": (()=>isValidTokenAvailable)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-toastify/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/api/ApiHelper.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$jonkoops$2f$matomo$2d$tracker$2d$react$2f$es$2f$useMatomo$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useMatomo$3e$__ = __turbopack_context__.i("[project]/node_modules/@jonkoops/matomo-tracker-react/es/useMatomo.js [app-client] (ecmascript) <export default as useMatomo>");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Hooks$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/Hooks.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SSRUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/SSRUtils.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/api/ApiTypes.d.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$oauth$2f$google$2f$dist$2f$index$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@react-oauth/google/dist/index.esm.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$GoogleSignIn$2f$GoogleSignIn$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/components/GoogleSignIn/GoogleSignIn.module.css [app-client] (css module)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SettingsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/SettingsUtils.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Base64Utils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/Base64Utils.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Modal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Modal$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/Modal.js [app-client] (ecmascript) <export default as Modal>");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
;
;
;
;
;
;
function GoogleSignIn(props) {
    _s();
    let [wasAlreadyLoggedInThisSession, setWasAlreadyLoggedInThisSession] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SSRUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isClientSideRendering"])() ? isValidTokenAvailable(localStorage.getItem('googleId')) : false);
    let [isLoggedIn, setIsLoggedIn] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    let [isSSR, setIsSSR] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    let [isLoginNotShowing, setIsLoginNotShowing] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    let [showButtonNotRenderingModal, setShowButtonNotRenderingModal] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    let { trackEvent } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$jonkoops$2f$matomo$2d$tracker$2d$react$2f$es$2f$useMatomo$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useMatomo$3e$__["useMatomo"])();
    let forceUpdate = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Hooks$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForceUpdate"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "GoogleSignIn.useEffect": ()=>{
            setIsSSR(false);
            if (wasAlreadyLoggedInThisSession) {
                let token = localStorage.getItem('googleId');
                let userObject = JSON.parse((0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Base64Utils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["atobUnicode"])(token.split('.')[1]));
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SettingsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setSetting"])(__TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SettingsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["GOOGLE_EMAIL"], userObject.email);
                onLoginSucces(token);
            } else {
                setTimeout({
                    "GoogleSignIn.useEffect": ()=>{
                        let isShown = false;
                        document.querySelectorAll('iframe').forEach({
                            "GoogleSignIn.useEffect": (e)=>{
                                if (e.src && e.src.includes('accounts.google.com')) {
                                    isShown = true;
                                }
                            }
                        }["GoogleSignIn.useEffect"]);
                        if (!isShown) {
                            setIsLoggedIn(false);
                            setIsLoginNotShowing(true);
                            sessionStorage.removeItem('googleId');
                            localStorage.removeItem('googleId');
                        }
                    }
                }["GoogleSignIn.useEffect"], 5000);
            }
        // eslint-disable-next-line react-hooks/exhaustive-deps
        }
    }["GoogleSignIn.useEffect"], []);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "GoogleSignIn.useEffect": ()=>{
            if (wasAlreadyLoggedInThisSession) {
                setIsLoggedIn(true);
            }
        }
    }["GoogleSignIn.useEffect"], [
        wasAlreadyLoggedInThisSession
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "GoogleSignIn.useEffect": ()=>{
            forceUpdate();
            setIsLoggedIn(sessionStorage.getItem('googleId') !== null);
        // eslint-disable-next-line react-hooks/exhaustive-deps
        }
    }["GoogleSignIn.useEffect"], [
        props.rerenderFlip
    ]);
    function onLoginSucces(token) {
        setIsLoggedIn(true);
        __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].loginWithToken(token).then((token)=>{
            localStorage.setItem('googleId', token);
            sessionStorage.setItem('googleId', token);
            let refId = window.refId;
            if (refId) {
                __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].setRef(refId);
            }
            document.dispatchEvent(new CustomEvent(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CUSTOM_EVENTS"].GOOGLE_LOGIN));
            if (props.onAfterLogin) {
                props.onAfterLogin();
            }
        }).catch((error)=>{
            // dont show the error message for the invalid token error
            // the google sign component sometimes sends an outdated token, causing this error
            if (error.slug !== 'invalid_token') {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(`An error occoured while trying to sign in with Google. ${error ? error.slug || JSON.stringify(error) : null}`);
            } else {
                console.warn('setGoogle: Invalid token error', error);
                sessionStorage.removeItem('googleId');
                localStorage.removeItem('googleId');
            }
            setIsLoggedIn(false);
            setWasAlreadyLoggedInThisSession(false);
            sessionStorage.removeItem('googleId');
            localStorage.removeItem('googleId');
        });
    }
    function onLoginFail() {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('Something went wrong, please try again.', {
            autoClose: 20000
        });
    }
    function onLoginClick() {
        if (props.onManualLoginClick) {
            props.onManualLoginClick();
        }
        trackEvent({
            category: 'login',
            action: 'click'
        });
    }
    let style = isLoggedIn ? {
        visibility: 'collapse',
        height: 0
    } : {};
    if (isSSR) {
        return null;
    }
    let buttonNotRenderingModal = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Modal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Modal$3e$__["Modal"], {
        show: showButtonNotRenderingModal,
        onHide: ()=>{
            setShowButtonNotRenderingModal(false);
        },
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Modal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Modal$3e$__["Modal"].Header, {
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Modal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Modal$3e$__["Modal"].Title, {
                    children: "Google Login button not showing up?"
                }, void 0, false, {
                    fileName: "[project]/components/GoogleSignIn/GoogleSignIn.tsx",
                    lineNumber: 137,
                    columnNumber: 17
                }, this)
            }, void 0, false, {
                fileName: "[project]/components/GoogleSignIn/GoogleSignIn.tsx",
                lineNumber: 136,
                columnNumber: 13
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Modal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Modal$3e$__["Modal"].Body, {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: "This is most likely caused by either an external software like an anti virus or your browser/extension blocking it."
                    }, void 0, false, {
                        fileName: "[project]/components/GoogleSignIn/GoogleSignIn.tsx",
                        lineNumber: 140,
                        columnNumber: 17
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("hr", {}, void 0, false, {
                        fileName: "[project]/components/GoogleSignIn/GoogleSignIn.tsx",
                        lineNumber: 141,
                        columnNumber: 17
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: "Known issues:"
                    }, void 0, false, {
                        fileName: "[project]/components/GoogleSignIn/GoogleSignIn.tsx",
                        lineNumber: 142,
                        columnNumber: 17
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                children: 'Kaspersky\'s "Secure Browse" feature seems to block the Google login.'
                            }, void 0, false, {
                                fileName: "[project]/components/GoogleSignIn/GoogleSignIn.tsx",
                                lineNumber: 144,
                                columnNumber: 21
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                children: "Opera GX seems to sometimes blocks the login button. The specific setting or reason on when it blocks it is unknown."
                            }, void 0, false, {
                                fileName: "[project]/components/GoogleSignIn/GoogleSignIn.tsx",
                                lineNumber: 145,
                                columnNumber: 21
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/GoogleSignIn/GoogleSignIn.tsx",
                        lineNumber: 143,
                        columnNumber: 17
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/components/GoogleSignIn/GoogleSignIn.tsx",
                lineNumber: 139,
                columnNumber: 13
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/components/GoogleSignIn/GoogleSignIn.tsx",
        lineNumber: 130,
        columnNumber: 9
    }, this);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        style: style,
        onClickCapture: onLoginClick,
        children: [
            !wasAlreadyLoggedInThisSession ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$GoogleSignIn$2f$GoogleSignIn$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].googleButton,
                        children: !isSSR ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$oauth$2f$google$2f$dist$2f$index$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["GoogleLogin"], {
                            onSuccess: (response)=>{
                                try {
                                    let userObject = JSON.parse((0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Base64Utils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["atobUnicode"])(response.credential.split('.')[1]));
                                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SettingsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setSetting"])(__TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SettingsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["GOOGLE_PROFILE_PICTURE_URL"], userObject.picture);
                                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SettingsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setSetting"])(__TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SettingsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["GOOGLE_EMAIL"], userObject.email);
                                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SettingsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setSetting"])(__TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SettingsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["GOOGLE_NAME"], userObject.name);
                                } catch  {
                                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].warn('Parsing issue with the google token. There might be issues when displaying details on the account page!');
                                }
                                onLoginSucces(response.credential);
                            },
                            onError: onLoginFail,
                            theme: 'filled_blue',
                            size: 'large',
                            useOneTap: true,
                            auto_select: true
                        }, void 0, false, {
                            fileName: "[project]/components/GoogleSignIn/GoogleSignIn.tsx",
                            lineNumber: 157,
                            columnNumber: 29
                        }, this) : null
                    }, void 0, false, {
                        fileName: "[project]/components/GoogleSignIn/GoogleSignIn.tsx",
                        lineNumber: 155,
                        columnNumber: 21
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: [
                            "I have read and agree to the ",
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                                href: "https://coflnet.com/privacy",
                                children: "Privacy Policy"
                            }, void 0, false, {
                                fileName: "[project]/components/GoogleSignIn/GoogleSignIn.tsx",
                                lineNumber: 178,
                                columnNumber: 54
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/GoogleSignIn/GoogleSignIn.tsx",
                        lineNumber: 177,
                        columnNumber: 21
                    }, this),
                    isLoginNotShowing ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: [
                            "Login button not showing? Click",
                            ' ',
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                style: {
                                    color: '#007bff',
                                    cursor: 'pointer'
                                },
                                onClick: ()=>{
                                    setShowButtonNotRenderingModal(true);
                                },
                                children: "here"
                            }, void 0, false, {
                                fileName: "[project]/components/GoogleSignIn/GoogleSignIn.tsx",
                                lineNumber: 183,
                                columnNumber: 29
                            }, this),
                            "."
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/GoogleSignIn/GoogleSignIn.tsx",
                        lineNumber: 181,
                        columnNumber: 25
                    }, this) : null
                ]
            }, void 0, true) : null,
            buttonNotRenderingModal
        ]
    }, void 0, true, {
        fileName: "[project]/components/GoogleSignIn/GoogleSignIn.tsx",
        lineNumber: 152,
        columnNumber: 9
    }, this);
}
_s(GoogleSignIn, "yI8N0aYJ97NxxDhTACnH53SYTnc=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$jonkoops$2f$matomo$2d$tracker$2d$react$2f$es$2f$useMatomo$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useMatomo$3e$__["useMatomo"],
        __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Hooks$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForceUpdate"]
    ];
});
_c = GoogleSignIn;
const __TURBOPACK__default__export__ = GoogleSignIn;
function isValidTokenAvailable(token) {
    if (!token || token === 'null') {
        return;
    }
    try {
        let details = JSON.parse((0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Base64Utils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["atobUnicode"])(token.split('.')[1]));
        let expirationDate = new Date(parseInt(details.exp) * 1000);
        return expirationDate.getTime() - 10000 > new Date().getTime();
    } catch (e) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].warn("Parsing issue with the google token. Can't automatically login!");
        return false;
    }
}
var _c;
__turbopack_context__.k.register(_c, "GoogleSignIn");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/ItemFilter/ItemFilterPropertiesDisplay.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
/* eslint-disable react-hooks/exhaustive-deps */ /* eslint-disable jsx-a11y/anchor-is-valid */ var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Remove$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/icons-material/esm/Remove.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/api/ApiHelper.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Formatter$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/Formatter.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Hooks$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/Hooks.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Tooltip$2f$Tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/Tooltip/Tooltip.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Help$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/icons-material/esm/Help.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
;
const DATE_FORMAT_FILTER = [
    'EndBefore',
    'EndAfter',
    'ItemCreatedBefore',
    'ItemCreatedAfter'
];
const SELLER_FORMAT_FILTER = 'Seller';
const SKIN_FILTER = 'Skin';
const PET_SKIN_FILTER = 'PetSkin';
function ItemFilterPropertiesDisplay(props) {
    _s();
    let [localFilter, setLocalFilter] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(props.filter || {});
    let forceUpdate = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Hooks$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForceUpdate"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ItemFilterPropertiesDisplay.useEffect": ()=>{
            updateLocalFilter();
        }
    }["ItemFilterPropertiesDisplay.useEffect"], [
        JSON.stringify(props.filter)
    ]);
    function updateLocalFilter() {
        if (!props.filter) {
            return;
        }
        let localFilter = JSON.parse(JSON.stringify(props.filter));
        setLocalFilter(localFilter);
        checkForSellerName(localFilter);
    }
    function checkForSellerName(filter) {
        if (filter) {
            Object.keys(filter).forEach((key)=>{
                if (key === SELLER_FORMAT_FILTER) {
                    filter._hide = true;
                    __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getPlayerName(filter[key]).then((name)=>{
                        filter._hide = false;
                        filter._label = name || '-';
                        setLocalFilter(filter);
                        forceUpdate();
                    });
                }
            });
        }
    }
    function onRemoveClick(key) {
        let newLocalFilter = {
            ...localFilter
        };
        delete newLocalFilter[key];
        setLocalFilter(newLocalFilter);
        if (props.onAfterEdit) {
            props.onAfterEdit(newLocalFilter);
        }
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        children: !localFilter ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {}, void 0, false) : Object.keys(localFilter).map((key)=>{
            if (!localFilter || localFilter._hide) {
                return '';
            }
            let display = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Formatter$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["convertTagToName"])(localFilter[key]);
            let info = null;
            if (key === 'ItemNameContains') {
                display = localFilter[key];
            }
            if (key.startsWith('_')) {
                return '';
            }
            // finds ">","<","="" and combinations at the beginning
            let beginningSymbolRegexp = new RegExp(/^[<>=]+/);
            if (!isNaN(Number(display.replace(beginningSymbolRegexp, '')))) {
                let symbols = display.match(beginningSymbolRegexp);
                let number = display.replace(beginningSymbolRegexp, '');
                display = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Formatter$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["numberWithThousandsSeparators"])(Number(number));
                display = symbols ? symbols[0] + display : display;
            }
            // finds number ranges (e.g. "10000-999999")
            let numberRangeRegex = new RegExp(/^\d+-\d+$/);
            if (display.match(numberRangeRegex)) {
                let numbers = display.split('-').map((numberString)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Formatter$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["numberWithThousandsSeparators"])(Number(numberString)));
                if (numbers[0] === numbers[1]) {
                    display = numbers[0].toString();
                } else {
                    display = numbers.join('-');
                }
            }
            // Special case -> display as date
            if (localFilter[key] && DATE_FORMAT_FILTER.findIndex((f)=>f === key) !== -1) {
                display = new Date(Number(localFilter[key]) * 1000).toLocaleDateString();
            }
            // Special case if the restriction has a special label
            if (localFilter._sellerName && key === SELLER_FORMAT_FILTER) {
                display = localFilter._sellerName;
            }
            // Special case for skin filter
            if (key === SKIN_FILTER || key === PET_SKIN_FILTER) {
                info = 'This filter only works on applied skins on pets/armor. For the items there is the "ItemCategory" filter.';
            }
            if (!localFilter[key] && !display) {
                display = '-';
            }
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "ellipse mb-2",
                        title: display,
                        children: [
                            (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Formatter$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["camelCaseToSentenceCase"])(key),
                            ": ",
                            display,
                            info ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Tooltip$2f$Tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                type: "hover",
                                content: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Help$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    style: {
                                        color: '#007bff',
                                        cursor: 'pointer',
                                        marginLeft: 5
                                    }
                                }, void 0, false, {
                                    fileName: "[project]/components/ItemFilter/ItemFilterPropertiesDisplay.tsx",
                                    lineNumber: 133,
                                    columnNumber: 46
                                }, void 0),
                                tooltipContent: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    children: info
                                }, void 0, false, {
                                    fileName: "[project]/components/ItemFilter/ItemFilterPropertiesDisplay.tsx",
                                    lineNumber: 134,
                                    columnNumber: 53
                                }, void 0)
                            }, void 0, false, {
                                fileName: "[project]/components/ItemFilter/ItemFilterPropertiesDisplay.tsx",
                                lineNumber: 131,
                                columnNumber: 41
                            }, this) : null
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/ItemFilter/ItemFilterPropertiesDisplay.tsx",
                        lineNumber: 129,
                        columnNumber: 29
                    }, this),
                    props.isEditable ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        style: {
                            color: 'red',
                            cursor: 'pointer'
                        },
                        onClick: ()=>{
                            onRemoveClick(key);
                        },
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Remove$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                            fileName: "[project]/components/ItemFilter/ItemFilterPropertiesDisplay.tsx",
                            lineNumber: 145,
                            columnNumber: 37
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/components/ItemFilter/ItemFilterPropertiesDisplay.tsx",
                        lineNumber: 139,
                        columnNumber: 33
                    }, this) : null
                ]
            }, key, true, {
                fileName: "[project]/components/ItemFilter/ItemFilterPropertiesDisplay.tsx",
                lineNumber: 128,
                columnNumber: 25
            }, this);
        })
    }, void 0, false, {
        fileName: "[project]/components/ItemFilter/ItemFilterPropertiesDisplay.tsx",
        lineNumber: 67,
        columnNumber: 9
    }, this);
}
_s(ItemFilterPropertiesDisplay, "vy0i3QHH8+uTJs2JrIHcyYmbWKU=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Hooks$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForceUpdate"]
    ];
});
_c = ItemFilterPropertiesDisplay;
const __TURBOPACK__default__export__ = ItemFilterPropertiesDisplay;
var _c;
__turbopack_context__.k.register(_c, "ItemFilterPropertiesDisplay");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/Number/Number.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>NumberElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Formatter$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/Formatter.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
function NumberElement(props) {
    _s();
    let [isSSR, setIsSSR] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    let value = Number(props.number);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "NumberElement.useEffect": ()=>{
            setIsSSR(false);
        }
    }["NumberElement.useEffect"], []);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: isSSR ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Formatter$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["numberWithThousandsSeparators"])(value, ',', '.') : (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Formatter$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["numberWithThousandsSeparators"])(value)
    }, void 0, false);
}
_s(NumberElement, "DdT/RTUMRscU32S7lal5R4JlHrU=");
_c = NumberElement;
var _c;
__turbopack_context__.k.register(_c, "NumberElement");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/SubscribeButton/SubscribeButton.module.css [app-client] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "inputIata": "SubscribeButton-module__09S1Mq__inputIata",
  "multiSearch": "SubscribeButton-module__09S1Mq__multiSearch",
  "notifyButton": "SubscribeButton-module__09S1Mq__notifyButton",
  "priceInput": "SubscribeButton-module__09S1Mq__priceInput",
  "subscribe-dialog": "SubscribeButton-module__09S1Mq__subscribe-dialog",
});
}}),
"[project]/components/FilterElement/FilterElements/DateFilterElement.module.css [app-client] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "calendarIcon": "DateFilterElement-module__aG3NAG__calendarIcon",
  "dateFilter": "DateFilterElement-module__aG3NAG__dateFilter",
  "datePickerPopper": "DateFilterElement-module__aG3NAG__datePickerPopper",
});
}}),
"[project]/components/FilterElement/FilterElements/DateFilterElement.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "DateFilterElement": (()=>DateFilterElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$datepicker$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-datepicker/dist/index.es.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterElements$2f$DateFilterElement$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/components/FilterElement/FilterElements/DateFilterElement.module.css [app-client] (css module)");
'use client';
;
;
;
function DateFilterElement(props) {
    function _onChange(date) {
        date = date || new Date();
        props.onChange(Math.round(date.getTime() / 1000));
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$datepicker$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
            showIcon: true,
            calendarIconClassName: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterElements$2f$DateFilterElement$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].calendarIcon,
            showTimeSelect: true,
            className: `date-filter form-control ${__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterElements$2f$DateFilterElement$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].dateFilter}`,
            selected: props.selected,
            dateFormat: 'yyyy/MM/dd HH:mm',
            onChange: _onChange,
            popperClassName: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterElements$2f$DateFilterElement$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].datePickerPopper
        }, void 0, false, {
            fileName: "[project]/components/FilterElement/FilterElements/DateFilterElement.tsx",
            lineNumber: 19,
            columnNumber: 13
        }, this)
    }, void 0, false, {
        fileName: "[project]/components/FilterElement/FilterElements/DateFilterElement.tsx",
        lineNumber: 18,
        columnNumber: 9
    }, this);
}
_c = DateFilterElement;
var _c;
__turbopack_context__.k.register(_c, "DateFilterElement");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/FilterElement/FilterElements/RangeFilterElement.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "RangeFilterElement": (()=>RangeFilterElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/Form.js [app-client] (ecmascript) <export default as Form>");
'use client';
;
;
function RangeFilterElement(props) {
    function _onChange(event) {
        props.onChange(event.target.value);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__["Form"].Control, {
        isInvalid: !props.isValid,
        defaultValue: props.defaultValue,
        onChange: _onChange
    }, void 0, false, {
        fileName: "[project]/components/FilterElement/FilterElements/RangeFilterElement.tsx",
        lineNumber: 16,
        columnNumber: 12
    }, this);
}
_c = RangeFilterElement;
var _c;
__turbopack_context__.k.register(_c, "RangeFilterElement");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/FilterElement/FilterElements/PlayerFilterElement.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "PlayerFilterElement": (()=>PlayerFilterElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2d$typeahead$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap-typeahead/es/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2d$typeahead$2f$es$2f$components$2f$AsyncTypeahead$2f$AsyncTypeahead$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__AsyncTypeahead$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap-typeahead/es/components/AsyncTypeahead/AsyncTypeahead.js [app-client] (ecmascript) <export default as AsyncTypeahead>");
var __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/api/ApiHelper.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__ = __turbopack_context__.i("[project]/node_modules/uuid/dist/esm-browser/v4.js [app-client] (ecmascript) <export default as v4>");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
let PlayerFilterElement = /*#__PURE__*/ _s((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(_c = _s((props, ref)=>{
    _s();
    // for player search
    let [players, setPlayers] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    let [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    function _onChange(selected) {
        props.onChange(selected[0] || '');
    }
    function handlePlayerSearch(query) {
        setIsLoading(true);
        __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].playerSearch(query).then((players)=>{
            setPlayers(players);
            setIsLoading(false);
        });
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2d$typeahead$2f$es$2f$components$2f$AsyncTypeahead$2f$AsyncTypeahead$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__AsyncTypeahead$3e$__["AsyncTypeahead"], {
        id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
        disabled: props.disabled,
        filterBy: ()=>true,
        isLoading: isLoading,
        labelKey: "name",
        minLength: 1,
        isInvalid: !props.isValid,
        onSearch: handlePlayerSearch,
        defaultInputValue: props.defaultValue,
        options: players,
        placeholder: props.placeholder || 'Search users...',
        onChange: (selected)=>_onChange(selected.map((s)=>{
                if (props.returnType === 'player') {
                    return s;
                }
                return s[props.returnType];
            })),
        ref: ref
    }, void 0, false, {
        fileName: "[project]/components/FilterElement/FilterElements/PlayerFilterElement.tsx",
        lineNumber: 36,
        columnNumber: 9
    }, this);
}, "kJwltaJVqKcvCQGdHvHVupnEGtM=")), "kJwltaJVqKcvCQGdHvHVupnEGtM=");
_c1 = PlayerFilterElement;
var _c, _c1;
__turbopack_context__.k.register(_c, "PlayerFilterElement$forwardRef");
__turbopack_context__.k.register(_c1, "PlayerFilterElement");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/FilterElement/FilterElements/SimpleEqualFilterElement.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "SimpleEqualFilterElement": (()=>SimpleEqualFilterElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/Form.js [app-client] (ecmascript) <export default as Form>");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Formatter$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/Formatter.tsx [app-client] (ecmascript)");
'use client';
;
;
;
function SimpleEqualFilterElement(props) {
    function _onChange(event) {
        let selectedIndex = event.target.options.selectedIndex;
        let value = event.target.options[selectedIndex].getAttribute('data-id');
        props.onChange(value);
    }
    function getSelectOptions() {
        return props.options.map((option)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                "data-id": option,
                value: option,
                children: (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Formatter$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["convertTagToName"])(option)
            }, option, false, {
                fileName: "[project]/components/FilterElement/FilterElements/SimpleEqualFilterElement.tsx",
                lineNumber: 22,
                columnNumber: 13
            }, this));
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__["Form"].Select, {
        isInvalid: !props.isValid,
        defaultValue: props.defaultValue,
        onChange: _onChange,
        children: getSelectOptions()
    }, void 0, false, {
        fileName: "[project]/components/FilterElement/FilterElements/SimpleEqualFilterElement.tsx",
        lineNumber: 29,
        columnNumber: 9
    }, this);
}
_c = SimpleEqualFilterElement;
var _c;
__turbopack_context__.k.register(_c, "SimpleEqualFilterElement");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/FilterElement/FilterElements/EqualFilterElement.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "EqualFilterElement": (()=>EqualFilterElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Formatter$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/Formatter.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2d$typeahead$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap-typeahead/es/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2d$typeahead$2f$es$2f$components$2f$Menu$2f$Menu$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Menu$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap-typeahead/es/components/Menu/Menu.js [app-client] (ecmascript) <export default as Menu>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2d$typeahead$2f$es$2f$components$2f$MenuItem$2f$MenuItem$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__MenuItem$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap-typeahead/es/components/MenuItem/MenuItem.js [app-client] (ecmascript) <export default as MenuItem>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2d$typeahead$2f$es$2f$components$2f$Typeahead$2f$Typeahead$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Typeahead$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap-typeahead/es/components/Typeahead/Typeahead.js [app-client] (ecmascript) <export default as Typeahead>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2d$typeahead$2f$es$2f$behaviors$2f$item$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap-typeahead/es/behaviors/item.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/api/ApiHelper.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
const Item = (props)=>/*#__PURE__*/ {
    _s();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2d$typeahead$2f$es$2f$components$2f$MenuItem$2f$MenuItem$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__MenuItem$3e$__["MenuItem"], {
        ...props,
        ...(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2d$typeahead$2f$es$2f$behaviors$2f$item$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useItem"])(props)
    }, void 0, false, {
        fileName: "[project]/components/FilterElement/FilterElements/EqualFilterElement.tsx",
        lineNumber: 8,
        columnNumber: 23
    }, this);
};
_s(Item, "V76djdvZ5YECSG1Rw+djQRghpxI=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2d$typeahead$2f$es$2f$behaviors$2f$item$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useItem"]
    ];
});
_c = Item;
function EqualFilterElement(props) {
    function _onChange(selected) {
        props.onChange(selected[0] || '');
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2d$typeahead$2f$es$2f$components$2f$Typeahead$2f$Typeahead$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Typeahead$3e$__["Typeahead"], {
        id: props.options.name,
        style: {
            display: 'block'
        },
        defaultSelected: props.defaultValue ? [
            props.defaultValue
        ] : undefined,
        onChange: _onChange,
        options: props.options?.options,
        labelKey: (option)=>{
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Formatter$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["convertTagToName"])(option);
        },
        isInvalid: !props.isValid,
        selectHint: (shouldSelect, event)=>{
            return event.key === 'Enter' || shouldSelect;
        },
        renderMenu: (results, menuProps)=>{
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2d$typeahead$2f$es$2f$components$2f$Menu$2f$Menu$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Menu$3e$__["Menu"], {
                id: menuProps.id,
                style: menuProps.style,
                innerRef: menuProps.innerRef,
                children: results.map((result, index)=>{
                    if (result['paginationOption']) {
                        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2d$typeahead$2f$es$2f$components$2f$MenuItem$2f$MenuItem$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__MenuItem$3e$__["MenuItem"], {
                            option: result,
                            position: index,
                            children: "More results..."
                        }, index, false, {
                            fileName: "[project]/components/FilterElement/FilterElements/EqualFilterElement.tsx",
                            lineNumber: 42,
                            columnNumber: 33
                        }, void 0);
                    }
                    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Item, {
                        option: result,
                        position: index,
                        children: [
                            typeof result === 'string' ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Formatter$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["convertTagToName"])(result) : result['label'],
                            props.showIcon && result !== 'None' && result !== 'Any' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                style: {
                                    float: 'right'
                                },
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                                    src: __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getItemImageUrl({
                                        tag: result
                                    }),
                                    style: {
                                        width: '24px',
                                        height: '24px'
                                    }
                                }, void 0, false, {
                                    fileName: "[project]/components/FilterElement/FilterElements/EqualFilterElement.tsx",
                                    lineNumber: 52,
                                    columnNumber: 41
                                }, void 0)
                            }, void 0, false, {
                                fileName: "[project]/components/FilterElement/FilterElements/EqualFilterElement.tsx",
                                lineNumber: 51,
                                columnNumber: 37
                            }, void 0)
                        ]
                    }, index, true, {
                        fileName: "[project]/components/FilterElement/FilterElements/EqualFilterElement.tsx",
                        lineNumber: 48,
                        columnNumber: 29
                    }, void 0);
                })
            }, void 0, false, {
                fileName: "[project]/components/FilterElement/FilterElements/EqualFilterElement.tsx",
                lineNumber: 38,
                columnNumber: 24
            }, void 0);
        }
    }, void 0, false, {
        fileName: "[project]/components/FilterElement/FilterElements/EqualFilterElement.tsx",
        lineNumber: 24,
        columnNumber: 9
    }, this);
}
_c1 = EqualFilterElement;
var _c, _c1;
__turbopack_context__.k.register(_c, "Item");
__turbopack_context__.k.register(_c1, "EqualFilterElement");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/FilterElement/FilterElements/PlayerWithRankFilterElement.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "PlayerWithRankFilterElement": (()=>PlayerWithRankFilterElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/Form.js [app-client] (ecmascript) <export default as Form>");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterElements$2f$PlayerFilterElement$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/FilterElement/FilterElements/PlayerFilterElement.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
const RANKS = [
    {
        color: '#AAAAAA',
        tag: '[NO_PLAYER]',
        value: ' '
    },
    {
        color: '#AAAAAA',
        tag: '[NO_RANK]',
        value: ' '
    },
    {
        color: '#FF5555',
        tag: '[OWNER]'
    },
    {
        color: '#FF5555',
        tag: '[ADMIN]'
    },
    {
        color: '#00AAAA',
        tag: '[BUILD TEAM]'
    },
    {
        color: '#00AA00',
        tag: '[MOD]'
    },
    {
        color: '#00AA00',
        tag: '[GM]'
    },
    {
        color: '#5555FF',
        tag: '[HELPER]'
    },
    {
        color: '#FF5555',
        tag: '[YOUTUBE]'
    },
    {
        color: '#FFAA00',
        tag: '[MVP++]'
    },
    {
        color: '#55FFFF',
        tag: '[MVP+]'
    },
    {
        color: '#55FFFF',
        tag: '[MVP]'
    },
    {
        color: '#55FF55',
        tag: '[VIP+]'
    },
    {
        color: '#55FF55',
        tag: '[VIP]'
    },
    {
        color: '#FF55FF',
        tag: '[PIG+++]'
    },
    {
        color: '#FF5555',
        tag: '[MINISTER]'
    },
    {
        color: '#FF55FF',
        tag: '[MAYOR]'
    }
];
function PlayerWithRankFilterElement(props) {
    _s();
    let [rank, setRank] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(getDefaultValues()[0]);
    let [player, setPlayer] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(getDefaultValues()[1]);
    let playerRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    function getDefaultValues() {
        let splits = [];
        if (props.defaultValue) {
            let s = props.defaultValue.trim().split(']');
            if (s.length === 1) {
                if (props.defaultValue.indexOf(']') !== -1) {
                    splits = [
                        props.defaultValue,
                        ''
                    ];
                } else {
                    splits = [
                        RANKS[1].tag,
                        props.defaultValue.trim()
                    ];
                }
            } else {
                splits = [
                    s[0] + ']',
                    s[1]
                ];
            }
        } else {
            splits = [
                RANKS[0].tag,
                ''
            ];
        }
        return splits;
    }
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "PlayerWithRankFilterElement.useEffect": ()=>{
            // If there is no default value, call _onChange, so the default is set from here
            // timeout, so the useEffect of the FilterElement-Component is executed first
            if (!props.defaultValue) {
                setTimeout({
                    "PlayerWithRankFilterElement.useEffect": ()=>{
                        _onChange();
                    }
                }["PlayerWithRankFilterElement.useEffect"]);
            }
        // eslint-disable-next-line react-hooks/exhaustive-deps
        }
    }["PlayerWithRankFilterElement.useEffect"], []);
    function _onChange() {
        let rankObject = RANKS.find((r)=>r.tag === rank);
        let rankValue = rankObject.value !== undefined ? rankObject.value : rankObject.tag;
        props.onChange(rankValue + ' ' + player);
    }
    function _onPlayerChange(uuid) {
        player = uuid;
        setPlayer(uuid);
        _onChange();
    }
    function _onRankChange(event) {
        let selectedIndex = event.target.options.selectedIndex;
        let _rank = event.target.options[selectedIndex].value;
        rank = _rank;
        if (RANKS[0].tag === rank) {
            setPlayer('');
            player = '';
            if (playerRef && playerRef.current) {
                ;
                playerRef.current.clear();
            }
        }
        setRank(_rank);
        _onChange();
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        style: {
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center'
        },
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__["Form"].Select, {
                style: {
                    width: '50%',
                    color: RANKS.find((r)=>r.tag === rank)?.color
                },
                defaultValue: rank,
                onChange: _onRankChange,
                children: RANKS.map((rank)=>{
                    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                        value: rank.tag,
                        style: {
                            color: rank.color
                        },
                        children: rank.tag
                    }, rank.tag, false, {
                        fileName: "[project]/components/FilterElement/FilterElements/PlayerWithRankFilterElement.tsx",
                        lineNumber: 154,
                        columnNumber: 25
                    }, this);
                })
            }, void 0, false, {
                fileName: "[project]/components/FilterElement/FilterElements/PlayerWithRankFilterElement.tsx",
                lineNumber: 151,
                columnNumber: 13
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                style: {
                    width: '50%'
                },
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterElements$2f$PlayerFilterElement$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PlayerFilterElement"], {
                    ref: playerRef,
                    returnType: "name",
                    defaultValue: player,
                    onChange: _onPlayerChange,
                    disabled: rank === RANKS[0].tag
                }, void 0, false, {
                    fileName: "[project]/components/FilterElement/FilterElements/PlayerWithRankFilterElement.tsx",
                    lineNumber: 161,
                    columnNumber: 17
                }, this)
            }, void 0, false, {
                fileName: "[project]/components/FilterElement/FilterElements/PlayerWithRankFilterElement.tsx",
                lineNumber: 160,
                columnNumber: 13
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/components/FilterElement/FilterElements/PlayerWithRankFilterElement.tsx",
        lineNumber: 150,
        columnNumber: 9
    }, this);
}
_s(PlayerWithRankFilterElement, "53cciARV6bfRpogwao8offQ0jxA=");
_c = PlayerWithRankFilterElement;
var _c;
__turbopack_context__.k.register(_c, "PlayerWithRankFilterElement");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/FilterElement/FilterElements/ColorFilterElement.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "ColorFilterElement": (()=>ColorFilterElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$colorful$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-colorful/dist/index.mjs [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
function ColorFilterElement(props) {
    _s();
    const [color, setColor] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(props.defaultValue);
    function _onChange(color) {
        color = color.replace('#', '');
        setColor(color);
        props.onChange(color);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$colorful$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["HexColorInput"], {
            className: "form-control",
            style: {
                textTransform: 'uppercase'
            },
            color: color,
            onChange: _onChange,
            prefixed: true
        }, void 0, false, {
            fileName: "[project]/components/FilterElement/FilterElements/ColorFilterElement.tsx",
            lineNumber: 21,
            columnNumber: 13
        }, this)
    }, void 0, false, {
        fileName: "[project]/components/FilterElement/FilterElements/ColorFilterElement.tsx",
        lineNumber: 20,
        columnNumber: 9
    }, this);
}
_s(ColorFilterElement, "JQsfHQN5Ityoq1cnZzGsS1/XiJo=");
_c = ColorFilterElement;
var _c;
__turbopack_context__.k.register(_c, "ColorFilterElement");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/FilterElement/FilterElements/BooleanFilterElement.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "BooleanFilterElement": (()=>BooleanFilterElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/Form.js [app-client] (ecmascript) <export default as Form>");
'use client';
;
;
function BooleanFilterElement(props) {
    function _onChange(e) {
        props.onChange(e.target.value);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__["Form"].Select, {
            defaultValue: props.defaultValue || 'true',
            onChange: _onChange,
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                    value: 'true',
                    children: "true"
                }, 'true', false, {
                    fileName: "[project]/components/FilterElement/FilterElements/BooleanFilterElement.tsx",
                    lineNumber: 18,
                    columnNumber: 17
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                    value: 'false',
                    children: "false"
                }, 'false', false, {
                    fileName: "[project]/components/FilterElement/FilterElements/BooleanFilterElement.tsx",
                    lineNumber: 21,
                    columnNumber: 17
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/components/FilterElement/FilterElements/BooleanFilterElement.tsx",
            lineNumber: 17,
            columnNumber: 13
        }, this)
    }, void 0, false, {
        fileName: "[project]/components/FilterElement/FilterElements/BooleanFilterElement.tsx",
        lineNumber: 16,
        columnNumber: 9
    }, this);
}
_c = BooleanFilterElement;
var _c;
__turbopack_context__.k.register(_c, "BooleanFilterElement");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/FilterElement/FilterElement.module.css [app-client] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "genericFilter": "FilterElement-module__NQNxTa__genericFilter",
});
}}),
"[project]/components/FilterElement/FilterElements/NumericalFilterElement.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "NumericalFilterElement": (()=>NumericalFilterElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/Form.js [app-client] (ecmascript) <export default as Form>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$number$2d$format$2f$dist$2f$react$2d$number$2d$format$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-number-format/dist/react-number-format.es.js [app-client] (ecmascript)");
'use client';
;
;
;
function NumericalFilterElement(props) {
    function _onChange(values) {
        props.onChange(values.value || '');
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$number$2d$format$2f$dist$2f$react$2d$number$2d$format$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumericFormat"], {
        onValueChange: _onChange,
        customInput: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__["Form"].Control,
        defaultValue: props.defaultValue,
        allowNegative: false,
        isAllowed: (value)=>{
            if (!value.floatValue) {
                return true;
            }
            let options = props.options?.options;
            if (options.length === 2 && !isNaN(+options[0]) && !isNaN(+options[1])) {
                return value.floatValue >= +options[0] && value.floatValue <= +options[1];
            }
            return true;
        },
        decimalScale: 2
    }, void 0, false, {
        fileName: "[project]/components/FilterElement/FilterElements/NumericalFilterElement.tsx",
        lineNumber: 19,
        columnNumber: 9
    }, this);
}
_c = NumericalFilterElement;
var _c;
__turbopack_context__.k.register(_c, "NumericalFilterElement");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/FilterElement/FilterElements/NumberRangeFilterElement.module.css [app-client] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "container": "NumberRangeFilterElement-module__oxFKZG__container",
  "slider": "NumberRangeFilterElement-module__oxFKZG__slider",
  "textField": "NumberRangeFilterElement-module__oxFKZG__textField",
});
}}),
"[project]/components/FilterElement/FilterElements/NumberRangeFilterElement.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "NumberRangeFilterElement": (()=>NumberRangeFilterElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$slider$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-slider/es/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterElements$2f$NumberRangeFilterElement$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/components/FilterElement/FilterElements/NumberRangeFilterElement.module.css [app-client] (css module)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/Form.js [app-client] (ecmascript) <export default as Form>");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
function NumberRangeFilterElement(props) {
    _s();
    let defaultValue = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "NumberRangeFilterElement.useMemo[defaultValue]": ()=>{
            return parseValue(props.defaultValue);
        }
    }["NumberRangeFilterElement.useMemo[defaultValue]"], [
        props.defaultValue
    ]);
    let [value, setValue] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(defaultValue);
    let [textValue, setTextValue] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(props.defaultValue);
    function parseValue(value) {
        if (!value) {
            return [
                5,
                5
            ];
        }
        if (Array.isArray(value)) {
            return value.map((v)=>parseInt(v.toString()));
        }
        let checks = [
            {
                regexp: new RegExp(/^\d+-\d+$/),
                handler: (value)=>value.split('-').map((v)=>parseInt(v))
            },
            {
                regexp: new RegExp(/^\d+$/),
                handler: (value)=>[
                        parseInt(value),
                        parseInt(value)
                    ]
            },
            {
                regexp: new RegExp(/^<\d+$/),
                handler: (value)=>[
                        props.min || 0,
                        parseInt(value.split('<')[1]) - 1
                    ]
            },
            {
                regexp: new RegExp(/^>\d+$/),
                handler: (value)=>[
                        parseInt(value.split('>')[1]) + 1,
                        props.max
                    ]
            }
        ];
        let result;
        checks.forEach((check)=>{
            if (value.toString().match(check.regexp)) {
                result = check.handler(value);
            }
        });
        return result;
    }
    function _onTextChange(e) {
        setTextValue(e.target.value);
        if (!e.target.value) {
            return;
        }
        let parsed = parseValue(e.target.value);
        if (!parsed) {
            return;
        }
        setValue(parsed);
        props.onChange(`${parsed[0]}-${parsed[1]}`);
    }
    function _onRangeChange(values) {
        setTextValue(`${values[0]}-${values[1]}`);
        setValue(values);
        props.onChange(`${values[0]}-${values[1]}`);
    }
    function getMarks() {
        if (props.max === undefined || props.max === null) {
            return undefined;
        }
        let marks = {};
        for(let i = props.min || 0; i <= props.max; i++){
            marks[i] = i === 0 ? 'None' : i.toString();
        }
        return marks;
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterElements$2f$NumberRangeFilterElement$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].container,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__["Form"].Control, {
                value: textValue,
                onChange: _onTextChange,
                className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterElements$2f$NumberRangeFilterElement$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].textField
            }, void 0, false, {
                fileName: "[project]/components/FilterElement/FilterElements/NumberRangeFilterElement.tsx",
                lineNumber: 92,
                columnNumber: 13
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$slider$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterElements$2f$NumberRangeFilterElement$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].slider,
                range: true,
                marks: getMarks(),
                allowCross: false,
                onChange: _onRangeChange,
                min: props.min || 0,
                max: props.max,
                value: value
            }, void 0, false, {
                fileName: "[project]/components/FilterElement/FilterElements/NumberRangeFilterElement.tsx",
                lineNumber: 93,
                columnNumber: 13
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/components/FilterElement/FilterElements/NumberRangeFilterElement.tsx",
        lineNumber: 91,
        columnNumber: 9
    }, this);
}
_s(NumberRangeFilterElement, "IMtub/vPgAWPOifeOAYaG+ntX5Q=");
_c = NumberRangeFilterElement;
var _c;
__turbopack_context__.k.register(_c, "NumberRangeFilterElement");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/utils/NumberValidationUtils.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "validateFilterNumber": (()=>validateFilterNumber),
    "validateFilterRange": (()=>validateFilterRange)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Formatter$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/Formatter.tsx [app-client] (ecmascript)");
;
const getRangeErrorMessage = (lower, higher)=>'Please choose a value between ' + (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Formatter$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["numberWithThousandsSeparators"])(parseInt(lower)) + ' and ' + (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Formatter$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["numberWithThousandsSeparators"])(parseInt(higher)) + '. Options not available have not been listed on ah before (won\'t find anything).';
const INVALID_NUMBER_ERROR = 'This is not a valid number';
const INVALID_NUMBER_RANGE_ERROR = 'This is not a valid number range';
const RANGE_ORDER_ERROR = 'The range order is invalid';
function removeRangeSymbols(input) {
    if (input.startsWith('<') || input.startsWith('>')) {
        input = input.substring(1);
        if (input.startsWith('=')) {
            input = input.substring(1);
        }
    }
    return input;
}
function getFilterNumber(number) {
    let numberRegexp = new RegExp(/^({{MIN_PROFIT}}\*|{{MAX_COST}}\*|{{MIN_VOLUME}}\*|{{MIN_PROFIT_PERCENT}}\*|)(\d*\.?\d+[kKmMbB]?)\|?$/);
    if (numberRegexp.test(number)) {
        if (number.includes('{')) return 1; // static 1 for variables
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Formatter$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getNumberFromShortenString"])(number);
    }
    return null;
}
function isNumberInRange(number, options) {
    if (options?.options?.length === 2) {
        let lowEnd = parseInt(options.options[0]);
        let highEnd = parseInt(options.options[1]);
        if (number < lowEnd || number > highEnd) {
            return false;
        }
    }
    return true;
}
function validateFilterNumber(input, options) {
    let number = getFilterNumber(input);
    if (number === null) {
        return [
            false,
            INVALID_NUMBER_ERROR
        ];
    }
    if (!isNumberInRange(number, options)) {
        return [
            false,
            getRangeErrorMessage(options.options[0], options.options[1])
        ];
    }
    return [
        true
    ];
}
function validateFilterRange(input, options) {
    if (input.includes('|')) {
        let parts = input.split('|');
        for (let part of parts){
            console.log('Validating part:', part);
            let [isValid, errorMessage] = validateFilterRange(part, options);
            if (!isValid) {
                console.error('Validation failed for part:', part, 'Error:', errorMessage);
                return [
                    false,
                    errorMessage
                ];
            }
        }
        return [
            true
        ];
    }
    if (!input.includes('-')) {
        input = removeRangeSymbols(input);
        let number = getFilterNumber(input);
        if (number === null) {
            return [
                false,
                INVALID_NUMBER_RANGE_ERROR
            ];
        }
        if (!isNumberInRange(number, options)) {
            return [
                false,
                getRangeErrorMessage(options.options[0], options.options[1])
            ];
        }
        return [
            true
        ];
    }
    let [n1, n2] = input.split('-');
    let number1 = getFilterNumber(n1);
    let number2 = getFilterNumber(n2);
    if (number1 === null || number2 === null) {
        return [
            false,
            INVALID_NUMBER_RANGE_ERROR
        ];
    }
    if (number1 > number2) {
        return [
            false,
            RANGE_ORDER_ERROR
        ];
    }
    if (!isNumberInRange(number1, options) || !isNumberInRange(number2, options)) {
        return [
            false,
            getRangeErrorMessage(options.options[0], options.options[1])
        ];
    }
    return [
        true
    ];
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/FilterElement/FilterElement.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
/* eslint-disable react-hooks/exhaustive-deps */ /* eslint-disable jsx-a11y/anchor-is-valid */ var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/Form.js [app-client] (ecmascript) <export default as Form>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Spinner$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Spinner$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/Spinner.js [app-client] (ecmascript) <export default as Spinner>");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Formatter$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/Formatter.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterType$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/FilterElement/FilterType.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterElements$2f$DateFilterElement$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/FilterElement/FilterElements/DateFilterElement.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterElements$2f$RangeFilterElement$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/FilterElement/FilterElements/RangeFilterElement.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterElements$2f$PlayerFilterElement$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/FilterElement/FilterElements/PlayerFilterElement.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterElements$2f$SimpleEqualFilterElement$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/FilterElement/FilterElements/SimpleEqualFilterElement.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterElements$2f$EqualFilterElement$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/FilterElement/FilterElements/EqualFilterElement.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterElements$2f$PlayerWithRankFilterElement$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/FilterElement/FilterElements/PlayerWithRankFilterElement.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterElements$2f$ColorFilterElement$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/FilterElement/FilterElements/ColorFilterElement.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterElements$2f$BooleanFilterElement$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/FilterElement/FilterElements/BooleanFilterElement.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterElement$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/components/FilterElement/FilterElement.module.css [app-client] (css module)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterElements$2f$NumericalFilterElement$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/FilterElement/FilterElements/NumericalFilterElement.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterElements$2f$NumberRangeFilterElement$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/FilterElement/FilterElements/NumberRangeFilterElement.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$NumberValidationUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/NumberValidationUtils.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Tooltip$2f$Tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/Tooltip/Tooltip.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Help$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/icons-material/esm/Help.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
function FilterElement(props) {
    _s();
    let [value, _setValue] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])();
    let [isValid, _setIsValid] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    let [errorText, setErrorText] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "FilterElement.useEffect": ()=>{
            if (value) {
                return;
            }
            let parsedDefaultValue = parseValue(props.defaultValue);
            updateValue(parsedDefaultValue);
            setValue(parsedDefaultValue);
        }
    }["FilterElement.useEffect"], []);
    function parseValue(newValue) {
        if (props.options && (0, __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterType$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["hasFlag"])(props.options.type, __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterType$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FilterType"].DATE)) {
            if (!newValue) {
                return Math.round(new Date().getTime() / 1000);
            }
            if (!isNaN(newValue)) {
                return newValue;
            }
            let date = Math.round(Date.parse(newValue) / 1000);
            if (!isNaN(date)) {
                return date;
            }
            return newValue;
        } else {
            if (!newValue && newValue !== 0) {
                return '';
            }
            return newValue;
        }
    }
    function onFilterElementChange(value) {
        if (!value) {
            setValue('');
            updateValue('');
        } else {
            setValue(value);
            updateValue(value.toString());
        }
    }
    function updateValue(value) {
        if (!validate(value)) {
            return;
        }
        let newFilter = {};
        newFilter[props.options.name] = value.toString();
        props.onFilterChange(newFilter);
    }
    function setValue(value) {
        _setValue(parseValue(value));
    }
    function setIsValid(newValue) {
        if (props.onIsValidChange) {
            props.onIsValidChange(newValue);
        }
        _setIsValid(newValue);
    }
    function validate(value) {
        if (!value && value !== 0) {
            setErrorText('Please fill the filter or remove it');
            setIsValid(false);
            return false;
        }
        if (props.options && (0, __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterType$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["hasFlag"])(props.options.type, __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterType$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FilterType"].NUMERICAL) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterType$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["hasFlag"])(props.options.type, __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterType$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FilterType"].RANGE)) {
            let validationResult = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$NumberValidationUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["validateFilterRange"])(value.toString(), props.options);
            setIsValid(validationResult[0]);
            if (!validationResult[0]) {
                setErrorText(validationResult[1] || '');
                return false;
            }
            return true;
        }
        if (props.options && (0, __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterType$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["hasFlag"])(props.options.type, __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterType$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FilterType"].DATE)) {
            let date = new Date(value * 1000);
            if (date < new Date(props.options.options[0])) {
                setErrorText(`Date needs to be after ${props.options.options[0]}`);
                setIsValid(false);
                return false;
            }
            if (date > new Date(props.options.options[1])) {
                setErrorText(`Date needs to be before ${props.options.options[1]}`);
                setIsValid(false);
                return false;
            }
            setIsValid(true);
            return true;
        }
        if (props.options && (0, __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterType$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["hasFlag"])(props.options.type, __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterType$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FilterType"].RANGE)) {
            if (props.options?.options.length === 2 && props.options.options[0] === '000000000000' && props.options.options[1] === 'ffffffffffff') {
                let result = new RegExp(/^[0-9A-Fa-f]{12}$/).test(value);
                setIsValid(result);
                if (!isValid) {
                    setErrorText('This field needs to be 12 characters long and must only include hex characters.');
                }
                return result;
            }
        }
        if (props.options && (0, __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterType$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["hasFlag"])(props.options.type, __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterType$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FilterType"].NUMERICAL)) {
            let validationResult = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$NumberValidationUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["validateFilterNumber"])(value.toString(), props.options);
            setIsValid(validationResult[0]);
            if (!validationResult[0]) {
                setErrorText(validationResult[1] || '');
                return false;
            }
            return true;
        }
        setIsValid(true);
        return true;
    }
    function getFilterElement(type, options) {
        // Special case for the color filter, as there is no FilterType on the backend for that
        if (options.name === 'Color') {
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterElements$2f$ColorFilterElement$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ColorFilterElement"], {
                defaultValue: props.defaultValue,
                onChange: onFilterElementChange
            }, options.name, false, {
                fileName: "[project]/components/FilterElement/FilterElement.tsx",
                lineNumber: 153,
                columnNumber: 20
            }, this);
        }
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterType$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["hasFlag"])(type, __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterType$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FilterType"].NUMERICAL) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterType$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["hasFlag"])(type, __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterType$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FilterType"].RANGE) && options.options.length === 2 && !isNaN(parseInt(options.options[0])) && !isNaN(parseInt(options.options[1])) && parseInt(options.options[1]) <= 10) {
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterElements$2f$NumberRangeFilterElement$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberRangeFilterElement"], {
                defaultValue: props.defaultValue,
                min: props.options ? parseInt(props.options.options[0]) : undefined,
                max: props.options ? parseInt(props.options.options[1]) : undefined,
                onChange: onFilterElementChange
            }, options.name, false, {
                fileName: "[project]/components/FilterElement/FilterElement.tsx",
                lineNumber: 164,
                columnNumber: 17
            }, this);
        }
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterType$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["hasFlag"])(type, __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterType$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FilterType"].DATE)) {
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterElements$2f$DateFilterElement$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DateFilterElement"], {
                selected: value ? new Date(value * 1000) : new Date(),
                onChange: onFilterElementChange
            }, options.name, false, {
                fileName: "[project]/components/FilterElement/FilterElement.tsx",
                lineNumber: 174,
                columnNumber: 20
            }, this);
        }
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterType$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["hasFlag"])(type, __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterType$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FilterType"].RANGE)) {
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterElements$2f$RangeFilterElement$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RangeFilterElement"], {
                isValid: isValid,
                defaultValue: props.defaultValue,
                onChange: onFilterElementChange
            }, options.name, false, {
                fileName: "[project]/components/FilterElement/FilterElement.tsx",
                lineNumber: 177,
                columnNumber: 20
            }, this);
        }
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterType$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["hasFlag"])(type, __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterType$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FilterType"].PLAYER_WITH_RANK)) {
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterElements$2f$PlayerWithRankFilterElement$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PlayerWithRankFilterElement"], {
                defaultValue: props.defaultValue,
                onChange: onFilterElementChange
            }, options.name, false, {
                fileName: "[project]/components/FilterElement/FilterElement.tsx",
                lineNumber: 180,
                columnNumber: 20
            }, this);
        }
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterType$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["hasFlag"])(type, __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterType$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FilterType"].PLAYER)) {
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterElements$2f$PlayerFilterElement$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PlayerFilterElement"], {
                defaultValue: props.defaultValue,
                isValid: isValid,
                returnType: "uuid",
                onChange: onFilterElementChange
            }, options.name, false, {
                fileName: "[project]/components/FilterElement/FilterElement.tsx",
                lineNumber: 184,
                columnNumber: 17
            }, this);
        }
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterType$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["hasFlag"])(type, __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterType$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FilterType"].BOOLEAN)) {
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterElements$2f$BooleanFilterElement$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BooleanFilterElement"], {
                defaultValue: props.defaultValue,
                onChange: onFilterElementChange
            }, options.name, false, {
                fileName: "[project]/components/FilterElement/FilterElement.tsx",
                lineNumber: 194,
                columnNumber: 20
            }, this);
        }
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterType$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["hasFlag"])(type, __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterType$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FilterType"].EQUAL)) {
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterType$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["hasFlag"])(options.type, __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterType$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FilterType"].SIMPLE)) {
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterElements$2f$SimpleEqualFilterElement$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SimpleEqualFilterElement"], {
                    options: options.options,
                    defaultValue: props.defaultValue,
                    isValid: isValid,
                    onChange: onFilterElementChange
                }, options.name, false, {
                    fileName: "[project]/components/FilterElement/FilterElement.tsx",
                    lineNumber: 199,
                    columnNumber: 21
                }, this);
            } else {
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterElements$2f$EqualFilterElement$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EqualFilterElement"], {
                    isValid: isValid,
                    options: options,
                    defaultValue: props.defaultValue,
                    onChange: onFilterElementChange,
                    showIcon: (0, __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterType$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["hasFlag"])(options.type, __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterType$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FilterType"].SHOW_ICON)
                }, options.name, false, {
                    fileName: "[project]/components/FilterElement/FilterElement.tsx",
                    lineNumber: 209,
                    columnNumber: 21
                }, this);
            }
        }
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterType$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["hasFlag"])(type, __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterType$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FilterType"].NUMERICAL)) {
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterElements$2f$NumericalFilterElement$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumericalFilterElement"], {
                defaultValue: props.defaultValue,
                options: options,
                onChange: onFilterElementChange
            }, options.name, false, {
                fileName: "[project]/components/FilterElement/FilterElement.tsx",
                lineNumber: 221,
                columnNumber: 20
            }, this);
        }
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {}, void 0, false, {
            fileName: "[project]/components/FilterElement/FilterElement.tsx",
            lineNumber: 223,
            columnNumber: 16
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterElement$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].genericFilter,
        children: !props.options ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Spinner$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Spinner$3e$__["Spinner"], {
            animation: "border",
            role: "status",
            variant: "primary"
        }, void 0, false, {
            fileName: "[project]/components/FilterElement/FilterElement.tsx",
            lineNumber: 229,
            columnNumber: 17
        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            style: {
                display: 'grid'
            },
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__["Form"].Label, {
                    style: {
                        float: 'left',
                        display: 'flex',
                        alignContent: 'center'
                    },
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("b", {
                            style: {
                                marginRight: 5
                            },
                            children: props.options.name[0].toLowerCase() === props.options.name[0] ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Formatter$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["convertTagToName"])(props.options.name) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Formatter$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["camelCaseToSentenceCase"])(props.options.name)
                        }, void 0, false, {
                            fileName: "[project]/components/FilterElement/FilterElement.tsx",
                            lineNumber: 233,
                            columnNumber: 25
                        }, this),
                        props.options.description ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Tooltip$2f$Tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                            type: "hover",
                            content: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Help$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                style: {
                                    color: '#007bff',
                                    cursor: 'pointer'
                                }
                            }, void 0, false, {
                                fileName: "[project]/components/FilterElement/FilterElement.tsx",
                                lineNumber: 241,
                                columnNumber: 42
                            }, void 0),
                            tooltipContent: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: props.options.description
                            }, void 0, false, {
                                fileName: "[project]/components/FilterElement/FilterElement.tsx",
                                lineNumber: 242,
                                columnNumber: 49
                            }, void 0)
                        }, void 0, false, {
                            fileName: "[project]/components/FilterElement/FilterElement.tsx",
                            lineNumber: 239,
                            columnNumber: 29
                        }, this) : null
                    ]
                }, void 0, true, {
                    fileName: "[project]/components/FilterElement/FilterElement.tsx",
                    lineNumber: 232,
                    columnNumber: 21
                }, this),
                getFilterElement(props.options.type, props.options),
                !isValid ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        style: {
                            color: 'red'
                        },
                        children: errorText
                    }, void 0, false, {
                        fileName: "[project]/components/FilterElement/FilterElement.tsx",
                        lineNumber: 249,
                        columnNumber: 29
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/components/FilterElement/FilterElement.tsx",
                    lineNumber: 248,
                    columnNumber: 25
                }, this) : null
            ]
        }, void 0, true, {
            fileName: "[project]/components/FilterElement/FilterElement.tsx",
            lineNumber: 231,
            columnNumber: 17
        }, this)
    }, void 0, false, {
        fileName: "[project]/components/FilterElement/FilterElement.tsx",
        lineNumber: 227,
        columnNumber: 9
    }, this);
}
_s(FilterElement, "Mt1QHD+pjSJYNOvdLPbnY5dWHfI=");
_c = FilterElement;
const __TURBOPACK__default__export__ = FilterElement;
var _c;
__turbopack_context__.k.register(_c, "FilterElement");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/ItemFilter/ItemFilter.module.css [app-client] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "addFilterSelect": "ItemFilter-module__EG8HRq__addFilterSelect",
  "filterContainer": "ItemFilter-module__EG8HRq__filterContainer",
  "filterElement": "ItemFilter-module__EG8HRq__filterElement",
  "itemFilter": "ItemFilter-module__EG8HRq__itemFilter",
  "removeFilter": "ItemFilter-module__EG8HRq__removeFilter",
});
}}),
"[project]/components/ItemFilter/ModAdvert.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>ModAdvert)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Card$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/Card.js [app-client] (ecmascript) <export default as Card>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$CancelOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/icons-material/esm/CancelOutlined.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
function ModAdvert() {
    _s();
    let [show, setShow] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ModAdvert.useEffect": ()=>{
            setShow(localStorage.getItem('hideModAdvertInFilter') !== 'true');
        }
    }["ModAdvert.useEffect"], []);
    function onClose() {
        setShow(false);
        localStorage.setItem('hideModAdvertInFilter', 'true');
    }
    if (!show) {
        return null;
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Card$3e$__["Card"], {
                style: {
                    marginBottom: '15px'
                },
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Card$3e$__["Card"].Header, {
                    style: {
                        borderRadius: '5px'
                    },
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Card$3e$__["Card"].Text, {
                        children: [
                            "Checking item prices? We provide a mod showing market data like volume, lowest bin and median prices in game. ",
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("br", {}, void 0, false, {
                                fileName: "[project]/components/ItemFilter/ModAdvert.tsx",
                                lineNumber: 28,
                                columnNumber: 135
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                href: "/mod",
                                style: {
                                    color: '#007bff',
                                    cursor: 'pointer'
                                },
                                children: "Check out the mod"
                            }, void 0, false, {
                                fileName: "[project]/components/ItemFilter/ModAdvert.tsx",
                                lineNumber: 29,
                                columnNumber: 25
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                style: {
                                    position: 'absolute',
                                    top: 1,
                                    right: 5,
                                    color: 'red',
                                    cursor: 'pointer'
                                },
                                onClick: onClose,
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$CancelOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                    fileName: "[project]/components/ItemFilter/ModAdvert.tsx",
                                    lineNumber: 39,
                                    columnNumber: 29
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/components/ItemFilter/ModAdvert.tsx",
                                lineNumber: 38,
                                columnNumber: 25
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/ItemFilter/ModAdvert.tsx",
                        lineNumber: 27,
                        columnNumber: 21
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/components/ItemFilter/ModAdvert.tsx",
                    lineNumber: 26,
                    columnNumber: 17
                }, this)
            }, void 0, false, {
                fileName: "[project]/components/ItemFilter/ModAdvert.tsx",
                lineNumber: 25,
                columnNumber: 13
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("hr", {}, void 0, false, {
                fileName: "[project]/components/ItemFilter/ModAdvert.tsx",
                lineNumber: 44,
                columnNumber: 13
            }, this)
        ]
    }, void 0, true);
}
_s(ModAdvert, "bXBd/WbmO9A8Q7bxaOKZvuJyGc0=");
_c = ModAdvert;
var _c;
__turbopack_context__.k.register(_c, "ModAdvert");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/ItemFilter/ItemFilter.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__),
    "getPrefillFilter": (()=>getPrefillFilter)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
/* eslint-disable react-hooks/exhaustive-deps */ /* eslint-disable jsx-a11y/anchor-is-valid */ var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Badge$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Badge$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/Badge.js [app-client] (ecmascript) <export default as Badge>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Button$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/Button.js [app-client] (ecmascript) <export default as Button>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Card$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/Card.js [app-client] (ecmascript) <export default as Card>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/Form.js [app-client] (ecmascript) <export default as Form>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Modal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Modal$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/Modal.js [app-client] (ecmascript) <export default as Modal>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Spinner$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Spinner$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/Spinner.js [app-client] (ecmascript) <export default as Spinner>");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Parser$2f$URLParser$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/Parser/URLParser.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterElement$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/FilterElement/FilterElement.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Delete$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/icons-material/esm/Delete.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Help$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/icons-material/esm/Help.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$AddCircleOutline$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/icons-material/esm/AddCircleOutline.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Formatter$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/Formatter.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterType$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/FilterElement/FilterType.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2d$typeahead$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap-typeahead/es/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2d$typeahead$2f$es$2f$components$2f$Typeahead$2f$Typeahead$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Typeahead$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap-typeahead/es/components/Typeahead/Typeahead.js [app-client] (ecmascript) <export default as Typeahead>");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ItemFilter$2f$ItemFilter$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/components/ItemFilter/ItemFilter.module.css [app-client] (css module)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SettingsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/SettingsUtils.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ItemFilter$2f$ModAdvert$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ItemFilter/ModAdvert.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const groupedFilter = [
    [
        'Enchantment',
        'EnchantLvl'
    ],
    [
        'SecondEnchantment',
        'SecondEnchantLvl'
    ]
];
function ItemFilter(props) {
    _s();
    let router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    let pathname = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePathname"])();
    let [itemFilter, _setItemFilter] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({});
    let [expanded, setExpanded] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(props.forceOpen || false);
    let [selectedFilters, setSelectedFilters] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    let [showInfoDialog, setShowInfoDialog] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    let [invalidFilters, _setInvalidFilters] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(new Set());
    let typeaheadRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ItemFilter.useEffect": ()=>{
            if (props.filters && props.filters.length > 0) {
                initFilter();
            }
        }
    }["ItemFilter.useEffect"], [
        JSON.stringify(props.filters)
    ]);
    let sortedFilterOptions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "ItemFilter.useMemo[sortedFilterOptions]": ()=>{
            if (!props.filters) {
                return undefined;
            }
            let sorting = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SettingsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getSettingsObject"])(__TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SettingsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ITEM_FILTER_USE_COUNT"], {});
            let sorted = props.filters.sort({
                "ItemFilter.useMemo[sortedFilterOptions].sorted": (a, b)=>{
                    let aCount = sorting[a.name] || 0;
                    let bCount = sorting[b.name] || 0;
                    return bCount - aCount;
                }
            }["ItemFilter.useMemo[sortedFilterOptions].sorted"]);
            return sorted;
        }
    }["ItemFilter.useMemo[sortedFilterOptions]"], [
        props.filters
    ]);
    function initFilter() {
        if (props.ignoreURL && !props.defaultFilter) {
            return;
        }
        itemFilter = props.defaultFilter ? JSON.parse(JSON.stringify(props.defaultFilter)) : getPrefillFilter(props.filters, props.ignoreURL, props.disableLastUsedFilter);
        if (Object.keys(itemFilter).length > 0) {
            setExpanded(true);
            Object.keys(itemFilter).forEach((name)=>{
                if (!props.filters?.find((f)=>f.name === name)) {
                    delete itemFilter[name];
                    return;
                }
                enableFilter(name);
                getGroupedFilter(name).forEach((filter)=>enableFilter(filter));
            });
            setItemFilter(itemFilter);
            onFilterChange(itemFilter);
        }
    }
    function getGroupedFilter(filterName) {
        let result = [];
        let index = groupedFilter.findIndex((group)=>{
            let groupIndex = group.findIndex((element)=>{
                return filterName === element;
            });
            return groupIndex !== -1;
        });
        if (index !== -1) {
            let groupToEnable = groupedFilter[index];
            groupToEnable.forEach((filter)=>{
                if (filter !== filterName) {
                    result.push(filter);
                }
            });
        }
        return result;
    }
    let enableFilter = (filterName, filterValue)=>{
        if (selectedFilters.some((n)=>n === filterName)) {
            return;
        }
        selectedFilters = [
            ...selectedFilters,
            filterName
        ];
        setSelectedFilters(selectedFilters);
        if (itemFilter[filterName] === undefined && !filterValue) {
            itemFilter[filterName] = getDefaultValue(filterName);
        }
        if (itemFilter[filterName] === undefined && filterValue) {
            itemFilter[filterName] = filterValue;
        }
        updateURLQuery(itemFilter);
        setItemFilter(itemFilter);
    };
    let addFilter = ([selectedFilter])=>{
        if (!selectedFilter) {
            return;
        }
        let sortingByUsedMost = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SettingsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getSettingsObject"])(__TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SettingsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ITEM_FILTER_USE_COUNT"], {});
        if (sortingByUsedMost[selectedFilter.name] !== undefined) {
            sortingByUsedMost[selectedFilter.name] = sortingByUsedMost[selectedFilter.name] + 1;
        } else {
            sortingByUsedMost[selectedFilter.name] = 1;
        }
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SettingsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setSetting"])(__TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SettingsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ITEM_FILTER_USE_COUNT"], JSON.stringify(sortingByUsedMost));
        let currentText = typeaheadRef?.current?.state.text;
        let match = currentText?.match(/\((\w+)\)$/);
        let prefillValue = undefined;
        if (match) {
            prefillValue = selectedFilter.options.find((option)=>option.toLowerCase() === match[1].toLowerCase());
        }
        typeaheadRef?.current?.clear();
        typeaheadRef?.current?.blur();
        enableFilter(selectedFilter.name, prefillValue);
        getGroupedFilter(selectedFilter.name).forEach((filter)=>enableFilter(filter));
    };
    let onFilterClose = ()=>{
        setSelectedFilters([]);
        setExpanded(false);
        setItemFilter({});
        onFilterChange({});
    };
    function onFilterRemoveClick(filterName) {
        if (invalidFilters.has(filterName)) {
            let newInvalidFilters = new Set(invalidFilters);
            newInvalidFilters.delete(filterName);
            setInvalidFilters(newInvalidFilters);
        }
        removeFilter(filterName);
        getGroupedFilter(filterName).forEach((filter)=>removeFilter(filter));
    }
    function removeFilter(filterName) {
        if (itemFilter) {
            delete itemFilter[filterName];
            setItemFilter(itemFilter);
            updateURLQuery(itemFilter);
            onFilterChange(itemFilter);
        }
        let newSelectedFilters = selectedFilters.filter((f)=>f !== filterName);
        selectedFilters = newSelectedFilters;
        setSelectedFilters(newSelectedFilters);
    }
    let onEnable = ()=>{
        setExpanded(true);
        if (!itemFilter) {
            itemFilter = {};
            setItemFilter(itemFilter);
        }
        updateURLQuery(itemFilter);
    };
    let setItemFilter = (itemFilter)=>{
        _setItemFilter({
            ...itemFilter
        });
        updateURLQuery(itemFilter);
    };
    let updateURLQuery = (filter)=>{
        if (props.ignoreURL) {
            return;
        }
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Parser$2f$URLParser$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setFilterIntoUrlParams"])(router, pathname, filter || {});
    };
    function onFilterChange(filter) {
        let filterCopy = {
            ...filter
        };
        let valid = true;
        Object.keys(filterCopy).forEach((key)=>{
            if (!checkForValidGroupedFilter(key, filterCopy)) {
                valid = false;
                return;
            }
        });
        if (!valid) {
            return;
        }
        setItemFilter(filterCopy);
        if (!props.disableLastUsedFilter) {
            localStorage.setItem(__TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SettingsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LAST_USED_FILTER"], JSON.stringify(filterCopy));
        }
        if (props.onFilterChange) {
            Object.keys(filterCopy).forEach((key)=>{
                if (filterCopy[key] === '' || filterCopy[key] === null) {
                    delete filterCopy[key];
                }
            });
            props.onFilterChange(filterCopy);
        }
    }
    function checkForValidGroupedFilter(filterName, filter) {
        let groupFilters = getGroupedFilter(filterName);
        let invalid = false;
        groupFilters.forEach((name)=>{
            if (filter[name] === undefined || filter[name] === null) {
                invalid = true;
            }
        });
        return !invalid;
    }
    function onFilterElementChange(filter) {
        let newFilter = itemFilter;
        var keys = Object.keys(filter);
        if (keys.length > 0) {
            var key = keys[0];
            newFilter[key] = filter[key];
        }
        if ((newFilter.EnchantLvl || newFilter.Enchantment) && !(newFilter.EnchantLvl && newFilter.Enchantment)) {
            return;
        }
        onFilterChange(newFilter);
    }
    function onIsValidChange(filterName, newIsValid) {
        let newInvalidFilters = new Set(invalidFilters);
        if (newIsValid) {
            newInvalidFilters.delete(filterName);
        } else {
            newInvalidFilters.add(filterName);
        }
        setInvalidFilters(newInvalidFilters);
    }
    function setInvalidFilters(newInvalidFilters) {
        if (props.onIsValidChange) {
            props.onIsValidChange(newInvalidFilters.size === 0);
        }
        _setInvalidFilters(newInvalidFilters);
    }
    function getDefaultValue(filterName) {
        let options = props.filters?.find((f)=>f.name === filterName);
        let defaultValue = '';
        if (options && options.options[0] !== null && options.options[0] !== undefined) {
            // dont set the first option for search-selects
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterType$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["hasFlag"])(options.type, __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterType$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FilterType"].EQUAL) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterType$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["hasFlag"])(options.type, __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterType$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FilterType"].SIMPLE) || (0, __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterType$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["hasFlag"])(options.type, __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterType$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FilterType"].BOOLEAN)) {
                defaultValue = options.options[0];
                if (options.name === 'Everything') {
                    defaultValue = 'true';
                }
            }
        }
        if (filterName === 'Color') {
            defaultValue = '#000000';
        }
        return defaultValue;
    }
    let filterList = selectedFilters.map((filterName)=>{
        let options = props.filters?.find((f)=>f.name === filterName);
        if (!options) {
            return null;
        }
        let defaultValue = getDefaultValue(filterName);
        if (itemFilter[filterName]) {
            defaultValue = itemFilter[filterName];
        }
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ItemFilter$2f$ItemFilter$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].filterElement,
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterElement$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    onFilterChange: onFilterElementChange,
                    options: options,
                    defaultValue: defaultValue,
                    onIsValidChange: (newValue)=>onIsValidChange(filterName, newValue)
                }, void 0, false, {
                    fileName: "[project]/components/ItemFilter/ItemFilter.tsx",
                    lineNumber: 318,
                    columnNumber: 17
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ItemFilter$2f$ItemFilter$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].removeFilter,
                    onClick: ()=>onFilterRemoveClick(filterName),
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Delete$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                        color: "error"
                    }, void 0, false, {
                        fileName: "[project]/components/ItemFilter/ItemFilter.tsx",
                        lineNumber: 325,
                        columnNumber: 21
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/components/ItemFilter/ItemFilter.tsx",
                    lineNumber: 324,
                    columnNumber: 17
                }, this)
            ]
        }, filterName, true, {
            fileName: "[project]/components/ItemFilter/ItemFilter.tsx",
            lineNumber: 317,
            columnNumber: 13
        }, this);
    });
    let infoIconElement = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                style: {
                    cursor: 'pointer',
                    position: 'absolute',
                    top: '10px',
                    right: '10px',
                    color: '#007bff'
                },
                onClick: ()=>{
                    setShowInfoDialog(true);
                },
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Help$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                    fileName: "[project]/components/ItemFilter/ItemFilter.tsx",
                    lineNumber: 339,
                    columnNumber: 17
                }, this)
            }, void 0, false, {
                fileName: "[project]/components/ItemFilter/ItemFilter.tsx",
                lineNumber: 333,
                columnNumber: 13
            }, this),
            showInfoDialog ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Modal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Modal$3e$__["Modal"], {
                show: showInfoDialog,
                onHide: ()=>{
                    setShowInfoDialog(false);
                },
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Modal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Modal$3e$__["Modal"].Header, {
                        closeButton: true,
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                            children: "Item Filter Information"
                        }, void 0, false, {
                            fileName: "[project]/components/ItemFilter/ItemFilter.tsx",
                            lineNumber: 349,
                            columnNumber: 25
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/components/ItemFilter/ItemFilter.tsx",
                        lineNumber: 348,
                        columnNumber: 21
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Modal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Modal$3e$__["Modal"].Body, {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                children: "You can add various filters depending on the item type. The graph and recent/active auctions will be updated to only include items with the selected properties."
                            }, void 0, false, {
                                fileName: "[project]/components/ItemFilter/ItemFilter.tsx",
                                lineNumber: 352,
                                columnNumber: 25
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("hr", {}, void 0, false, {
                                fileName: "[project]/components/ItemFilter/ItemFilter.tsx",
                                lineNumber: 356,
                                columnNumber: 25
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Badge$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Badge$3e$__["Badge"], {
                                    bg: "danger",
                                    children: "Caution"
                                }, void 0, false, {
                                    fileName: "[project]/components/ItemFilter/ItemFilter.tsx",
                                    lineNumber: 358,
                                    columnNumber: 29
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/components/ItemFilter/ItemFilter.tsx",
                                lineNumber: 357,
                                columnNumber: 25
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                children: "Some filter requests take quite some time to process. That's because we have to search through millions of auctions that potentially match your filter. This can lead to no auctions being displayed at all because your browser thinks that our server is unavailable. If that happens please let us know. We may implement scheduled filters where you will get an email or push notification when we computed a result for your filter."
                            }, void 0, false, {
                                fileName: "[project]/components/ItemFilter/ItemFilter.tsx",
                                lineNumber: 360,
                                columnNumber: 25
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                children: [
                                    "If you are missing a filter please ask for it on our",
                                    ' ',
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                                        target: "_blank",
                                        rel: "noreferrer",
                                        href: "https://discord.gg/wvKXfTgCfb",
                                        children: "Discord"
                                    }, void 0, false, {
                                        fileName: "[project]/components/ItemFilter/ItemFilter.tsx",
                                        lineNumber: 368,
                                        columnNumber: 29
                                    }, this),
                                    "."
                                ]
                            }, void 0, true, {
                                fileName: "[project]/components/ItemFilter/ItemFilter.tsx",
                                lineNumber: 366,
                                columnNumber: 25
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/ItemFilter/ItemFilter.tsx",
                        lineNumber: 351,
                        columnNumber: 21
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/components/ItemFilter/ItemFilter.tsx",
                lineNumber: 342,
                columnNumber: 17
            }, this) : ''
        ]
    }, void 0, true, {
        fileName: "[project]/components/ItemFilter/ItemFilter.tsx",
        lineNumber: 332,
        columnNumber: 9
    }, this);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ItemFilter$2f$ItemFilter$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].itemFilter,
        children: !expanded ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                href: "#",
                onClick: ()=>onEnable(),
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$AddCircleOutline$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                        fileName: "[project]/components/ItemFilter/ItemFilter.tsx",
                        lineNumber: 386,
                        columnNumber: 25
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        children: " Add Filter"
                    }, void 0, false, {
                        fileName: "[project]/components/ItemFilter/ItemFilter.tsx",
                        lineNumber: 387,
                        columnNumber: 25
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/components/ItemFilter/ItemFilter.tsx",
                lineNumber: 385,
                columnNumber: 21
            }, this)
        }, void 0, false, {
            fileName: "[project]/components/ItemFilter/ItemFilter.tsx",
            lineNumber: 384,
            columnNumber: 17
        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Card$3e$__["Card"], {
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Card$3e$__["Card"].Title, {
                    style: {
                        margin: '10px'
                    },
                    children: [
                        "Filter",
                        props.showFilterInfoElement ? infoIconElement : null
                    ]
                }, void 0, true, {
                    fileName: "[project]/components/ItemFilter/ItemFilter.tsx",
                    lineNumber: 392,
                    columnNumber: 21
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Card$3e$__["Card"].Body, {
                    children: [
                        props.showModAdvert ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ItemFilter$2f$ModAdvert$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                            fileName: "[project]/components/ItemFilter/ItemFilter.tsx",
                            lineNumber: 397,
                            columnNumber: 48
                        }, this) : null,
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__["Form"], {
                            style: {
                                marginBottom: '5px'
                            },
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__["Form"].Group, {
                                    children: props?.filters && props.filters?.length > 0 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2d$typeahead$2f$es$2f$components$2f$Typeahead$2f$Typeahead$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Typeahead$3e$__["Typeahead"], {
                                        id: "add-filter-typeahead",
                                        autoFocus: props.autoSelect === undefined ? Object.keys(getPrefillFilter(props.filters, props.ignoreURL, props.disableLastUsedFilter)).length === 0 : props.autoSelect,
                                        defaultOpen: props.autoSelect === undefined ? Object.keys(getPrefillFilter(props.filters, props.ignoreURL, props.disableLastUsedFilter)).length === 0 : props.autoSelect,
                                        ref: typeaheadRef,
                                        placeholder: "Add filter",
                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ItemFilter$2f$ItemFilter$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].addFilterSelect,
                                        onChange: addFilter,
                                        options: sortedFilterOptions || [],
                                        labelKey: (options)=>{
                                            let text = typeaheadRef.current?.state.text;
                                            let optionsString = '';
                                            let name = options.name;
                                            let searchString = name?.replace(/\s/g, '').toLowerCase();
                                            let description = options.description ? options.description.replace(/\s/g, '').toLowerCase() : '';
                                            // If the restult was found because of the options, show the options at the end of the string
                                            if (text && !searchString?.includes(text) && !description.includes(text)) {
                                                let searchString = text.replace(/\s/g, '').toLowerCase();
                                                let matchingOptions = options.options.filter((option)=>option.toLowerCase().includes(searchString));
                                                if (matchingOptions.length > 0) {
                                                    optionsString = matchingOptions.map((option)=>{
                                                        if (option.toLowerCase() === option) {
                                                            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Formatter$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["convertTagToName"])(option).trim();
                                                        }
                                                        if (option.toUpperCase() === option) {
                                                            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Formatter$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["convertTagToName"])(option).trim();
                                                        }
                                                        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Formatter$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["camelCaseToSentenceCase"])(option).trim();
                                                    }).join(', ');
                                                }
                                            }
                                            if (name[0].toLowerCase() === name[0]) {
                                                return `${(0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Formatter$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["convertTagToName"])(name)} ${optionsString ? `(${optionsString})` : ''}`;
                                            }
                                            return `${(0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Formatter$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["camelCaseToSentenceCase"])(name)} ${optionsString ? `(${optionsString})` : ''}`;
                                        },
                                        filterBy: (options, props)=>{
                                            let searchString = props.text.replace(/\s/g, '').toLowerCase();
                                            let name = props.labelKey(options).toLowerCase();
                                            let initials = name.match(/\b\w/g).join('');
                                            let description = options.description ? options.description.replace(/\s/g, '').toLowerCase() : '';
                                            let matchingOptions = options.options.filter((option)=>option.toLowerCase().includes(searchString));
                                            return name.replace(/\s/g, '').includes(searchString) || initials.includes(searchString) || description.includes(searchString) || matchingOptions.length > 0;
                                        },
                                        emptyLabel: props.emptyLabel || 'No matches found. Filters which would not show any results are hidden'
                                    }, void 0, false, {
                                        fileName: "[project]/components/ItemFilter/ItemFilter.tsx",
                                        lineNumber: 401,
                                        columnNumber: 37
                                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Spinner$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Spinner$3e$__["Spinner"], {
                                        animation: "border",
                                        role: "status",
                                        variant: "primary"
                                    }, void 0, false, {
                                        fileName: "[project]/components/ItemFilter/ItemFilter.tsx",
                                        lineNumber: 469,
                                        columnNumber: 37
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/components/ItemFilter/ItemFilter.tsx",
                                    lineNumber: 399,
                                    columnNumber: 29
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ItemFilter$2f$ItemFilter$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].filterContainer,
                                    children: filterList
                                }, void 0, false, {
                                    fileName: "[project]/components/ItemFilter/ItemFilter.tsx",
                                    lineNumber: 472,
                                    columnNumber: 29
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/ItemFilter/ItemFilter.tsx",
                            lineNumber: 398,
                            columnNumber: 25
                        }, this),
                        props.forceOpen ? null : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            style: {
                                display: 'flex',
                                justifyContent: 'end',
                                marginTop: '10px'
                            },
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Button$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__["Button"], {
                                variant: "danger",
                                onClick: ()=>onFilterClose(),
                                children: "Close"
                            }, void 0, false, {
                                fileName: "[project]/components/ItemFilter/ItemFilter.tsx",
                                lineNumber: 476,
                                columnNumber: 33
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/components/ItemFilter/ItemFilter.tsx",
                            lineNumber: 475,
                            columnNumber: 29
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/components/ItemFilter/ItemFilter.tsx",
                    lineNumber: 396,
                    columnNumber: 21
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/components/ItemFilter/ItemFilter.tsx",
            lineNumber: 391,
            columnNumber: 17
        }, this)
    }, void 0, false, {
        fileName: "[project]/components/ItemFilter/ItemFilter.tsx",
        lineNumber: 382,
        columnNumber: 9
    }, this);
}
_s(ItemFilter, "FuFAHTF18Vj9wb8deJFTCzZ0WvE=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePathname"]
    ];
});
_c = ItemFilter;
const __TURBOPACK__default__export__ = ItemFilter;
function getPrefillFilter(filterOptions = [], ignoreURL = false, disableLastUsedFilter = false) {
    let itemFilter = !ignoreURL ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Parser$2f$URLParser$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getItemFilterFromUrl"])() : {};
    if (Object.keys(itemFilter).length === 0 && !disableLastUsedFilter) {
        itemFilter = getFilterFromLocalStorage(filterOptions) || {};
    }
    return itemFilter;
}
/**
 * Gets the last used filter from the local storage and removes all properties not available in the allowed filters
 * @returns the filter or null if no last used filter is found
 */ function getFilterFromLocalStorage(filterOptions = []) {
    let localStorageLastFilter = localStorage.getItem(__TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SettingsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LAST_USED_FILTER"]);
    if (localStorageLastFilter === null) {
        return null;
    }
    let filter = JSON.parse(localStorageLastFilter);
    Object.keys(filter).forEach((key)=>{
        if (filterOptions.findIndex((f)=>f.name === key) === -1) {
            delete filter[key];
        }
    });
    return filter;
}
var _c;
__turbopack_context__.k.register(_c, "ItemFilter");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/SubscribeButton/SubscribeItemContent/SubscribeItemContent.module.css [app-client] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "checkBox": "SubscribeItemContent-module__LsBmca__checkBox",
});
}}),
"[project]/components/SubscribeButton/SubscribeItemContent/SubscribeItemContent.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$FormControl$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__FormControl$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/FormControl.js [app-client] (ecmascript) <export default as FormControl>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$InputGroup$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__InputGroup$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/InputGroup.js [app-client] (ecmascript) <export default as InputGroup>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/Form.js [app-client] (ecmascript) <export default as Form>");
var __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/api/ApiHelper.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/api/ApiTypes.d.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ItemFilter$2f$ItemFilter$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ItemFilter/ItemFilter.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$SubscribeButton$2f$SubscribeItemContent$2f$SubscribeItemContent$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/components/SubscribeButton/SubscribeItemContent/SubscribeItemContent.module.css [app-client] (css module)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
;
function SubscribeItemContent(props) {
    _s();
    let [filterOptions, setFilterOptions] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "SubscribeItemContent.useEffect": ()=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getFilters(props.itemTag).then({
                "SubscribeItemContent.useEffect": (options)=>{
                    setFilterOptions(options);
                }
            }["SubscribeItemContent.useEffect"]);
        // eslint-disable-next-line react-hooks/exhaustive-deps
        }
    }["SubscribeItemContent.useEffect"], []);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "item-forms",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$InputGroup$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__InputGroup$3e$__["InputGroup"], {
                    className: "price-input",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$InputGroup$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__InputGroup$3e$__["InputGroup"].Text, {
                            id: "inputGroup-sizing-sm",
                            children: "Item price"
                        }, void 0, false, {
                            fileName: "[project]/components/SubscribeButton/SubscribeItemContent/SubscribeItemContent.tsx",
                            lineNumber: 34,
                            columnNumber: 21
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$FormControl$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__FormControl$3e$__["FormControl"], {
                            "aria-label": "Small",
                            "aria-describedby": "inputGroup-sizing-sm",
                            type: "number",
                            defaultValue: props.prefill?.price,
                            onChange: (e)=>props.onPriceChange(e.target.value)
                        }, void 0, false, {
                            fileName: "[project]/components/SubscribeButton/SubscribeItemContent/SubscribeItemContent.tsx",
                            lineNumber: 35,
                            columnNumber: 21
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/components/SubscribeButton/SubscribeItemContent/SubscribeItemContent.tsx",
                    lineNumber: 33,
                    columnNumber: 17
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("hr", {}, void 0, false, {
                    fileName: "[project]/components/SubscribeButton/SubscribeItemContent/SubscribeItemContent.tsx",
                    lineNumber: 43,
                    columnNumber: 17
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                    style: {
                        marginBottom: '20px'
                    },
                    children: "Notify me..."
                }, void 0, false, {
                    fileName: "[project]/components/SubscribeButton/SubscribeItemContent/SubscribeItemContent.tsx",
                    lineNumber: 44,
                    columnNumber: 17
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__["Form"].Group, {
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__["Form"].Label, {
                            htmlFor: "priceAboveCheckbox",
                            children: "if the price is above the selected value"
                        }, void 0, false, {
                            fileName: "[project]/components/SubscribeButton/SubscribeItemContent/SubscribeItemContent.tsx",
                            lineNumber: 46,
                            columnNumber: 21
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__["Form"].Check, {
                            type: "radio",
                            id: "priceAboveCheckbox",
                            name: "priceState",
                            defaultChecked: props.prefill && props.prefill.types.includes(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SubscriptionType"][__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SubscriptionType"].PRICE_HIGHER_THAN]),
                            onChange: (e)=>props.onIsPriceAboveChange(true),
                            className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$SubscribeButton$2f$SubscribeItemContent$2f$SubscribeItemContent$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].checkBox
                        }, void 0, false, {
                            fileName: "[project]/components/SubscribeButton/SubscribeItemContent/SubscribeItemContent.tsx",
                            lineNumber: 47,
                            columnNumber: 21
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/components/SubscribeButton/SubscribeItemContent/SubscribeItemContent.tsx",
                    lineNumber: 45,
                    columnNumber: 17
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__["Form"].Group, {
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__["Form"].Label, {
                            htmlFor: "priceBelowCheckbox",
                            children: "if the price is below the selected value"
                        }, void 0, false, {
                            fileName: "[project]/components/SubscribeButton/SubscribeItemContent/SubscribeItemContent.tsx",
                            lineNumber: 59,
                            columnNumber: 21
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__["Form"].Check, {
                            type: "radio",
                            id: "priceBelowCheckbox",
                            name: "priceState",
                            defaultChecked: props.prefill && props.prefill.types.includes(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SubscriptionType"][__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SubscriptionType"].PRICE_LOWER_THAN]),
                            onChange: (e)=>props.onIsPriceAboveChange(false),
                            className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$SubscribeButton$2f$SubscribeItemContent$2f$SubscribeItemContent$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].checkBox
                        }, void 0, false, {
                            fileName: "[project]/components/SubscribeButton/SubscribeItemContent/SubscribeItemContent.tsx",
                            lineNumber: 60,
                            columnNumber: 21
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/components/SubscribeButton/SubscribeItemContent/SubscribeItemContent.tsx",
                    lineNumber: 58,
                    columnNumber: 17
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__["Form"].Group, {
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__["Form"].Label, {
                            htmlFor: "onlyIstantBuy",
                            children: "only for instant buy"
                        }, void 0, false, {
                            fileName: "[project]/components/SubscribeButton/SubscribeItemContent/SubscribeItemContent.tsx",
                            lineNumber: 72,
                            columnNumber: 21
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__["Form"].Check, {
                            className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$SubscribeButton$2f$SubscribeItemContent$2f$SubscribeItemContent$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].checkBox,
                            type: "checkbox",
                            defaultChecked: props.prefill && props.prefill.types.includes(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SubscriptionType"][__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SubscriptionType"].BIN]),
                            id: "onlyIstantBuy",
                            onClick: (e)=>{
                                props.onOnlyInstantBuyChange(e.target.checked);
                            }
                        }, void 0, false, {
                            fileName: "[project]/components/SubscribeButton/SubscribeItemContent/SubscribeItemContent.tsx",
                            lineNumber: 73,
                            columnNumber: 21
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/components/SubscribeButton/SubscribeItemContent/SubscribeItemContent.tsx",
                    lineNumber: 71,
                    columnNumber: 17
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__["Form"].Group, {
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ItemFilter$2f$ItemFilter$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                        defaultFilter: props.prefill?.filter,
                        autoSelect: false,
                        filters: filterOptions,
                        forceOpen: true,
                        ignoreURL: true,
                        onFilterChange: props.onFilterChange,
                        onIsValidChange: props.onIsFilterValidChange
                    }, void 0, false, {
                        fileName: "[project]/components/SubscribeButton/SubscribeItemContent/SubscribeItemContent.tsx",
                        lineNumber: 84,
                        columnNumber: 21
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/components/SubscribeButton/SubscribeItemContent/SubscribeItemContent.tsx",
                    lineNumber: 83,
                    columnNumber: 17
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/components/SubscribeButton/SubscribeItemContent/SubscribeItemContent.tsx",
            lineNumber: 32,
            columnNumber: 13
        }, this)
    }, void 0, false);
}
_s(SubscribeItemContent, "pj4zKWCN7wKDf66/P5NbQfUobps=");
_c = SubscribeItemContent;
const __TURBOPACK__default__export__ = SubscribeItemContent;
var _c;
__turbopack_context__.k.register(_c, "SubscribeItemContent");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/SubscribeButton/SubscribePlayerContent/SubscribePlayerContent.module.css [app-client] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "checkBox": "SubscribePlayerContent-module__gZHVOW__checkBox",
});
}}),
"[project]/components/SubscribeButton/SubscribePlayerContent/SubscribePlayerContent.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/api/ApiTypes.d.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$SubscribeButton$2f$SubscribePlayerContent$2f$SubscribePlayerContent$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/components/SubscribeButton/SubscribePlayerContent/SubscribePlayerContent.module.css [app-client] (css module)");
;
;
;
function SubscribePlayerContent(props) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "player-forms",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                    style: {
                        marginBottom: '20px'
                    },
                    children: "Notify me..."
                }, void 0, false, {
                    fileName: "[project]/components/SubscribeButton/SubscribePlayerContent/SubscribePlayerContent.tsx",
                    lineNumber: 17,
                    columnNumber: 17
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "input-data",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                            type: "checkbox",
                            defaultChecked: props.prefill && props.prefill.types.includes(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SubscriptionType"][__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SubscriptionType"].PLAYER_CREATES_AUCTION]),
                            className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$SubscribeButton$2f$SubscribePlayerContent$2f$SubscribePlayerContent$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].checkBox,
                            id: "isPlayerAuctionCreation",
                            onChange: (e)=>props.onIsPlayerAuctionCreation(e.target.checked)
                        }, void 0, false, {
                            fileName: "[project]/components/SubscribeButton/SubscribePlayerContent/SubscribePlayerContent.tsx",
                            lineNumber: 19,
                            columnNumber: 21
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                            htmlFor: "isPlayerAuctionCreation",
                            children: "if the player creates an auction"
                        }, void 0, false, {
                            fileName: "[project]/components/SubscribeButton/SubscribePlayerContent/SubscribePlayerContent.tsx",
                            lineNumber: 28,
                            columnNumber: 21
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/components/SubscribeButton/SubscribePlayerContent/SubscribePlayerContent.tsx",
                    lineNumber: 18,
                    columnNumber: 17
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "input-data",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                            defaultChecked: props.prefill && props.prefill.types.includes(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SubscriptionType"][__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SubscriptionType"].OUTBID]),
                            type: "checkbox",
                            className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$SubscribeButton$2f$SubscribePlayerContent$2f$SubscribePlayerContent$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].checkBox,
                            id: "outbidCheckbox",
                            onChange: (e)=>props.onGotOutbidChange(e.target.checked)
                        }, void 0, false, {
                            fileName: "[project]/components/SubscribeButton/SubscribePlayerContent/SubscribePlayerContent.tsx",
                            lineNumber: 31,
                            columnNumber: 21
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                            htmlFor: "outbidCheckbox",
                            children: "if the player gets outbid"
                        }, void 0, false, {
                            fileName: "[project]/components/SubscribeButton/SubscribePlayerContent/SubscribePlayerContent.tsx",
                            lineNumber: 38,
                            columnNumber: 21
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/components/SubscribeButton/SubscribePlayerContent/SubscribePlayerContent.tsx",
                    lineNumber: 30,
                    columnNumber: 17
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "input-data",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                            defaultChecked: props.prefill && props.prefill.types.includes(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SubscriptionType"][__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SubscriptionType"].SOLD]),
                            type: "checkbox",
                            className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$SubscribeButton$2f$SubscribePlayerContent$2f$SubscribePlayerContent$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].checkBox,
                            id: "isSoldCheckbox",
                            onChange: (e)=>props.onIsSoldChange(e.target.checked)
                        }, void 0, false, {
                            fileName: "[project]/components/SubscribeButton/SubscribePlayerContent/SubscribePlayerContent.tsx",
                            lineNumber: 41,
                            columnNumber: 21
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                            htmlFor: "isSoldCheckbox",
                            children: "if an auction of the player has ended"
                        }, void 0, false, {
                            fileName: "[project]/components/SubscribeButton/SubscribePlayerContent/SubscribePlayerContent.tsx",
                            lineNumber: 48,
                            columnNumber: 21
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/components/SubscribeButton/SubscribePlayerContent/SubscribePlayerContent.tsx",
                    lineNumber: 40,
                    columnNumber: 17
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "input-data",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                            defaultChecked: props.prefill && props.prefill.types.includes(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SubscriptionType"][__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SubscriptionType"].BOUGHT_ANY_AUCTION]),
                            type: "checkbox",
                            className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$SubscribeButton$2f$SubscribePlayerContent$2f$SubscribePlayerContent$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].checkBox,
                            id: "hasBoughtAnyAuction",
                            onChange: (e)=>props.onBoughtAnyAuctionChange(e.target.checked)
                        }, void 0, false, {
                            fileName: "[project]/components/SubscribeButton/SubscribePlayerContent/SubscribePlayerContent.tsx",
                            lineNumber: 51,
                            columnNumber: 21
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                            htmlFor: "hasBoughtAnyAuction",
                            children: "if the player bought any auction"
                        }, void 0, false, {
                            fileName: "[project]/components/SubscribeButton/SubscribePlayerContent/SubscribePlayerContent.tsx",
                            lineNumber: 60,
                            columnNumber: 21
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/components/SubscribeButton/SubscribePlayerContent/SubscribePlayerContent.tsx",
                    lineNumber: 50,
                    columnNumber: 17
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/components/SubscribeButton/SubscribePlayerContent/SubscribePlayerContent.tsx",
            lineNumber: 16,
            columnNumber: 13
        }, this)
    }, void 0, false);
}
_c = SubscribePlayerContent;
const __TURBOPACK__default__export__ = SubscribePlayerContent;
var _c;
__turbopack_context__.k.register(_c, "SubscribePlayerContent");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/SubscribeButton/SubscribeAuctionContent/SubscribeAuctionContent.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
;
function SubscribeAuctionContent() {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
            style: {
                marginBottom: '20px'
            },
            children: "Notify me if someone bids on or buys the auction."
        }, void 0, false, {
            fileName: "[project]/components/SubscribeButton/SubscribeAuctionContent/SubscribeAuctionContent.tsx",
            lineNumber: 6,
            columnNumber: 13
        }, this)
    }, void 0, false);
}
_c = SubscribeAuctionContent;
const __TURBOPACK__default__export__ = SubscribeAuctionContent;
var _c;
__turbopack_context__.k.register(_c, "SubscribeAuctionContent");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/utils/NotificationPermisson.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>askForNotificationPermissons)
});
function askForNotificationPermissons() {
    return new Promise((resolve, reject)=>{
        let token = localStorage.getItem('fcmToken');
        if (token) {
            resolve(token);
        }
        // Retrieve an instance of Firebase Messaging so that it can handle background
        // messages.
        // @ts-ignore
        waitTilSet();
        function waitTilSet() {
            if (!window.messaging) {
                setTimeout(waitTilSet, 50) //wait 50 millisecnds then recheck
                ;
                return;
            }
            ;
            window.messaging.getToken({
                vapidKey: 'BESZjJEHTRUVz5_8NW-jjOToWiSJFZHDzK9AYZP6No8cqGHkP7UQ_1XnEPqShuQtGj8lvtjBlkfoV86m_PadW30'
            }).then((token)=>{
                localStorage.setItem('fcmToken', token);
                resolve(token);
            });
        }
    });
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/NotificationTargets/NotificationTargetForm.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Button$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/Button.js [app-client] (ecmascript) <export default as Button>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/Form.js [app-client] (ecmascript) <export default as Form>");
var __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/api/ApiHelper.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$NotificationPermisson$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/NotificationPermisson.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$NotificationUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/NotificationUtils.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
const TYPE_OPTIONS = [
    'DiscordWebhook',
    'FIREBASE',
    'InGame'
];
const WHEN_OPITIONS = [
    'NEVER',
    'AfterFail',
    'ALWAYS'
];
function NotificationTargetForm(props) {
    _s();
    let [name, setName] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(props.defaultNotificationTarget?.name || '');
    let [type, setType] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(props.defaultNotificationTarget?.type || 'FIREBASE');
    let [target, setTarget] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(props.defaultNotificationTarget?.target || null);
    let [when, setWhen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(props.defaultNotificationTarget?.when || 'ALWAYS');
    let [disabled, setDisabled] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    async function addNotificationTarget() {
        let targetToSend = target;
        if (type === 'FIREBASE') {
            if (localStorage.getItem('fcmToken') === null) {
                let token = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$NotificationPermisson$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])();
                await __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].setToken(token);
                localStorage.setItem('fcmToken', token);
                targetToSend = token;
            } else {
                targetToSend = localStorage.getItem('fcmToken');
            }
        }
        setDisabled(true);
        let notificationTarget = {
            id: props.defaultNotificationTarget?.id,
            name: name,
            type: type,
            target: targetToSend,
            when: when,
            useCount: 0
        };
        let updateOrCreateFunction = props.type === 'CREATE' ? __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].addNotificationTarget : __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].updateNotificationTarget;
        updateOrCreateFunction(notificationTarget).then((newTarget)=>{
            props.onSubmit(newTarget);
        }).finally(()=>{
            setDisabled(false);
        });
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__["Form"], {
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__["Form"].Group, {
                    className: "mb-3",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__["Form"].Label, {
                            children: "Name"
                        }, void 0, false, {
                            fileName: "[project]/components/NotificationTargets/NotificationTargetForm.tsx",
                            lineNumber: 63,
                            columnNumber: 21
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__["Form"].Control, {
                            defaultValue: name,
                            type: "text",
                            onChange: (e)=>setName(e.target.value),
                            placeholder: "Enter name"
                        }, void 0, false, {
                            fileName: "[project]/components/NotificationTargets/NotificationTargetForm.tsx",
                            lineNumber: 64,
                            columnNumber: 21
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/components/NotificationTargets/NotificationTargetForm.tsx",
                    lineNumber: 62,
                    columnNumber: 17
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__["Form"].Group, {
                    className: "mb-3",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__["Form"].Label, {
                            children: "Type"
                        }, void 0, false, {
                            fileName: "[project]/components/NotificationTargets/NotificationTargetForm.tsx",
                            lineNumber: 67,
                            columnNumber: 21
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__["Form"].Select, {
                            defaultValue: type,
                            onChange: (e)=>setType(e.target.value),
                            children: TYPE_OPTIONS.map((type)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                    value: type,
                                    children: (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$NotificationUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getNotificationTypeAsString"])(type)
                                }, type, false, {
                                    fileName: "[project]/components/NotificationTargets/NotificationTargetForm.tsx",
                                    lineNumber: 70,
                                    columnNumber: 29
                                }, this))
                        }, void 0, false, {
                            fileName: "[project]/components/NotificationTargets/NotificationTargetForm.tsx",
                            lineNumber: 68,
                            columnNumber: 21
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/components/NotificationTargets/NotificationTargetForm.tsx",
                    lineNumber: 66,
                    columnNumber: 17
                }, this),
                type === 'WEBHOOK' || type === 'DiscordWebhook' || type === 3 || type === 1 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__["Form"].Group, {
                    className: "mb-3",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__["Form"].Label, {
                            children: "Target"
                        }, void 0, false, {
                            fileName: "[project]/components/NotificationTargets/NotificationTargetForm.tsx",
                            lineNumber: 78,
                            columnNumber: 25
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__["Form"].Control, {
                            defaultValue: target || undefined,
                            type: "text",
                            onChange: (e)=>setTarget(e.target.value),
                            placeholder: type === 'DiscordWebhook' ? 'Discord Webhook Url (https://discord.com/api/...)' : 'Webhook Url',
                            isInvalid: (type === 'DiscordWebhook' && target && !target?.startsWith('https://discord.com/api/')) === true
                        }, void 0, false, {
                            fileName: "[project]/components/NotificationTargets/NotificationTargetForm.tsx",
                            lineNumber: 79,
                            columnNumber: 25
                        }, this),
                        type === 'DiscordWebhook' && target && !target?.startsWith('https://discord.com/api/') ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                style: {
                                    color: 'red'
                                },
                                children: 'The Discord Webhook URL has to start with "https://discord.com/api/..."'
                            }, void 0, false, {
                                fileName: "[project]/components/NotificationTargets/NotificationTargetForm.tsx",
                                lineNumber: 88,
                                columnNumber: 33
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/components/NotificationTargets/NotificationTargetForm.tsx",
                            lineNumber: 87,
                            columnNumber: 29
                        }, this) : null
                    ]
                }, void 0, true, {
                    fileName: "[project]/components/NotificationTargets/NotificationTargetForm.tsx",
                    lineNumber: 77,
                    columnNumber: 21
                }, this) : null,
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__["Form"].Group, {
                    className: "mb-3",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__["Form"].Label, {
                            children: "When"
                        }, void 0, false, {
                            fileName: "[project]/components/NotificationTargets/NotificationTargetForm.tsx",
                            lineNumber: 94,
                            columnNumber: 21
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__["Form"].Select, {
                            defaultValue: when,
                            onChange: (e)=>setWhen(e.target.value),
                            children: WHEN_OPITIONS.map((when)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                    value: when,
                                    children: (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$NotificationUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getNotficationWhenEnumAsString"])(when)
                                }, when, false, {
                                    fileName: "[project]/components/NotificationTargets/NotificationTargetForm.tsx",
                                    lineNumber: 97,
                                    columnNumber: 29
                                }, this))
                        }, void 0, false, {
                            fileName: "[project]/components/NotificationTargets/NotificationTargetForm.tsx",
                            lineNumber: 95,
                            columnNumber: 21
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/components/NotificationTargets/NotificationTargetForm.tsx",
                    lineNumber: 93,
                    columnNumber: 17
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Button$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__["Button"], {
                    variant: "primary",
                    onClick: addNotificationTarget,
                    disabled: disabled,
                    children: props.type === 'CREATE' ? 'Add' : 'Update'
                }, void 0, false, {
                    fileName: "[project]/components/NotificationTargets/NotificationTargetForm.tsx",
                    lineNumber: 103,
                    columnNumber: 17
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/components/NotificationTargets/NotificationTargetForm.tsx",
            lineNumber: 61,
            columnNumber: 13
        }, this)
    }, void 0, false);
}
_s(NotificationTargetForm, "FLcZyG3eU0qqNG0QLdyTN+QUzqg=");
_c = NotificationTargetForm;
const __TURBOPACK__default__export__ = NotificationTargetForm;
var _c;
__turbopack_context__.k.register(_c, "NotificationTargetForm");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/SubscribeButton/SubscribeBazaarItemContent/SubscribeBazaarItemContent.module.css [app-client] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "checkBox": "SubscribeBazaarItemContent-module__FucSta__checkBox",
});
}}),
"[project]/components/SubscribeButton/SubscribeBazaarItemContent/SubscribeBazaarItemContent.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$FormControl$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__FormControl$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/FormControl.js [app-client] (ecmascript) <export default as FormControl>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$InputGroup$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__InputGroup$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/InputGroup.js [app-client] (ecmascript) <export default as InputGroup>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/Form.js [app-client] (ecmascript) <export default as Form>");
var __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/api/ApiTypes.d.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$SubscribeButton$2f$SubscribeBazaarItemContent$2f$SubscribeBazaarItemContent$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/components/SubscribeButton/SubscribeBazaarItemContent/SubscribeBazaarItemContent.module.css [app-client] (css module)");
'use client';
;
;
;
;
;
function SubscribeBazaarItemContent(props) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "item-forms",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$InputGroup$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__InputGroup$3e$__["InputGroup"], {
                    className: "price-input",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$InputGroup$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__InputGroup$3e$__["InputGroup"].Text, {
                            id: "inputGroup-sizing-sm",
                            children: "Item price"
                        }, void 0, false, {
                            fileName: "[project]/components/SubscribeButton/SubscribeBazaarItemContent/SubscribeBazaarItemContent.tsx",
                            lineNumber: 20,
                            columnNumber: 21
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$FormControl$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__FormControl$3e$__["FormControl"], {
                            "aria-label": "Small",
                            "aria-describedby": "inputGroup-sizing-sm",
                            type: "number",
                            defaultValue: props.prefill?.price,
                            onChange: (e)=>props.onPriceChange(e.target.value)
                        }, void 0, false, {
                            fileName: "[project]/components/SubscribeButton/SubscribeBazaarItemContent/SubscribeBazaarItemContent.tsx",
                            lineNumber: 21,
                            columnNumber: 21
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/components/SubscribeButton/SubscribeBazaarItemContent/SubscribeBazaarItemContent.tsx",
                    lineNumber: 19,
                    columnNumber: 17
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("hr", {}, void 0, false, {
                    fileName: "[project]/components/SubscribeButton/SubscribeBazaarItemContent/SubscribeBazaarItemContent.tsx",
                    lineNumber: 29,
                    columnNumber: 17
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                    style: {
                        marginBottom: '20px'
                    },
                    children: "Notify me..."
                }, void 0, false, {
                    fileName: "[project]/components/SubscribeButton/SubscribeBazaarItemContent/SubscribeBazaarItemContent.tsx",
                    lineNumber: 30,
                    columnNumber: 17
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__["Form"].Group, {
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__["Form"].Label, {
                            htmlFor: "priceAboveCheckbox",
                            children: "if the price is above the selected value"
                        }, void 0, false, {
                            fileName: "[project]/components/SubscribeButton/SubscribeBazaarItemContent/SubscribeBazaarItemContent.tsx",
                            lineNumber: 32,
                            columnNumber: 21
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__["Form"].Check, {
                            type: "radio",
                            id: "priceAboveCheckbox",
                            name: "priceState",
                            defaultChecked: props.prefill && props.prefill.types.includes(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SubscriptionType"][__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SubscriptionType"].PRICE_HIGHER_THAN]),
                            onChange: (e)=>props.onIsPriceAboveChange(true),
                            className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$SubscribeButton$2f$SubscribeBazaarItemContent$2f$SubscribeBazaarItemContent$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].checkBox
                        }, void 0, false, {
                            fileName: "[project]/components/SubscribeButton/SubscribeBazaarItemContent/SubscribeBazaarItemContent.tsx",
                            lineNumber: 33,
                            columnNumber: 21
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/components/SubscribeButton/SubscribeBazaarItemContent/SubscribeBazaarItemContent.tsx",
                    lineNumber: 31,
                    columnNumber: 17
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__["Form"].Group, {
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__["Form"].Label, {
                            htmlFor: "priceBelowCheckbox",
                            children: "if the price is below the selected value"
                        }, void 0, false, {
                            fileName: "[project]/components/SubscribeButton/SubscribeBazaarItemContent/SubscribeBazaarItemContent.tsx",
                            lineNumber: 45,
                            columnNumber: 21
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__["Form"].Check, {
                            type: "radio",
                            id: "priceBelowCheckbox",
                            name: "priceState",
                            defaultChecked: props.prefill && props.prefill.types.includes(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SubscriptionType"][__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SubscriptionType"].PRICE_LOWER_THAN]),
                            onChange: (e)=>props.onIsPriceAboveChange(false),
                            className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$SubscribeButton$2f$SubscribeBazaarItemContent$2f$SubscribeBazaarItemContent$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].checkBox
                        }, void 0, false, {
                            fileName: "[project]/components/SubscribeButton/SubscribeBazaarItemContent/SubscribeBazaarItemContent.tsx",
                            lineNumber: 46,
                            columnNumber: 21
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/components/SubscribeButton/SubscribeBazaarItemContent/SubscribeBazaarItemContent.tsx",
                    lineNumber: 44,
                    columnNumber: 17
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__["Form"].Group, {
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__["Form"].Label, {
                            htmlFor: "useSellPriceCheckbox",
                            children: "if the sell price should be used"
                        }, void 0, false, {
                            fileName: "[project]/components/SubscribeButton/SubscribeBazaarItemContent/SubscribeBazaarItemContent.tsx",
                            lineNumber: 58,
                            columnNumber: 21
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__["Form"].Check, {
                            type: "checkbox",
                            id: "useSellPriceCheckbox",
                            defaultChecked: props.prefill && props.prefill.types.includes(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SubscriptionType"][__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SubscriptionType"].USE_SELL_NOT_BUY]),
                            onChange: (e)=>props.onUseSellPriceChange(e.target.checked),
                            className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$SubscribeButton$2f$SubscribeBazaarItemContent$2f$SubscribeBazaarItemContent$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].checkBox
                        }, void 0, false, {
                            fileName: "[project]/components/SubscribeButton/SubscribeBazaarItemContent/SubscribeBazaarItemContent.tsx",
                            lineNumber: 59,
                            columnNumber: 21
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/components/SubscribeButton/SubscribeBazaarItemContent/SubscribeBazaarItemContent.tsx",
                    lineNumber: 57,
                    columnNumber: 17
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/components/SubscribeButton/SubscribeBazaarItemContent/SubscribeBazaarItemContent.tsx",
            lineNumber: 18,
            columnNumber: 13
        }, this)
    }, void 0, false);
}
_c = SubscribeBazaarItemContent;
const __TURBOPACK__default__export__ = SubscribeBazaarItemContent;
var _c;
__turbopack_context__.k.register(_c, "SubscribeBazaarItemContent");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/SubscribeButton/SubscribeButton.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Button$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/Button.js [app-client] (ecmascript) <export default as Button>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Modal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Modal$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/Modal.js [app-client] (ecmascript) <export default as Modal>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$jonkoops$2f$matomo$2d$tracker$2d$react$2f$es$2f$useMatomo$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useMatomo$3e$__ = __turbopack_context__.i("[project]/node_modules/@jonkoops/matomo-tracker-react/es/useMatomo.js [app-client] (ecmascript) <export default as useMatomo>");
var __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/api/ApiHelper.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/api/ApiTypes.d.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$GoogleSignIn$2f$GoogleSignIn$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/GoogleSignIn/GoogleSignIn.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-toastify/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$NotificationsOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/icons-material/esm/NotificationsOutlined.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$SubscribeButton$2f$SubscribeButton$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/components/SubscribeButton/SubscribeButton.module.css [app-client] (css module)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$SubscribeButton$2f$SubscribeItemContent$2f$SubscribeItemContent$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/SubscribeButton/SubscribeItemContent/SubscribeItemContent.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$LoadingUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/LoadingUtils.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$SubscribeButton$2f$SubscribePlayerContent$2f$SubscribePlayerContent$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/SubscribeButton/SubscribePlayerContent/SubscribePlayerContent.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$SubscribeButton$2f$SubscribeAuctionContent$2f$SubscribeAuctionContent$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/SubscribeButton/SubscribeAuctionContent/SubscribeAuctionContent.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Hooks$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/Hooks.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Edit$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/icons-material/esm/Edit.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2d$typeahead$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap-typeahead/es/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2d$typeahead$2f$es$2f$components$2f$Typeahead$2f$Typeahead$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Typeahead$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap-typeahead/es/components/Typeahead/Typeahead.js [app-client] (ecmascript) <export default as Typeahead>");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$NotificationTargets$2f$NotificationTargetForm$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/NotificationTargets/NotificationTargetForm.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$SubscribeButton$2f$SubscribeBazaarItemContent$2f$SubscribeBazaarItemContent$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/SubscribeButton/SubscribeBazaarItemContent/SubscribeBazaarItemContent.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const MAX_FILTERS = 5;
function SubscribeButton(props) {
    _s();
    let { trackEvent } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$jonkoops$2f$matomo$2d$tracker$2d$react$2f$es$2f$useMatomo$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useMatomo$3e$__["useMatomo"])();
    let router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    let [showDialog, setShowDialog] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    let [price, setPrice] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(props.prefill?.listener?.price?.toString() || '0');
    let [isPriceAbove, setIsPriceAbove] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(props.prefill?.listener?.types?.includes(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SubscriptionType"].PRICE_HIGHER_THAN) ?? false);
    let [onlyInstantBuy, setOnlyInstantBuy] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(props.prefill?.listener?.types?.includes(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SubscriptionType"].BIN) ?? false);
    let [gotOutbid, setGotOutbid] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(props.prefill?.listener?.types?.includes(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SubscriptionType"].OUTBID) ?? false);
    let [isSold, setIsSold] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(props.prefill?.listener?.types?.includes(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SubscriptionType"].SOLD) ?? false);
    let [isPlayerAuctionCreation, setIsPlayerAuctionCreation] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(props.prefill?.listener?.types?.includes(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SubscriptionType"].PLAYER_CREATES_AUCTION) ?? false);
    let [hasPlayerBoughtAnyAuction, setHasPlayerBoughtAnyAuction] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(props.prefill?.listener?.types?.includes(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SubscriptionType"].BOUGHT_ANY_AUCTION) ?? false);
    let [isUseBazaarSellNotBuy, setIsUseBazaarSellNotBuy] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(props.prefill?.listener?.types?.includes(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SubscriptionType"].USE_SELL_NOT_BUY) ?? false);
    let [isLoggedIn, setIsLoggedIn] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    let [itemFilter, setItemFilter] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(props.prefill?.listener?.filter || undefined);
    let [isItemFilterValid, setIsItemFilterValid] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    let wasAlreadyLoggedIn = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Hooks$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useWasAlreadyLoggedIn"])();
    let [notificationTargets, setNotificationTargets] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    let [selectedNotificationTargets, setSelectedNotificationTargets] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    let [isLoadingNotificationTargets, setIsLoadingNotificationTargets] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    let [showCreateTargetDialog, setShowCreateTargetDialog] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    async function onSubscribe() {
        trackEvent({
            action: 'subscribed',
            category: 'subscriptions'
        });
        setShowDialog(false);
        // Set price to 0 per default for item subscriptions
        // This happens if a user only selects a filter and leaves the price field empty
        if (props.type === 'item' && !price) {
            price = '0';
        }
        if (props.type === 'bazaar' && !price) {
            price = '0';
        }
        if (props.type === 'item' && !itemFilter) {
            itemFilter = {};
        }
        __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].subscribe(props.topic, getSubscriptionTypes(), selectedNotificationTargets, price ? parseInt(price) : undefined, itemFilter).then(()=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success(props.successMessage || 'Notifier successfully created!', {
                onClick: ()=>{
                    router.push('/subscriptions');
                }
            });
            if (props.onAfterSubscribe) {
                props.onAfterSubscribe();
            }
        }).catch((error)=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(error.message, {
                onClick: ()=>{
                    router.push('/subscriptions');
                }
            });
        });
    }
    function getSubscriptionTypes() {
        let types = [];
        if (props.type === 'item') {
            if (isPriceAbove) {
                types.push(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SubscriptionType"].PRICE_HIGHER_THAN);
            }
            if (!isPriceAbove) {
                types.push(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SubscriptionType"].PRICE_LOWER_THAN);
            }
            if (onlyInstantBuy) {
                types.push(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SubscriptionType"].BIN);
            }
        }
        if (props.type === 'player') {
            if (gotOutbid) {
                types.push(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SubscriptionType"].OUTBID);
            }
            if (isSold) {
                types.push(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SubscriptionType"].SOLD);
            }
            if (isPlayerAuctionCreation) {
                types.push(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SubscriptionType"].PLAYER_CREATES_AUCTION);
            }
            if (hasPlayerBoughtAnyAuction) {
                types.push(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SubscriptionType"].BOUGHT_ANY_AUCTION);
            }
        }
        if (props.type === 'auction') {
            types.push(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SubscriptionType"].AUCTION);
        }
        if (props.type === 'bazaar') {
            if (isPriceAbove) {
                types.push(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SubscriptionType"].PRICE_HIGHER_THAN);
            }
            if (!isPriceAbove) {
                types.push(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SubscriptionType"].PRICE_LOWER_THAN);
            }
            if (isUseBazaarSellNotBuy) {
                types.push(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SubscriptionType"].USE_SELL_NOT_BUY);
            }
        }
        return types;
    }
    function onLogin() {
        setIsLoggedIn(true);
        setIsLoadingNotificationTargets(true);
        __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getNotificationTargets().then((targets)=>{
            if (props.prefill?.targetNames) {
                setSelectedNotificationTargets(targets.filter((target)=>target.name ? props.prefill?.targetNames.includes(target.name) : false));
            }
            setNotificationTargets(targets);
            setIsLoadingNotificationTargets(false);
        });
    }
    function isNotifyDisabled() {
        if (itemFilter && Object.keys(itemFilter).length > MAX_FILTERS) {
            return true;
        }
        if (props.type === 'item') {
            return itemFilter && Object.keys(itemFilter).length > 0 ? false : price === undefined || price === '';
        }
        if (props.type === 'player') {
            return !gotOutbid && !isSold && !isPlayerAuctionCreation;
        }
    }
    function closeDialog() {
        trackEvent({
            action: 'subscription dialog closed',
            category: 'subscriptions'
        });
        setShowDialog(false);
    }
    function openDialog() {
        trackEvent({
            action: 'subscription dialog opened',
            category: 'subscriptions'
        });
        setShowDialog(true);
    }
    let dialog2 = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Modal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Modal$3e$__["Modal"], {
        show: showCreateTargetDialog,
        onHide: ()=>{
            setShowCreateTargetDialog(false);
        },
        className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$SubscribeButton$2f$SubscribeButton$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].subscribeDialog,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Modal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Modal$3e$__["Modal"].Header, {
                closeButton: true,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Modal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Modal$3e$__["Modal"].Title, {
                    children: props.popupTitle || 'Create a Notification Target'
                }, void 0, false, {
                    fileName: "[project]/components/SubscribeButton/SubscribeButton.tsx",
                    lineNumber: 187,
                    columnNumber: 17
                }, this)
            }, void 0, false, {
                fileName: "[project]/components/SubscribeButton/SubscribeButton.tsx",
                lineNumber: 186,
                columnNumber: 13
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Modal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Modal$3e$__["Modal"].Body, {
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$NotificationTargets$2f$NotificationTargetForm$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    type: "CREATE",
                    onSubmit: (target)=>{
                        setSelectedNotificationTargets([
                            ...selectedNotificationTargets,
                            target
                        ]);
                    }
                }, void 0, false, {
                    fileName: "[project]/components/SubscribeButton/SubscribeButton.tsx",
                    lineNumber: 190,
                    columnNumber: 17
                }, this)
            }, void 0, false, {
                fileName: "[project]/components/SubscribeButton/SubscribeButton.tsx",
                lineNumber: 189,
                columnNumber: 13
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/components/SubscribeButton/SubscribeButton.tsx",
        lineNumber: 179,
        columnNumber: 9
    }, this);
    let dialog = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Modal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Modal$3e$__["Modal"], {
        show: showDialog,
        onHide: closeDialog,
        className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$SubscribeButton$2f$SubscribeButton$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].subscribeDialog,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Modal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Modal$3e$__["Modal"].Header, {
                closeButton: true,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Modal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Modal$3e$__["Modal"].Title, {
                    children: props.popupTitle || 'Create a Notifier'
                }, void 0, false, {
                    fileName: "[project]/components/SubscribeButton/SubscribeButton.tsx",
                    lineNumber: 203,
                    columnNumber: 17
                }, this)
            }, void 0, false, {
                fileName: "[project]/components/SubscribeButton/SubscribeButton.tsx",
                lineNumber: 202,
                columnNumber: 13
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Modal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Modal$3e$__["Modal"].Body, {
                children: [
                    isLoggedIn ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            props.type === 'item' ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$SubscribeButton$2f$SubscribeItemContent$2f$SubscribeItemContent$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                itemTag: props.topic,
                                onFilterChange: (filter)=>{
                                    setItemFilter({
                                        ...filter
                                    });
                                },
                                onIsPriceAboveChange: setIsPriceAbove,
                                onOnlyInstantBuyChange: setOnlyInstantBuy,
                                onPriceChange: setPrice,
                                prefill: props.prefill?.listener,
                                onIsFilterValidChange: setIsItemFilterValid
                            }, void 0, false, {
                                fileName: "[project]/components/SubscribeButton/SubscribeButton.tsx",
                                lineNumber: 209,
                                columnNumber: 29
                            }, this) : null,
                            props.type === 'bazaar' ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$SubscribeButton$2f$SubscribeBazaarItemContent$2f$SubscribeBazaarItemContent$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                itemTag: props.topic,
                                onPriceChange: setPrice,
                                onIsPriceAboveChange: setIsPriceAbove,
                                onUseSellPriceChange: setIsUseBazaarSellNotBuy,
                                prefill: props.prefill?.listener
                            }, void 0, false, {
                                fileName: "[project]/components/SubscribeButton/SubscribeButton.tsx",
                                lineNumber: 222,
                                columnNumber: 29
                            }, this) : null,
                            props.type === 'player' ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$SubscribeButton$2f$SubscribePlayerContent$2f$SubscribePlayerContent$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                onGotOutbidChange: setGotOutbid,
                                onIsSoldChange: setIsSold,
                                onIsPlayerAuctionCreation: setIsPlayerAuctionCreation,
                                onBoughtAnyAuctionChange: setHasPlayerBoughtAnyAuction,
                                prefill: props.prefill?.listener
                            }, void 0, false, {
                                fileName: "[project]/components/SubscribeButton/SubscribeButton.tsx",
                                lineNumber: 231,
                                columnNumber: 29
                            }, this) : null,
                            props.type === 'auction' ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$SubscribeButton$2f$SubscribeAuctionContent$2f$SubscribeAuctionContent$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                fileName: "[project]/components/SubscribeButton/SubscribeButton.tsx",
                                lineNumber: 239,
                                columnNumber: 53
                            }, this) : null,
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                htmlFor: "notificationTargetsTypeahead",
                                children: "Targets: "
                            }, void 0, false, {
                                fileName: "[project]/components/SubscribeButton/SubscribeButton.tsx",
                                lineNumber: 240,
                                columnNumber: 25
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                style: {
                                    display: 'flex',
                                    gap: 10
                                },
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2d$typeahead$2f$es$2f$components$2f$Typeahead$2f$Typeahead$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Typeahead$3e$__["Typeahead"], {
                                        id: "notificationTargetsTypeahead",
                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$SubscribeButton$2f$SubscribeButton$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].multiSearch,
                                        isLoading: isLoadingNotificationTargets,
                                        labelKey: "name",
                                        style: {
                                            flex: 1
                                        },
                                        options: notificationTargets,
                                        placeholder: 'Select targets...',
                                        selected: selectedNotificationTargets,
                                        onChange: (selected)=>{
                                            setSelectedNotificationTargets(selected);
                                        },
                                        multiple: true
                                    }, void 0, false, {
                                        fileName: "[project]/components/SubscribeButton/SubscribeButton.tsx",
                                        lineNumber: 242,
                                        columnNumber: 29
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Button$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__["Button"], {
                                        onClick: ()=>{
                                            setShowCreateTargetDialog(true);
                                        },
                                        children: "Create new target"
                                    }, void 0, false, {
                                        fileName: "[project]/components/SubscribeButton/SubscribeButton.tsx",
                                        lineNumber: 256,
                                        columnNumber: 29
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/components/SubscribeButton/SubscribeButton.tsx",
                                lineNumber: 241,
                                columnNumber: 25
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Button$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__["Button"], {
                                onClick: onSubscribe,
                                disabled: isNotifyDisabled() || !isItemFilterValid,
                                className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$SubscribeButton$2f$SubscribeButton$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].notifyButton,
                                children: props.popupButtonText || 'Notify me'
                            }, void 0, false, {
                                fileName: "[project]/components/SubscribeButton/SubscribeButton.tsx",
                                lineNumber: 264,
                                columnNumber: 25
                            }, this),
                            itemFilter && Object.keys(itemFilter).length > MAX_FILTERS ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                style: {
                                    color: 'red'
                                },
                                children: "You currently can't use more than 5 filters for Notifiers"
                            }, void 0, false, {
                                fileName: "[project]/components/SubscribeButton/SubscribeButton.tsx",
                                lineNumber: 268,
                                columnNumber: 29
                            }, this) : null
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/SubscribeButton/SubscribeButton.tsx",
                        lineNumber: 207,
                        columnNumber: 21
                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: "To use notifiers, please login with Google: "
                    }, void 0, false, {
                        fileName: "[project]/components/SubscribeButton/SubscribeButton.tsx",
                        lineNumber: 272,
                        columnNumber: 21
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$GoogleSignIn$2f$GoogleSignIn$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                        onAfterLogin: onLogin
                    }, void 0, false, {
                        fileName: "[project]/components/SubscribeButton/SubscribeButton.tsx",
                        lineNumber: 274,
                        columnNumber: 17
                    }, this),
                    wasAlreadyLoggedIn && !isLoggedIn ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$LoadingUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getLoadingElement"])() : ''
                ]
            }, void 0, true, {
                fileName: "[project]/components/SubscribeButton/SubscribeButton.tsx",
                lineNumber: 205,
                columnNumber: 13
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/components/SubscribeButton/SubscribeButton.tsx",
        lineNumber: 201,
        columnNumber: 9
    }, this);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$SubscribeButton$2f$SubscribeButton$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].subscribeButton,
        children: [
            dialog,
            dialog2,
            props.isEditButton ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                onClick: openDialog,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Edit$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                    fileName: "[project]/components/SubscribeButton/SubscribeButton.tsx",
                    lineNumber: 286,
                    columnNumber: 21
                }, this)
            }, void 0, false, {
                fileName: "[project]/components/SubscribeButton/SubscribeButton.tsx",
                lineNumber: 285,
                columnNumber: 17
            }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Button$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__["Button"], {
                style: {
                    width: 'max-content'
                },
                onClick: openDialog,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$NotificationsOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                        fileName: "[project]/components/SubscribeButton/SubscribeButton.tsx",
                        lineNumber: 290,
                        columnNumber: 21
                    }, this),
                    props.buttonContent || ' Notify'
                ]
            }, void 0, true, {
                fileName: "[project]/components/SubscribeButton/SubscribeButton.tsx",
                lineNumber: 289,
                columnNumber: 17
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/components/SubscribeButton/SubscribeButton.tsx",
        lineNumber: 281,
        columnNumber: 9
    }, this);
}
_s(SubscribeButton, "Nq1ozou3thcEbT9kUWyOsudE1F8=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$jonkoops$2f$matomo$2d$tracker$2d$react$2f$es$2f$useMatomo$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useMatomo$3e$__["useMatomo"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"],
        __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Hooks$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useWasAlreadyLoggedIn"]
    ];
});
_c = SubscribeButton;
const __TURBOPACK__default__export__ = SubscribeButton;
var _c;
__turbopack_context__.k.register(_c, "SubscribeButton");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/SubscriptionList/SubscriptionList.module.css [app-client] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "multiSearch": "SubscriptionList-module__SzugLq__multiSearch",
  "subscriptionList": "SubscriptionList-module__SzugLq__subscriptionList",
});
}}),
"[project]/components/NotificationTargets/NotificationTargets.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Button$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/Button.js [app-client] (ecmascript) <export default as Button>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Table$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Table$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/Table.js [app-client] (ecmascript) <export default as Table>");
var __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/api/ApiHelper.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$NotificationTargets$2f$NotificationTargetForm$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/NotificationTargets/NotificationTargetForm.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Delete$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/icons-material/esm/Delete.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Edit$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/icons-material/esm/Edit.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$LoadingUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/LoadingUtils.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$NotificationUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/NotificationUtils.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
;
;
function NotificationTargets() {
    _s();
    let [notificationTargets, setNotificationTargets] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    let [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    let [showAddNotificationTarget, setShowAddNotificationTarget] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    let [notificationTargetToUpdate, setNotificationTargetToUpdate] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    let [notificationTargetFormType, setNotificationTargetType] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('CREATE');
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "NotificationTargets.useEffect": ()=>{
            loadNotificatinoTargets();
        }
    }["NotificationTargets.useEffect"], []);
    function loadNotificatinoTargets() {
        setIsLoading(true);
        __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getNotificationTargets().then((targets)=>{
            setNotificationTargets(targets);
            setIsLoading(false);
        });
    }
    function deleteNotificationTarget(target) {
        __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].deleteNotificationTarget(target).then(()=>{
            setNotificationTargets(notificationTargets.filter((t)=>t.name !== target.name));
        });
    }
    function sendTestNotification(target) {
        __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].sendTestNotification(target);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                style: {
                    display: 'flex',
                    justifyContent: 'flex-end'
                },
                children: !showAddNotificationTarget ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Button$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__["Button"], {
                    onClick: ()=>setShowAddNotificationTarget(true),
                    children: "Add Notification Target"
                }, void 0, false, {
                    fileName: "[project]/components/NotificationTargets/NotificationTargets.tsx",
                    lineNumber: 45,
                    columnNumber: 21
                }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Button$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__["Button"], {
                    onClick: ()=>{
                        setShowAddNotificationTarget(false);
                    },
                    children: "Hide Form"
                }, void 0, false, {
                    fileName: "[project]/components/NotificationTargets/NotificationTargets.tsx",
                    lineNumber: 47,
                    columnNumber: 21
                }, this)
            }, void 0, false, {
                fileName: "[project]/components/NotificationTargets/NotificationTargets.tsx",
                lineNumber: 43,
                columnNumber: 13
            }, this),
            showAddNotificationTarget && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$NotificationTargets$2f$NotificationTargetForm$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                        type: notificationTargetFormType,
                        defaultNotificationTarget: notificationTargetFormType === 'UPDATE' ? notificationTargetToUpdate : null,
                        onSubmit: ()=>{
                            setShowAddNotificationTarget(false);
                            loadNotificatinoTargets();
                        }
                    }, void 0, false, {
                        fileName: "[project]/components/NotificationTargets/NotificationTargets.tsx",
                        lineNumber: 58,
                        columnNumber: 21
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("hr", {}, void 0, false, {
                        fileName: "[project]/components/NotificationTargets/NotificationTargets.tsx",
                        lineNumber: 66,
                        columnNumber: 21
                    }, this)
                ]
            }, void 0, true),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Table$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Table$3e$__["Table"], {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("thead", {
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                    children: "Name"
                                }, void 0, false, {
                                    fileName: "[project]/components/NotificationTargets/NotificationTargets.tsx",
                                    lineNumber: 72,
                                    columnNumber: 25
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                    children: "Target"
                                }, void 0, false, {
                                    fileName: "[project]/components/NotificationTargets/NotificationTargets.tsx",
                                    lineNumber: 73,
                                    columnNumber: 25
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                    children: "Type"
                                }, void 0, false, {
                                    fileName: "[project]/components/NotificationTargets/NotificationTargets.tsx",
                                    lineNumber: 74,
                                    columnNumber: 25
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                    children: "When"
                                }, void 0, false, {
                                    fileName: "[project]/components/NotificationTargets/NotificationTargets.tsx",
                                    lineNumber: 75,
                                    columnNumber: 25
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                    children: "Use Count"
                                }, void 0, false, {
                                    fileName: "[project]/components/NotificationTargets/NotificationTargets.tsx",
                                    lineNumber: 76,
                                    columnNumber: 25
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {}, void 0, false, {
                                    fileName: "[project]/components/NotificationTargets/NotificationTargets.tsx",
                                    lineNumber: 77,
                                    columnNumber: 25
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {}, void 0, false, {
                                    fileName: "[project]/components/NotificationTargets/NotificationTargets.tsx",
                                    lineNumber: 78,
                                    columnNumber: 25
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {}, void 0, false, {
                                    fileName: "[project]/components/NotificationTargets/NotificationTargets.tsx",
                                    lineNumber: 79,
                                    columnNumber: 25
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/NotificationTargets/NotificationTargets.tsx",
                            lineNumber: 71,
                            columnNumber: 21
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/components/NotificationTargets/NotificationTargets.tsx",
                        lineNumber: 70,
                        columnNumber: 17
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tbody", {
                        children: isLoading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                colSpan: 99,
                                children: (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$LoadingUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getLoadingElement"])()
                            }, void 0, false, {
                                fileName: "[project]/components/NotificationTargets/NotificationTargets.tsx",
                                lineNumber: 85,
                                columnNumber: 29
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/components/NotificationTargets/NotificationTargets.tsx",
                            lineNumber: 84,
                            columnNumber: 25
                        }, this) : notificationTargets.map((target)=>{
                            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                        className: "ellipse",
                                        style: {
                                            maxWidth: '250px'
                                        },
                                        title: target.target || '',
                                        children: target.name
                                    }, void 0, false, {
                                        fileName: "[project]/components/NotificationTargets/NotificationTargets.tsx",
                                        lineNumber: 91,
                                        columnNumber: 37
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                        className: "ellipse",
                                        style: {
                                            maxWidth: '150px'
                                        },
                                        title: target.target || '',
                                        children: target.target
                                    }, void 0, false, {
                                        fileName: "[project]/components/NotificationTargets/NotificationTargets.tsx",
                                        lineNumber: 94,
                                        columnNumber: 37
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                        children: (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$NotificationUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getNotificationTypeAsString"])(target.type)
                                    }, void 0, false, {
                                        fileName: "[project]/components/NotificationTargets/NotificationTargets.tsx",
                                        lineNumber: 97,
                                        columnNumber: 37
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                        children: (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$NotificationUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getNotficationWhenEnumAsString"])(target.when)
                                    }, void 0, false, {
                                        fileName: "[project]/components/NotificationTargets/NotificationTargets.tsx",
                                        lineNumber: 98,
                                        columnNumber: 37
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                        children: target.useCount || 0
                                    }, void 0, false, {
                                        fileName: "[project]/components/NotificationTargets/NotificationTargets.tsx",
                                        lineNumber: 99,
                                        columnNumber: 37
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Button$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__["Button"], {
                                            variant: "primary",
                                            onClick: ()=>{
                                                sendTestNotification(target);
                                            },
                                            children: "Test"
                                        }, void 0, false, {
                                            fileName: "[project]/components/NotificationTargets/NotificationTargets.tsx",
                                            lineNumber: 101,
                                            columnNumber: 41
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/components/NotificationTargets/NotificationTargets.tsx",
                                        lineNumber: 100,
                                        columnNumber: 37
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Button$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__["Button"], {
                                            variant: "primary",
                                            onClick: ()=>{
                                                setShowAddNotificationTarget(true);
                                                setNotificationTargetToUpdate(target);
                                                setNotificationTargetType('UPDATE');
                                            },
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Edit$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                                fileName: "[project]/components/NotificationTargets/NotificationTargets.tsx",
                                                lineNumber: 119,
                                                columnNumber: 45
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/components/NotificationTargets/NotificationTargets.tsx",
                                            lineNumber: 111,
                                            columnNumber: 41
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/components/NotificationTargets/NotificationTargets.tsx",
                                        lineNumber: 110,
                                        columnNumber: 37
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Button$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__["Button"], {
                                            variant: "danger",
                                            onClick: ()=>{
                                                deleteNotificationTarget(target);
                                            },
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Delete$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                                fileName: "[project]/components/NotificationTargets/NotificationTargets.tsx",
                                                lineNumber: 129,
                                                columnNumber: 45
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/components/NotificationTargets/NotificationTargets.tsx",
                                            lineNumber: 123,
                                            columnNumber: 41
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/components/NotificationTargets/NotificationTargets.tsx",
                                        lineNumber: 122,
                                        columnNumber: 37
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/components/NotificationTargets/NotificationTargets.tsx",
                                lineNumber: 90,
                                columnNumber: 33
                            }, this);
                        })
                    }, void 0, false, {
                        fileName: "[project]/components/NotificationTargets/NotificationTargets.tsx",
                        lineNumber: 82,
                        columnNumber: 17
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/components/NotificationTargets/NotificationTargets.tsx",
                lineNumber: 69,
                columnNumber: 13
            }, this)
        ]
    }, void 0, true);
}
_s(NotificationTargets, "EllHVGpb8E39IG7U+PtMoBIpXbc=");
_c = NotificationTargets;
const __TURBOPACK__default__export__ = NotificationTargets;
var _c;
__turbopack_context__.k.register(_c, "NotificationTargets");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/SubscriptionList/SubscriptionList.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Delete$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/icons-material/esm/Delete.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Undo$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/icons-material/esm/Undo.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Badge$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Badge$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/Badge.js [app-client] (ecmascript) <export default as Badge>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Button$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/Button.js [app-client] (ecmascript) <export default as Button>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$ListGroup$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ListGroup$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/ListGroup.js [app-client] (ecmascript) <export default as ListGroup>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Modal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Modal$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/Modal.js [app-client] (ecmascript) <export default as Modal>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-toastify/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/api/ApiHelper.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/api/ApiTypes.d.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Formatter$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/Formatter.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Hooks$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/Hooks.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$LoadingUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/LoadingUtils.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$GoogleSignIn$2f$GoogleSignIn$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/GoogleSignIn/GoogleSignIn.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ItemFilter$2f$ItemFilterPropertiesDisplay$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ItemFilter/ItemFilterPropertiesDisplay.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Number$2f$Number$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/Number/Number.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$SubscribeButton$2f$SubscribeButton$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/SubscribeButton/SubscribeButton.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$SubscriptionList$2f$SubscriptionList$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/components/SubscriptionList/SubscriptionList.module.css [app-client] (css module)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$NotificationTargets$2f$NotificationTargets$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/NotificationTargets/NotificationTargets.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2d$typeahead$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap-typeahead/es/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2d$typeahead$2f$es$2f$components$2f$Typeahead$2f$Typeahead$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Typeahead$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap-typeahead/es/components/Typeahead/Typeahead.js [app-client] (ecmascript) <export default as Typeahead>");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
let mounted = true;
function SubscriptionList() {
    _s();
    let [listener, setListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    let [subscriptions, setSubscriptions] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    let [isLoggedIn, setIsLoggedIn] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    let [showDeleteAllSubscriptionDialog, setShowDeleteAllSubscriptionDialog] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    let [showNotificationTargets, setShowNotificationTargets] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    let [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    let wasAlreadyLoggedIn = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Hooks$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useWasAlreadyLoggedIn"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "SubscriptionList.useEffect": ()=>{
            mounted = true;
        }
    }["SubscriptionList.useEffect"]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "SubscriptionList.useEffect": ()=>{
            return ({
                "SubscriptionList.useEffect": ()=>{
                    mounted = false;
                }
            })["SubscriptionList.useEffect"];
        }
    }["SubscriptionList.useEffect"], []);
    let subscriptionsToListenerMap = getListenerToTargetsMap();
    function getListenerToTargetsMap() {
        if (!listener) {
            return {};
        }
        let map = {};
        subscriptions.forEach((s)=>{
            if (s.sourceType === 'Subscription' && s.id !== undefined) {
                map[s.id] = listener.find((l)=>l.id?.toString() === s.sourceSubIdRegex);
            }
        });
        return map;
    }
    function loadListener() {
        return new Promise((resolve, reject)=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getNotificationListener().then((listeners)=>{
                if (!mounted) {
                    resolve(null);
                    return;
                }
                let newListeners = [
                    ...listeners
                ];
                let promises = [];
                newListeners.forEach((listener, i)=>{
                    let p = getSubscriptionTitle(listener).then((title)=>{
                        newListeners[i].title = title;
                    });
                    promises.push(p);
                });
                Promise.all(promises).then(()=>{
                    setListener(newListeners);
                    resolve(null);
                }).catch(()=>{
                    reject();
                });
            });
        });
    }
    function loadSubscriptions() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getNotificationSubscriptions().then((subscriptions)=>{
            if (!mounted) {
                return;
            }
            setSubscriptions(subscriptions);
        });
    }
    function onLogin() {
        let googleId = sessionStorage.getItem('googleId');
        if (googleId) {
            setIsLoggedIn(true);
            setIsLoading(true);
            Promise.all([
                loadListener(),
                loadSubscriptions()
            ]).then(()=>{
                setIsLoading(false);
            });
        }
    }
    function onLoginFail() {
        setIsLoggedIn(false);
    }
    function getSubTypesAsList(subTypes, price) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
            children: subTypes.map((subType, i)=>{
                let result;
                switch(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SubscriptionType"][subType].toString()){
                    case __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SubscriptionType"].BIN.toString():
                        result = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                            children: "Notify only for instant buy"
                        }, i, false, {
                            fileName: "[project]/components/SubscriptionList/SubscriptionList.tsx",
                            lineNumber: 120,
                            columnNumber: 38
                        }, this);
                        break;
                    case __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SubscriptionType"].PRICE_HIGHER_THAN.toString():
                        result = price > 0 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                            children: [
                                "Notify if price is higher than",
                                ' ',
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("b", {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Number$2f$Number$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            number: price
                                        }, void 0, false, {
                                            fileName: "[project]/components/SubscriptionList/SubscriptionList.tsx",
                                            lineNumber: 128,
                                            columnNumber: 45
                                        }, this),
                                        " Coins"
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/components/SubscriptionList/SubscriptionList.tsx",
                                    lineNumber: 127,
                                    columnNumber: 41
                                }, this)
                            ]
                        }, i, true, {
                            fileName: "[project]/components/SubscriptionList/SubscriptionList.tsx",
                            lineNumber: 125,
                            columnNumber: 37
                        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                            children: "Any price"
                        }, "2", false, {
                            fileName: "[project]/components/SubscriptionList/SubscriptionList.tsx",
                            lineNumber: 132,
                            columnNumber: 37
                        }, this);
                        break;
                    case __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SubscriptionType"].PRICE_LOWER_THAN.toString():
                        result = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                            children: [
                                "Notify if price is lower than",
                                ' ',
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("b", {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Number$2f$Number$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            number: price
                                        }, void 0, false, {
                                            fileName: "[project]/components/SubscriptionList/SubscriptionList.tsx",
                                            lineNumber: 140,
                                            columnNumber: 41
                                        }, this),
                                        " Coins"
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/components/SubscriptionList/SubscriptionList.tsx",
                                    lineNumber: 139,
                                    columnNumber: 37
                                }, this)
                            ]
                        }, i, true, {
                            fileName: "[project]/components/SubscriptionList/SubscriptionList.tsx",
                            lineNumber: 137,
                            columnNumber: 33
                        }, this);
                        break;
                    case __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SubscriptionType"].OUTBID.toString():
                        result = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                            children: "Notify if player outbid something"
                        }, i, false, {
                            fileName: "[project]/components/SubscriptionList/SubscriptionList.tsx",
                            lineNumber: 146,
                            columnNumber: 38
                        }, this);
                        break;
                    case __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SubscriptionType"].SOLD.toString():
                        result = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                            children: "Notify if player sold something"
                        }, i, false, {
                            fileName: "[project]/components/SubscriptionList/SubscriptionList.tsx",
                            lineNumber: 149,
                            columnNumber: 38
                        }, this);
                        break;
                    case __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SubscriptionType"].PLAYER_CREATES_AUCTION.toString():
                        result = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                            children: "Notify if player creates an auction"
                        }, i, false, {
                            fileName: "[project]/components/SubscriptionList/SubscriptionList.tsx",
                            lineNumber: 152,
                            columnNumber: 38
                        }, this);
                        break;
                    case __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SubscriptionType"].BOUGHT_ANY_AUCTION.toString():
                        result = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                            children: "Notify if player bought any auction"
                        }, i, false, {
                            fileName: "[project]/components/SubscriptionList/SubscriptionList.tsx",
                            lineNumber: 155,
                            columnNumber: 38
                        }, this);
                        break;
                    case __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SubscriptionType"].USE_SELL_NOT_BUY.toString():
                        result = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                            children: "Use sell price instead of buy price"
                        }, i, false, {
                            fileName: "[project]/components/SubscriptionList/SubscriptionList.tsx",
                            lineNumber: 158,
                            columnNumber: 38
                        }, this);
                        break;
                }
                return result;
            })
        }, void 0, false, {
            fileName: "[project]/components/SubscriptionList/SubscriptionList.tsx",
            lineNumber: 115,
            columnNumber: 13
        }, this);
    }
    function onDelete(notificationListener) {
        if (!notificationListener.id) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('Could not delete notifier, no id available...');
            return;
        }
        let subscriptionToDelete = subscriptions.find((s)=>s.sourceSubIdRegex === notificationListener.id.toString());
        Promise.all([
            subscriptionToDelete ? __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].deleteNotificationSubscription(subscriptionToDelete) : Promise.resolve(),
            __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].unsubscribe(notificationListener)
        ]).then(()=>{
            let newListeners = listener.filter((s)=>s !== notificationListener);
            listener = newListeners;
            setListener(newListeners);
            if (subscriptionToDelete) {
                let newSubscriptions = subscriptions.filter((s)=>s.id !== subscriptionToDelete?.id);
                subscriptions = newSubscriptions;
                setSubscriptions(newSubscriptions);
            }
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success(/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                children: [
                    "Notifier deleted",
                    ' ',
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Button$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__["Button"], {
                        style: {
                            float: 'right',
                            marginRight: '5px'
                        },
                        variant: "info",
                        onClick: ()=>{
                            resubscribe(notificationListener, subscriptionToDelete);
                            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].dismiss();
                        },
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Undo$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                fileName: "[project]/components/SubscriptionList/SubscriptionList.tsx",
                                lineNumber: 199,
                                columnNumber: 25
                            }, this),
                            " Undo"
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/SubscriptionList/SubscriptionList.tsx",
                        lineNumber: 191,
                        columnNumber: 21
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/components/SubscriptionList/SubscriptionList.tsx",
                lineNumber: 189,
                columnNumber: 17
            }, this));
        });
    }
    function deleteAll() {
        __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].unsubscribeAll().then(()=>{
            setListener([]);
            setSubscriptions([]);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success('All notifiers were sucessfully removed');
        }).catch(()=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('Could not unsubscribe, please try again in a few minutes');
        });
        subscriptions.forEach((subscription)=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].deleteNotificationSubscription({
                ...subscription
            });
        });
        setShowDeleteAllSubscriptionDialog(false);
    }
    async function resubscribe(listener, subscription) {
        setIsLoading(true);
        await __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].subscribe(listener.topicId, listener.types, subscription.targets, listener.price, listener.filter);
        await Promise.all([
            loadListener(),
            loadSubscriptions()
        ]);
        setIsLoading(false);
    }
    function onAfterSubscribeEdit(oldSubscription) {
        let subscriptionToDelete = subscriptions.find((s)=>s.sourceSubIdRegex === oldSubscription.id.toString());
        Promise.all([
            subscriptionToDelete ? __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].deleteNotificationSubscription(subscriptionToDelete) : Promise.resolve(),
            __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].unsubscribe(oldSubscription)
        ]).then(()=>{
            setListener([]);
            setSubscriptions([]);
            setIsLoading(true);
            Promise.all([
                loadListener(),
                loadSubscriptions()
            ]).then(()=>{
                setIsLoading(false);
            });
        });
    }
    function getSubscriptionTitle(subscription) {
        return new Promise((resolve, reject)=>{
            switch(subscription.type){
                case 'item':
                case 'bazaar':
                    resolve((0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Formatter$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["convertTagToName"])(subscription.topicId));
                    break;
                case 'player':
                    __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getPlayerName(subscription.topicId).then((playerName)=>{
                        resolve(playerName);
                    }).catch(()=>{
                        resolve('Player could not be loaded...');
                    });
                    break;
                case 'auction':
                    __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getAuctionDetails(subscription.topicId).then((result)=>{
                        let auctionDetails = result.parsed;
                        resolve(auctionDetails.auction.item.name || auctionDetails.auction.item.tag);
                    }).catch(()=>{
                        resolve('Auction title could not be loaded...');
                    });
                    break;
                default:
                    resolve(subscription.topicId);
                    break;
            }
        });
    }
    function getSubscriptionTitleElement(subscription) {
        switch(subscription.type){
            case 'item':
            case 'bazaar':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    href: '/item/' + subscription.topicId,
                    className: "disableLinkStyle",
                    children: subscription.title
                }, void 0, false, {
                    fileName: "[project]/components/SubscriptionList/SubscriptionList.tsx",
                    lineNumber: 286,
                    columnNumber: 21
                }, this);
            case 'player':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    href: '/player/' + subscription.topicId,
                    className: "disableLinkStyle",
                    children: subscription.title
                }, void 0, false, {
                    fileName: "[project]/components/SubscriptionList/SubscriptionList.tsx",
                    lineNumber: 292,
                    columnNumber: 21
                }, this);
            case 'auction':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    href: '/auction/' + subscription.topicId,
                    className: "disableLinkStyle",
                    children: subscription.title
                }, void 0, false, {
                    fileName: "[project]/components/SubscriptionList/SubscriptionList.tsx",
                    lineNumber: 298,
                    columnNumber: 21
                }, this);
            default:
                return subscription.title;
        }
    }
    let subscriptionsTableBody = subscriptions.map((subscription, i)=>{
        let listener;
        if (subscription.id) {
            listener = subscriptionsToListenerMap[subscription.id?.toString()];
        }
        // Show a generic entry for subscription without a listener
        if (!listener) {
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$ListGroup$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ListGroup$3e$__["ListGroup"].Item, {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h5", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Badge$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Badge$3e$__["Badge"], {
                                style: {
                                    marginRight: '5px'
                                },
                                bg: "primary",
                                children: i + 1
                            }, void 0, false, {
                                fileName: "[project]/components/SubscriptionList/SubscriptionList.tsx",
                                lineNumber: 317,
                                columnNumber: 25
                            }, this),
                            subscription.sourceType
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/SubscriptionList/SubscriptionList.tsx",
                        lineNumber: 316,
                        columnNumber: 21
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: [
                            "SourceSubIdRegex: ",
                            subscription.sourceSubIdRegex
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/SubscriptionList/SubscriptionList.tsx",
                        lineNumber: 322,
                        columnNumber: 21
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        style: {
                            float: 'right'
                        },
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                children: [
                                    "Notification Targets: ",
                                    subscription.targets.length > 0 ? null : 'None'
                                ]
                            }, void 0, true, {
                                fileName: "[project]/components/SubscriptionList/SubscriptionList.tsx",
                                lineNumber: 324,
                                columnNumber: 25
                            }, this),
                            subscriptions.length > 0 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2d$typeahead$2f$es$2f$components$2f$Typeahead$2f$Typeahead$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Typeahead$3e$__["Typeahead"], {
                                disabled: true,
                                id: `notificationTargetsTypeahead-${subscription.id}`,
                                multiple: true,
                                labelKey: 'name',
                                defaultSelected: subscription.targets,
                                options: subscription.targets ? subscription.targets : []
                            }, void 0, false, {
                                fileName: "[project]/components/SubscriptionList/SubscriptionList.tsx",
                                lineNumber: 326,
                                columnNumber: 29
                            }, this) : null
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/SubscriptionList/SubscriptionList.tsx",
                        lineNumber: 323,
                        columnNumber: 21
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        style: {
                            position: 'absolute',
                            top: '0.75rem',
                            right: '1.25rem',
                            cursor: 'pointer',
                            display: 'flex',
                            alignItems: 'end'
                        },
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Delete$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                            color: "error",
                            onClick: async ()=>{
                                await __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].deleteNotificationSubscription(subscription);
                                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success('Subscription deleted');
                                setIsLoading(true);
                                await Promise.all([
                                    loadListener(),
                                    loadSubscriptions()
                                ]);
                                setIsLoading(false);
                            }
                        }, void 0, false, {
                            fileName: "[project]/components/SubscriptionList/SubscriptionList.tsx",
                            lineNumber: 337,
                            columnNumber: 25
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/components/SubscriptionList/SubscriptionList.tsx",
                        lineNumber: 336,
                        columnNumber: 21
                    }, this)
                ]
            }, i, true, {
                fileName: "[project]/components/SubscriptionList/SubscriptionList.tsx",
                lineNumber: 315,
                columnNumber: 17
            }, this);
        }
        // Show normal entry for subscriptions with a listener
        if (listener) {
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$ListGroup$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ListGroup$3e$__["ListGroup"].Item, {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h5", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Badge$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Badge$3e$__["Badge"], {
                                style: {
                                    marginRight: '5px'
                                },
                                bg: "primary",
                                children: i + 1
                            }, void 0, false, {
                                fileName: "[project]/components/SubscriptionList/SubscriptionList.tsx",
                                lineNumber: 357,
                                columnNumber: 25
                            }, this),
                            getSubscriptionTitleElement(listener)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/SubscriptionList/SubscriptionList.tsx",
                        lineNumber: 356,
                        columnNumber: 21
                    }, this),
                    getSubTypesAsList(listener.types, listener.price),
                    listener.filter ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("hr", {}, void 0, false, {
                        fileName: "[project]/components/SubscriptionList/SubscriptionList.tsx",
                        lineNumber: 363,
                        columnNumber: 40
                    }, this) : null,
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ItemFilter$2f$ItemFilterPropertiesDisplay$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                        filter: listener.filter
                    }, void 0, false, {
                        fileName: "[project]/components/SubscriptionList/SubscriptionList.tsx",
                        lineNumber: 364,
                        columnNumber: 21
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        style: {
                            float: 'right'
                        },
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                children: [
                                    "Notification Targets: ",
                                    subscription.targets.length > 0 ? null : 'None'
                                ]
                            }, void 0, true, {
                                fileName: "[project]/components/SubscriptionList/SubscriptionList.tsx",
                                lineNumber: 366,
                                columnNumber: 25
                            }, this),
                            subscriptions.length > 0 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2d$typeahead$2f$es$2f$components$2f$Typeahead$2f$Typeahead$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Typeahead$3e$__["Typeahead"], {
                                disabled: true,
                                id: `notificationTargetsTypeahead-${subscription.id}`,
                                multiple: true,
                                labelKey: 'name',
                                defaultSelected: subscription.targets,
                                options: subscription.targets ? subscription.targets : []
                            }, void 0, false, {
                                fileName: "[project]/components/SubscriptionList/SubscriptionList.tsx",
                                lineNumber: 368,
                                columnNumber: 29
                            }, this) : null
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/SubscriptionList/SubscriptionList.tsx",
                        lineNumber: 365,
                        columnNumber: 21
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        style: {
                            position: 'absolute',
                            top: '0.75rem',
                            right: '1.25rem',
                            cursor: 'pointer',
                            display: 'flex',
                            alignItems: 'end'
                        },
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$SubscribeButton$2f$SubscribeButton$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                topic: listener.topicId,
                                type: listener.type,
                                isEditButton: true,
                                onAfterSubscribe: ()=>{
                                    onAfterSubscribeEdit(listener);
                                },
                                prefill: {
                                    listener: listener,
                                    targetNames: subscription.id && subscription.targets.length > 0 ? subscription.targets.map((t)=>t.name) : []
                                },
                                popupTitle: "Update Notifier",
                                popupButtonText: "Update",
                                successMessage: "Notifier successfully updated"
                            }, void 0, false, {
                                fileName: "[project]/components/SubscriptionList/SubscriptionList.tsx",
                                lineNumber: 379,
                                columnNumber: 25
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Delete$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                color: "error",
                                onClick: ()=>{
                                    onDelete(listener);
                                }
                            }, void 0, false, {
                                fileName: "[project]/components/SubscriptionList/SubscriptionList.tsx",
                                lineNumber: 394,
                                columnNumber: 25
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/SubscriptionList/SubscriptionList.tsx",
                        lineNumber: 378,
                        columnNumber: 21
                    }, this)
                ]
            }, i, true, {
                fileName: "[project]/components/SubscriptionList/SubscriptionList.tsx",
                lineNumber: 355,
                columnNumber: 17
            }, this);
        }
    });
    let resetSettingsElement = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Modal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Modal$3e$__["Modal"], {
        show: showDeleteAllSubscriptionDialog,
        onHide: ()=>{
            setShowDeleteAllSubscriptionDialog(false);
        },
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Modal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Modal$3e$__["Modal"].Header, {
                closeButton: true,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Modal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Modal$3e$__["Modal"].Title, {
                    children: "Confirmation"
                }, void 0, false, {
                    fileName: "[project]/components/SubscriptionList/SubscriptionList.tsx",
                    lineNumber: 414,
                    columnNumber: 17
                }, this)
            }, void 0, false, {
                fileName: "[project]/components/SubscriptionList/SubscriptionList.tsx",
                lineNumber: 413,
                columnNumber: 13
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Modal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Modal$3e$__["Modal"].Body, {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: "Are you sure you want to delete all your notifiers?"
                    }, void 0, false, {
                        fileName: "[project]/components/SubscriptionList/SubscriptionList.tsx",
                        lineNumber: 417,
                        columnNumber: 17
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("b", {
                            children: [
                                "All ",
                                listener.length,
                                " notifier will be deleted!"
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/SubscriptionList/SubscriptionList.tsx",
                            lineNumber: 419,
                            columnNumber: 21
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/components/SubscriptionList/SubscriptionList.tsx",
                        lineNumber: 418,
                        columnNumber: 17
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        style: {
                            display: 'flex',
                            justifyContent: 'space-between'
                        },
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Button$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__["Button"], {
                                variant: "danger",
                                style: {
                                    width: '45%'
                                },
                                onClick: deleteAll,
                                children: [
                                    "RESET ",
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Delete$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                        fileName: "[project]/components/SubscriptionList/SubscriptionList.tsx",
                                        lineNumber: 423,
                                        columnNumber: 31
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/components/SubscriptionList/SubscriptionList.tsx",
                                lineNumber: 422,
                                columnNumber: 21
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Button$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__["Button"], {
                                style: {
                                    width: '45%'
                                },
                                onClick: ()=>{
                                    setShowDeleteAllSubscriptionDialog(false);
                                },
                                children: "Cancel"
                            }, void 0, false, {
                                fileName: "[project]/components/SubscriptionList/SubscriptionList.tsx",
                                lineNumber: 425,
                                columnNumber: 21
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/SubscriptionList/SubscriptionList.tsx",
                        lineNumber: 421,
                        columnNumber: 17
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/components/SubscriptionList/SubscriptionList.tsx",
                lineNumber: 416,
                columnNumber: 13
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/components/SubscriptionList/SubscriptionList.tsx",
        lineNumber: 407,
        columnNumber: 9
    }, this);
    let notificationTargetsElement = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Modal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Modal$3e$__["Modal"], {
        show: showNotificationTargets,
        size: "lg",
        onHide: ()=>{
            setShowNotificationTargets(false);
        },
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Modal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Modal$3e$__["Modal"].Header, {
                closeButton: true,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Modal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Modal$3e$__["Modal"].Title, {
                    children: "Notification Targets"
                }, void 0, false, {
                    fileName: "[project]/components/SubscriptionList/SubscriptionList.tsx",
                    lineNumber: 447,
                    columnNumber: 17
                }, this)
            }, void 0, false, {
                fileName: "[project]/components/SubscriptionList/SubscriptionList.tsx",
                lineNumber: 446,
                columnNumber: 13
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Modal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Modal$3e$__["Modal"].Body, {
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$NotificationTargets$2f$NotificationTargets$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                    fileName: "[project]/components/SubscriptionList/SubscriptionList.tsx",
                    lineNumber: 450,
                    columnNumber: 17
                }, this)
            }, void 0, false, {
                fileName: "[project]/components/SubscriptionList/SubscriptionList.tsx",
                lineNumber: 449,
                columnNumber: 13
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/components/SubscriptionList/SubscriptionList.tsx",
        lineNumber: 439,
        columnNumber: 9
    }, this);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$SubscriptionList$2f$SubscriptionList$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].subscriptionList,
        children: [
            isLoggedIn ? isLoading ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$LoadingUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getLoadingElement"])() : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        style: {
                            height: 'auto',
                            display: 'flex',
                            justifyContent: 'flex-end',
                            gap: 10
                        },
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Button$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__["Button"], {
                                variant: "primary",
                                onClick: ()=>{
                                    setShowNotificationTargets(true);
                                },
                                children: "Notification Targets"
                            }, void 0, false, {
                                fileName: "[project]/components/SubscriptionList/SubscriptionList.tsx",
                                lineNumber: 463,
                                columnNumber: 29
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Button$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__["Button"], {
                                variant: "danger",
                                onClick: ()=>{
                                    setShowDeleteAllSubscriptionDialog(true);
                                },
                                disabled: subscriptions.length === 0,
                                children: "Delete all notifiers"
                            }, void 0, false, {
                                fileName: "[project]/components/SubscriptionList/SubscriptionList.tsx",
                                lineNumber: 471,
                                columnNumber: 29
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/SubscriptionList/SubscriptionList.tsx",
                        lineNumber: 462,
                        columnNumber: 25
                    }, this),
                    subscriptions.length > 0 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$ListGroup$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ListGroup$3e$__["ListGroup"], {
                            style: {
                                marginTop: '20px'
                            },
                            children: subscriptionsTableBody
                        }, void 0, false, {
                            fileName: "[project]/components/SubscriptionList/SubscriptionList.tsx",
                            lineNumber: 483,
                            columnNumber: 33
                        }, this)
                    }, void 0, false) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: "You dont have any notifiers"
                    }, void 0, false, {
                        fileName: "[project]/components/SubscriptionList/SubscriptionList.tsx",
                        lineNumber: 486,
                        columnNumber: 29
                    }, this)
                ]
            }, void 0, true) : null,
            wasAlreadyLoggedIn && !isLoggedIn ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$LoadingUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getLoadingElement"])() : '',
            !wasAlreadyLoggedIn && !isLoggedIn ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                children: "To use subscriptions, please login with Google:"
            }, void 0, false, {
                fileName: "[project]/components/SubscriptionList/SubscriptionList.tsx",
                lineNumber: 492,
                columnNumber: 51
            }, this) : '',
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$GoogleSignIn$2f$GoogleSignIn$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                onAfterLogin: onLogin,
                onLoginFail: onLoginFail
            }, void 0, false, {
                fileName: "[project]/components/SubscriptionList/SubscriptionList.tsx",
                lineNumber: 493,
                columnNumber: 13
            }, this),
            resetSettingsElement,
            notificationTargetsElement
        ]
    }, void 0, true, {
        fileName: "[project]/components/SubscriptionList/SubscriptionList.tsx",
        lineNumber: 456,
        columnNumber: 9
    }, this);
}
_s(SubscriptionList, "u2hMQDwsqUrxAn2MXv+lt8hNJNs=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Hooks$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useWasAlreadyLoggedIn"]
    ];
});
_c = SubscriptionList;
const __TURBOPACK__default__export__ = SubscriptionList;
var _c;
__turbopack_context__.k.register(_c, "SubscriptionList");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=_ee4d4bbe._.js.map