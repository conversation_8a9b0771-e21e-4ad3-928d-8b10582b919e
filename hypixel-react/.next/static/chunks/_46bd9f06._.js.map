{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/Providers/Providers.tsx"], "sourcesContent": ["'use client'\nimport { MatomoProvider, createInstance } from '@jonkoops/matomo-tracker-react'\nimport { GoogleOAuthProvider } from '@react-oauth/google'\n\nconst matomoTrackingInstance = createInstance({\n    urlBase: 'https://track.coflnet.com',\n    siteId: 1\n})\n\nexport function Providers({ children }) {\n    return (\n        <MatomoProvider value={matomoTrackingInstance}>\n            <GoogleOAuthProvider clientId=\"570302890760-nlkgd99b71q4d61am4lpqdhen1penddt.apps.googleusercontent.com\">{children}</GoogleOAuthProvider>\n        </MatomoProvider>\n    )\n}\n"], "names": [], "mappings": ";;;;AACA;AAAA;AACA;AAFA;;;;AAIA,MAAM,yBAAyB,CAAA,GAAA,0NAAA,CAAA,iBAAc,AAAD,EAAE;IAC1C,SAAS;IACT,QAAQ;AACZ;AAEO,SAAS,UAAU,EAAE,QAAQ,EAAE;IAClC,qBACI,6LAAC,gOAAA,CAAA,iBAAc;QAAC,OAAO;kBACnB,cAAA,6LAAC,qKAAA,CAAA,sBAAmB;YAAC,UAAS;sBAA4E;;;;;;;;;;;AAGtH;KANgB", "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/components/OfflineBanner/OfflineBanner.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"offlineBanner\": \"OfflineBanner-module__7FQ3SW__offlineBanner\",\n  \"slideFromTop\": \"OfflineBanner-module__7FQ3SW__slideFromTop\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA"}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/OfflineBanner/OfflineBanner.tsx"], "sourcesContent": ["'use client'\nimport { useEffect, useState } from 'react'\nimport styles from './OfflineBanner.module.css'\n\nexport function OfflineBanner(props: any) {\n    let [isOnline, setIsOnline] = useState(true)\n\n    useEffect(() => {\n        window.addEventListener('online', setOnlineState)\n        window.addEventListener('offline', setOnlineState)\n\n        return () => {\n            window.removeEventListener('online', setOnlineState)\n            window.removeEventListener('offline', setOnlineState)\n        }\n    }, [])\n\n    function setOnlineState() {\n        setIsOnline(navigator.onLine)\n    }\n\n    return (\n        <div>\n            {!isOnline ? (\n                <div id=\"offline-banner\" className={styles.offlineBanner}>\n                    <span style={{ color: 'white' }}>No connection</span>\n                </div>\n            ) : (\n                ''\n            )}\n        </div>\n    )\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;;;AAFA;;;AAIO,SAAS,cAAc,KAAU;;IACpC,IAAI,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACN,OAAO,gBAAgB,CAAC,UAAU;YAClC,OAAO,gBAAgB,CAAC,WAAW;YAEnC;2CAAO;oBACH,OAAO,mBAAmB,CAAC,UAAU;oBACrC,OAAO,mBAAmB,CAAC,WAAW;gBAC1C;;QACJ;kCAAG,EAAE;IAEL,SAAS;QACL,YAAY,UAAU,MAAM;IAChC;IAEA,qBACI,6LAAC;kBACI,CAAC,yBACE,6LAAC;YAAI,IAAG;YAAiB,WAAW,2JAAA,CAAA,UAAM,CAAC,aAAa;sBACpD,cAAA,6LAAC;gBAAK,OAAO;oBAAE,OAAO;gBAAQ;0BAAG;;;;;;;;;;mBAGrC;;;;;;AAIhB;GA5BgB;KAAA", "debugId": null}}, {"offset": {"line": 127, "column": 0}, "map": {"version": 3, "sources": ["file:///app/utils/Base64Utils.tsx"], "sourcesContent": ["export function btoaUnicode(str) {\n    return btoa(\n        encodeURIComponent(str).replace(/%([0-9A-F]{2})/g, function (_match, p1) {\n            return String.fromCharCode(parseInt(p1, 16))\n        })\n    );\n}\n\n// Decoding base64 ⇢ UTF8\n\nexport function atobUnicode(str) {\n    return decodeURIComponent(\n        Array.prototype.map\n            .call(atob(str), function (c) {\n                return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2)\n            })\n            .join('')\n    )\n}\n"], "names": [], "mappings": ";;;;AAAO,SAAS,YAAY,GAAG;IAC3B,OAAO,KACH,mBAAmB,KAAK,OAAO,CAAC,mBAAmB,SAAU,MAAM,EAAE,EAAE;QACnE,OAAO,OAAO,YAAY,CAAC,SAAS,IAAI;IAC5C;AAER;AAIO,SAAS,YAAY,GAAG;IAC3B,OAAO,mBACH,MAAM,SAAS,CAAC,GAAG,CACd,IAAI,CAAC,KAAK,MAAM,SAAU,CAAC;QACxB,OAAO,MAAM,CAAC,OAAO,EAAE,UAAU,CAAC,GAAG,QAAQ,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;IAC9D,GACC,IAAI,CAAC;AAElB", "debugId": null}}, {"offset": {"line": 150, "column": 0}, "map": {"version": 3, "sources": ["file:///app/api/ApiTypes.d.tsx"], "sourcesContent": ["export enum RequestType {\n    SEARCH = 'search',\n    PLAYER_DETAIL = 'playerDetails',\n    ITEM_PRICES = 'pricerdicer',\n    BAZAAR_PRICES = 'bazaarPrices',\n    AUCTION_DETAILS = 'auction',\n    ITEM_DETAILS = 'itemDetails',\n    PLAYER_AUCTION = 'playerAuctions',\n    PLAYER_BIDS = 'playerBids',\n    ALL_ENCHANTMENTS = 'getEnchantments',\n    TRACK_SEARCH = 'trackSearch',\n    PLAYER_NAME = 'playerName',\n    PLAYER_NAMES = 'playerNames',\n    SET_CONNECTION_ID = 'setConId',\n    GET_VERSION = 'version',\n    SUBSCRIBE = 'subscribe',\n    UNSUBSCRIBE = 'unsubscribe',\n    GET_SUBSCRIPTIONS = 'subscriptions',\n    LOGIN_WITH_TOKEN = 'loginWithToken',\n    STRIPE_PAYMENT_SESSION = 'topup/stripe',\n    GET_PRODUCTS = 'topup/options',\n    PREMIUM_EXPIRATION = 'premiumExpiration',\n    FCM_TOKEN = 'token',\n    GET_STRIPE_PRODUCTS = 'getProducts',\n    GET_STRIPE_PRICES = 'getPrices',\n    VALIDATE_PAYMENT_TOKEN = 'gPurchase',\n    RECENT_AUCTIONS = 'recentAuctions',\n    SUBSCRIBE_FLIPS = 'subFlip',\n    UNSUBSCRIBE_FLIPS = 'unsubFlip',\n    GET_FLIPS = 'getFlips',\n    GET_FILTER = 'getFilter',\n    NEW_AUCTIONS = 'newAuctions',\n    NEW_PLAYERS = 'newPlayers',\n    NEW_ITEMS = 'newItems',\n    POPULAR_SEARCHES = 'popularSearches',\n    ENDED_AUCTIONS = 'endedAuctions',\n    GET_FLIP_BASED_AUCTIONS = 'flipBased',\n    PAYPAL_PAYMENT = 'topup/paypal',\n    LEMONSQUEEZY_PAYMENT = 'topup/lemonsqueezy',\n    GET_REF_INFO = 'referral/info',\n    SET_REF = 'referral/referred/by',\n    ACTIVE_AUCTIONS = 'activeAuctions',\n    FLIP_FILTERS = 'flipFilters',\n    CONNECT_MINECRAFT_ACCOUNT = 'conMc',\n    GET_ACCOUNT_INFO = 'accountInfo',\n    ITEM_SEARCH = 'item/search',\n    AUTHENTICATE_MOD_CONNECTION = 'authCon',\n    FLIP_UPDATE_TIME = 'flip/update/when',\n    PLAYER_SEARCH = 'search/player',\n    GET_PROFITABLE_CRAFTS = 'craft/profit',\n    GET_LOW_SUPPLY_ITEMS = 'auctions/supply/low',\n    SEND_FEEDBACK = 'sendFeedback',\n    TRIGGER_PLAYER_NAME_CHECK = 'triggerNameCheck',\n    GET_PLAYER_PROFILES = 'profile',\n    GET_CRAFTING_RECIPE = 'craft/recipe',\n    GET_LOWEST_BIN = 'lowestBin',\n    GET_BAZAAR_TAGS = 'items/bazaar/tags',\n    ITEM_PRICE_SUMMARY = 'item/price',\n    GET_KAT_FLIPS = 'kat/profit',\n    GET_TRACKED_FLIPS_FOR_PLAYER = 'flip/stats/player',\n    PURCHASE_WITH_COFLCOiNS = 'service/purchase',\n    SUBSCRIBE_EVENTS = 'subEvents',\n    GET_COFLCOIN_BALANCE = 'getCoflBalance',\n    GET_FLIP_SETTINGS = 'getFlipSettings',\n    SET_FLIP_SETTING = 'setFlipSetting',\n    TRASFER_COFLCOINS = 'transferCofl',\n    GET_BAZAAR_SNAPSHOT = 'getBazaarSnapshot',\n    SUBSCRIBE_FLIPS_ANONYM = 'subFlipAnonym',\n    GET_PRIVACY_SETTINGS = 'getPrivacySettings',\n    SET_PRIVACY_SETTINGS = 'setPrivacySettings',\n    CHECK_FOR_RAT = 'checkForRat',\n    GET_PREMIUM_PRODUCTS = 'premium/user/owns',\n    UNSUBSCRIBE_ALL = 'unsubscribeAll',\n    GET_ITEM_NAMES = 'items/names',\n    RELATED_ITEMS = 'realtedItems',\n    CHECK_FILTER = 'checkFilter',\n    OWNER_HISOTRY = 'ownerHistory',\n    MAYOR_DATA = 'mayorData',\n    INVENTORY_DATA = 'inventoryData',\n    CREATE_TRADE_OFFER = 'createTradeOffer',\n    DELETE_TRADE_OFFER = 'deleteTradeOffer',\n    GET_TRADES = 'getTrades',\n    GET_TRANSACTIONS = 'getTransactions',\n    GET_NOTIFICATION_TARGETS = 'getNotificationTargets',\n    ADD_NOTIFICATION_TARGETS = 'addNotificationTargets',\n    DELETE_NOTIFICATION_TARGETS = 'deleteNotificationTargets',\n    UPDATE_NOTIFICATION_TARGET = 'updateNotificationTarget',\n    SEND_TEST_NOTIFICATION = 'sendTestNotification',\n    GET_NOTIFICATION_SUBSCRIPTION = 'getNotificationSubscription',\n    ADD_NOTIFICATION_SUBSCRIPTION = 'addNotificationSubscription',\n    DELETE_NOTIFICATION_SUBSCRIPTION = 'deleteNotificationSubscription',\n    GET_PUBLISHED_CONFIGS = 'publishedConfigs',\n    LOAD_CONFIG = 'loadConfig',\n    UPDATE_CONFIG = 'updateConfig',\n    ARCHIVED_AUCTIONS = 'archivedAuctions',\n    EXPORT_ARCHIVED_AUCTIONS = 'exportArchivedAuctions',\n    GET_LINKVERTISE_LINK = 'getLinkvertiseLink',\n    CREATE_PREMIUM_SUBSCRIPTION = 'createPremiumSubscription',\n    DELETE_PREMIUM_SUBSCRIPTION = 'deletePremiumSubscription',\n    PURCHASE_PREMIUM_SUBSCRIPTION = 'purchasePremiumSubscription',\n    GET_CRAFTING_INSTRUCTIONS = 'craft/instructions'\n}\n\nexport enum SubscriptionType {\n    NONE = 0,\n    PRICE_LOWER_THAN = 1,\n    PRICE_HIGHER_THAN = 2,\n    OUTBID = 4,\n    SOLD = 8,\n    BIN = 16,\n    USE_SELL_NOT_BUY = 32,\n    AUCTION = 64,\n    PLAYER_CREATES_AUCTION = 128,\n    BOUGHT_ANY_AUCTION = 1024\n}\n\nexport interface ApiRequest {\n    mId?: number\n    type: RequestType\n    data: any\n    resolve: Function\n    reject: Function\n    customRequestURL?: string\n    requestMethod?: string\n    requestHeader?: any\n}\n\nexport interface ApiSubscription {\n    mId?: number\n    type: RequestType\n    data: any\n    callback(request: ApiResponse)\n    resubscribe(subscription: ApiSubscription)\n    onError(message: string)\n}\n\nexport interface NotificationListener {\n    id: number | undefined\n    topicId: string\n    price: number\n    types: SubscriptionType[]\n    type: 'player' | 'item' | 'auction' | 'bazaar'\n    title?: string\n    filter?: ItemFilter\n}\n\nexport interface Connection {\n    sendRequest(request: ApiRequest): void\n}\n\nexport interface WebsocketHelper extends Connection {\n    subscribe(subscription: ApiSubscription): void\n    removeOldSubscriptionByType(type: RequestType): void\n}\n\nexport interface HttpApi extends Connection {\n    sendApiRequest(request: ApiRequest, body?: any): Promise<void>\n    sendLimitedCacheRequest(request: ApiRequest, grouping: number)\n    sendLimitedCacheRequest(request: ApiRequest)\n}\n\nexport let CUSTOM_EVENTS = {\n    FLIP_SETTINGS_CHANGE: 'flipSettingsChange',\n    COFLCOIN_UPDATE: 'coflCoinRefresh',\n    GOOGLE_LOGIN: 'googleLogin',\n    BAZAAR_SNAPSHOT_UPDATE: 'bazaarSnapshotUpdate'\n}\n"], "names": [], "mappings": ";;;;;AAAO,IAAA,AAAK,qCAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;WAAA;;AAuGL,IAAA,AAAK,0CAAA;;;;;;;;;;;WAAA;;AA0DL,IAAI,gBAAgB;IACvB,sBAAsB;IACtB,iBAAiB;IACjB,cAAc;IACd,wBAAwB;AAC5B", "debugId": null}}, {"offset": {"line": 286, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/FilterElement/FilterType.tsx"], "sourcesContent": ["export enum FilterType {\n    EQUAL = 1,\n    HIGHER = 2,\n    LOWER = 4,\n    DATE = 8,\n    NUMERICAL = 16,\n    RANGE = 32,\n    PLAYER = 64,\n    SIMPLE = 128,\n    BOOLEAN = 256,\n    PLAYER_WITH_RANK = 512,\n    SHOW_ICON = 1024,\n}\n\n/**\n * Checks an FilterType if a flag is present\n * @param full the enum that should contain the flag\n * @param flag the flag to test against\n * @returns true if the enum contains the flag\n */\nexport function hasFlag(full?: any, flag?: any) {\n    let result = full && flag && (full & flag) === flag\n    return result === null ? false : result\n}\n"], "names": [], "mappings": ";;;;AAAO,IAAA,AAAK,oCAAA;;;;;;;;;;;;WAAA;;AAoBL,SAAS,QAAQ,IAAU,EAAE,IAAU;IAC1C,IAAI,SAAS,QAAQ,QAAQ,CAAC,OAAO,IAAI,MAAM;IAC/C,OAAO,WAAW,OAAO,QAAQ;AACrC", "debugId": null}}, {"offset": {"line": 317, "column": 0}, "map": {"version": 3, "sources": ["file:///app/utils/SSRUtils.tsx"], "sourcesContent": ["import { Metadata } from 'next'\n\nexport function isClientSideRendering() {\n    return typeof window !== 'undefined'\n}\n\nexport function getHeadMetadata(\n    title: string = 'Skyblock Auction House History | Hypixel SkyBlock AH history',\n    description: string = 'Browse over 600 million auctions, and the bazaar of Hypixel SkyBlock.',\n    imageUrl: string = 'https://sky.coflnet.com/logo192.png',\n    keywords: string[] = [],\n    embedTitle: string = 'Skyblock Auction House History | Hypixel SkyBlock AH history'\n): Metadata {\n    return {\n        title: title,\n        description: description,\n        manifest: '/manifest.json',\n        openGraph: {\n            title: embedTitle,\n            description: description,\n            images: {\n                url: imageUrl,\n                height: 64,\n                width: 64\n            }\n        },\n        keywords: [...keywords, 'hypixel', 'skyblock', 'auction', 'history', 'bazaar', 'tracker'].join(',')\n    }\n}\n"], "names": [], "mappings": ";;;;AAEO,SAAS;IACZ,OAAO,aAAkB;AAC7B;AAEO,SAAS,gBACZ,QAAgB,8DAA8D,EAC9E,cAAsB,uEAAuE,EAC7F,WAAmB,qCAAqC,EACxD,WAAqB,EAAE,EACvB,aAAqB,8DAA8D;IAEnF,OAAO;QACH,OAAO;QACP,aAAa;QACb,UAAU;QACV,WAAW;YACP,OAAO;YACP,aAAa;YACb,QAAQ;gBACJ,KAAK;gBACL,QAAQ;gBACR,OAAO;YACX;QACJ;QACA,UAAU;eAAI;YAAU;YAAW;YAAY;YAAW;YAAW;YAAU;SAAU,CAAC,IAAI,CAAC;IACnG;AACJ", "debugId": null}}, {"offset": {"line": 358, "column": 0}, "map": {"version": 3, "sources": ["file:///app/utils/Formatter.tsx"], "sourcesContent": ["import { CSSProperties, cloneElement, type JSX } from 'react';\nimport { isClientSideRendering } from './SSRUtils'\nimport { update } from 'idb-keyval'\n\n/*\n Returns a given number as string with thousands-separators. Example:\n 1234567 => 1.234.567\n*/\nexport function numberWithThousandsSeparators(number?: number, thousandSeperator?: string, decimalSeperator?: string): string {\n    if (!number) {\n        return '0'\n    }\n    return number.toLocaleString()\n}\n\n/**\n * Converts a tag (e.g. WOODEN_AXE) to a item name (e.g. Wooden Axe)\n * - replaces all _ with spaces\n * - lowercases the word exept first letter (with exception of the defined words)\n * @param item\n */\nexport function convertTagToName(itemTag?: string): string {\n    if (!itemTag) {\n        return ''\n    }\n\n    // special case for PET_SKIN to avoid confusion\n    if (itemTag === 'PET_SKIN') {\n        return 'Pet Skin (unapplied)'\n    }\n\n    // words that should remain lowercase\n    const exceptions = ['of', 'the']\n\n    function capitalizeWords(text: string): string {\n        return text.replace(/\\w\\S*/g, function (txt) {\n            if (exceptions.findIndex(a => a === txt) > -1) {\n                return txt\n            }\n            return txt.charAt(0).toUpperCase() + txt.slice(1).toLowerCase()\n        });\n    }\n\n    let formatted: string = itemTag.toString().replace(new RegExp('_', 'g'), ' ').toLowerCase()\n\n    formatted = capitalizeWords(formatted)\n\n    // special per item Formating\n    formatted = formatted?.replace('Pet Item', '')\n    if (formatted?.startsWith('Pet')) {\n        formatted = formatted?.replace('Pet', '') + ' Pet'\n    }\n    if (formatted?.startsWith('Ring')) {\n        formatted = formatted?.replace('Ring ', '') + ' Ring'\n    }\n    return formatted\n}\n\n/**\n * Converts a camelCase string (e.g. woodenAxe) to a sentence case (e.g. Wooden axe)\n * @param camelCase\n */\nexport function camelCaseToSentenceCase(camelCase: string): string {\n    const exceptions = ['UId']\n\n    if (exceptions.findIndex(a => a === camelCase) > -1) {\n        return camelCase\n    }\n\n    var result = camelCase.replace(/([A-Z])/g, ' $1')\n    var finalResult = result.split(' ')\n    var isFirstWord = true\n    finalResult.forEach((word, i) => {\n        if (word !== '' && isFirstWord) {\n            isFirstWord = false\n            return\n        }\n        finalResult[i] = word.toLowerCase()\n    })\n    return finalResult.join(' ')\n}\n\nexport function getStyleForTier(tier?: string | number): CSSProperties {\n    interface TierColour {\n        colourCode: string\n        type: string\n    }\n\n    let tierColors: TierColour[] = [\n        { type: 'UNKNOWN', colourCode: 'black' },\n        { type: 'COMMON', colourCode: 'black' },\n        { type: 'UNCOMMON', colourCode: '#55ff55' },\n        { type: 'RARE', colourCode: '#5555ff' },\n        { type: 'EPIC', colourCode: '#aa00aa' },\n        { type: 'LEGENDARY', colourCode: '#ffaa00' },\n        { type: 'SPECIAL', colourCode: '#FF5555' },\n        { type: 'VERY_SPECIAL', colourCode: '#FF5555' },\n        { type: 'MYTHIC', colourCode: '#ff55ff' },\n        { type: 'SUPREME', colourCode: '#AA0000' }\n    ]\n\n    let color: TierColour | undefined\n\n    if (tier) {\n        //!tier ? DEFAULT_COLOR : (TIER_COLORS[tier.toString().toUpperCase()] ||\n        if (!isNaN(Number(tier))) {\n            color = tierColors[tier]\n        } else {\n            color = tierColors.find(color => {\n                return color.type === tier.toString().toUpperCase()\n            })\n        }\n    }\n\n    return {\n        color: color ? color.colourCode : undefined,\n        fontFamily: 'monospace',\n        fontWeight: 'bold'\n    }\n}\n\nexport function enchantmentAndReforgeCompare(a: Enchantment | Reforge, b: Enchantment | Reforge): number {\n    let aName = a.name ? a.name.toLowerCase() : ''\n    let bName = b.name ? b.name.toLowerCase() : ''\n\n    if (aName === 'any' || (aName === 'none' && bName !== 'any')) {\n        return -1\n    }\n    if (bName === 'any' || bName === 'none') {\n        return 1\n    }\n\n    return aName.localeCompare(bName)\n}\n\nexport function formatToPriceToShorten(num: number, decimals: number = 0): string {\n    let multMap = [\n        { mult: 1e12, suffix: 'T' },\n        { mult: 1e9, suffix: 'B' },\n        { mult: 1e6, suffix: 'M' },\n        { mult: 1e3, suffix: 'k' },\n        { mult: 1, suffix: '' }\n    ]\n    let multIndex = multMap.findIndex(m => num >= m.mult)\n    if (multIndex === -1) {\n        multIndex = multMap.length - 1\n    }\n    if (multIndex !== 0) {\n        if (Math.round(num / multMap[multIndex].mult) === 1000) {\n            multIndex -= 1\n        }\n    }\n\n    let mult = multMap[multIndex]\n    return (num / mult.mult).toFixed(decimals) + mult.suffix\n}\n\nexport function getThousandSeparator() {\n    let langTag = isClientSideRendering() ? navigator.language : 'en'\n    if (langTag.startsWith('en')) {\n        return ','\n    } else {\n        return '.'\n    }\n}\n\nexport function getDecimalSeparator() {\n    let langTag = isClientSideRendering() ? navigator.language : 'en'\n    if (langTag.startsWith('en')) {\n        return '.'\n    } else {\n        return ','\n    }\n}\n\n/**\n * Returs a number from a short representation string of a price (e.g. 12M => 12_000_000)\n * @param shortString A string representing a larger number (e.g. 12M)\n * @returns The number represented by the string (e.g. 12_000_000)\n */\nexport function getNumberFromShortenString(shortString?: string): number | undefined {\n    if (!shortString) {\n        return\n    }\n    let val = [\n        { value: 1e12, suffix: 'T' },\n        { value: 1e12, suffix: 't' },\n        { value: 1e9, suffix: 'B' },\n        { value: 1e9, suffix: 'b' },\n        { value: 1e6, suffix: 'M' },\n        { value: 1e6, suffix: 'm' },\n        { value: 1e3, suffix: 'K' },\n        { value: 1e3, suffix: 'k' },\n        { value: 1, suffix: '' }\n    ].find(val => shortString.includes(val.suffix)) || { value: 1, suffix: '' }\n    return parseFloat(shortString.at(-1) == val.suffix ? shortString.slice(0, -1) : shortString) * val.value\n}\n\nexport function getLocalDateAndTime(d: Date): string {\n    if (!d) {\n        return ''\n    }\n    return d.toLocaleDateString() + ', ' + d.toLocaleTimeString()\n}\n\nexport function formatAsCoins(number: number): string {\n    if (typeof number === 'string') {\n        try {\n            number = parseInt(number)\n        } catch {\n            return ''\n        }\n    }\n    return `${numberWithThousandsSeparators(number)} Coins`\n}\nexport function formatDungeonStarsInString(stringWithStars: string = '', style: CSSProperties = {}, dungeonItemLevelString?: string): JSX.Element {\n    let yellowStarStyle = { color: '#ffaa00', fontWeight: 'normal', height: '100%' }\n    let redStarStyle = { color: 'red', fontWeight: 'normal', height: '100%' }\n    let itemNameStyle = {\n        height: '32px',\n        marginRight: '-5px'\n    }\n    let stars = stringWithStars?.match(/✪.*/gm)\n\n    let numberOfMasterstars = 0\n    if (dungeonItemLevelString) {\n        try {\n            numberOfMasterstars = Math.max(parseInt(dungeonItemLevelString) - 5, 0)\n        } catch {}\n    }\n\n    if (!stars || stars.length === 0) {\n        return <span style={style}>{stringWithStars}</span>\n    }\n\n    let starsString = stars[0]\n    let itemName = stringWithStars.split(stars[0])[0]\n    let starsLastChar = starsString.charAt(starsString.length - 1)\n    let starWithNumber = starsLastChar === '✪' ? undefined : starsLastChar\n\n    let normalStarElement = <span style={yellowStarStyle}>{'✪'.repeat(starsString.length - numberOfMasterstars)}</span>\n    if (starWithNumber) {\n        normalStarElement = <span style={yellowStarStyle}>{starsString.substring(0, starsString.length - 1)}</span>\n    }\n\n    return (\n        <span style={style}>\n            {itemName ? <span style={itemNameStyle}>{itemName}</span> : null} {normalStarElement}\n            {starWithNumber || numberOfMasterstars ? (\n                <span style={redStarStyle}>{starWithNumber ? starWithNumber : '✪'.repeat(numberOfMasterstars)}</span>\n            ) : null}\n        </span>\n    )\n}\n\nexport function getMinecraftColorCodedElement(text: string = '', autoFormat = true): JSX.Element {\n    let styleMap: { [key: string]: React.CSSProperties } = {\n        '0': { color: '#000000' },\n        '1': { color: '#0000aa' },\n        '2': { color: '#00aa00' },\n        '3': { color: '#00aaaa' },\n        '4': { color: '#aa0000' },\n        '5': { color: '#aa00aa' },\n        '6': { color: '#ffaa00' },\n        '7': { color: '#aaaaaa' },\n        '8': { color: '#555555' },\n        '9': { color: '#5555ff' },\n        a: { color: '#55ff55' },\n        b: { color: '#55ffff' },\n        c: { color: '#FF5555' },\n        d: { color: '#FF55FF' },\n        e: { color: '#FFFF55' },\n        f: { color: '#FFFFFF' },\n        l: { fontWeight: 'bold' },\n        n: { textDecorationLine: 'underline', textDecorationSkip: 'spaces' },\n        o: { fontStyle: 'italic' },\n        m: { textDecoration: 'line-through', textDecorationSkip: 'spaces' },\n        r: { textDecoration: 'none', textDecorationLine: 'none', textDecorationSkip: 'none', fontWeight: 'normal', fontStyle: 'normal', color: '#FFFFFF' }\n    }\n\n    let splits = text.split('§')\n    let elements: JSX.Element[] = []\n    let currentStyle = {}\n\n    splits.forEach((split, i) => {\n        if (i === 0) {\n            if (split !== '') {\n                elements.push(<span key={i}>{split}</span>)\n            }\n            return\n        }\n        let code = split.substring(0, 1)\n        let text = autoFormat ? convertTagToName(split.substring(1, split.length)) : split.substring(1, split.length)\n\n        // get new style. Use reset if a unknown color code is used\n        let newStyle = styleMap[code] || styleMap['r']\n\n        currentStyle = { ...currentStyle, ...newStyle }\n        elements.push(\n            <span key={i} style={currentStyle}>\n                {text}\n            </span>\n        )\n    })\n\n    function textContent(elem: React.ReactElement<any> | string): string {\n        if (!elem) {\n            return ''\n        }\n        if (typeof elem === 'string') {\n            return elem\n        }\n        const children = elem.props && elem.props.children\n        if (children instanceof Array) {\n            return children.map(textContent).join('')\n        }\n        return textContent(children)\n    }\n\n    function addBreaks(elements: JSX.Element[]) {\n        const updatedElements = elements.map(element => {\n            if (element.type === 'span' && element.props.children) {\n                let text = textContent(element)\n                if (text.includes('\\n')) {\n                    const parts = text.split('\\n')\n                    return cloneElement(\n                        element,\n                        element.props,\n                        <>\n                            <span>{parts[0]}</span>\n                            <br />\n                            <span>{parts[1]}</span>\n                        </>\n                    )\n                }\n            }\n            return element\n        })\n        return updatedElements\n    }\n\n    elements = addBreaks(elements)\n\n    return <span>{elements}</span>\n}\n\nexport function removeMinecraftColorCoding(text: string = ''): string {\n    return text.replace(/§[0-9a-fk-or]/gi, '');\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAAA;AACA;;;;AAOO,SAAS,8BAA8B,MAAe,EAAE,iBAA0B,EAAE,gBAAyB;IAChH,IAAI,CAAC,QAAQ;QACT,OAAO;IACX;IACA,OAAO,OAAO,cAAc;AAChC;AAQO,SAAS,iBAAiB,OAAgB;IAC7C,IAAI,CAAC,SAAS;QACV,OAAO;IACX;IAEA,+CAA+C;IAC/C,IAAI,YAAY,YAAY;QACxB,OAAO;IACX;IAEA,qCAAqC;IACrC,MAAM,aAAa;QAAC;QAAM;KAAM;IAEhC,SAAS,gBAAgB,IAAY;QACjC,OAAO,KAAK,OAAO,CAAC,UAAU,SAAU,GAAG;YACvC,IAAI,WAAW,SAAS,CAAC,CAAA,IAAK,MAAM,OAAO,CAAC,GAAG;gBAC3C,OAAO;YACX;YACA,OAAO,IAAI,MAAM,CAAC,GAAG,WAAW,KAAK,IAAI,KAAK,CAAC,GAAG,WAAW;QACjE;IACJ;IAEA,IAAI,YAAoB,QAAQ,QAAQ,GAAG,OAAO,CAAC,IAAI,OAAO,KAAK,MAAM,KAAK,WAAW;IAEzF,YAAY,gBAAgB;IAE5B,6BAA6B;IAC7B,YAAY,WAAW,QAAQ,YAAY;IAC3C,IAAI,WAAW,WAAW,QAAQ;QAC9B,YAAY,WAAW,QAAQ,OAAO,MAAM;IAChD;IACA,IAAI,WAAW,WAAW,SAAS;QAC/B,YAAY,WAAW,QAAQ,SAAS,MAAM;IAClD;IACA,OAAO;AACX;AAMO,SAAS,wBAAwB,SAAiB;IACrD,MAAM,aAAa;QAAC;KAAM;IAE1B,IAAI,WAAW,SAAS,CAAC,CAAA,IAAK,MAAM,aAAa,CAAC,GAAG;QACjD,OAAO;IACX;IAEA,IAAI,SAAS,UAAU,OAAO,CAAC,YAAY;IAC3C,IAAI,cAAc,OAAO,KAAK,CAAC;IAC/B,IAAI,cAAc;IAClB,YAAY,OAAO,CAAC,CAAC,MAAM;QACvB,IAAI,SAAS,MAAM,aAAa;YAC5B,cAAc;YACd;QACJ;QACA,WAAW,CAAC,EAAE,GAAG,KAAK,WAAW;IACrC;IACA,OAAO,YAAY,IAAI,CAAC;AAC5B;AAEO,SAAS,gBAAgB,IAAsB;IAMlD,IAAI,aAA2B;QAC3B;YAAE,MAAM;YAAW,YAAY;QAAQ;QACvC;YAAE,MAAM;YAAU,YAAY;QAAQ;QACtC;YAAE,MAAM;YAAY,YAAY;QAAU;QAC1C;YAAE,MAAM;YAAQ,YAAY;QAAU;QACtC;YAAE,MAAM;YAAQ,YAAY;QAAU;QACtC;YAAE,MAAM;YAAa,YAAY;QAAU;QAC3C;YAAE,MAAM;YAAW,YAAY;QAAU;QACzC;YAAE,MAAM;YAAgB,YAAY;QAAU;QAC9C;YAAE,MAAM;YAAU,YAAY;QAAU;QACxC;YAAE,MAAM;YAAW,YAAY;QAAU;KAC5C;IAED,IAAI;IAEJ,IAAI,MAAM;QACN,wEAAwE;QACxE,IAAI,CAAC,MAAM,OAAO,QAAQ;YACtB,QAAQ,UAAU,CAAC,KAAK;QAC5B,OAAO;YACH,QAAQ,WAAW,IAAI,CAAC,CAAA;gBACpB,OAAO,MAAM,IAAI,KAAK,KAAK,QAAQ,GAAG,WAAW;YACrD;QACJ;IACJ;IAEA,OAAO;QACH,OAAO,QAAQ,MAAM,UAAU,GAAG;QAClC,YAAY;QACZ,YAAY;IAChB;AACJ;AAEO,SAAS,6BAA6B,CAAwB,EAAE,CAAwB;IAC3F,IAAI,QAAQ,EAAE,IAAI,GAAG,EAAE,IAAI,CAAC,WAAW,KAAK;IAC5C,IAAI,QAAQ,EAAE,IAAI,GAAG,EAAE,IAAI,CAAC,WAAW,KAAK;IAE5C,IAAI,UAAU,SAAU,UAAU,UAAU,UAAU,OAAQ;QAC1D,OAAO,CAAC;IACZ;IACA,IAAI,UAAU,SAAS,UAAU,QAAQ;QACrC,OAAO;IACX;IAEA,OAAO,MAAM,aAAa,CAAC;AAC/B;AAEO,SAAS,uBAAuB,GAAW,EAAE,WAAmB,CAAC;IACpE,IAAI,UAAU;QACV;YAAE,MAAM;YAAM,QAAQ;QAAI;QAC1B;YAAE,MAAM;YAAK,QAAQ;QAAI;QACzB;YAAE,MAAM;YAAK,QAAQ;QAAI;QACzB;YAAE,MAAM;YAAK,QAAQ;QAAI;QACzB;YAAE,MAAM;YAAG,QAAQ;QAAG;KACzB;IACD,IAAI,YAAY,QAAQ,SAAS,CAAC,CAAA,IAAK,OAAO,EAAE,IAAI;IACpD,IAAI,cAAc,CAAC,GAAG;QAClB,YAAY,QAAQ,MAAM,GAAG;IACjC;IACA,IAAI,cAAc,GAAG;QACjB,IAAI,KAAK,KAAK,CAAC,MAAM,OAAO,CAAC,UAAU,CAAC,IAAI,MAAM,MAAM;YACpD,aAAa;QACjB;IACJ;IAEA,IAAI,OAAO,OAAO,CAAC,UAAU;IAC7B,OAAO,CAAC,MAAM,KAAK,IAAI,EAAE,OAAO,CAAC,YAAY,KAAK,MAAM;AAC5D;AAEO,SAAS;IACZ,IAAI,UAAU,CAAA,GAAA,qHAAA,CAAA,wBAAqB,AAAD,MAAM,UAAU,QAAQ,GAAG;IAC7D,IAAI,QAAQ,UAAU,CAAC,OAAO;QAC1B,OAAO;IACX,OAAO;QACH,OAAO;IACX;AACJ;AAEO,SAAS;IACZ,IAAI,UAAU,CAAA,GAAA,qHAAA,CAAA,wBAAqB,AAAD,MAAM,UAAU,QAAQ,GAAG;IAC7D,IAAI,QAAQ,UAAU,CAAC,OAAO;QAC1B,OAAO;IACX,OAAO;QACH,OAAO;IACX;AACJ;AAOO,SAAS,2BAA2B,WAAoB;IAC3D,IAAI,CAAC,aAAa;QACd;IACJ;IACA,IAAI,MAAM;QACN;YAAE,OAAO;YAAM,QAAQ;QAAI;QAC3B;YAAE,OAAO;YAAM,QAAQ;QAAI;QAC3B;YAAE,OAAO;YAAK,QAAQ;QAAI;QAC1B;YAAE,OAAO;YAAK,QAAQ;QAAI;QAC1B;YAAE,OAAO;YAAK,QAAQ;QAAI;QAC1B;YAAE,OAAO;YAAK,QAAQ;QAAI;QAC1B;YAAE,OAAO;YAAK,QAAQ;QAAI;QAC1B;YAAE,OAAO;YAAK,QAAQ;QAAI;QAC1B;YAAE,OAAO;YAAG,QAAQ;QAAG;KAC1B,CAAC,IAAI,CAAC,CAAA,MAAO,YAAY,QAAQ,CAAC,IAAI,MAAM,MAAM;QAAE,OAAO;QAAG,QAAQ;IAAG;IAC1E,OAAO,WAAW,YAAY,EAAE,CAAC,CAAC,MAAM,IAAI,MAAM,GAAG,YAAY,KAAK,CAAC,GAAG,CAAC,KAAK,eAAe,IAAI,KAAK;AAC5G;AAEO,SAAS,oBAAoB,CAAO;IACvC,IAAI,CAAC,GAAG;QACJ,OAAO;IACX;IACA,OAAO,EAAE,kBAAkB,KAAK,OAAO,EAAE,kBAAkB;AAC/D;AAEO,SAAS,cAAc,MAAc;IACxC,IAAI,OAAO,WAAW,UAAU;QAC5B,IAAI;YACA,SAAS,SAAS;QACtB,EAAE,OAAM;YACJ,OAAO;QACX;IACJ;IACA,OAAO,GAAG,8BAA8B,QAAQ,MAAM,CAAC;AAC3D;AACO,SAAS,2BAA2B,kBAA0B,EAAE,EAAE,QAAuB,CAAC,CAAC,EAAE,sBAA+B;IAC/H,IAAI,kBAAkB;QAAE,OAAO;QAAW,YAAY;QAAU,QAAQ;IAAO;IAC/E,IAAI,eAAe;QAAE,OAAO;QAAO,YAAY;QAAU,QAAQ;IAAO;IACxE,IAAI,gBAAgB;QAChB,QAAQ;QACR,aAAa;IACjB;IACA,IAAI,QAAQ,iBAAiB,MAAM;IAEnC,IAAI,sBAAsB;IAC1B,IAAI,wBAAwB;QACxB,IAAI;YACA,sBAAsB,KAAK,GAAG,CAAC,SAAS,0BAA0B,GAAG;QACzE,EAAE,OAAM,CAAC;IACb;IAEA,IAAI,CAAC,SAAS,MAAM,MAAM,KAAK,GAAG;QAC9B,qBAAO,6LAAC;YAAK,OAAO;sBAAQ;;;;;;IAChC;IAEA,IAAI,cAAc,KAAK,CAAC,EAAE;IAC1B,IAAI,WAAW,gBAAgB,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;IACjD,IAAI,gBAAgB,YAAY,MAAM,CAAC,YAAY,MAAM,GAAG;IAC5D,IAAI,iBAAiB,kBAAkB,MAAM,YAAY;IAEzD,IAAI,kCAAoB,6LAAC;QAAK,OAAO;kBAAkB,IAAI,MAAM,CAAC,YAAY,MAAM,GAAG;;;;;;IACvF,IAAI,gBAAgB;QAChB,kCAAoB,6LAAC;YAAK,OAAO;sBAAkB,YAAY,SAAS,CAAC,GAAG,YAAY,MAAM,GAAG;;;;;;IACrG;IAEA,qBACI,6LAAC;QAAK,OAAO;;YACR,yBAAW,6LAAC;gBAAK,OAAO;0BAAgB;;;;;uBAAmB;YAAK;YAAE;YAClE,kBAAkB,oCACf,6LAAC;gBAAK,OAAO;0BAAe,iBAAiB,iBAAiB,IAAI,MAAM,CAAC;;;;;uBACzE;;;;;;;AAGhB;AAEO,SAAS,8BAA8B,OAAe,EAAE,EAAE,aAAa,IAAI;IAC9E,IAAI,WAAmD;QACnD,KAAK;YAAE,OAAO;QAAU;QACxB,KAAK;YAAE,OAAO;QAAU;QACxB,KAAK;YAAE,OAAO;QAAU;QACxB,KAAK;YAAE,OAAO;QAAU;QACxB,KAAK;YAAE,OAAO;QAAU;QACxB,KAAK;YAAE,OAAO;QAAU;QACxB,KAAK;YAAE,OAAO;QAAU;QACxB,KAAK;YAAE,OAAO;QAAU;QACxB,KAAK;YAAE,OAAO;QAAU;QACxB,KAAK;YAAE,OAAO;QAAU;QACxB,GAAG;YAAE,OAAO;QAAU;QACtB,GAAG;YAAE,OAAO;QAAU;QACtB,GAAG;YAAE,OAAO;QAAU;QACtB,GAAG;YAAE,OAAO;QAAU;QACtB,GAAG;YAAE,OAAO;QAAU;QACtB,GAAG;YAAE,OAAO;QAAU;QACtB,GAAG;YAAE,YAAY;QAAO;QACxB,GAAG;YAAE,oBAAoB;YAAa,oBAAoB;QAAS;QACnE,GAAG;YAAE,WAAW;QAAS;QACzB,GAAG;YAAE,gBAAgB;YAAgB,oBAAoB;QAAS;QAClE,GAAG;YAAE,gBAAgB;YAAQ,oBAAoB;YAAQ,oBAAoB;YAAQ,YAAY;YAAU,WAAW;YAAU,OAAO;QAAU;IACrJ;IAEA,IAAI,SAAS,KAAK,KAAK,CAAC;IACxB,IAAI,WAA0B,EAAE;IAChC,IAAI,eAAe,CAAC;IAEpB,OAAO,OAAO,CAAC,CAAC,OAAO;QACnB,IAAI,MAAM,GAAG;YACT,IAAI,UAAU,IAAI;gBACd,SAAS,IAAI,eAAC,6LAAC;8BAAc;mBAAJ;;;;;YAC7B;YACA;QACJ;QACA,IAAI,OAAO,MAAM,SAAS,CAAC,GAAG;QAC9B,IAAI,OAAO,aAAa,iBAAiB,MAAM,SAAS,CAAC,GAAG,MAAM,MAAM,KAAK,MAAM,SAAS,CAAC,GAAG,MAAM,MAAM;QAE5G,2DAA2D;QAC3D,IAAI,WAAW,QAAQ,CAAC,KAAK,IAAI,QAAQ,CAAC,IAAI;QAE9C,eAAe;YAAE,GAAG,YAAY;YAAE,GAAG,QAAQ;QAAC;QAC9C,SAAS,IAAI,eACT,6LAAC;YAAa,OAAO;sBAChB;WADM;;;;;IAInB;IAEA,SAAS,YAAY,IAAsC;QACvD,IAAI,CAAC,MAAM;YACP,OAAO;QACX;QACA,IAAI,OAAO,SAAS,UAAU;YAC1B,OAAO;QACX;QACA,MAAM,WAAW,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC,QAAQ;QAClD,IAAI,oBAAoB,OAAO;YAC3B,OAAO,SAAS,GAAG,CAAC,aAAa,IAAI,CAAC;QAC1C;QACA,OAAO,YAAY;IACvB;IAEA,SAAS,UAAU,QAAuB;QACtC,MAAM,kBAAkB,SAAS,GAAG,CAAC,CAAA;YACjC,IAAI,QAAQ,IAAI,KAAK,UAAU,QAAQ,KAAK,CAAC,QAAQ,EAAE;gBACnD,IAAI,OAAO,YAAY;gBACvB,IAAI,KAAK,QAAQ,CAAC,OAAO;oBACrB,MAAM,QAAQ,KAAK,KAAK,CAAC;oBACzB,qBAAO,CAAA,GAAA,6JAAA,CAAA,eAAY,AAAD,EACd,SACA,QAAQ,KAAK,gBACb;;0CACI,6LAAC;0CAAM,KAAK,CAAC,EAAE;;;;;;0CACf,6LAAC;;;;;0CACD,6LAAC;0CAAM,KAAK,CAAC,EAAE;;;;;;;;gBAG3B;YACJ;YACA,OAAO;QACX;QACA,OAAO;IACX;IAEA,WAAW,UAAU;IAErB,qBAAO,6LAAC;kBAAM;;;;;;AAClB;AAEO,SAAS,2BAA2B,OAAe,EAAE;IACxD,OAAO,KAAK,OAAO,CAAC,mBAAmB;AAC3C", "debugId": null}}, {"offset": {"line": 880, "column": 0}, "map": {"version": 3, "sources": ["file:///app/utils/Parser/ParseBMConfig.tsx"], "sourcesContent": ["import { DEFAULT_FLIP_SETTINGS } from '../FlipUtils'\n\ntype BMBlacklist = string[]\n\ntype BMWhitelist = {\n    [name: string]: {\n        profit?: number\n        profit_percentage?: number\n    }\n}\n\ntype BMTrueBlacklist = string[]\n\ntype BMFilter = {\n    blacklist: BMBlacklist\n    whitelist: BMWhitelist\n    true_blacklist: BMTrueBlacklist\n    global: {\n        profit: number\n        profit_percentage: number\n    }\n}\n\n// probably a better way to standardize this\nconst enchants = new Set(['cleave', 'life_steal', 'smite', 'sharpness', 'titan_killer', 'giant_killer', 'growth', 'protection', 'overload', 'power'])\n// bm doenst match on stuff like bustling\nconst reforges = new Set(['mossy'])\nconst attributes = new Set([\n    'mana_pool',\n    'arachno_resistance',\n    'blazing_resistance',\n    'breeze',\n    'ender_resistance',\n    'experience',\n    'fortitude',\n    'lifeline',\n    'magic_find',\n    'blazing_resistance',\n    'life_regeneration',\n    'mana_regeneration',\n    'mending',\n    'speed',\n    'undead_resistance',\n    'veteran',\n    'dominance'\n])\n\n// return the filter:{...} portion\nconst parseModifiers = (name: string, modifiers: string): { [modifier: string]: string } => {\n    const filter = {}\n    if (modifiers.length == 0) {\n        return filter\n    }\n    const modifiersList = modifiers.split('&')\n    for (const modifier of modifiersList) {\n        const tokens = modifier.split(':')\n        // weird case\n        if (tokens[0] == 'ultimate_reiterate') {\n            tokens[0] = 'ultimate_duplex'\n        }\n        if (tokens[0] == 'nil') {\n            filter['Clean'] = 'yes'\n        }\n        // sometimes OFA doesnt use a secondary identifier\n        else if (tokens.length == 1 && !reforges.has(tokens[0])) {\n            filter[tokens[0]] = '>0'\n        } else {\n            if (tokens.length != 2) throw new Error(`malformed bm config at current modifier ${modifier} at ${modifiers} on ${name}`)\n            const [arg, val] = tokens\n            if (arg == 'rarity_upgraded') {\n                filter['Recombobulated'] = val\n            } else if (arg == 'stars') {\n                // can only identify one level at a time in bm\n                filter['Stars'] = `${val}-${val}`\n            } else if (arg.startsWith('ultimate_')) {\n                filter[arg] = `${val}-${val}`\n            } else if (arg == 'candyUsed') {\n                if (val == 'false') {\n                    filter['Candy'] = '0'\n                } else {\n                    filter['Candy'] = '1-10'\n                }\n            } else if (arg == 'rounded_level') {\n                filter['PetLevel'] = `<=${val}`\n            } else if (arg == 'heldItem') {\n                filter['PetItem'] = val\n            } else if (enchants.has(arg) || attributes.has(arg)) {\n                filter[arg] = `${val}-${val}`\n            } else if (reforges.has(arg)) {\n                filter['Reforge'] = arg\n            } else if (arg == 'winning_bid_value') {\n                const maxBid = name == 'MIDAS_STAFF' ? 100 : 50\n                let winningBid\n                if (val == 'high') {\n                    winningBid = `>=${maxBid}m`\n                } else if (val == 'medium') {\n                    winningBid = `${((maxBid * 2) / 3).toFixed(4)}m-${maxBid}m`\n                } else {\n                    winningBid = `0-${((maxBid * 2) / 3).toFixed(4)}m`\n                }\n                filter['WinningBid'] = winningBid\n            } else if (arg !== 'global') {\n                throw new Error(`error parsing token ${modifier} at ${modifiers} on ${name}`)\n            }\n        }\n    }\n    return filter\n}\n\nconst petRarities = new Set(['COMMON', 'UNCOMMON', 'RARE', 'EPIC', 'LEGENDARY'])\n\nexport const parseBMName = (entry: string): { item: Item; itemFilter: ItemFilter } | undefined => {\n    let [name, modifiers] = entry.includes('=') ? entry.split('=') : [entry, '']\n    const filter: ItemFilter = {}\n    // handle pets\n    if (name.startsWith('PET_') && !name.startsWith('PET_ITEM_') && petRarities.has(name.substring(name.lastIndexOf('_') + 1))) {\n        const i = name.lastIndexOf('_')\n        const rarity = name.substring(i + 1)\n        name = name.substring(0, i)\n        filter['Rarity'] = rarity\n    }\n    if (/^.*_RUNE_\\d$/.test(name)) {\n        const i = name.lastIndexOf('_')\n        const level = name.substring(i + 1)\n        name = name.substring(0, i)\n        const incorrectIDReplacements = {\n            PESTILENCE_RUNE: 'ZOMBIE_SLAYER_RUNE',\n            MAGICAL_RUNE: 'MAGIC_RUNE'\n        }\n        // mistake in common filters\n        if (name in incorrectIDReplacements) {\n            name = incorrectIDReplacements[name]\n        }\n        const suffixIndex = name.indexOf('_RUNE')\n        let shortenedName = name.substring(0, suffixIndex)\n        name = `RUNE_${shortenedName}`\n        // end rune is common error, blood rune item id coded incorrect, rest dont have levels yet\n        const exceptions = new Set(['RUNE_END', 'RUNE_BLOOD', 'RUNE_HEARTS', 'RUNE_ICE', 'RUNE_SNOW', 'RUNE_MAGIC', 'RUNE_ZOMBIE_SLAYER'])\n        if (exceptions.has(name)) {\n            return\n        }\n        const levelNameReplacements = {\n            RUNE_DRAGON: 'END',\n            RUNE_HEARTS: 'COEURS',\n            RUNE_ICE: 'GLACE',\n            RUNE_SNOW: 'NEVE',\n            ZOMBIE_SLAYER: 'PESTILENCE',\n            RUNE_MAGIC: 'MAGICAL'\n        }\n        if (name in levelNameReplacements) {\n            shortenedName = levelNameReplacements[name]\n        }\n        const runeLevel = `${shortenedName}_RUNE`.toLowerCase().replace(/(_.)|^./g, group => group.toUpperCase().replace('_', ''))\n        filter[runeLevel] = level\n    }\n    if (name.startsWith('POTION_')) {\n        const i = name.indexOf('_')\n        name = `POTION_${name.substring(i + 1, name.length)}`\n    }\n    // common error in popular filter\n    if (name.startsWith('STARRED_FROZEN_BLAZE')) {\n        const i = name.indexOf('_')\n        name = name.substring(i + 1, name.length)\n    }\n    // random exceptions\n    const replacements = {\n        PIONEER_PICKAXE: 'ALPHA_PICK',\n        FLAKE_THE_FISH: 'SNOWFLAKE_THE_FISH',\n        SPIRIT_SCEPTRE: 'BAT_WAND',\n        // a common filter has these typo so i will fix it here\n        POCKET_ESPRESSO_MACINE: 'POCKET_ESPRESSO_MACHINE',\n        ADVENT_CALENDER_DISPLAY: 'ADVENT_CALENDAR_DISPLAY'\n    }\n    if (name in replacements) name = replacements[name]\n    // handle modifiers\n    const modifierFilter = parseModifiers(name, modifiers)\n    return { item: { tag: name }, itemFilter: { ...filter, ...modifierFilter } }\n}\n\nexport const parseBMConfig = (\n    input: BMFilter\n): {\n    filter: FlipperFilter\n    flipCustomizeSettings: FlipCustomizeSettings\n    restrictions: FlipRestriction[]\n} => {\n    const output = {\n        filter: DEFAULT_FLIP_SETTINGS.FILTER,\n        flipCustomizeSettings: DEFAULT_FLIP_SETTINGS.FLIP_CUSTOMIZE,\n        restrictions: DEFAULT_FLIP_SETTINGS.RESTRICTIONS\n    }\n    // true_blacklist -> ForceBlacklist\n    for (const TBLEntry of input.true_blacklist) {\n        output.restrictions.push({\n            type: 'blacklist' as const,\n            item: { tag: TBLEntry } as Item,\n            itemFilter: {\n                ForceBlacklist: 'true'\n            }\n        })\n    }\n    // regular blacklist\n    for (const BLEntry of input.blacklist) {\n        const parsed = parseBMName(BLEntry)\n        if (parsed)\n            output.restrictions.push({\n                type: 'blacklist' as const,\n                ...parsed\n            })\n    }\n    for (const [WLEntry, { profit, profit_percentage }] of Object.entries(input.whitelist)) {\n        const parsed = parseBMName(WLEntry)\n        if (parsed) {\n            const entry = {\n                type: 'whitelist' as const,\n                ...parsed\n            }\n            entry['itemFilter']['MinProfit'] = `${profit}`\n            entry['itemFilter']['MinProfitPercentage'] = `${profit_percentage}`\n            output.restrictions.push(entry)\n        }\n    }\n    output.filter.minProfit = input.global.profit\n    output.filter.minProfitPercent = input.global.profit_percentage\n    // probably more elegant way to do this\n    output.filter.maxCost = 10 ** 10\n    return output\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAuBA,4CAA4C;AAC5C,MAAM,WAAW,IAAI,IAAI;IAAC;IAAU;IAAc;IAAS;IAAa;IAAgB;IAAgB;IAAU;IAAc;IAAY;CAAQ;AACpJ,yCAAyC;AACzC,MAAM,WAAW,IAAI,IAAI;IAAC;CAAQ;AAClC,MAAM,aAAa,IAAI,IAAI;IACvB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACH;AAED,kCAAkC;AAClC,MAAM,iBAAiB,CAAC,MAAc;IAClC,MAAM,SAAS,CAAC;IAChB,IAAI,UAAU,MAAM,IAAI,GAAG;QACvB,OAAO;IACX;IACA,MAAM,gBAAgB,UAAU,KAAK,CAAC;IACtC,KAAK,MAAM,YAAY,cAAe;QAClC,MAAM,SAAS,SAAS,KAAK,CAAC;QAC9B,aAAa;QACb,IAAI,MAAM,CAAC,EAAE,IAAI,sBAAsB;YACnC,MAAM,CAAC,EAAE,GAAG;QAChB;QACA,IAAI,MAAM,CAAC,EAAE,IAAI,OAAO;YACpB,MAAM,CAAC,QAAQ,GAAG;QACtB,OAEK,IAAI,OAAO,MAAM,IAAI,KAAK,CAAC,SAAS,GAAG,CAAC,MAAM,CAAC,EAAE,GAAG;YACrD,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG;QACxB,OAAO;YACH,IAAI,OAAO,MAAM,IAAI,GAAG,MAAM,IAAI,MAAM,CAAC,wCAAwC,EAAE,SAAS,IAAI,EAAE,UAAU,IAAI,EAAE,MAAM;YACxH,MAAM,CAAC,KAAK,IAAI,GAAG;YACnB,IAAI,OAAO,mBAAmB;gBAC1B,MAAM,CAAC,iBAAiB,GAAG;YAC/B,OAAO,IAAI,OAAO,SAAS;gBACvB,8CAA8C;gBAC9C,MAAM,CAAC,QAAQ,GAAG,GAAG,IAAI,CAAC,EAAE,KAAK;YACrC,OAAO,IAAI,IAAI,UAAU,CAAC,cAAc;gBACpC,MAAM,CAAC,IAAI,GAAG,GAAG,IAAI,CAAC,EAAE,KAAK;YACjC,OAAO,IAAI,OAAO,aAAa;gBAC3B,IAAI,OAAO,SAAS;oBAChB,MAAM,CAAC,QAAQ,GAAG;gBACtB,OAAO;oBACH,MAAM,CAAC,QAAQ,GAAG;gBACtB;YACJ,OAAO,IAAI,OAAO,iBAAiB;gBAC/B,MAAM,CAAC,WAAW,GAAG,CAAC,EAAE,EAAE,KAAK;YACnC,OAAO,IAAI,OAAO,YAAY;gBAC1B,MAAM,CAAC,UAAU,GAAG;YACxB,OAAO,IAAI,SAAS,GAAG,CAAC,QAAQ,WAAW,GAAG,CAAC,MAAM;gBACjD,MAAM,CAAC,IAAI,GAAG,GAAG,IAAI,CAAC,EAAE,KAAK;YACjC,OAAO,IAAI,SAAS,GAAG,CAAC,MAAM;gBAC1B,MAAM,CAAC,UAAU,GAAG;YACxB,OAAO,IAAI,OAAO,qBAAqB;gBACnC,MAAM,SAAS,QAAQ,gBAAgB,MAAM;gBAC7C,IAAI;gBACJ,IAAI,OAAO,QAAQ;oBACf,aAAa,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;gBAC/B,OAAO,IAAI,OAAO,UAAU;oBACxB,aAAa,GAAG,CAAC,AAAC,SAAS,IAAK,CAAC,EAAE,OAAO,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,CAAC;gBAC/D,OAAO;oBACH,aAAa,CAAC,EAAE,EAAE,CAAC,AAAC,SAAS,IAAK,CAAC,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;gBACtD;gBACA,MAAM,CAAC,aAAa,GAAG;YAC3B,OAAO,IAAI,QAAQ,UAAU;gBACzB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,IAAI,EAAE,UAAU,IAAI,EAAE,MAAM;YAChF;QACJ;IACJ;IACA,OAAO;AACX;AAEA,MAAM,cAAc,IAAI,IAAI;IAAC;IAAU;IAAY;IAAQ;IAAQ;CAAY;AAExE,MAAM,cAAc,CAAC;IACxB,IAAI,CAAC,MAAM,UAAU,GAAG,MAAM,QAAQ,CAAC,OAAO,MAAM,KAAK,CAAC,OAAO;QAAC;QAAO;KAAG;IAC5E,MAAM,SAAqB,CAAC;IAC5B,cAAc;IACd,IAAI,KAAK,UAAU,CAAC,WAAW,CAAC,KAAK,UAAU,CAAC,gBAAgB,YAAY,GAAG,CAAC,KAAK,SAAS,CAAC,KAAK,WAAW,CAAC,OAAO,KAAK;QACxH,MAAM,IAAI,KAAK,WAAW,CAAC;QAC3B,MAAM,SAAS,KAAK,SAAS,CAAC,IAAI;QAClC,OAAO,KAAK,SAAS,CAAC,GAAG;QACzB,MAAM,CAAC,SAAS,GAAG;IACvB;IACA,IAAI,eAAe,IAAI,CAAC,OAAO;QAC3B,MAAM,IAAI,KAAK,WAAW,CAAC;QAC3B,MAAM,QAAQ,KAAK,SAAS,CAAC,IAAI;QACjC,OAAO,KAAK,SAAS,CAAC,GAAG;QACzB,MAAM,0BAA0B;YAC5B,iBAAiB;YACjB,cAAc;QAClB;QACA,4BAA4B;QAC5B,IAAI,QAAQ,yBAAyB;YACjC,OAAO,uBAAuB,CAAC,KAAK;QACxC;QACA,MAAM,cAAc,KAAK,OAAO,CAAC;QACjC,IAAI,gBAAgB,KAAK,SAAS,CAAC,GAAG;QACtC,OAAO,CAAC,KAAK,EAAE,eAAe;QAC9B,0FAA0F;QAC1F,MAAM,aAAa,IAAI,IAAI;YAAC;YAAY;YAAc;YAAe;YAAY;YAAa;YAAc;SAAqB;QACjI,IAAI,WAAW,GAAG,CAAC,OAAO;YACtB;QACJ;QACA,MAAM,wBAAwB;YAC1B,aAAa;YACb,aAAa;YACb,UAAU;YACV,WAAW;YACX,eAAe;YACf,YAAY;QAChB;QACA,IAAI,QAAQ,uBAAuB;YAC/B,gBAAgB,qBAAqB,CAAC,KAAK;QAC/C;QACA,MAAM,YAAY,GAAG,cAAc,KAAK,CAAC,CAAC,WAAW,GAAG,OAAO,CAAC,YAAY,CAAA,QAAS,MAAM,WAAW,GAAG,OAAO,CAAC,KAAK;QACtH,MAAM,CAAC,UAAU,GAAG;IACxB;IACA,IAAI,KAAK,UAAU,CAAC,YAAY;QAC5B,MAAM,IAAI,KAAK,OAAO,CAAC;QACvB,OAAO,CAAC,OAAO,EAAE,KAAK,SAAS,CAAC,IAAI,GAAG,KAAK,MAAM,GAAG;IACzD;IACA,iCAAiC;IACjC,IAAI,KAAK,UAAU,CAAC,yBAAyB;QACzC,MAAM,IAAI,KAAK,OAAO,CAAC;QACvB,OAAO,KAAK,SAAS,CAAC,IAAI,GAAG,KAAK,MAAM;IAC5C;IACA,oBAAoB;IACpB,MAAM,eAAe;QACjB,iBAAiB;QACjB,gBAAgB;QAChB,gBAAgB;QAChB,uDAAuD;QACvD,wBAAwB;QACxB,yBAAyB;IAC7B;IACA,IAAI,QAAQ,cAAc,OAAO,YAAY,CAAC,KAAK;IACnD,mBAAmB;IACnB,MAAM,iBAAiB,eAAe,MAAM;IAC5C,OAAO;QAAE,MAAM;YAAE,KAAK;QAAK;QAAG,YAAY;YAAE,GAAG,MAAM;YAAE,GAAG,cAAc;QAAC;IAAE;AAC/E;AAEO,MAAM,gBAAgB,CACzB;IAMA,MAAM,SAAS;QACX,QAAQ,sHAAA,CAAA,wBAAqB,CAAC,MAAM;QACpC,uBAAuB,sHAAA,CAAA,wBAAqB,CAAC,cAAc;QAC3D,cAAc,sHAAA,CAAA,wBAAqB,CAAC,YAAY;IACpD;IACA,mCAAmC;IACnC,KAAK,MAAM,YAAY,MAAM,cAAc,CAAE;QACzC,OAAO,YAAY,CAAC,IAAI,CAAC;YACrB,MAAM;YACN,MAAM;gBAAE,KAAK;YAAS;YACtB,YAAY;gBACR,gBAAgB;YACpB;QACJ;IACJ;IACA,oBAAoB;IACpB,KAAK,MAAM,WAAW,MAAM,SAAS,CAAE;QACnC,MAAM,SAAS,YAAY;QAC3B,IAAI,QACA,OAAO,YAAY,CAAC,IAAI,CAAC;YACrB,MAAM;YACN,GAAG,MAAM;QACb;IACR;IACA,KAAK,MAAM,CAAC,SAAS,EAAE,MAAM,EAAE,iBAAiB,EAAE,CAAC,IAAI,OAAO,OAAO,CAAC,MAAM,SAAS,EAAG;QACpF,MAAM,SAAS,YAAY;QAC3B,IAAI,QAAQ;YACR,MAAM,QAAQ;gBACV,MAAM;gBACN,GAAG,MAAM;YACb;YACA,KAAK,CAAC,aAAa,CAAC,YAAY,GAAG,GAAG,QAAQ;YAC9C,KAAK,CAAC,aAAa,CAAC,sBAAsB,GAAG,GAAG,mBAAmB;YACnE,OAAO,YAAY,CAAC,IAAI,CAAC;QAC7B;IACJ;IACA,OAAO,MAAM,CAAC,SAAS,GAAG,MAAM,MAAM,CAAC,MAAM;IAC7C,OAAO,MAAM,CAAC,gBAAgB,GAAG,MAAM,MAAM,CAAC,iBAAiB;IAC/D,uCAAuC;IACvC,OAAO,MAAM,CAAC,OAAO,GAAG,MAAM;IAC9B,OAAO;AACX", "debugId": null}}, {"offset": {"line": 1127, "column": 0}, "map": {"version": 3, "sources": ["file:///app/utils/SettingsUtils.tsx"], "sourcesContent": ["import { toast } from 'react-toastify'\nimport api from '../api/ApiHelper'\nimport { CUSTOM_EVENTS } from '../api/ApiTypes.d'\nimport { hasFlag } from '../components/FilterElement/FilterType'\nimport { DEFAULT_FLIP_SETTINGS, FLIP_FINDERS } from './FlipUtils'\nimport { isClientSideRendering } from './SSRUtils'\nimport { getNumberFromShortenString } from './Formatter'\nimport { parseBMConfig } from './Parser/ParseBMConfig'\n\nconst LOCAL_STORAGE_SETTINGS_KEY = 'userSettings'\n\nlet settings = getInitUserSettings()\n\nfunction getInitUserSettings(): any {\n    if (!isClientSideRendering()) {\n        return {}\n    }\n\n    let item = localStorage.getItem(LOCAL_STORAGE_SETTINGS_KEY)\n\n    // item === \"\\\"{}\\\"\" is a wrong state and has to be reset (fix for users that still have this in the local storage)\n    if (!item || item === '\"{}\"') {\n        item = '{}'\n        localStorage.setItem(LOCAL_STORAGE_SETTINGS_KEY, item)\n    }\n    try {\n        return JSON.parse(item)\n    } catch {\n        return {}\n    }\n}\n\nexport function getSetting(key: string, defaultValue: any = ''): string {\n    return settings[key] || defaultValue\n}\n\nexport function getSettingsObject<T>(key: string, defaultValue: T) {\n    if (!isClientSideRendering()) {\n        return defaultValue\n    }\n    let object = settings[key] || JSON.stringify(defaultValue)\n    let parsed: T\n    try {\n        parsed = JSON.parse(object)\n    } catch {\n        parsed = defaultValue\n    }\n    return parsed\n}\n\nexport function setSetting(key: any, value: any) {\n    if (!isClientSideRendering()) {\n        return\n    }\n    settings[key] = value\n    localStorage.setItem(LOCAL_STORAGE_SETTINGS_KEY, JSON.stringify(settings))\n}\n\nexport function setSettingsFromServerSide(\n    settings: any,\n    updateLocalSettings = true\n): Promise<{ flipCustomizing: FlipCustomizeSettings; filter: FlipperFilter; restrictions: FlipRestriction[] }> {\n    return new Promise(resolve => {\n        settings.visibility = settings.visibility || {}\n        settings.mod = settings.mod || {}\n        settings.filters = settings.filters || {}\n\n        let flipCustomizing = {\n            hideCost: !settings.visibility.cost,\n            hideEstimatedProfit: !settings.visibility.estProfit,\n            hideLowestBin: !settings.visibility.lbin,\n            hideSecondLowestBin: !settings.visibility.slbin,\n            hideMedianPrice: !settings.visibility.medPrice,\n            hideSeller: !settings.visibility.seller,\n            hideVolume: !settings.visibility.volume,\n            maxExtraInfoFields: settings.visibility.extraFields,\n            hideProfitPercent: !settings.visibility.profitPercent,\n            hideSellerOpenBtn: !settings.visibility.sellerOpenBtn,\n            hideLore: !settings.visibility.lore,\n            useLowestBinForProfit: settings.lbin,\n            shortNumbers: settings.mod.shortNumbers,\n            soundOnFlip: settings.mod.soundOnFlip,\n            justProfit: settings.mod.justProfit,\n            blockTenSecMsg: settings.mod.blockTenSecMsg,\n            hideModChat: !settings.mod.chat,\n            modFormat: settings.mod.format,\n            modCountdown: settings.mod.countdown,\n            disableLinks: !settings.visibility.links,\n            hideCopySuccessMessage: !settings.visibility.copySuccessMessage,\n            finders: FLIP_FINDERS.filter(finder => {\n                return hasFlag(parseInt(settings.finders), parseInt(finder.value))\n            }).map(finder => parseInt(finder.value)),\n            blockExport: settings.blockExport\n        } as FlipCustomizeSettings\n\n        let filter = {\n            maxCost: settings.maxCost,\n            minProfit: settings.minProfit,\n            minProfitPercent: settings.minProfitPercent,\n            minVolume: settings.minVolume,\n            onlyBin: settings.onlyBin,\n            onlyUnsold: settings.visibility.hideSold\n        } as FlipperFilter\n\n        let restrictions = getSettingsObject<FlipRestriction[]>(RESTRICTIONS_SETTINGS_KEY, [])\n        let itemMap = {}\n\n        restrictions.forEach(restriction => {\n            if (restriction.item?.tag) {\n                itemMap[restriction.item.tag] = restriction.item?.name\n            }\n        })\n\n        let _addListToRestrictions = async function (list, type): Promise<FlipRestriction[]> {\n            return new Promise((resolve, reject) => {\n                if (list) {\n                    let newRestrictions: FlipRestriction[] = []\n                    let tagsToFindNamesFor = new Set()\n                    let restrictionsToLoadNamesFor: FlipRestriction[] = []\n                    list.forEach(item => {\n                        let itemName = item.displayName || itemMap[item.tag]\n                        if (!item.tag) {\n                            newRestrictions.push({\n                                type: type,\n                                itemFilter: item.filter,\n                                tags: item.tags,\n                                disabled: item.disabled\n                            })\n                        } else if (itemName && item.tag) {\n                            newRestrictions.push({\n                                type: type,\n                                item: {\n                                    tag: item.tag,\n                                    name: itemName,\n                                    iconUrl: api.getItemImageUrl(item)\n                                },\n                                itemFilter: item.filter,\n                                tags: item.tags,\n                                disabled: item.disabled\n                            })\n                        } else {\n                            tagsToFindNamesFor.add(item.tag)\n                            restrictionsToLoadNamesFor.push({\n                                type: type,\n                                item: {\n                                    tag: item.tag,\n                                    name: '',\n                                    iconUrl: api.getItemImageUrl(item)\n                                },\n                                itemFilter: item.filter,\n                                tags: item.tags,\n                                disabled: item.disabled\n                            })\n                        }\n                    })\n                    if (tagsToFindNamesFor.size > 0) {\n                        let tags = Array.from(tagsToFindNamesFor)\n                        api.getItemNames(\n                            tags.map(tag => {\n                                return { tag: tag } as Item\n                            })\n                        ).then(nameMap => {\n                            restrictionsToLoadNamesFor.forEach(newRestriction => {\n                                newRestriction.item!.name = nameMap[newRestriction.item!.tag]\n                                newRestrictions.push(newRestriction)\n                            })\n                            resolve(newRestrictions)\n                        })\n                    } else {\n                        resolve(newRestrictions)\n                    }\n                } else {\n                    resolve([])\n                }\n            })\n        }\n\n        if (updateLocalSettings) {\n            setSetting(FLIP_CUSTOMIZING_KEY, JSON.stringify(flipCustomizing))\n            setSetting(FLIPPER_FILTER_KEY, JSON.stringify(filter))\n        }\n        Promise.all([_addListToRestrictions(settings.whitelist, 'whitelist'), _addListToRestrictions(settings.blacklist, 'blacklist')]).then(results => {\n            let newRestrictions = getCleanRestrictionsForApi(results[0].concat(results[1]))\n\n            if (updateLocalSettings) {\n                let newRestrictionsString = JSON.stringify(newRestrictions)\n                let oldRestrictionsString = getSetting(RESTRICTIONS_SETTINGS_KEY, '[]')\n\n                setSetting(RESTRICTIONS_SETTINGS_KEY, JSON.stringify(newRestrictions))\n\n                if (newRestrictionsString !== oldRestrictionsString) {\n                    document.dispatchEvent(new CustomEvent(CUSTOM_EVENTS.FLIP_SETTINGS_CHANGE, { detail: { apiUpdate: true } }))\n                }\n            }\n            resolve({\n                filter,\n                flipCustomizing,\n                restrictions: newRestrictions\n            })\n        })\n    })\n}\n\nexport async function handleSettingsImport(importString: string) {\n    let filter: FlipperFilter = DEFAULT_FLIP_SETTINGS.FILTER\n    let flipCustomizeSettings: FlipCustomizeSettings = DEFAULT_FLIP_SETTINGS.FLIP_CUSTOMIZE\n    let restrictions: FlipRestriction[] = DEFAULT_FLIP_SETTINGS.RESTRICTIONS\n    let promises: Promise<any>[] = []\n    try {\n        let importObject = JSON.parse(importString)\n        // Check for global field (BM format)\n        if (importObject.global !== undefined) {\n            const converted = parseBMConfig(importObject)\n            filter = converted.filter\n            flipCustomizeSettings = converted.flipCustomizeSettings\n            restrictions = converted.restrictions\n        } else if (importObject.whitelist !== undefined) {\n            // Handle import in server-side format\n            let settings = await setSettingsFromServerSide(importObject, false)\n            filter = settings.filter\n            flipCustomizeSettings = settings.flipCustomizing\n            restrictions = settings.restrictions\n        } else {\n            // Handle import in client-side format\n            filter = importObject[FLIPPER_FILTER_KEY] ? JSON.parse(importObject[FLIPPER_FILTER_KEY]) : {}\n            flipCustomizeSettings = importObject[FLIP_CUSTOMIZING_KEY] ? JSON.parse(importObject[FLIP_CUSTOMIZING_KEY]) : {}\n            restrictions = importObject[RESTRICTIONS_SETTINGS_KEY] ? JSON.parse(importObject[RESTRICTIONS_SETTINGS_KEY]) : []\n        }\n    } catch (e) {\n        // Handle toml settings import\n        try {\n            var json = (await import('toml')).parse(importString)\n\n            if (json.thresholds.blacklist.blacklist_bypass_percent) {\n                restrictions.push({\n                    type: 'whitelist',\n                    itemFilter: {\n                        MinProfitPercentage: (json.thresholds.blacklist.blacklist_bypass_percent * 100).toString()\n                    }\n                })\n            }\n\n            if (json.thresholds.blacklist.blacklist_bypass_profit) {\n                restrictions.push({\n                    type: 'whitelist',\n                    itemFilter: {\n                        MinProfit: json.thresholds.blacklist.blacklist_bypass_profit.toString()\n                    }\n                })\n            }\n\n            if (json.thresholds.blacklist.blacklist_bypass_volume) {\n                restrictions.push({\n                    type: 'whitelist',\n                    itemFilter: {\n                        Volume: json.thresholds.blacklist.blacklist_bypass_volume.toString()\n                    }\n                })\n            }\n\n            if (json.thresholds.blacklist.user_blacklist) {\n                json.thresholds.blacklist.user_blacklist.split(',').forEach(user => {\n                    restrictions.push({\n                        type: 'blacklist',\n                        itemFilter: {\n                            Seller: user\n                        }\n                    })\n                })\n            }\n\n            json.thresholds.blacklist.enchant_blacklist.split(',').forEach(item => {\n                let restriction: FlipRestriction = {\n                    type: 'blacklist'\n                }\n                let split = item.split('-')\n                if (split[0].length > 0) {\n                    restriction.itemFilter = {\n                        Enchantment: split[0]\n                    }\n\n                    if (split[1] && split[1].length > 0) {\n                        restriction.itemFilter.EnchantLvl = split[1]\n                    }\n                }\n                restrictions.push(restriction)\n            })\n            let tagsToLoad = new Set()\n            let entriesToLoadNamesFor: FlipRestriction[] = []\n            json.flipping.others.blacklist.split(',').forEach(item => {\n                let restriction: FlipRestriction = {\n                    type: 'blacklist'\n                }\n                let split = item.split('_+_')\n                if (split[0].length > 0) {\n                    let split2 = split[0].split('==')\n                    if (split2[1] && split2[1].length > 0) {\n                        restriction.itemFilter = {\n                            Stars: split2[1].split('_STARRED_')[0]\n                        }\n                    }\n                    restriction.item = {\n                        tag: split2[0]\n                    }\n                }\n                if (split[1] && split[1].length > 0) {\n                    if (!restriction.itemFilter) {\n                        restriction.itemFilter = {}\n                    }\n                    restriction.itemFilter.Rarity = split[1]\n                }\n                if (restriction.item?.tag) {\n                    tagsToLoad.add(restriction.item?.tag)\n                    entriesToLoadNamesFor.push(restriction)\n                } else {\n                    restrictions.push(restriction)\n                }\n            })\n\n            let nameMap = await api.getItemNames(\n                Array.from(tagsToLoad).map(tag => {\n                    return { tag: tag } as Item\n                })\n            )\n            entriesToLoadNamesFor.forEach(entry => {\n                entry.item!.name = nameMap[entry.item!.tag]\n                entry.item!.iconUrl = api.getItemImageUrl({\n                    tag: entry.item!.tag\n                })\n                restrictions.push(entry)\n            })\n\n            flipCustomizeSettings.soundOnFlip = !!json.flipping.others.enable_sounds\n            flipCustomizeSettings.hideModChat = !json.flipping.others.enable_chat\n            filter.minProfit = getNumberFromShortenString(json.thresholds.threshold.threshold)\n            filter.minProfitPercent = json.thresholds.threshold.threshold_percentage * 100\n            filter.maxCost = getNumberFromShortenString(json.thresholds.threshold.max_cost)\n        } catch {\n            toast.error('The import of the filter settings failed. Please make sure this is a valid filter file.')\n            return\n        }\n    }\n\n    if (flipCustomizeSettings.finders && localStorage.getItem('disableRiskyFinderImportProtection') !== 'true') {\n        // These finders are considered \"safe\". All others are removed when importing a config\n        // \"1\" = \"Flipper\", \"2\" = \"Sniper\", \"4\" = \"Sniper (Median)\"\n        const safe_finders = ['1', '2', '4']\n\n        let removed: string[] = []\n        let newFinders = flipCustomizeSettings.finders.filter(finder => {\n            let isSafe = safe_finders.includes(finder.toString())\n            if (!isSafe) {\n                removed.push(FLIP_FINDERS.find(f => f.value === finder.toString())!.label)\n            }\n            return isSafe\n        })\n        if (removed.length > 0) {\n            toast.warn(\n                `Removed potentially dangerous finder${removed.length > 1 ? 's' : ''} (${removed.toString()}). Re-add them if you know what you are doing.`\n            )\n            await sleep(5000)\n        }\n        flipCustomizeSettings.finders = newFinders\n    }\n\n    await Promise.allSettled(promises)\n\n    if (restrictions.length > 1000) {\n        toast('You are importing a large config! This may take a while...', {\n            type: 'info',\n            autoClose: false\n        })\n    }\n\n    let toastId = toast('Uploading config...', {\n        type: 'info',\n        autoClose: false\n    })\n\n    api.subscribeFlips(\n        restrictions || [],\n        filter,\n        flipCustomizeSettings,\n        undefined,\n        undefined,\n        undefined,\n        () => {\n            setSetting(FLIPPER_FILTER_KEY, JSON.stringify(filter))\n            setSetting(FLIP_CUSTOMIZING_KEY, JSON.stringify(flipCustomizeSettings))\n            setSetting(RESTRICTIONS_SETTINGS_KEY, JSON.stringify(getCleanRestrictionsForApi(restrictions)))\n            window.location.reload()\n        },\n        () => {\n            toast.dismiss(toastId)\n        },\n        true\n    )\n}\n\nexport function sleep(ms: number): Promise<void> {\n    return new Promise(resolve => {\n        setTimeout(() => {\n            resolve()\n        }, ms)\n    })\n}\n\nexport function mapSettingsToApiFormat(filter: FlipperFilter, flipSettings: FlipCustomizeSettings, restrictions: FlipRestriction[]) {\n    return {\n        whitelist: mapRestrictionsToApiFormat(restrictions.filter(restriction => restriction.type === 'whitelist')),\n        blacklist: mapRestrictionsToApiFormat(restrictions.filter(restriction => restriction.type === 'blacklist')),\n        minProfit: filter.minProfit || 0,\n        minProfitPercent: filter.minProfitPercent || 0,\n        minVolume: filter.minVolume || 0,\n        maxCost: filter.maxCost || 0,\n        onlyBin: filter.onlyBin,\n        lbin: flipSettings.useLowestBinForProfit,\n        mod: {\n            justProfit: flipSettings.justProfit,\n            soundOnFlip: flipSettings.soundOnFlip,\n            shortNumbers: flipSettings.shortNumbers,\n            blockTenSecMsg: flipSettings.blockTenSecMsg,\n            format: flipSettings.modFormat,\n            chat: !flipSettings.hideModChat,\n            countdown: flipSettings.modCountdown\n        },\n        visibility: {\n            cost: !flipSettings.hideCost,\n            estProfit: !flipSettings.hideEstimatedProfit,\n            lbin: !flipSettings.hideLowestBin,\n            slbin: !flipSettings.hideSecondLowestBin,\n            medPrice: !flipSettings.hideMedianPrice,\n            seller: !flipSettings.hideSeller,\n            volume: !flipSettings.hideVolume,\n            extraFields: flipSettings.maxExtraInfoFields || 0,\n            profitPercent: !flipSettings.hideProfitPercent,\n            sellerOpenBtn: !flipSettings.hideSellerOpenBtn,\n            lore: !flipSettings.hideLore,\n            copySuccessMessage: !flipSettings.hideCopySuccessMessage,\n            links: !flipSettings.disableLinks\n        },\n        blockExport: flipSettings.blockExport,\n        finders: flipSettings.finders?.reduce((a, b) => +a + +b, 0),\n        changer: window.sessionStorage.getItem('sessionId')\n    }\n}\n\nexport function mapRestrictionsToApiFormat(restrictions: FlipRestriction[]) {\n    return restrictions.map(restriction => {\n        return {\n            tag: restriction.item?.tag,\n            filter: restriction.itemFilter,\n            displayName: restriction.item?.name,\n            tags: restriction.tags,\n            disabled: restriction.disabled\n        }\n    })\n}\n\nexport function storeUsedTagsInLocalStorage(restrictions: FlipRestriction[]) {\n    let tags: Set<string> = new Set()\n    restrictions.forEach(restriction => {\n        if (restriction.tags) {\n            restriction.tags.forEach(tag => tags.add(tag))\n        }\n    })\n    localStorage.setItem(CURRENTLY_USED_TAGS, tags.size > 0 ? JSON.stringify(Array.from(tags)) : '[]')\n}\n\n/**\n * Removes private properties starting with a _ from the restrictions, because the backend cant handle these.\n * These also have to be saved into the localStorage because they could get sent to the api from there\n * @param restrictions The restrictions\n * @returns A new array containing restrictions without private properties\n */\nexport function getCleanRestrictionsForApi(restrictions: FlipRestriction[]) {\n    return restrictions.map(restriction => {\n        let newRestriction = {\n            type: restriction.type,\n            tags: restriction.tags,\n            disabled: restriction.disabled\n        } as FlipRestriction\n\n        if (restriction.item) {\n            newRestriction.item = {\n                tag: restriction.item?.tag,\n                name: restriction.item?.name\n            }\n        }\n\n        if (restriction.itemFilter) {\n            newRestriction.itemFilter = {}\n            Object.keys(restriction.itemFilter).forEach(key => {\n                if (!key.startsWith('_')) {\n                    newRestriction.itemFilter![key] = restriction.itemFilter![key]\n                }\n            })\n        }\n        return newRestriction\n    })\n}\n\nexport const FLIP_CUSTOMIZING_KEY = 'flipCustomizing'\nexport const RESTRICTIONS_SETTINGS_KEY = 'flipRestrictions'\nexport const FLIPPER_FILTER_KEY = 'flipperFilters'\nexport const PREMIUM_EXPIRATION_NOFIFY_DATE_KEY = 'premiumExpirationNotifyDate'\nexport const BAZAAR_GRAPH_TYPE = 'bazaarGraphType'\nexport const BAZAAR_GRAPH_LEGEND_SELECTION = 'bazaarGraphLegendSelection'\nexport const AUCTION_GRAPH_LEGEND_SELECTION = 'auctionGraphLegendSelection'\nexport const RECENT_AUCTIONS_FETCH_TYPE_KEY = 'recentAuctionsFetchType'\nexport const CANCELLATION_RIGHT_CONFIRMED = 'cancellationRightConfirmed'\nexport const LAST_USED_FILTER = 'lastUsedFilter'\nexport const IGNORE_FLIP_TRACKING_PROFIT = 'ignoreFlipTrackingProfit'\nexport const LAST_PREMIUM_PRODUCTS = 'lastPremiumProducts'\nexport const CURRENTLY_USED_TAGS = 'currentlyUsedTags'\nexport const HIDE_RELATED_ITEMS = 'hideRelatedItems'\nexport const GOOGLE_PROFILE_PICTURE_URL = 'googleProfilePictureUrl'\nexport const GOOGLE_EMAIL = 'googleEmail'\nexport const GOOGLE_NAME = 'googleName'\nexport const USER_COUNTRY_CODE = 'userCountryCode'\nexport const ITEM_FILTER_USE_COUNT = 'itemFilterUseCount'\nexport const ITEM_FILER_SHOW_ADVANCED = 'itemFilterShowAdvanced'\nexport const AUTO_REDIRECT_FROM_LINKVERTISE_EXPLANATION = 'autoRedirectFromLinkvertiseExplanation'\nexport const ITEM_ICON_TYPE = 'itemIconType'\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AAEA,MAAM,6BAA6B;AAEnC,IAAI,WAAW;AAEf,SAAS;IACL,IAAI,CAAC,CAAA,GAAA,qHAAA,CAAA,wBAAqB,AAAD,KAAK;QAC1B,OAAO,CAAC;IACZ;IAEA,IAAI,OAAO,aAAa,OAAO,CAAC;IAEhC,mHAAmH;IACnH,IAAI,CAAC,QAAQ,SAAS,QAAQ;QAC1B,OAAO;QACP,aAAa,OAAO,CAAC,4BAA4B;IACrD;IACA,IAAI;QACA,OAAO,KAAK,KAAK,CAAC;IACtB,EAAE,OAAM;QACJ,OAAO,CAAC;IACZ;AACJ;AAEO,SAAS,WAAW,GAAW,EAAE,eAAoB,EAAE;IAC1D,OAAO,QAAQ,CAAC,IAAI,IAAI;AAC5B;AAEO,SAAS,kBAAqB,GAAW,EAAE,YAAe;IAC7D,IAAI,CAAC,CAAA,GAAA,qHAAA,CAAA,wBAAqB,AAAD,KAAK;QAC1B,OAAO;IACX;IACA,IAAI,SAAS,QAAQ,CAAC,IAAI,IAAI,KAAK,SAAS,CAAC;IAC7C,IAAI;IACJ,IAAI;QACA,SAAS,KAAK,KAAK,CAAC;IACxB,EAAE,OAAM;QACJ,SAAS;IACb;IACA,OAAO;AACX;AAEO,SAAS,WAAW,GAAQ,EAAE,KAAU;IAC3C,IAAI,CAAC,CAAA,GAAA,qHAAA,CAAA,wBAAqB,AAAD,KAAK;QAC1B;IACJ;IACA,QAAQ,CAAC,IAAI,GAAG;IAChB,aAAa,OAAO,CAAC,4BAA4B,KAAK,SAAS,CAAC;AACpE;AAEO,SAAS,0BACZ,QAAa,EACb,sBAAsB,IAAI;IAE1B,OAAO,IAAI,QAAQ,CAAA;QACf,SAAS,UAAU,GAAG,SAAS,UAAU,IAAI,CAAC;QAC9C,SAAS,GAAG,GAAG,SAAS,GAAG,IAAI,CAAC;QAChC,SAAS,OAAO,GAAG,SAAS,OAAO,IAAI,CAAC;QAExC,IAAI,kBAAkB;YAClB,UAAU,CAAC,SAAS,UAAU,CAAC,IAAI;YACnC,qBAAqB,CAAC,SAAS,UAAU,CAAC,SAAS;YACnD,eAAe,CAAC,SAAS,UAAU,CAAC,IAAI;YACxC,qBAAqB,CAAC,SAAS,UAAU,CAAC,KAAK;YAC/C,iBAAiB,CAAC,SAAS,UAAU,CAAC,QAAQ;YAC9C,YAAY,CAAC,SAAS,UAAU,CAAC,MAAM;YACvC,YAAY,CAAC,SAAS,UAAU,CAAC,MAAM;YACvC,oBAAoB,SAAS,UAAU,CAAC,WAAW;YACnD,mBAAmB,CAAC,SAAS,UAAU,CAAC,aAAa;YACrD,mBAAmB,CAAC,SAAS,UAAU,CAAC,aAAa;YACrD,UAAU,CAAC,SAAS,UAAU,CAAC,IAAI;YACnC,uBAAuB,SAAS,IAAI;YACpC,cAAc,SAAS,GAAG,CAAC,YAAY;YACvC,aAAa,SAAS,GAAG,CAAC,WAAW;YACrC,YAAY,SAAS,GAAG,CAAC,UAAU;YACnC,gBAAgB,SAAS,GAAG,CAAC,cAAc;YAC3C,aAAa,CAAC,SAAS,GAAG,CAAC,IAAI;YAC/B,WAAW,SAAS,GAAG,CAAC,MAAM;YAC9B,cAAc,SAAS,GAAG,CAAC,SAAS;YACpC,cAAc,CAAC,SAAS,UAAU,CAAC,KAAK;YACxC,wBAAwB,CAAC,SAAS,UAAU,CAAC,kBAAkB;YAC/D,SAAS,sHAAA,CAAA,eAAY,CAAC,MAAM,CAAC,CAAA;gBACzB,OAAO,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EAAE,SAAS,SAAS,OAAO,GAAG,SAAS,OAAO,KAAK;YACpE,GAAG,GAAG,CAAC,CAAA,SAAU,SAAS,OAAO,KAAK;YACtC,aAAa,SAAS,WAAW;QACrC;QAEA,IAAI,SAAS;YACT,SAAS,SAAS,OAAO;YACzB,WAAW,SAAS,SAAS;YAC7B,kBAAkB,SAAS,gBAAgB;YAC3C,WAAW,SAAS,SAAS;YAC7B,SAAS,SAAS,OAAO;YACzB,YAAY,SAAS,UAAU,CAAC,QAAQ;QAC5C;QAEA,IAAI,eAAe,kBAAqC,2BAA2B,EAAE;QACrF,IAAI,UAAU,CAAC;QAEf,aAAa,OAAO,CAAC,CAAA;YACjB,IAAI,YAAY,IAAI,EAAE,KAAK;gBACvB,OAAO,CAAC,YAAY,IAAI,CAAC,GAAG,CAAC,GAAG,YAAY,IAAI,EAAE;YACtD;QACJ;QAEA,IAAI,yBAAyB,eAAgB,IAAI,EAAE,IAAI;YACnD,OAAO,IAAI,QAAQ,CAAC,SAAS;gBACzB,IAAI,MAAM;oBACN,IAAI,kBAAqC,EAAE;oBAC3C,IAAI,qBAAqB,IAAI;oBAC7B,IAAI,6BAAgD,EAAE;oBACtD,KAAK,OAAO,CAAC,CAAA;wBACT,IAAI,WAAW,KAAK,WAAW,IAAI,OAAO,CAAC,KAAK,GAAG,CAAC;wBACpD,IAAI,CAAC,KAAK,GAAG,EAAE;4BACX,gBAAgB,IAAI,CAAC;gCACjB,MAAM;gCACN,YAAY,KAAK,MAAM;gCACvB,MAAM,KAAK,IAAI;gCACf,UAAU,KAAK,QAAQ;4BAC3B;wBACJ,OAAO,IAAI,YAAY,KAAK,GAAG,EAAE;4BAC7B,gBAAgB,IAAI,CAAC;gCACjB,MAAM;gCACN,MAAM;oCACF,KAAK,KAAK,GAAG;oCACb,MAAM;oCACN,SAAS,oHAAA,CAAA,UAAG,CAAC,eAAe,CAAC;gCACjC;gCACA,YAAY,KAAK,MAAM;gCACvB,MAAM,KAAK,IAAI;gCACf,UAAU,KAAK,QAAQ;4BAC3B;wBACJ,OAAO;4BACH,mBAAmB,GAAG,CAAC,KAAK,GAAG;4BAC/B,2BAA2B,IAAI,CAAC;gCAC5B,MAAM;gCACN,MAAM;oCACF,KAAK,KAAK,GAAG;oCACb,MAAM;oCACN,SAAS,oHAAA,CAAA,UAAG,CAAC,eAAe,CAAC;gCACjC;gCACA,YAAY,KAAK,MAAM;gCACvB,MAAM,KAAK,IAAI;gCACf,UAAU,KAAK,QAAQ;4BAC3B;wBACJ;oBACJ;oBACA,IAAI,mBAAmB,IAAI,GAAG,GAAG;wBAC7B,IAAI,OAAO,MAAM,IAAI,CAAC;wBACtB,oHAAA,CAAA,UAAG,CAAC,YAAY,CACZ,KAAK,GAAG,CAAC,CAAA;4BACL,OAAO;gCAAE,KAAK;4BAAI;wBACtB,IACF,IAAI,CAAC,CAAA;4BACH,2BAA2B,OAAO,CAAC,CAAA;gCAC/B,eAAe,IAAI,CAAE,IAAI,GAAG,OAAO,CAAC,eAAe,IAAI,CAAE,GAAG,CAAC;gCAC7D,gBAAgB,IAAI,CAAC;4BACzB;4BACA,QAAQ;wBACZ;oBACJ,OAAO;wBACH,QAAQ;oBACZ;gBACJ,OAAO;oBACH,QAAQ,EAAE;gBACd;YACJ;QACJ;QAEA,IAAI,qBAAqB;YACrB,WAAW,sBAAsB,KAAK,SAAS,CAAC;YAChD,WAAW,oBAAoB,KAAK,SAAS,CAAC;QAClD;QACA,QAAQ,GAAG,CAAC;YAAC,uBAAuB,SAAS,SAAS,EAAE;YAAc,uBAAuB,SAAS,SAAS,EAAE;SAAa,EAAE,IAAI,CAAC,CAAA;YACjI,IAAI,kBAAkB,2BAA2B,OAAO,CAAC,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE;YAE7E,IAAI,qBAAqB;gBACrB,IAAI,wBAAwB,KAAK,SAAS,CAAC;gBAC3C,IAAI,wBAAwB,WAAW,2BAA2B;gBAElE,WAAW,2BAA2B,KAAK,SAAS,CAAC;gBAErD,IAAI,0BAA0B,uBAAuB;oBACjD,SAAS,aAAa,CAAC,IAAI,YAAY,wHAAA,CAAA,gBAAa,CAAC,oBAAoB,EAAE;wBAAE,QAAQ;4BAAE,WAAW;wBAAK;oBAAE;gBAC7G;YACJ;YACA,QAAQ;gBACJ;gBACA;gBACA,cAAc;YAClB;QACJ;IACJ;AACJ;AAEO,eAAe,qBAAqB,YAAoB;IAC3D,IAAI,SAAwB,sHAAA,CAAA,wBAAqB,CAAC,MAAM;IACxD,IAAI,wBAA+C,sHAAA,CAAA,wBAAqB,CAAC,cAAc;IACvF,IAAI,eAAkC,sHAAA,CAAA,wBAAqB,CAAC,YAAY;IACxE,IAAI,WAA2B,EAAE;IACjC,IAAI;QACA,IAAI,eAAe,KAAK,KAAK,CAAC;QAC9B,qCAAqC;QACrC,IAAI,aAAa,MAAM,KAAK,WAAW;YACnC,MAAM,YAAY,CAAA,GAAA,oIAAA,CAAA,gBAAa,AAAD,EAAE;YAChC,SAAS,UAAU,MAAM;YACzB,wBAAwB,UAAU,qBAAqB;YACvD,eAAe,UAAU,YAAY;QACzC,OAAO,IAAI,aAAa,SAAS,KAAK,WAAW;YAC7C,sCAAsC;YACtC,IAAI,WAAW,MAAM,0BAA0B,cAAc;YAC7D,SAAS,SAAS,MAAM;YACxB,wBAAwB,SAAS,eAAe;YAChD,eAAe,SAAS,YAAY;QACxC,OAAO;YACH,sCAAsC;YACtC,SAAS,YAAY,CAAC,mBAAmB,GAAG,KAAK,KAAK,CAAC,YAAY,CAAC,mBAAmB,IAAI,CAAC;YAC5F,wBAAwB,YAAY,CAAC,qBAAqB,GAAG,KAAK,KAAK,CAAC,YAAY,CAAC,qBAAqB,IAAI,CAAC;YAC/G,eAAe,YAAY,CAAC,0BAA0B,GAAG,KAAK,KAAK,CAAC,YAAY,CAAC,0BAA0B,IAAI,EAAE;QACrH;IACJ,EAAE,OAAO,GAAG;QACR,8BAA8B;QAC9B,IAAI;YACA,IAAI,OAAO,CAAC,sIAAoB,EAAE,KAAK,CAAC;YAExC,IAAI,KAAK,UAAU,CAAC,SAAS,CAAC,wBAAwB,EAAE;gBACpD,aAAa,IAAI,CAAC;oBACd,MAAM;oBACN,YAAY;wBACR,qBAAqB,CAAC,KAAK,UAAU,CAAC,SAAS,CAAC,wBAAwB,GAAG,GAAG,EAAE,QAAQ;oBAC5F;gBACJ;YACJ;YAEA,IAAI,KAAK,UAAU,CAAC,SAAS,CAAC,uBAAuB,EAAE;gBACnD,aAAa,IAAI,CAAC;oBACd,MAAM;oBACN,YAAY;wBACR,WAAW,KAAK,UAAU,CAAC,SAAS,CAAC,uBAAuB,CAAC,QAAQ;oBACzE;gBACJ;YACJ;YAEA,IAAI,KAAK,UAAU,CAAC,SAAS,CAAC,uBAAuB,EAAE;gBACnD,aAAa,IAAI,CAAC;oBACd,MAAM;oBACN,YAAY;wBACR,QAAQ,KAAK,UAAU,CAAC,SAAS,CAAC,uBAAuB,CAAC,QAAQ;oBACtE;gBACJ;YACJ;YAEA,IAAI,KAAK,UAAU,CAAC,SAAS,CAAC,cAAc,EAAE;gBAC1C,KAAK,UAAU,CAAC,SAAS,CAAC,cAAc,CAAC,KAAK,CAAC,KAAK,OAAO,CAAC,CAAA;oBACxD,aAAa,IAAI,CAAC;wBACd,MAAM;wBACN,YAAY;4BACR,QAAQ;wBACZ;oBACJ;gBACJ;YACJ;YAEA,KAAK,UAAU,CAAC,SAAS,CAAC,iBAAiB,CAAC,KAAK,CAAC,KAAK,OAAO,CAAC,CAAA;gBAC3D,IAAI,cAA+B;oBAC/B,MAAM;gBACV;gBACA,IAAI,QAAQ,KAAK,KAAK,CAAC;gBACvB,IAAI,KAAK,CAAC,EAAE,CAAC,MAAM,GAAG,GAAG;oBACrB,YAAY,UAAU,GAAG;wBACrB,aAAa,KAAK,CAAC,EAAE;oBACzB;oBAEA,IAAI,KAAK,CAAC,EAAE,IAAI,KAAK,CAAC,EAAE,CAAC,MAAM,GAAG,GAAG;wBACjC,YAAY,UAAU,CAAC,UAAU,GAAG,KAAK,CAAC,EAAE;oBAChD;gBACJ;gBACA,aAAa,IAAI,CAAC;YACtB;YACA,IAAI,aAAa,IAAI;YACrB,IAAI,wBAA2C,EAAE;YACjD,KAAK,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,OAAO,CAAC,CAAA;gBAC9C,IAAI,cAA+B;oBAC/B,MAAM;gBACV;gBACA,IAAI,QAAQ,KAAK,KAAK,CAAC;gBACvB,IAAI,KAAK,CAAC,EAAE,CAAC,MAAM,GAAG,GAAG;oBACrB,IAAI,SAAS,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC;oBAC5B,IAAI,MAAM,CAAC,EAAE,IAAI,MAAM,CAAC,EAAE,CAAC,MAAM,GAAG,GAAG;wBACnC,YAAY,UAAU,GAAG;4BACrB,OAAO,MAAM,CAAC,EAAE,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE;wBAC1C;oBACJ;oBACA,YAAY,IAAI,GAAG;wBACf,KAAK,MAAM,CAAC,EAAE;oBAClB;gBACJ;gBACA,IAAI,KAAK,CAAC,EAAE,IAAI,KAAK,CAAC,EAAE,CAAC,MAAM,GAAG,GAAG;oBACjC,IAAI,CAAC,YAAY,UAAU,EAAE;wBACzB,YAAY,UAAU,GAAG,CAAC;oBAC9B;oBACA,YAAY,UAAU,CAAC,MAAM,GAAG,KAAK,CAAC,EAAE;gBAC5C;gBACA,IAAI,YAAY,IAAI,EAAE,KAAK;oBACvB,WAAW,GAAG,CAAC,YAAY,IAAI,EAAE;oBACjC,sBAAsB,IAAI,CAAC;gBAC/B,OAAO;oBACH,aAAa,IAAI,CAAC;gBACtB;YACJ;YAEA,IAAI,UAAU,MAAM,oHAAA,CAAA,UAAG,CAAC,YAAY,CAChC,MAAM,IAAI,CAAC,YAAY,GAAG,CAAC,CAAA;gBACvB,OAAO;oBAAE,KAAK;gBAAI;YACtB;YAEJ,sBAAsB,OAAO,CAAC,CAAA;gBAC1B,MAAM,IAAI,CAAE,IAAI,GAAG,OAAO,CAAC,MAAM,IAAI,CAAE,GAAG,CAAC;gBAC3C,MAAM,IAAI,CAAE,OAAO,GAAG,oHAAA,CAAA,UAAG,CAAC,eAAe,CAAC;oBACtC,KAAK,MAAM,IAAI,CAAE,GAAG;gBACxB;gBACA,aAAa,IAAI,CAAC;YACtB;YAEA,sBAAsB,WAAW,GAAG,CAAC,CAAC,KAAK,QAAQ,CAAC,MAAM,CAAC,aAAa;YACxE,sBAAsB,WAAW,GAAG,CAAC,KAAK,QAAQ,CAAC,MAAM,CAAC,WAAW;YACrE,OAAO,SAAS,GAAG,CAAA,GAAA,sHAAA,CAAA,6BAA0B,AAAD,EAAE,KAAK,UAAU,CAAC,SAAS,CAAC,SAAS;YACjF,OAAO,gBAAgB,GAAG,KAAK,UAAU,CAAC,SAAS,CAAC,oBAAoB,GAAG;YAC3E,OAAO,OAAO,GAAG,CAAA,GAAA,sHAAA,CAAA,6BAA0B,AAAD,EAAE,KAAK,UAAU,CAAC,SAAS,CAAC,QAAQ;QAClF,EAAE,OAAM;YACJ,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACJ;IACJ;IAEA,IAAI,sBAAsB,OAAO,IAAI,aAAa,OAAO,CAAC,0CAA0C,QAAQ;QACxG,sFAAsF;QACtF,2DAA2D;QAC3D,MAAM,eAAe;YAAC;YAAK;YAAK;SAAI;QAEpC,IAAI,UAAoB,EAAE;QAC1B,IAAI,aAAa,sBAAsB,OAAO,CAAC,MAAM,CAAC,CAAA;YAClD,IAAI,SAAS,aAAa,QAAQ,CAAC,OAAO,QAAQ;YAClD,IAAI,CAAC,QAAQ;gBACT,QAAQ,IAAI,CAAC,sHAAA,CAAA,eAAY,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK,OAAO,QAAQ,IAAK,KAAK;YAC7E;YACA,OAAO;QACX;QACA,IAAI,QAAQ,MAAM,GAAG,GAAG;YACpB,sJAAA,CAAA,QAAK,CAAC,IAAI,CACN,CAAC,oCAAoC,EAAE,QAAQ,MAAM,GAAG,IAAI,MAAM,GAAG,EAAE,EAAE,QAAQ,QAAQ,GAAG,8CAA8C,CAAC;YAE/I,MAAM,MAAM;QAChB;QACA,sBAAsB,OAAO,GAAG;IACpC;IAEA,MAAM,QAAQ,UAAU,CAAC;IAEzB,IAAI,aAAa,MAAM,GAAG,MAAM;QAC5B,CAAA,GAAA,sJAAA,CAAA,QAAK,AAAD,EAAE,8DAA8D;YAChE,MAAM;YACN,WAAW;QACf;IACJ;IAEA,IAAI,UAAU,CAAA,GAAA,sJAAA,CAAA,QAAK,AAAD,EAAE,uBAAuB;QACvC,MAAM;QACN,WAAW;IACf;IAEA,oHAAA,CAAA,UAAG,CAAC,cAAc,CACd,gBAAgB,EAAE,EAClB,QACA,uBACA,WACA,WACA,WACA;QACI,WAAW,oBAAoB,KAAK,SAAS,CAAC;QAC9C,WAAW,sBAAsB,KAAK,SAAS,CAAC;QAChD,WAAW,2BAA2B,KAAK,SAAS,CAAC,2BAA2B;QAChF,OAAO,QAAQ,CAAC,MAAM;IAC1B,GACA;QACI,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAClB,GACA;AAER;AAEO,SAAS,MAAM,EAAU;IAC5B,OAAO,IAAI,QAAQ,CAAA;QACf,WAAW;YACP;QACJ,GAAG;IACP;AACJ;AAEO,SAAS,uBAAuB,MAAqB,EAAE,YAAmC,EAAE,YAA+B;IAC9H,OAAO;QACH,WAAW,2BAA2B,aAAa,MAAM,CAAC,CAAA,cAAe,YAAY,IAAI,KAAK;QAC9F,WAAW,2BAA2B,aAAa,MAAM,CAAC,CAAA,cAAe,YAAY,IAAI,KAAK;QAC9F,WAAW,OAAO,SAAS,IAAI;QAC/B,kBAAkB,OAAO,gBAAgB,IAAI;QAC7C,WAAW,OAAO,SAAS,IAAI;QAC/B,SAAS,OAAO,OAAO,IAAI;QAC3B,SAAS,OAAO,OAAO;QACvB,MAAM,aAAa,qBAAqB;QACxC,KAAK;YACD,YAAY,aAAa,UAAU;YACnC,aAAa,aAAa,WAAW;YACrC,cAAc,aAAa,YAAY;YACvC,gBAAgB,aAAa,cAAc;YAC3C,QAAQ,aAAa,SAAS;YAC9B,MAAM,CAAC,aAAa,WAAW;YAC/B,WAAW,aAAa,YAAY;QACxC;QACA,YAAY;YACR,MAAM,CAAC,aAAa,QAAQ;YAC5B,WAAW,CAAC,aAAa,mBAAmB;YAC5C,MAAM,CAAC,aAAa,aAAa;YACjC,OAAO,CAAC,aAAa,mBAAmB;YACxC,UAAU,CAAC,aAAa,eAAe;YACvC,QAAQ,CAAC,aAAa,UAAU;YAChC,QAAQ,CAAC,aAAa,UAAU;YAChC,aAAa,aAAa,kBAAkB,IAAI;YAChD,eAAe,CAAC,aAAa,iBAAiB;YAC9C,eAAe,CAAC,aAAa,iBAAiB;YAC9C,MAAM,CAAC,aAAa,QAAQ;YAC5B,oBAAoB,CAAC,aAAa,sBAAsB;YACxD,OAAO,CAAC,aAAa,YAAY;QACrC;QACA,aAAa,aAAa,WAAW;QACrC,SAAS,aAAa,OAAO,EAAE,OAAO,CAAC,GAAG,IAAM,CAAC,IAAI,CAAC,GAAG;QACzD,SAAS,OAAO,cAAc,CAAC,OAAO,CAAC;IAC3C;AACJ;AAEO,SAAS,2BAA2B,YAA+B;IACtE,OAAO,aAAa,GAAG,CAAC,CAAA;QACpB,OAAO;YACH,KAAK,YAAY,IAAI,EAAE;YACvB,QAAQ,YAAY,UAAU;YAC9B,aAAa,YAAY,IAAI,EAAE;YAC/B,MAAM,YAAY,IAAI;YACtB,UAAU,YAAY,QAAQ;QAClC;IACJ;AACJ;AAEO,SAAS,4BAA4B,YAA+B;IACvE,IAAI,OAAoB,IAAI;IAC5B,aAAa,OAAO,CAAC,CAAA;QACjB,IAAI,YAAY,IAAI,EAAE;YAClB,YAAY,IAAI,CAAC,OAAO,CAAC,CAAA,MAAO,KAAK,GAAG,CAAC;QAC7C;IACJ;IACA,aAAa,OAAO,CAAC,qBAAqB,KAAK,IAAI,GAAG,IAAI,KAAK,SAAS,CAAC,MAAM,IAAI,CAAC,SAAS;AACjG;AAQO,SAAS,2BAA2B,YAA+B;IACtE,OAAO,aAAa,GAAG,CAAC,CAAA;QACpB,IAAI,iBAAiB;YACjB,MAAM,YAAY,IAAI;YACtB,MAAM,YAAY,IAAI;YACtB,UAAU,YAAY,QAAQ;QAClC;QAEA,IAAI,YAAY,IAAI,EAAE;YAClB,eAAe,IAAI,GAAG;gBAClB,KAAK,YAAY,IAAI,EAAE;gBACvB,MAAM,YAAY,IAAI,EAAE;YAC5B;QACJ;QAEA,IAAI,YAAY,UAAU,EAAE;YACxB,eAAe,UAAU,GAAG,CAAC;YAC7B,OAAO,IAAI,CAAC,YAAY,UAAU,EAAE,OAAO,CAAC,CAAA;gBACxC,IAAI,CAAC,IAAI,UAAU,CAAC,MAAM;oBACtB,eAAe,UAAU,AAAC,CAAC,IAAI,GAAG,YAAY,UAAU,AAAC,CAAC,IAAI;gBAClE;YACJ;QACJ;QACA,OAAO;IACX;AACJ;AAEO,MAAM,uBAAuB;AAC7B,MAAM,4BAA4B;AAClC,MAAM,qBAAqB;AAC3B,MAAM,qCAAqC;AAC3C,MAAM,oBAAoB;AAC1B,MAAM,gCAAgC;AACtC,MAAM,iCAAiC;AACvC,MAAM,iCAAiC;AACvC,MAAM,+BAA+B;AACrC,MAAM,mBAAmB;AACzB,MAAM,8BAA8B;AACpC,MAAM,wBAAwB;AAC9B,MAAM,sBAAsB;AAC5B,MAAM,qBAAqB;AAC3B,MAAM,6BAA6B;AACnC,MAAM,eAAe;AACrB,MAAM,cAAc;AACpB,MAAM,oBAAoB;AAC1B,MAAM,wBAAwB;AAC9B,MAAM,2BAA2B;AACjC,MAAM,6CAA6C;AACnD,MAAM,iBAAiB", "debugId": null}}, {"offset": {"line": 1651, "column": 0}, "map": {"version": 3, "sources": ["file:///app/utils/FlipUtils.tsx"], "sourcesContent": ["import { FLIP_CUSTOMIZING_KEY, getSetting, setSetting } from './SettingsUtils'\n\nexport const DEMO_FLIP: FlipAuction = {\n    bin: true,\n    cost: 45000000,\n    item: {\n        category: 'WEAPON',\n        name: \"<PERSON> Midas' Sword\",\n        tag: 'MIDAS_SWORD',\n        tier: 'LEGENDARY',\n        iconUrl: 'https://sky.coflnet.com/static/icon/MIDAS_SWORD'\n    },\n    profit: 5000000,\n    lowestBin: 46000000,\n    secondLowestBin: 47000000,\n    median: 50000000,\n    sellerName: 'Testuser',\n    showLink: true,\n    uuid: 'e4723502450544c8a3711a0a5b1e8cd0',\n    volume: 5.*********,\n    sold: true,\n    finder: 1,\n    props: [\n        'Top Bid: 50.000.000',\n        'Recombobulated',\n        'Hot Potato Book: 2',\n        'Ultimate Wise 1',\n        'Sharpness 6',\n        'Thunderlord 6',\n        'Vampirism 6',\n        'Critical 6',\n        'Luck 6',\n        'Giant Killer 6',\n        'Smite 6',\n        'Ender Slayer 6',\n        '...',\n        '...',\n        '...',\n        '...',\n        '...',\n        '...',\n        '...',\n        '...',\n        '...',\n        '...',\n        '...',\n        '...',\n        '...',\n        '...',\n        '...',\n        '...',\n        '...',\n        '...'\n    ]\n}\n\nexport const DEFAULT_MOD_FORMAT = '{0}: {1}{2} {3}{4} -> {5} (+{6} {7}) Med: {8} Lbin: {9} Volume: {10}'\n\nexport function getFlipCustomizeSettings(): FlipCustomizeSettings {\n    let settings: FlipCustomizeSettings\n\n    try {\n        settings = JSON.parse(getSetting(FLIP_CUSTOMIZING_KEY))\n\n        // Fields that have special default values\n        if (settings.hideSecondLowestBin !== false) {\n            settings.hideSecondLowestBin = true\n        }\n        if (settings.soundOnFlip !== false) {\n            settings.soundOnFlip = true\n        }\n        if (!settings.finders) {\n            settings.finders = FLIP_FINDERS.map(finder => +finder.value)\n        }\n    } catch {\n        settings = DEFAULT_FLIP_SETTINGS.FLIP_CUSTOMIZE\n        setSetting(FLIP_CUSTOMIZING_KEY, JSON.stringify(DEFAULT_FLIP_SETTINGS.FLIP_CUSTOMIZE))\n    }\n    return settings\n}\n\nexport const FLIP_FINDERS = [\n    {\n        value: '1',\n        label: 'Flipper',\n        shortLabel: 'FLIP',\n        default: true,\n        description:\n            'Is the classical flip finding algorithm using the Skyblock AH history database. It searches the history for similar items but searching for references takes time thus this is relatively slow.',\n        selectable: true\n    },\n    {\n        value: '2',\n        label: 'Sniper',\n        shortLabel: 'SNIPE',\n        default: true,\n        description:\n            'Is a classical sniping algorithm that stores prices in a dictionary grouped by any relevant modifiers. It only outputs flips that are below lbin and median for a combination of relevant modifiers. Its faster by about 3000x but may not find as many flips as the flipper.',\n        selectable: true\n    },\n    {\n        value: '4',\n        label: 'Sniper (Median)',\n        shortLabel: 'MSNIPE',\n        default: true,\n        description: \"Uses the same algorithm as Sniper but doesn't require the item to be below lowest bin and only 5% below the median sell value.\",\n        selectable: true\n    },\n    { value: '8', label: 'AI', shortLabel: 'AI', default: false, description: '', selectable: false },\n    {\n        value: '16',\n        label: 'User (whitelists)',\n        shortLabel: 'User (whitelisted by you)',\n        default: false,\n        description: (\n            <span>\n                Forwards all new auctions with a target value set to the starting bid (0 profit)\n                <br /> You can use this together with whitelist/blacklist of <b>Starting Bid</b> and other filters to create your own flip rules.\n                <br /> Different to the other finders this one won't pre-filter auctions its all up to you.\",\n            </span>\n        ),\n        selectable: true\n    },\n    {\n        value: '32',\n        label: 'TFM',\n        shortLabel: 'TFM',\n        default: false,\n        description: (\n            <span>\n                These are flips from TFM (TheFlippingMod)\n                <br />\n                The integration is currently under development.\",\n            </span>\n        ),\n        selectable: true\n    },\n    {\n        value: '64',\n        label: 'Stonks',\n        shortLabel: 'Stonks',\n        default: false,\n        description: (\n            <span>\n                Experimental finder trying to predict the value of an item without references <br />\n                This is under active development and will occasionally overvalue flips, use with caution.\",\n            </span>\n        ),\n        selectable: true\n    },\n    { value: '128', label: 'External', shortLabel: 'External', default: false, description: '', selectable: false },\n    {\n        value: '1024',\n        label: 'CraftCost',\n        shortLabel: 'CraftCost',\n        default: false,\n        description: (\n            <span>\n                Displays any auction that would be at least 5% more expensive to craft. <br />\n                Sums up clean+modifier cost. <br />\n                You can adjust weights of every attribute with the <b>CraftCostWeight</b> filter.<br />\n                Note that this does not indicate that the item will sell for that price.\n            </span>\n        ),\n        selectable: true\n    }\n]\n\nexport function getFlipFinders(finderValues: number[]) {\n    let finders = FLIP_FINDERS.filter(option => finderValues.some(finder => finder.toString() === option.value))\n    let notFoundFinder = {\n        value: '',\n        label: 'Unknown',\n        shortLabel: 'Unknown',\n        default: false,\n        description: '',\n        selectable: false\n    }\n    return finders.length > 0 ? finders : [notFoundFinder]\n}\n\nexport const DEFAULT_FLIP_SETTINGS = {\n    FLIP_CUSTOMIZE: {\n        hideCost: false,\n        hideEstimatedProfit: false,\n        hideLowestBin: true,\n        hideMedianPrice: false,\n        hideSeller: true,\n        hideVolume: false,\n        maxExtraInfoFields: 3,\n        hideCopySuccessMessage: false,\n        hideSecondLowestBin: true,\n        useLowestBinForProfit: false,\n        disableLinks: false,\n        justProfit: false,\n        soundOnFlip: true,\n        shortNumbers: false,\n        hideProfitPercent: false,\n        blockTenSecMsg: false,\n        finders: FLIP_FINDERS.filter(finder => finder.default).map(finder => +finder.value),\n        hideLore: true,\n        hideModChat: false,\n        hideSellerOpenBtn: false,\n        modFormat: '',\n        modCountdown: false\n    } as FlipCustomizeSettings,\n    RESTRICTIONS: [] as FlipRestriction[],\n    FILTER: {\n        onlyBin: false,\n        maxCost: 10000000000,\n        minProfit: 0,\n        minProfitPercent: 0,\n        minVolume: 0,\n        onlyUnsold: false,\n        restrictions: []\n    } as FlipperFilter\n}\n\nexport function getCurrentProfitCalculationState(flipCustomizeSettings: FlipCustomizeSettings): 'lbin' | 'median' | 'custom' {\n    if (flipCustomizeSettings.useLowestBinForProfit) {\n        return 'lbin'\n    }\n    if (flipCustomizeSettings.finders?.length === 1 && flipCustomizeSettings.finders[0].toString() === '2') {\n        return 'lbin'\n    }\n    if (flipCustomizeSettings.finders?.length === 1 && flipCustomizeSettings.finders[0].toString() === '1') {\n        return 'median'\n    }\n    if (flipCustomizeSettings.finders?.length === 1 && flipCustomizeSettings.finders[0].toString() === '4') {\n        return 'median'\n    }\n    if (\n        flipCustomizeSettings.finders?.length === 2 &&\n        flipCustomizeSettings.finders.find(f => f.toString() === '1') &&\n        flipCustomizeSettings.finders.find(f => f.toString() === '4')\n    ) {\n        return 'median'\n    }\n\n    return 'custom'\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;;AAEO,MAAM,YAAyB;IAClC,KAAK;IACL,MAAM;IACN,MAAM;QACF,UAAU;QACV,MAAM;QACN,KAAK;QACL,MAAM;QACN,SAAS;IACb;IACA,QAAQ;IACR,WAAW;IACX,iBAAiB;IACjB,QAAQ;IACR,YAAY;IACZ,UAAU;IACV,MAAM;IACN,QAAQ;IACR,MAAM;IACN,QAAQ;IACR,OAAO;QACH;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACH;AACL;AAEO,MAAM,qBAAqB;AAE3B,SAAS;IACZ,IAAI;IAEJ,IAAI;QACA,WAAW,KAAK,KAAK,CAAC,CAAA,GAAA,0HAAA,CAAA,aAAU,AAAD,EAAE,0HAAA,CAAA,uBAAoB;QAErD,0CAA0C;QAC1C,IAAI,SAAS,mBAAmB,KAAK,OAAO;YACxC,SAAS,mBAAmB,GAAG;QACnC;QACA,IAAI,SAAS,WAAW,KAAK,OAAO;YAChC,SAAS,WAAW,GAAG;QAC3B;QACA,IAAI,CAAC,SAAS,OAAO,EAAE;YACnB,SAAS,OAAO,GAAG,aAAa,GAAG,CAAC,CAAA,SAAU,CAAC,OAAO,KAAK;QAC/D;IACJ,EAAE,OAAM;QACJ,WAAW,sBAAsB,cAAc;QAC/C,CAAA,GAAA,0HAAA,CAAA,aAAU,AAAD,EAAE,0HAAA,CAAA,uBAAoB,EAAE,KAAK,SAAS,CAAC,sBAAsB,cAAc;IACxF;IACA,OAAO;AACX;AAEO,MAAM,eAAe;IACxB;QACI,OAAO;QACP,OAAO;QACP,YAAY;QACZ,SAAS;QACT,aACI;QACJ,YAAY;IAChB;IACA;QACI,OAAO;QACP,OAAO;QACP,YAAY;QACZ,SAAS;QACT,aACI;QACJ,YAAY;IAChB;IACA;QACI,OAAO;QACP,OAAO;QACP,YAAY;QACZ,SAAS;QACT,aAAa;QACb,YAAY;IAChB;IACA;QAAE,OAAO;QAAK,OAAO;QAAM,YAAY;QAAM,SAAS;QAAO,aAAa;QAAI,YAAY;IAAM;IAChG;QACI,OAAO;QACP,OAAO;QACP,YAAY;QACZ,SAAS;QACT,2BACI,6LAAC;;gBAAK;8BAEF,6LAAC;;;;;gBAAK;8BAAuD,6LAAC;8BAAE;;;;;;gBAAgB;8BAChF,6LAAC;;;;;gBAAK;;;;;;;QAGd,YAAY;IAChB;IACA;QACI,OAAO;QACP,OAAO;QACP,YAAY;QACZ,SAAS;QACT,2BACI,6LAAC;;gBAAK;8BAEF,6LAAC;;;;;gBAAK;;;;;;;QAId,YAAY;IAChB;IACA;QACI,OAAO;QACP,OAAO;QACP,YAAY;QACZ,SAAS;QACT,2BACI,6LAAC;;gBAAK;8BAC4E,6LAAC;;;;;gBAAK;;;;;;;QAI5F,YAAY;IAChB;IACA;QAAE,OAAO;QAAO,OAAO;QAAY,YAAY;QAAY,SAAS;QAAO,aAAa;QAAI,YAAY;IAAM;IAC9G;QACI,OAAO;QACP,OAAO;QACP,YAAY;QACZ,SAAS;QACT,2BACI,6LAAC;;gBAAK;8BACsE,6LAAC;;;;;gBAAK;8BACjD,6LAAC;;;;;gBAAK;8BACgB,6LAAC;8BAAE;;;;;;gBAAmB;8BAAQ,6LAAC;;;;;gBAAK;;;;;;;QAI/F,YAAY;IAChB;CACH;AAEM,SAAS,eAAe,YAAsB;IACjD,IAAI,UAAU,aAAa,MAAM,CAAC,CAAA,SAAU,aAAa,IAAI,CAAC,CAAA,SAAU,OAAO,QAAQ,OAAO,OAAO,KAAK;IAC1G,IAAI,iBAAiB;QACjB,OAAO;QACP,OAAO;QACP,YAAY;QACZ,SAAS;QACT,aAAa;QACb,YAAY;IAChB;IACA,OAAO,QAAQ,MAAM,GAAG,IAAI,UAAU;QAAC;KAAe;AAC1D;AAEO,MAAM,wBAAwB;IACjC,gBAAgB;QACZ,UAAU;QACV,qBAAqB;QACrB,eAAe;QACf,iBAAiB;QACjB,YAAY;QACZ,YAAY;QACZ,oBAAoB;QACpB,wBAAwB;QACxB,qBAAqB;QACrB,uBAAuB;QACvB,cAAc;QACd,YAAY;QACZ,aAAa;QACb,cAAc;QACd,mBAAmB;QACnB,gBAAgB;QAChB,SAAS,aAAa,MAAM,CAAC,CAAA,SAAU,OAAO,OAAO,EAAE,GAAG,CAAC,CAAA,SAAU,CAAC,OAAO,KAAK;QAClF,UAAU;QACV,aAAa;QACb,mBAAmB;QACnB,WAAW;QACX,cAAc;IAClB;IACA,cAAc,EAAE;IAChB,QAAQ;QACJ,SAAS;QACT,SAAS;QACT,WAAW;QACX,kBAAkB;QAClB,WAAW;QACX,YAAY;QACZ,cAAc,EAAE;IACpB;AACJ;AAEO,SAAS,iCAAiC,qBAA4C;IACzF,IAAI,sBAAsB,qBAAqB,EAAE;QAC7C,OAAO;IACX;IACA,IAAI,sBAAsB,OAAO,EAAE,WAAW,KAAK,sBAAsB,OAAO,CAAC,EAAE,CAAC,QAAQ,OAAO,KAAK;QACpG,OAAO;IACX;IACA,IAAI,sBAAsB,OAAO,EAAE,WAAW,KAAK,sBAAsB,OAAO,CAAC,EAAE,CAAC,QAAQ,OAAO,KAAK;QACpG,OAAO;IACX;IACA,IAAI,sBAAsB,OAAO,EAAE,WAAW,KAAK,sBAAsB,OAAO,CAAC,EAAE,CAAC,QAAQ,OAAO,KAAK;QACpG,OAAO;IACX;IACA,IACI,sBAAsB,OAAO,EAAE,WAAW,KAC1C,sBAAsB,OAAO,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,QAAQ,OAAO,QACzD,sBAAsB,OAAO,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,QAAQ,OAAO,MAC3D;QACE,OAAO;IACX;IAEA,OAAO;AACX", "debugId": null}}, {"offset": {"line": 1979, "column": 0}, "map": {"version": 3, "sources": ["file:///app/utils/Parser/APIResponseParser.tsx"], "sourcesContent": ["import api from '../../api/ApiHelper'\nimport { NotificationListener, SubscriptionType } from '../../api/ApiTypes.d'\nimport { hasFlag } from '../../components/FilterElement/FilterType'\nimport { getFlipFinders } from '../FlipUtils'\nimport { convertTagToName } from '../Formatter'\n\nexport function parseItemBidForList(bid: any): BidForList {\n    return {\n        uuid: bid.uuid || bid.auctionId,\n        end: parseDate(bid.end),\n        item: {\n            name: bid.itemName,\n            tag: bid.tag\n        },\n        highestBid: bid.highestBid,\n        highestOwn: bid.highestOwnBid,\n        bin: bid.bin\n    } as BidForList\n}\n\nexport function parseItemBid(bid: any): ItemBid {\n    return {\n        auctionId: bid.auctionId,\n        amount: bid.amount,\n        bidder: parsePlayer(bid.bidder),\n        timestamp: parseDate(bid.timestamp),\n        profileId: bid.profileId,\n        bin: bid.bin\n    } as ItemBid\n}\n\nexport function parseAuction(auction: any): Auction {\n    let parsedAuction = {\n        uuid: auction.uuid || auction.auctionId,\n        end: parseDate(auction.end),\n        item: {\n            tag: auction.tag,\n            name: auction.itemName || auction.name\n        },\n        startingBid: auction.startingBid,\n        highestBid: auction.highestBid,\n        bin: auction.bin\n    } as Auction\n\n    parsedAuction.item.iconUrl = api.getItemImageUrl(parsedAuction.item)\n\n    return parsedAuction\n}\n\nexport function parsePlayerDetails(playerDetails: any): PlayerDetails {\n    return {\n        bids: playerDetails.bids.map(bid => {\n            return {\n                uuid: bid.uuid,\n                highestOwn: bid.highestOwn,\n                end: parseDate(bid.end),\n                highestBid: bid.highestBid,\n                item: {\n                    tag: bid.tag,\n                    name: bid.itemName\n                }\n            } as BidForList\n        }),\n        auctions: playerDetails.auctions.map(auction => {\n            return {\n                uuid: auction.auctionId,\n                highestBid: auction.highestBid,\n                end: parseDate(auction.end),\n                item: {\n                    tag: auction.tag,\n                    name: auction.itemName\n                },\n                bin: auction.bin\n            } as Auction\n        })\n    } as PlayerDetails\n}\n\nexport function parseItemPrice(priceData: any): ItemPrice {\n    return {\n        time: parseDate(priceData.time),\n        avg: priceData.avg,\n        max: priceData.max,\n        min: priceData.min,\n        volume: priceData.volume\n    } as ItemPrice\n}\n\nexport function parseItem(item: any): Item {\n    const CRAB_HAT_IMAGES = {\n        red: 'https://mc-heads.net/head/56b61f826dd6bfc1e3191a8369aeb0435c5a5335563431a538585ad039da1e0c',\n        orange: 'https://mc-heads.net/head/ae38a15704089676a24e9eeccf4c290644d352c7d8f2b4135fa3538625107db',\n        yellow: 'https://mc-heads.net/head/6b92684647051bd27ee04adb4098ee9bccca45a726a7cbf38e98b7e75cb889f4',\n        lime: 'https://mc-heads.net/head/993c60fd0dd130695e378eef010a7d3c5dfde77f6b82b20b8124cfb830017ff',\n        green: 'https://mc-heads.net/head/98d99983ab5986921251a29bba96c8e734f5de084a296cb627bcd64fd0fba593',\n        aqua: 'https://mc-heads.net/head/2e1556958b1df4fd6228c9dcbd8c053f5d8902a41c4a59376bd0df1c60be8369',\n        purple: 'https://mc-heads.net/head/2e3af5824014b57f4a4a84d4bb7fb88cac9e4ac75d00c7adb09dfe5ab737e224',\n        pink: 'https://mc-heads.net/head/effaf0dc89da58bd1ed08f917407853e58d7bcbf5e6b5f33586389eb863a5bbd',\n        black: 'https://mc-heads.net/head/cb85828267e59e83edc3bef235102e43fb70922622ccc3809a326a8c5632199a'\n    }\n\n    const BALLOON_HAT_IMAGES = {\n        red: 'https://mc-heads.net/head/567f93963ad7ac3abdc49d02c9861fa2d45d00da1a7b31193ceb246313d39bc5',\n        orange: 'https://mc-heads.net/head/841c5e8e0637acee09b68fab743db66c6714938c73792bc95acb1f393478144b',\n        yellow: 'https://mc-heads.net/head/a90d9adef4732e5832448973e3557fce8a2e9ec8129ed32143eb666b4fa88ab2',\n        lime: 'https://mc-heads.net/head/fb0209c346e04c3253f492ab904ef6e472faa617c681706f9153a116aa6481c2',\n        green: 'https://mc-heads.net/head/8ba968d27dd90f67434e818646b2b0042946b67a14c594b31698dae309cb52a4',\n        aqua: 'https://mc-heads.net/head/6432dda190146d4ad7a8569b389aea777684c44fa2624dcf74016467765e9693',\n        purple: 'https://mc-heads.net/head/98b97944067281fa1361aa43ac522fdf190e50d65479f89a35e8cf87154e005c',\n        pink: 'https://mc-heads.net/head/2ad4c874756ab9672aec51d53b9b94e6c9b5c6e7e0a2d44928df2186f4791e96',\n        black: 'https://mc-heads.net/head/63bdb1e21a9fdf98aff67f0ff4e7dfada05f340210dd70b688c09c3cf50f6410'\n    }\n\n    let parsed = {\n        tag: item.tag,\n        name: item.altNames && item.altNames[0] && item.altNames[0].Name ? item.altNames[0].Name : item.itemName || item.name,\n        category: item.category,\n        iconUrl: api.getItemImageUrl(item),\n        tier: item.tier,\n        bazaar: hasFlag(item.flags, 1)\n    }\n\n    if (item.flatNbt && item.flatNbt['party_hat_color'] && item.tag === 'PARTY_HAT_CRAB') {\n        parsed.iconUrl = CRAB_HAT_IMAGES[item.flatNbt['party_hat_color']] || parsed.iconUrl\n    }\n    if (item.flatNbt && item.flatNbt['party_hat_color'] && item.tag === 'BALLOON_HAT_2024') {\n        parsed.iconUrl = BALLOON_HAT_IMAGES[item.flatNbt['party_hat_color']] || parsed.iconUrl\n    }\n\n    return parsed\n}\n\nexport function parseEnchantment(enchantment: any): Enchantment {\n    console.log(enchantment)\n    return {\n        id: enchantment.id,\n        level: enchantment.level,\n        name: enchantment.type ? convertTagToName(enchantment.type) : '',\n        color: enchantment.color\n    }\n}\n\nexport function parseSearchResultItem(item: any): SearchResultItem {\n    let _getRoute = (): string => {\n        switch (item.type) {\n            case 'filter':\n                return '/item/' + item.id.split('?')[0]\n            case 'item':\n                return '/item/' + item.id\n            case 'player':\n                return '/player/' + item.id\n            case 'auction':\n                return '/auction/' + item.id\n        }\n        return ''\n    }\n\n    return {\n        dataItem: {\n            name: item.name,\n            iconUrl: item.img ? 'data:image/png;base64,' + item.img : item.type === 'item' ? api.getItemImageUrl(item) : api.getItemImageUrl(item) + '?size=8'\n        },\n        type: item.type,\n        route: _getRoute(),\n        urlSearchParams: item.type === 'filter' ? new URLSearchParams(item.id.split('?')[1] + '&apply=true') : undefined,\n        id: item.id,\n        tier: item.tier\n    }\n}\n\nexport function parsePlayer(player: any): Player {\n    if (typeof player === 'string') {\n        player = {\n            uuid: player,\n            name: player\n        }\n    }\n    return {\n        name: player.name,\n        uuid: player.uuid,\n        iconUrl: player.iconUrl ? player.iconUrl + '?size=8' : 'https://crafatar.com/avatars/' + player.uuid + '?size=8'\n    }\n}\n\nexport function parseAuctionDetails(auctionDetails: any): AuctionDetails {\n    return {\n        auction: {\n            uuid: auctionDetails.uuid,\n            end: parseDate(auctionDetails.end),\n            highestBid: auctionDetails.highestBidAmount,\n            startingBid: auctionDetails.startingBid,\n            item: parseItem(auctionDetails),\n            bin: auctionDetails.bin\n        },\n        start: parseDate(auctionDetails.start),\n        anvilUses: auctionDetails.anvilUses,\n        auctioneer: parsePlayer(auctionDetails.auctioneer),\n        bids: auctionDetails.bids.map(bid => {\n            return parseItemBid(bid)\n        }),\n        claimed: auctionDetails.claimed,\n        coop: auctionDetails.coop,\n        count: auctionDetails.count,\n        enchantments: auctionDetails.enchantments.map(enchantment => {\n            return parseEnchantment(enchantment)\n        }),\n        profileId: auctionDetails.profileId,\n        reforge: auctionDetails.reforge,\n        nbtData: auctionDetails.flatNbt ? auctionDetails.flatNbt : undefined,\n        itemCreatedAt: parseDate(auctionDetails.itemCreatedAt),\n        uuid: auctionDetails.uuid\n    }\n}\n\nexport function parseSubscriptionTypes(typeInNumeric: number): SubscriptionType[] {\n    let keys = Object.keys(SubscriptionType)\n    let subTypes: SubscriptionType[] = []\n\n    for (let i = keys.length; i >= 0; i--) {\n        let enumValue = SubscriptionType[keys[i]]\n        if (typeof SubscriptionType[enumValue] === 'number') {\n            let number = parseInt(SubscriptionType[enumValue])\n            if (number <= typeInNumeric && number > 0) {\n                typeInNumeric -= number\n                subTypes.push(SubscriptionType[number.toString()])\n            }\n        }\n    }\n\n    return subTypes\n}\n\nfunction _getTypeFromSubTypes(subTypes: SubscriptionType[], itemFilter: string): 'item' | 'player' | 'auction' {\n    let type\n    switch (SubscriptionType[subTypes[0].toString()]) {\n        case SubscriptionType.BIN:\n        case SubscriptionType.PRICE_HIGHER_THAN:\n        case SubscriptionType.PRICE_LOWER_THAN:\n        case SubscriptionType.USE_SELL_NOT_BUY:\n            if (!itemFilter) {\n                type = 'bazaar'\n            } else {\n                type = 'item'\n            }\n            break\n        case SubscriptionType.OUTBID:\n        case SubscriptionType.SOLD:\n        case SubscriptionType.PLAYER_CREATES_AUCTION:\n        case SubscriptionType.BOUGHT_ANY_AUCTION:\n            type = 'player'\n            break\n        case SubscriptionType.AUCTION:\n            type = 'auction'\n            break\n    }\n    return type\n}\n\nexport function parseSubscription(subscription: any): NotificationListener {\n    return {\n        id: subscription.id,\n        price: subscription.price,\n        topicId: subscription.topicId,\n        types: parseSubscriptionTypes(subscription.type),\n        type: _getTypeFromSubTypes(parseSubscriptionTypes(subscription.type), subscription.filter),\n        filter: subscription.filter ? JSON.parse(subscription.filter) : undefined\n    }\n}\n\nexport function parseProducts(products: any): Product[] {\n    return products.map((product: any) => {\n        return {\n            productId: product.slug,\n            description: product.description,\n            title: product.title,\n            ownershipSeconds: product.ownershipSeconds,\n            cost: product.cost\n        } as Product\n    })\n}\n\nexport function parseRecentAuction(auction): RecentAuction {\n    return {\n        end: parseDate(auction.end),\n        playerName: auction.playerName,\n        price: auction.price,\n        seller: parsePlayer(auction.seller),\n        uuid: auction.uuid\n    }\n}\n\nexport function parseFlipAuction(flip): FlipAuction {\n    let parsedFlip = {\n        showLink: true,\n        median: flip.median,\n        cost: flip.cost,\n        uuid: flip.uuid,\n        volume: flip.volume,\n        bin: flip.bin,\n        item: {\n            tag: flip.tag,\n            name: flip.name,\n            tier: flip.tier\n        },\n        secondLowestBin: flip.secondLowestBin,\n        sold: flip.sold,\n        sellerName: flip.sellerName,\n        lowestBin: flip.lowestBin,\n        props: flip.prop,\n        finder: flip.finder,\n        profit: flip.Profit\n    } as FlipAuction\n    parsedFlip.item.iconUrl = api.getItemImageUrl(parsedFlip.item)\n\n    return parsedFlip\n}\n\nexport function parsePopularSearch(search): PopularSearch {\n    return {\n        title: search.title,\n        url: search.url,\n        img: search.img\n    }\n}\n\nexport function parseRefInfo(refInfo): RefInfo {\n    return {\n        oldInfo: {\n            refId: refInfo.oldInfo.refId,\n            count: refInfo.oldInfo.count,\n            receivedHours: refInfo.oldInfo.receivedHours,\n            receivedTime: refInfo.oldInfo.receivedTime,\n            bougthPremium: refInfo.oldInfo.bougthPremium\n        },\n        purchasedCoins: refInfo.purchasedCoins,\n        referedCount: refInfo.referedCount,\n        validatedMinecraft: refInfo.validatedMinecraft,\n        referredBy: refInfo.referredBy\n    }\n}\n\nexport function parseFilterOption(filterOption): FilterOptions {\n    return {\n        name: filterOption.name,\n        options: filterOption.options,\n        type: filterOption.type,\n        description: filterOption.description\n    }\n}\n\nexport function parseAccountInfo(accountInfo): AccountInfo {\n    return {\n        email: accountInfo.email,\n        mcId: accountInfo.mcId,\n        mcName: accountInfo.mcName,\n        token: accountInfo.token\n    }\n}\n\nexport function parseMinecraftConnectionInfo(minecraftConnectionInfo): MinecraftConnectionInfo {\n    return {\n        code: minecraftConnectionInfo.code,\n        isConnected: minecraftConnectionInfo.isConnected\n    }\n}\n\nexport function parseDate(dateString: string) {\n    if (!dateString) {\n        return new Date()\n    }\n    if (typeof dateString === 'object' && typeof (dateString as any).getTime === 'function') {\n        dateString = (dateString as Date).toISOString()\n    }\n    if (dateString.slice(-1) === 'Z') {\n        return new Date(dateString)\n    }\n    return new Date(dateString + 'Z')\n}\n\nexport function parseProfitableCrafts(crafts: any[] = []): ProfitableCraft[] {\n    let parseCraftIngredient = (ingredient: any): CraftingIngredient => {\n        let result = {\n            cost: ingredient.cost,\n            count: ingredient.count,\n            type: ingredient.type,\n            item: {\n                tag: ingredient.itemId\n            }\n        } as CraftingIngredient\n\n        result.item.name = convertTagToName(result.item.tag)\n        result.item.iconUrl = api.getItemImageUrl(result.item)\n        if (result.type === 'craft') {\n            let toCraft = crafts.find(craft => craft.itemId === result.item.tag)\n            if (!toCraft) {\n                console.log(`craft not found for ${JSON.stringify(result)}`)\n                return result\n            }\n            result.ingredients = toCraft.ingredients.map(parseCraftIngredient)\n        }\n        return result\n    }\n\n    return crafts.map(craft => {\n        let c = {\n            item: {\n                tag: craft.itemId,\n                name: craft.itemName\n            },\n            craftCost: craft.craftCost,\n            sellPrice: craft.sellPrice,\n            ingredients: craft.ingredients.map(parseCraftIngredient),\n            median: craft.median,\n            volume: craft.volume,\n            requiredCollection: craft.reqCollection\n                ? {\n                      name: craft.reqCollection.name,\n                      level: craft.reqCollection.level\n                  }\n                : null,\n            requiredSlayer: craft.reqSlayer\n                ? {\n                      name: craft.reqSlayer.name,\n                      level: craft.reqSlayer.level\n                  }\n                : null\n        } as ProfitableCraft\n        c.item.name = convertTagToName(c.item.name)\n        c.item.iconUrl = api.getItemImageUrl(c.item)\n\n        return c\n    })\n}\n\nexport function parseLowSupplyItem(item): LowSupplyItem {\n    let lowSupplyItem = parseItem(item) as LowSupplyItem\n    lowSupplyItem.supply = item.supply\n    lowSupplyItem.medianPrice = item.median\n    lowSupplyItem.volume = item.volume\n    lowSupplyItem.iconUrl = api.getItemImageUrl(item)\n    lowSupplyItem.name = convertTagToName(item.tag)\n    return lowSupplyItem\n}\n\nexport function parseSkyblockProfile(profile): SkyblockProfile {\n    return {\n        current: profile.current,\n        cuteName: profile.cute_name,\n        id: profile.profile_id\n    }\n}\n\nexport function parseCraftingRecipe(recipe): CraftingRecipe {\n    return {\n        A1: parseCraftingRecipeSlot(recipe.A1),\n        A2: parseCraftingRecipeSlot(recipe.A2),\n        A3: parseCraftingRecipeSlot(recipe.A3),\n        B1: parseCraftingRecipeSlot(recipe.B1),\n        B2: parseCraftingRecipeSlot(recipe.B2),\n        B3: parseCraftingRecipeSlot(recipe.B3),\n        C1: parseCraftingRecipeSlot(recipe.C1),\n        C2: parseCraftingRecipeSlot(recipe.C2),\n        C3: parseCraftingRecipeSlot(recipe.C3)\n    }\n}\n\n// parses a crafting recipe slot string into a CraftingRecipeSlot object\n// example input: \"ENCHANTED_EYE_OF_ENDER:16\"\nexport function parseCraftingRecipeSlot(craftingRecipeSlotString: string): CraftingRecipeSlot | undefined {\n    if (!craftingRecipeSlotString) return undefined\n    let count = parseInt(craftingRecipeSlotString.split(':')[1])\n    return {\n        tag: craftingRecipeSlotString.split(':')[0],\n        count: count || 0\n    }\n}\n\nexport function parseItemSummary(price): ItemPriceSummary {\n    return {\n        max: price.max,\n        mean: price.mean,\n        median: price.median,\n        min: price.min,\n        mode: price.mode,\n        volume: price.volume\n    }\n}\n\nexport function parsePaymentResponse(payment): PaymentResponse {\n    return {\n        id: payment.id,\n        directLink: payment.dirctLink\n    } as PaymentResponse\n}\n\nexport function parseKatFlip(katFlip): KatFlip {\n    let flip = {\n        coreData: {\n            amount: katFlip.coreData.amount,\n            cost: katFlip.coreData.cost,\n            hours: katFlip.coreData.hours,\n            item: {\n                tag: katFlip.coreData.itemTag,\n                name: katFlip.coreData.name,\n                tier: katFlip.coreData.baseRarity\n            },\n            material: katFlip.coreData.material\n        },\n        cost: katFlip.purchaseCost + katFlip.materialCost + katFlip.upgradeCost,\n        purchaseCost: katFlip.purchaseCost,\n        materialCost: katFlip.materialCost,\n        median: katFlip.median,\n        originAuctionUUID: katFlip.originAuction,\n        profit: katFlip.profit,\n        referenceAuctionUUID: katFlip.referenceAuction,\n        targetRarity: katFlip.targetRarity,\n        upgradeCost: katFlip.upgradeCost,\n        volume: katFlip.volume,\n        originAuctionName: katFlip.originAuctionName\n    } as KatFlip\n    flip.coreData.item.iconUrl = api.getItemImageUrl(flip.coreData.item)\n    return flip\n}\n\nexport function parseFlipTrackingFlip(flip): FlipTrackingFlip {\n    let flipTrackingFlip = {\n        item: {\n            tag: flip?.itemTag,\n            name: flip?.itemName || flip?.itemTag || '---',\n            tier: flip?.tier\n        },\n        originAuction: flip?.originAuction,\n        pricePaid: flip?.pricePaid,\n        soldAuction: flip?.soldAuction,\n        soldFor: flip?.soldFor,\n        uId: flip?.uId,\n        finder: getFlipFinders([flip.finder || 0])[0],\n        sellTime: parseDate(flip?.sellTime),\n        buyTime: parseDate(flip?.buyTime),\n        profit: flip?.profit,\n        propertyChanges: flip.propertyChanges?.map(change => {\n            return {\n                description: change.description,\n                effect: change.effect\n            }\n        }),\n        flags: flip.flags ? new Set(flip.flags.split(', ')) : new Set()\n    } as FlipTrackingFlip\n    flipTrackingFlip.item.iconUrl = api.getItemImageUrl(flipTrackingFlip?.item)\n    return flipTrackingFlip\n}\n\nexport function parseBazaarOrder(order): BazaarOrder {\n    return {\n        amount: order.amount,\n        pricePerUnit: order.pricePerUnit,\n        orders: order.orders\n    }\n}\n\nexport function parseBazaarSnapshot(snapshot): BazaarSnapshot {\n    return {\n        item: parseItem({ tag: snapshot.productId }),\n        buyData: {\n            orderCount: snapshot.buyOrdersCount,\n            price: snapshot.buyPrice,\n            volume: snapshot.buyVolume,\n            moving: snapshot.buyMovingWeek\n        },\n        sellData: {\n            orderCount: snapshot.sellOrdersCount,\n            price: snapshot.sellPrice,\n            volume: snapshot.sellVolume,\n            moving: snapshot.sellMovingWeek\n        },\n        timeStamp: parseDate(snapshot.timeStamp),\n        buyOrders: snapshot.buyOrders.map(parseBazaarOrder),\n        sellOrders: snapshot.sellOrders.map(parseBazaarOrder)\n    }\n}\n\nexport function parseFlipTrackingResponse(flipTrackingResponse): FlipTrackingResponse {\n    return {\n        flips: flipTrackingResponse?.flips ? flipTrackingResponse.flips.map(parseFlipTrackingFlip) : [],\n        totalProfit: flipTrackingResponse?.totalProfit || 0\n    }\n}\n\nexport function parseBazaarPrice(bazaarPrice): BazaarPrice {\n    return {\n        buyData: {\n            max: bazaarPrice.maxBuy || 0,\n            min: bazaarPrice.minBuy || 0,\n            price: bazaarPrice.buy || 0,\n            volume: bazaarPrice.buyVolume || 0,\n            moving: bazaarPrice.buyMovingWeek || 0\n        },\n        sellData: {\n            max: bazaarPrice.maxSell || 0,\n            min: bazaarPrice.minSell || 0,\n            price: bazaarPrice.sell || 0,\n            volume: bazaarPrice.sellVolume || 0,\n            moving: bazaarPrice.sellMovingWeek || 0\n        },\n        timestamp: parseDate(bazaarPrice.timestamp)\n    }\n}\n\nexport function parsePrivacySettings(privacySettings): PrivacySettings {\n    return {\n        allowProxy: privacySettings.allowProxy,\n        autoStart: privacySettings.autoStart,\n        chatRegex: privacySettings.chatRegex,\n        collectChat: privacySettings.collectChat,\n        collectChatClicks: privacySettings.collectChatClicks,\n        collectEntities: privacySettings.collectEntities,\n        collectInvClick: privacySettings.collectInvClick,\n        collectInventory: privacySettings.collectInventory,\n        collectLobbyChanges: privacySettings.collectLobbyChanges,\n        collectScoreboard: privacySettings.collectScoreboard,\n        collectTab: privacySettings.collectTab,\n        commandPrefixes: privacySettings.commandPrefixes,\n        extendDescriptions: privacySettings.extendDescriptions\n    }\n}\n\nexport function parsePremiumProducts(productsObject): PremiumProduct[] {\n    let products: PremiumProduct[] = []\n    Object.keys(productsObject).forEach(key => {\n        products.push({\n            productSlug: key,\n            expires: parseDate(productsObject[key].expiresAt)\n        })\n    })\n    return products\n}\n\nexport function parseMayorData(mayorData): MayorData {\n    return {\n        end: parseDate(mayorData.end),\n        start: parseDate(mayorData.start),\n        winner: mayorData.winner,\n        year: mayorData.yearF\n    }\n}\n\nexport function parseInventoryData(data): InventoryData {\n    if (data === null || data.itemName === null) {\n        return data\n    }\n\n    if (data.enchantments !== null) {\n        Object.keys(data?.enchantments).forEach(key => {\n            data.enchantments[key] = data.enchantments[key].toString()\n        })\n    }\n\n    return {\n        color: data.data,\n        count: data.count,\n        description: data.description,\n        enchantments: data.enchantments || {},\n        extraAttributes: data.extraAttributes,\n        icon: api.getItemImageUrl({ tag: data.tag }),\n        id: data.id,\n        itemName: data.itemName,\n        tag: data.tag\n    }\n}\n\nexport function parseTradeObject(data): TradeObject {\n    return {\n        id: data.id,\n        playerUuid: data.playerUuid,\n        playerName: data.playerName,\n        buyerUuid: data.buyerUuid,\n        item: parseInventoryData(data.item),\n        wantedItems: data.wantedItems ? data.wantedItems.filter(wantedItem => wantedItem.tag !== 'SKYBLOCK_COIN') : [],\n        wantedCoins: data.wantedItems ? data.wantedItems.find(wantedItem => wantedItem.tag === 'SKYBLOCK_COIN')?.filters?.Count : null,\n        timestamp: parseDate(data.timestamp),\n        coins: data.coins\n    }\n}\n\nexport function parseTransaction(transaction): Transaction {\n    return {\n        productId: transaction.productId,\n        reference: transaction.reference,\n        amount: transaction.amount,\n        timeStamp: parseDate(transaction.timeStamp)\n    }\n}\n\nexport function parseOwnerHistory(ownerHistory): OwnerHistory {\n    return {\n        buyer: parsePlayer(ownerHistory.buyer),\n        seller: parsePlayer(ownerHistory.seller),\n        highestBid: ownerHistory.highestBid,\n        itemTag: ownerHistory.itemTag,\n        uuid: ownerHistory.uuid,\n        timestamp: parseDate(ownerHistory.timestamp)\n    }\n}\n\nexport function parseArchivedAuctions(archivedAuctionsResponse: any): ArchivedAuctionResponse {\n    return {\n        queryStatus: archivedAuctionsResponse.queryStatus,\n        auctions: archivedAuctionsResponse.auctions.map(a => {\n            return {\n                end: parseDate(a.end),\n                price: a.price,\n                seller: parsePlayer({\n                    name: a.playerName,\n                    uuid: a.seller\n                }),\n                uuid: a.uuid\n            } as ArchivedAuction\n        })\n    }\n}\n\nexport function parsePremiumSubscription(subscription: any): PremiumSubscription {\n    return {\n        externalId: subscription.externalId,\n        endsAt: subscription.endsAt ? parseDate(subscription.endsAt) : undefined,\n        productName: subscription.productName,\n        paymentAmount: subscription.paymentAmount,\n        renewsAt: parseDate(subscription.renewsAt),\n        createdAt: parseDate(subscription.createdAt)\n    }\n}\n\nexport function parseCraftingInstructions(craftingInstructions: any): CraftingInstructions {\n    return {\n        itemTag: craftingInstructions.itemTag,\n        recipe: parseCraftingRecipe(craftingInstructions.recipe),\n        copyCommands: craftingInstructions.copyCommands,\n        detailsPath: craftingInstructions.detailsPath\n    }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAEO,SAAS,oBAAoB,GAAQ;IACxC,OAAO;QACH,MAAM,IAAI,IAAI,IAAI,IAAI,SAAS;QAC/B,KAAK,UAAU,IAAI,GAAG;QACtB,MAAM;YACF,MAAM,IAAI,QAAQ;YAClB,KAAK,IAAI,GAAG;QAChB;QACA,YAAY,IAAI,UAAU;QAC1B,YAAY,IAAI,aAAa;QAC7B,KAAK,IAAI,GAAG;IAChB;AACJ;AAEO,SAAS,aAAa,GAAQ;IACjC,OAAO;QACH,WAAW,IAAI,SAAS;QACxB,QAAQ,IAAI,MAAM;QAClB,QAAQ,YAAY,IAAI,MAAM;QAC9B,WAAW,UAAU,IAAI,SAAS;QAClC,WAAW,IAAI,SAAS;QACxB,KAAK,IAAI,GAAG;IAChB;AACJ;AAEO,SAAS,aAAa,OAAY;IACrC,IAAI,gBAAgB;QAChB,MAAM,QAAQ,IAAI,IAAI,QAAQ,SAAS;QACvC,KAAK,UAAU,QAAQ,GAAG;QAC1B,MAAM;YACF,KAAK,QAAQ,GAAG;YAChB,MAAM,QAAQ,QAAQ,IAAI,QAAQ,IAAI;QAC1C;QACA,aAAa,QAAQ,WAAW;QAChC,YAAY,QAAQ,UAAU;QAC9B,KAAK,QAAQ,GAAG;IACpB;IAEA,cAAc,IAAI,CAAC,OAAO,GAAG,oHAAA,CAAA,UAAG,CAAC,eAAe,CAAC,cAAc,IAAI;IAEnE,OAAO;AACX;AAEO,SAAS,mBAAmB,aAAkB;IACjD,OAAO;QACH,MAAM,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA;YACzB,OAAO;gBACH,MAAM,IAAI,IAAI;gBACd,YAAY,IAAI,UAAU;gBAC1B,KAAK,UAAU,IAAI,GAAG;gBACtB,YAAY,IAAI,UAAU;gBAC1B,MAAM;oBACF,KAAK,IAAI,GAAG;oBACZ,MAAM,IAAI,QAAQ;gBACtB;YACJ;QACJ;QACA,UAAU,cAAc,QAAQ,CAAC,GAAG,CAAC,CAAA;YACjC,OAAO;gBACH,MAAM,QAAQ,SAAS;gBACvB,YAAY,QAAQ,UAAU;gBAC9B,KAAK,UAAU,QAAQ,GAAG;gBAC1B,MAAM;oBACF,KAAK,QAAQ,GAAG;oBAChB,MAAM,QAAQ,QAAQ;gBAC1B;gBACA,KAAK,QAAQ,GAAG;YACpB;QACJ;IACJ;AACJ;AAEO,SAAS,eAAe,SAAc;IACzC,OAAO;QACH,MAAM,UAAU,UAAU,IAAI;QAC9B,KAAK,UAAU,GAAG;QAClB,KAAK,UAAU,GAAG;QAClB,KAAK,UAAU,GAAG;QAClB,QAAQ,UAAU,MAAM;IAC5B;AACJ;AAEO,SAAS,UAAU,IAAS;IAC/B,MAAM,kBAAkB;QACpB,KAAK;QACL,QAAQ;QACR,QAAQ;QACR,MAAM;QACN,OAAO;QACP,MAAM;QACN,QAAQ;QACR,MAAM;QACN,OAAO;IACX;IAEA,MAAM,qBAAqB;QACvB,KAAK;QACL,QAAQ;QACR,QAAQ;QACR,MAAM;QACN,OAAO;QACP,MAAM;QACN,QAAQ;QACR,MAAM;QACN,OAAO;IACX;IAEA,IAAI,SAAS;QACT,KAAK,KAAK,GAAG;QACb,MAAM,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,EAAE,IAAI,KAAK,QAAQ,CAAC,EAAE,CAAC,IAAI,GAAG,KAAK,QAAQ,CAAC,EAAE,CAAC,IAAI,GAAG,KAAK,QAAQ,IAAI,KAAK,IAAI;QACrH,UAAU,KAAK,QAAQ;QACvB,SAAS,oHAAA,CAAA,UAAG,CAAC,eAAe,CAAC;QAC7B,MAAM,KAAK,IAAI;QACf,QAAQ,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EAAE,KAAK,KAAK,EAAE;IAChC;IAEA,IAAI,KAAK,OAAO,IAAI,KAAK,OAAO,CAAC,kBAAkB,IAAI,KAAK,GAAG,KAAK,kBAAkB;QAClF,OAAO,OAAO,GAAG,eAAe,CAAC,KAAK,OAAO,CAAC,kBAAkB,CAAC,IAAI,OAAO,OAAO;IACvF;IACA,IAAI,KAAK,OAAO,IAAI,KAAK,OAAO,CAAC,kBAAkB,IAAI,KAAK,GAAG,KAAK,oBAAoB;QACpF,OAAO,OAAO,GAAG,kBAAkB,CAAC,KAAK,OAAO,CAAC,kBAAkB,CAAC,IAAI,OAAO,OAAO;IAC1F;IAEA,OAAO;AACX;AAEO,SAAS,iBAAiB,WAAgB;IAC7C,QAAQ,GAAG,CAAC;IACZ,OAAO;QACH,IAAI,YAAY,EAAE;QAClB,OAAO,YAAY,KAAK;QACxB,MAAM,YAAY,IAAI,GAAG,CAAA,GAAA,sHAAA,CAAA,mBAAgB,AAAD,EAAE,YAAY,IAAI,IAAI;QAC9D,OAAO,YAAY,KAAK;IAC5B;AACJ;AAEO,SAAS,sBAAsB,IAAS;IAC3C,IAAI,YAAY;QACZ,OAAQ,KAAK,IAAI;YACb,KAAK;gBACD,OAAO,WAAW,KAAK,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;YAC3C,KAAK;gBACD,OAAO,WAAW,KAAK,EAAE;YAC7B,KAAK;gBACD,OAAO,aAAa,KAAK,EAAE;YAC/B,KAAK;gBACD,OAAO,cAAc,KAAK,EAAE;QACpC;QACA,OAAO;IACX;IAEA,OAAO;QACH,UAAU;YACN,MAAM,KAAK,IAAI;YACf,SAAS,KAAK,GAAG,GAAG,2BAA2B,KAAK,GAAG,GAAG,KAAK,IAAI,KAAK,SAAS,oHAAA,CAAA,UAAG,CAAC,eAAe,CAAC,QAAQ,oHAAA,CAAA,UAAG,CAAC,eAAe,CAAC,QAAQ;QAC7I;QACA,MAAM,KAAK,IAAI;QACf,OAAO;QACP,iBAAiB,KAAK,IAAI,KAAK,WAAW,IAAI,gBAAgB,KAAK,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG,iBAAiB;QACvG,IAAI,KAAK,EAAE;QACX,MAAM,KAAK,IAAI;IACnB;AACJ;AAEO,SAAS,YAAY,MAAW;IACnC,IAAI,OAAO,WAAW,UAAU;QAC5B,SAAS;YACL,MAAM;YACN,MAAM;QACV;IACJ;IACA,OAAO;QACH,MAAM,OAAO,IAAI;QACjB,MAAM,OAAO,IAAI;QACjB,SAAS,OAAO,OAAO,GAAG,OAAO,OAAO,GAAG,YAAY,kCAAkC,OAAO,IAAI,GAAG;IAC3G;AACJ;AAEO,SAAS,oBAAoB,cAAmB;IACnD,OAAO;QACH,SAAS;YACL,MAAM,eAAe,IAAI;YACzB,KAAK,UAAU,eAAe,GAAG;YACjC,YAAY,eAAe,gBAAgB;YAC3C,aAAa,eAAe,WAAW;YACvC,MAAM,UAAU;YAChB,KAAK,eAAe,GAAG;QAC3B;QACA,OAAO,UAAU,eAAe,KAAK;QACrC,WAAW,eAAe,SAAS;QACnC,YAAY,YAAY,eAAe,UAAU;QACjD,MAAM,eAAe,IAAI,CAAC,GAAG,CAAC,CAAA;YAC1B,OAAO,aAAa;QACxB;QACA,SAAS,eAAe,OAAO;QAC/B,MAAM,eAAe,IAAI;QACzB,OAAO,eAAe,KAAK;QAC3B,cAAc,eAAe,YAAY,CAAC,GAAG,CAAC,CAAA;YAC1C,OAAO,iBAAiB;QAC5B;QACA,WAAW,eAAe,SAAS;QACnC,SAAS,eAAe,OAAO;QAC/B,SAAS,eAAe,OAAO,GAAG,eAAe,OAAO,GAAG;QAC3D,eAAe,UAAU,eAAe,aAAa;QACrD,MAAM,eAAe,IAAI;IAC7B;AACJ;AAEO,SAAS,uBAAuB,aAAqB;IACxD,IAAI,OAAO,OAAO,IAAI,CAAC,wHAAA,CAAA,mBAAgB;IACvC,IAAI,WAA+B,EAAE;IAErC,IAAK,IAAI,IAAI,KAAK,MAAM,EAAE,KAAK,GAAG,IAAK;QACnC,IAAI,YAAY,wHAAA,CAAA,mBAAgB,CAAC,IAAI,CAAC,EAAE,CAAC;QACzC,IAAI,OAAO,wHAAA,CAAA,mBAAgB,CAAC,UAAU,KAAK,UAAU;YACjD,IAAI,SAAS,SAAS,wHAAA,CAAA,mBAAgB,CAAC,UAAU;YACjD,IAAI,UAAU,iBAAiB,SAAS,GAAG;gBACvC,iBAAiB;gBACjB,SAAS,IAAI,CAAC,wHAAA,CAAA,mBAAgB,CAAC,OAAO,QAAQ,GAAG;YACrD;QACJ;IACJ;IAEA,OAAO;AACX;AAEA,SAAS,qBAAqB,QAA4B,EAAE,UAAkB;IAC1E,IAAI;IACJ,OAAQ,wHAAA,CAAA,mBAAgB,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,GAAG;QAC5C,KAAK,wHAAA,CAAA,mBAAgB,CAAC,GAAG;QACzB,KAAK,wHAAA,CAAA,mBAAgB,CAAC,iBAAiB;QACvC,KAAK,wHAAA,CAAA,mBAAgB,CAAC,gBAAgB;QACtC,KAAK,wHAAA,CAAA,mBAAgB,CAAC,gBAAgB;YAClC,IAAI,CAAC,YAAY;gBACb,OAAO;YACX,OAAO;gBACH,OAAO;YACX;YACA;QACJ,KAAK,wHAAA,CAAA,mBAAgB,CAAC,MAAM;QAC5B,KAAK,wHAAA,CAAA,mBAAgB,CAAC,IAAI;QAC1B,KAAK,wHAAA,CAAA,mBAAgB,CAAC,sBAAsB;QAC5C,KAAK,wHAAA,CAAA,mBAAgB,CAAC,kBAAkB;YACpC,OAAO;YACP;QACJ,KAAK,wHAAA,CAAA,mBAAgB,CAAC,OAAO;YACzB,OAAO;YACP;IACR;IACA,OAAO;AACX;AAEO,SAAS,kBAAkB,YAAiB;IAC/C,OAAO;QACH,IAAI,aAAa,EAAE;QACnB,OAAO,aAAa,KAAK;QACzB,SAAS,aAAa,OAAO;QAC7B,OAAO,uBAAuB,aAAa,IAAI;QAC/C,MAAM,qBAAqB,uBAAuB,aAAa,IAAI,GAAG,aAAa,MAAM;QACzF,QAAQ,aAAa,MAAM,GAAG,KAAK,KAAK,CAAC,aAAa,MAAM,IAAI;IACpE;AACJ;AAEO,SAAS,cAAc,QAAa;IACvC,OAAO,SAAS,GAAG,CAAC,CAAC;QACjB,OAAO;YACH,WAAW,QAAQ,IAAI;YACvB,aAAa,QAAQ,WAAW;YAChC,OAAO,QAAQ,KAAK;YACpB,kBAAkB,QAAQ,gBAAgB;YAC1C,MAAM,QAAQ,IAAI;QACtB;IACJ;AACJ;AAEO,SAAS,mBAAmB,OAAO;IACtC,OAAO;QACH,KAAK,UAAU,QAAQ,GAAG;QAC1B,YAAY,QAAQ,UAAU;QAC9B,OAAO,QAAQ,KAAK;QACpB,QAAQ,YAAY,QAAQ,MAAM;QAClC,MAAM,QAAQ,IAAI;IACtB;AACJ;AAEO,SAAS,iBAAiB,IAAI;IACjC,IAAI,aAAa;QACb,UAAU;QACV,QAAQ,KAAK,MAAM;QACnB,MAAM,KAAK,IAAI;QACf,MAAM,KAAK,IAAI;QACf,QAAQ,KAAK,MAAM;QACnB,KAAK,KAAK,GAAG;QACb,MAAM;YACF,KAAK,KAAK,GAAG;YACb,MAAM,KAAK,IAAI;YACf,MAAM,KAAK,IAAI;QACnB;QACA,iBAAiB,KAAK,eAAe;QACrC,MAAM,KAAK,IAAI;QACf,YAAY,KAAK,UAAU;QAC3B,WAAW,KAAK,SAAS;QACzB,OAAO,KAAK,IAAI;QAChB,QAAQ,KAAK,MAAM;QACnB,QAAQ,KAAK,MAAM;IACvB;IACA,WAAW,IAAI,CAAC,OAAO,GAAG,oHAAA,CAAA,UAAG,CAAC,eAAe,CAAC,WAAW,IAAI;IAE7D,OAAO;AACX;AAEO,SAAS,mBAAmB,MAAM;IACrC,OAAO;QACH,OAAO,OAAO,KAAK;QACnB,KAAK,OAAO,GAAG;QACf,KAAK,OAAO,GAAG;IACnB;AACJ;AAEO,SAAS,aAAa,OAAO;IAChC,OAAO;QACH,SAAS;YACL,OAAO,QAAQ,OAAO,CAAC,KAAK;YAC5B,OAAO,QAAQ,OAAO,CAAC,KAAK;YAC5B,eAAe,QAAQ,OAAO,CAAC,aAAa;YAC5C,cAAc,QAAQ,OAAO,CAAC,YAAY;YAC1C,eAAe,QAAQ,OAAO,CAAC,aAAa;QAChD;QACA,gBAAgB,QAAQ,cAAc;QACtC,cAAc,QAAQ,YAAY;QAClC,oBAAoB,QAAQ,kBAAkB;QAC9C,YAAY,QAAQ,UAAU;IAClC;AACJ;AAEO,SAAS,kBAAkB,YAAY;IAC1C,OAAO;QACH,MAAM,aAAa,IAAI;QACvB,SAAS,aAAa,OAAO;QAC7B,MAAM,aAAa,IAAI;QACvB,aAAa,aAAa,WAAW;IACzC;AACJ;AAEO,SAAS,iBAAiB,WAAW;IACxC,OAAO;QACH,OAAO,YAAY,KAAK;QACxB,MAAM,YAAY,IAAI;QACtB,QAAQ,YAAY,MAAM;QAC1B,OAAO,YAAY,KAAK;IAC5B;AACJ;AAEO,SAAS,6BAA6B,uBAAuB;IAChE,OAAO;QACH,MAAM,wBAAwB,IAAI;QAClC,aAAa,wBAAwB,WAAW;IACpD;AACJ;AAEO,SAAS,UAAU,UAAkB;IACxC,IAAI,CAAC,YAAY;QACb,OAAO,IAAI;IACf;IACA,IAAI,OAAO,eAAe,YAAY,OAAO,AAAC,WAAmB,OAAO,KAAK,YAAY;QACrF,aAAa,AAAC,WAAoB,WAAW;IACjD;IACA,IAAI,WAAW,KAAK,CAAC,CAAC,OAAO,KAAK;QAC9B,OAAO,IAAI,KAAK;IACpB;IACA,OAAO,IAAI,KAAK,aAAa;AACjC;AAEO,SAAS,sBAAsB,SAAgB,EAAE;IACpD,IAAI,uBAAuB,CAAC;QACxB,IAAI,SAAS;YACT,MAAM,WAAW,IAAI;YACrB,OAAO,WAAW,KAAK;YACvB,MAAM,WAAW,IAAI;YACrB,MAAM;gBACF,KAAK,WAAW,MAAM;YAC1B;QACJ;QAEA,OAAO,IAAI,CAAC,IAAI,GAAG,CAAA,GAAA,sHAAA,CAAA,mBAAgB,AAAD,EAAE,OAAO,IAAI,CAAC,GAAG;QACnD,OAAO,IAAI,CAAC,OAAO,GAAG,oHAAA,CAAA,UAAG,CAAC,eAAe,CAAC,OAAO,IAAI;QACrD,IAAI,OAAO,IAAI,KAAK,SAAS;YACzB,IAAI,UAAU,OAAO,IAAI,CAAC,CAAA,QAAS,MAAM,MAAM,KAAK,OAAO,IAAI,CAAC,GAAG;YACnE,IAAI,CAAC,SAAS;gBACV,QAAQ,GAAG,CAAC,CAAC,oBAAoB,EAAE,KAAK,SAAS,CAAC,SAAS;gBAC3D,OAAO;YACX;YACA,OAAO,WAAW,GAAG,QAAQ,WAAW,CAAC,GAAG,CAAC;QACjD;QACA,OAAO;IACX;IAEA,OAAO,OAAO,GAAG,CAAC,CAAA;QACd,IAAI,IAAI;YACJ,MAAM;gBACF,KAAK,MAAM,MAAM;gBACjB,MAAM,MAAM,QAAQ;YACxB;YACA,WAAW,MAAM,SAAS;YAC1B,WAAW,MAAM,SAAS;YAC1B,aAAa,MAAM,WAAW,CAAC,GAAG,CAAC;YACnC,QAAQ,MAAM,MAAM;YACpB,QAAQ,MAAM,MAAM;YACpB,oBAAoB,MAAM,aAAa,GACjC;gBACI,MAAM,MAAM,aAAa,CAAC,IAAI;gBAC9B,OAAO,MAAM,aAAa,CAAC,KAAK;YACpC,IACA;YACN,gBAAgB,MAAM,SAAS,GACzB;gBACI,MAAM,MAAM,SAAS,CAAC,IAAI;gBAC1B,OAAO,MAAM,SAAS,CAAC,KAAK;YAChC,IACA;QACV;QACA,EAAE,IAAI,CAAC,IAAI,GAAG,CAAA,GAAA,sHAAA,CAAA,mBAAgB,AAAD,EAAE,EAAE,IAAI,CAAC,IAAI;QAC1C,EAAE,IAAI,CAAC,OAAO,GAAG,oHAAA,CAAA,UAAG,CAAC,eAAe,CAAC,EAAE,IAAI;QAE3C,OAAO;IACX;AACJ;AAEO,SAAS,mBAAmB,IAAI;IACnC,IAAI,gBAAgB,UAAU;IAC9B,cAAc,MAAM,GAAG,KAAK,MAAM;IAClC,cAAc,WAAW,GAAG,KAAK,MAAM;IACvC,cAAc,MAAM,GAAG,KAAK,MAAM;IAClC,cAAc,OAAO,GAAG,oHAAA,CAAA,UAAG,CAAC,eAAe,CAAC;IAC5C,cAAc,IAAI,GAAG,CAAA,GAAA,sHAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK,GAAG;IAC9C,OAAO;AACX;AAEO,SAAS,qBAAqB,OAAO;IACxC,OAAO;QACH,SAAS,QAAQ,OAAO;QACxB,UAAU,QAAQ,SAAS;QAC3B,IAAI,QAAQ,UAAU;IAC1B;AACJ;AAEO,SAAS,oBAAoB,MAAM;IACtC,OAAO;QACH,IAAI,wBAAwB,OAAO,EAAE;QACrC,IAAI,wBAAwB,OAAO,EAAE;QACrC,IAAI,wBAAwB,OAAO,EAAE;QACrC,IAAI,wBAAwB,OAAO,EAAE;QACrC,IAAI,wBAAwB,OAAO,EAAE;QACrC,IAAI,wBAAwB,OAAO,EAAE;QACrC,IAAI,wBAAwB,OAAO,EAAE;QACrC,IAAI,wBAAwB,OAAO,EAAE;QACrC,IAAI,wBAAwB,OAAO,EAAE;IACzC;AACJ;AAIO,SAAS,wBAAwB,wBAAgC;IACpE,IAAI,CAAC,0BAA0B,OAAO;IACtC,IAAI,QAAQ,SAAS,yBAAyB,KAAK,CAAC,IAAI,CAAC,EAAE;IAC3D,OAAO;QACH,KAAK,yBAAyB,KAAK,CAAC,IAAI,CAAC,EAAE;QAC3C,OAAO,SAAS;IACpB;AACJ;AAEO,SAAS,iBAAiB,KAAK;IAClC,OAAO;QACH,KAAK,MAAM,GAAG;QACd,MAAM,MAAM,IAAI;QAChB,QAAQ,MAAM,MAAM;QACpB,KAAK,MAAM,GAAG;QACd,MAAM,MAAM,IAAI;QAChB,QAAQ,MAAM,MAAM;IACxB;AACJ;AAEO,SAAS,qBAAqB,OAAO;IACxC,OAAO;QACH,IAAI,QAAQ,EAAE;QACd,YAAY,QAAQ,SAAS;IACjC;AACJ;AAEO,SAAS,aAAa,OAAO;IAChC,IAAI,OAAO;QACP,UAAU;YACN,QAAQ,QAAQ,QAAQ,CAAC,MAAM;YAC/B,MAAM,QAAQ,QAAQ,CAAC,IAAI;YAC3B,OAAO,QAAQ,QAAQ,CAAC,KAAK;YAC7B,MAAM;gBACF,KAAK,QAAQ,QAAQ,CAAC,OAAO;gBAC7B,MAAM,QAAQ,QAAQ,CAAC,IAAI;gBAC3B,MAAM,QAAQ,QAAQ,CAAC,UAAU;YACrC;YACA,UAAU,QAAQ,QAAQ,CAAC,QAAQ;QACvC;QACA,MAAM,QAAQ,YAAY,GAAG,QAAQ,YAAY,GAAG,QAAQ,WAAW;QACvE,cAAc,QAAQ,YAAY;QAClC,cAAc,QAAQ,YAAY;QAClC,QAAQ,QAAQ,MAAM;QACtB,mBAAmB,QAAQ,aAAa;QACxC,QAAQ,QAAQ,MAAM;QACtB,sBAAsB,QAAQ,gBAAgB;QAC9C,cAAc,QAAQ,YAAY;QAClC,aAAa,QAAQ,WAAW;QAChC,QAAQ,QAAQ,MAAM;QACtB,mBAAmB,QAAQ,iBAAiB;IAChD;IACA,KAAK,QAAQ,CAAC,IAAI,CAAC,OAAO,GAAG,oHAAA,CAAA,UAAG,CAAC,eAAe,CAAC,KAAK,QAAQ,CAAC,IAAI;IACnE,OAAO;AACX;AAEO,SAAS,sBAAsB,IAAI;IACtC,IAAI,mBAAmB;QACnB,MAAM;YACF,KAAK,MAAM;YACX,MAAM,MAAM,YAAY,MAAM,WAAW;YACzC,MAAM,MAAM;QAChB;QACA,eAAe,MAAM;QACrB,WAAW,MAAM;QACjB,aAAa,MAAM;QACnB,SAAS,MAAM;QACf,KAAK,MAAM;QACX,QAAQ,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE;YAAC,KAAK,MAAM,IAAI;SAAE,CAAC,CAAC,EAAE;QAC7C,UAAU,UAAU,MAAM;QAC1B,SAAS,UAAU,MAAM;QACzB,QAAQ,MAAM;QACd,iBAAiB,KAAK,eAAe,EAAE,IAAI,CAAA;YACvC,OAAO;gBACH,aAAa,OAAO,WAAW;gBAC/B,QAAQ,OAAO,MAAM;YACzB;QACJ;QACA,OAAO,KAAK,KAAK,GAAG,IAAI,IAAI,KAAK,KAAK,CAAC,KAAK,CAAC,SAAS,IAAI;IAC9D;IACA,iBAAiB,IAAI,CAAC,OAAO,GAAG,oHAAA,CAAA,UAAG,CAAC,eAAe,CAAC,kBAAkB;IACtE,OAAO;AACX;AAEO,SAAS,iBAAiB,KAAK;IAClC,OAAO;QACH,QAAQ,MAAM,MAAM;QACpB,cAAc,MAAM,YAAY;QAChC,QAAQ,MAAM,MAAM;IACxB;AACJ;AAEO,SAAS,oBAAoB,QAAQ;IACxC,OAAO;QACH,MAAM,UAAU;YAAE,KAAK,SAAS,SAAS;QAAC;QAC1C,SAAS;YACL,YAAY,SAAS,cAAc;YACnC,OAAO,SAAS,QAAQ;YACxB,QAAQ,SAAS,SAAS;YAC1B,QAAQ,SAAS,aAAa;QAClC;QACA,UAAU;YACN,YAAY,SAAS,eAAe;YACpC,OAAO,SAAS,SAAS;YACzB,QAAQ,SAAS,UAAU;YAC3B,QAAQ,SAAS,cAAc;QACnC;QACA,WAAW,UAAU,SAAS,SAAS;QACvC,WAAW,SAAS,SAAS,CAAC,GAAG,CAAC;QAClC,YAAY,SAAS,UAAU,CAAC,GAAG,CAAC;IACxC;AACJ;AAEO,SAAS,0BAA0B,oBAAoB;IAC1D,OAAO;QACH,OAAO,sBAAsB,QAAQ,qBAAqB,KAAK,CAAC,GAAG,CAAC,yBAAyB,EAAE;QAC/F,aAAa,sBAAsB,eAAe;IACtD;AACJ;AAEO,SAAS,iBAAiB,WAAW;IACxC,OAAO;QACH,SAAS;YACL,KAAK,YAAY,MAAM,IAAI;YAC3B,KAAK,YAAY,MAAM,IAAI;YAC3B,OAAO,YAAY,GAAG,IAAI;YAC1B,QAAQ,YAAY,SAAS,IAAI;YACjC,QAAQ,YAAY,aAAa,IAAI;QACzC;QACA,UAAU;YACN,KAAK,YAAY,OAAO,IAAI;YAC5B,KAAK,YAAY,OAAO,IAAI;YAC5B,OAAO,YAAY,IAAI,IAAI;YAC3B,QAAQ,YAAY,UAAU,IAAI;YAClC,QAAQ,YAAY,cAAc,IAAI;QAC1C;QACA,WAAW,UAAU,YAAY,SAAS;IAC9C;AACJ;AAEO,SAAS,qBAAqB,eAAe;IAChD,OAAO;QACH,YAAY,gBAAgB,UAAU;QACtC,WAAW,gBAAgB,SAAS;QACpC,WAAW,gBAAgB,SAAS;QACpC,aAAa,gBAAgB,WAAW;QACxC,mBAAmB,gBAAgB,iBAAiB;QACpD,iBAAiB,gBAAgB,eAAe;QAChD,iBAAiB,gBAAgB,eAAe;QAChD,kBAAkB,gBAAgB,gBAAgB;QAClD,qBAAqB,gBAAgB,mBAAmB;QACxD,mBAAmB,gBAAgB,iBAAiB;QACpD,YAAY,gBAAgB,UAAU;QACtC,iBAAiB,gBAAgB,eAAe;QAChD,oBAAoB,gBAAgB,kBAAkB;IAC1D;AACJ;AAEO,SAAS,qBAAqB,cAAc;IAC/C,IAAI,WAA6B,EAAE;IACnC,OAAO,IAAI,CAAC,gBAAgB,OAAO,CAAC,CAAA;QAChC,SAAS,IAAI,CAAC;YACV,aAAa;YACb,SAAS,UAAU,cAAc,CAAC,IAAI,CAAC,SAAS;QACpD;IACJ;IACA,OAAO;AACX;AAEO,SAAS,eAAe,SAAS;IACpC,OAAO;QACH,KAAK,UAAU,UAAU,GAAG;QAC5B,OAAO,UAAU,UAAU,KAAK;QAChC,QAAQ,UAAU,MAAM;QACxB,MAAM,UAAU,KAAK;IACzB;AACJ;AAEO,SAAS,mBAAmB,IAAI;IACnC,IAAI,SAAS,QAAQ,KAAK,QAAQ,KAAK,MAAM;QACzC,OAAO;IACX;IAEA,IAAI,KAAK,YAAY,KAAK,MAAM;QAC5B,OAAO,IAAI,CAAC,MAAM,cAAc,OAAO,CAAC,CAAA;YACpC,KAAK,YAAY,CAAC,IAAI,GAAG,KAAK,YAAY,CAAC,IAAI,CAAC,QAAQ;QAC5D;IACJ;IAEA,OAAO;QACH,OAAO,KAAK,IAAI;QAChB,OAAO,KAAK,KAAK;QACjB,aAAa,KAAK,WAAW;QAC7B,cAAc,KAAK,YAAY,IAAI,CAAC;QACpC,iBAAiB,KAAK,eAAe;QACrC,MAAM,oHAAA,CAAA,UAAG,CAAC,eAAe,CAAC;YAAE,KAAK,KAAK,GAAG;QAAC;QAC1C,IAAI,KAAK,EAAE;QACX,UAAU,KAAK,QAAQ;QACvB,KAAK,KAAK,GAAG;IACjB;AACJ;AAEO,SAAS,iBAAiB,IAAI;IACjC,OAAO;QACH,IAAI,KAAK,EAAE;QACX,YAAY,KAAK,UAAU;QAC3B,YAAY,KAAK,UAAU;QAC3B,WAAW,KAAK,SAAS;QACzB,MAAM,mBAAmB,KAAK,IAAI;QAClC,aAAa,KAAK,WAAW,GAAG,KAAK,WAAW,CAAC,MAAM,CAAC,CAAA,aAAc,WAAW,GAAG,KAAK,mBAAmB,EAAE;QAC9G,aAAa,KAAK,WAAW,GAAG,KAAK,WAAW,CAAC,IAAI,CAAC,CAAA,aAAc,WAAW,GAAG,KAAK,kBAAkB,SAAS,QAAQ;QAC1H,WAAW,UAAU,KAAK,SAAS;QACnC,OAAO,KAAK,KAAK;IACrB;AACJ;AAEO,SAAS,iBAAiB,WAAW;IACxC,OAAO;QACH,WAAW,YAAY,SAAS;QAChC,WAAW,YAAY,SAAS;QAChC,QAAQ,YAAY,MAAM;QAC1B,WAAW,UAAU,YAAY,SAAS;IAC9C;AACJ;AAEO,SAAS,kBAAkB,YAAY;IAC1C,OAAO;QACH,OAAO,YAAY,aAAa,KAAK;QACrC,QAAQ,YAAY,aAAa,MAAM;QACvC,YAAY,aAAa,UAAU;QACnC,SAAS,aAAa,OAAO;QAC7B,MAAM,aAAa,IAAI;QACvB,WAAW,UAAU,aAAa,SAAS;IAC/C;AACJ;AAEO,SAAS,sBAAsB,wBAA6B;IAC/D,OAAO;QACH,aAAa,yBAAyB,WAAW;QACjD,UAAU,yBAAyB,QAAQ,CAAC,GAAG,CAAC,CAAA;YAC5C,OAAO;gBACH,KAAK,UAAU,EAAE,GAAG;gBACpB,OAAO,EAAE,KAAK;gBACd,QAAQ,YAAY;oBAChB,MAAM,EAAE,UAAU;oBAClB,MAAM,EAAE,MAAM;gBAClB;gBACA,MAAM,EAAE,IAAI;YAChB;QACJ;IACJ;AACJ;AAEO,SAAS,yBAAyB,YAAiB;IACtD,OAAO;QACH,YAAY,aAAa,UAAU;QACnC,QAAQ,aAAa,MAAM,GAAG,UAAU,aAAa,MAAM,IAAI;QAC/D,aAAa,aAAa,WAAW;QACrC,eAAe,aAAa,aAAa;QACzC,UAAU,UAAU,aAAa,QAAQ;QACzC,WAAW,UAAU,aAAa,SAAS;IAC/C;AACJ;AAEO,SAAS,0BAA0B,oBAAyB;IAC/D,OAAO;QACH,SAAS,qBAAqB,OAAO;QACrC,QAAQ,oBAAoB,qBAAqB,MAAM;QACvD,cAAc,qBAAqB,YAAY;QAC/C,aAAa,qBAAqB,WAAW;IACjD;AACJ", "debugId": null}}, {"offset": {"line": 2719, "column": 0}, "map": {"version": 3, "sources": ["file:///app/utils/PremiumTypeUtils.tsx"], "sourcesContent": ["export enum PREMIUM_RANK {\n    STARTER = 1,\n    PREMIUM = 2,\n    PREMIUM_PLUS = 3\n}\n\nexport const TEST_PREMIUM_DAYS = 3\n\nexport const PREMIUM_TYPES: PremiumType[] = [\n    {\n        productId: 'premium',\n        label: 'Premium',\n        durationString: 'month',\n        priority: PREMIUM_RANK.PREMIUM,\n        options: generateNumberOptionArray(1, 12, 'premium', 1800)\n    },\n    {\n        productId: 'premium_plus',\n        label: 'Premium+',\n        durationString: '',\n        priority: PREMIUM_RANK.PREMIUM_PLUS,\n        options: [\n            { value: 1, label: '1 week for 2700', productId: 'premium_plus', price: 2700 },\n            { value: 2, label: '2 weeks', productId: 'premium_plus', price: 2700 },\n            { value: 1, label: '4 weeks (2250/week)', productId: 'premium_plus-weeks', price: 9000 },\n            { value: 1, label: '11 weeks (1964/w)', productId: 'premium_plus-months', price: 21600 },\n            { value: 1, label: '1 hour', productId: 'premium_plus-hour', price: 200 },\n            { value: 1, label: '1 day', productId: 'premium_plus-day', price: 600 }\n        ]\n    },\n    {\n        productId: 'starter_premium',\n        label: 'Starter Premium',\n        durationString: '',\n        priority: PREMIUM_RANK.STARTER,\n        options: [\n            { value: 1, label: '1 day', productId: 'starter_premium-day', price: 24 },\n            { value: 1, label: '1 week', productId: 'starter_premium-week', price: 120 },\n            { value: 4, label: '4 weeks', productId: 'starter_premium-week', price: 120 },\n            { value: 1, label: '6 months', productId: 'starter_premium', price: 1800 },\n            { value: 2, label: '12 months', productId: 'starter_premium', price: 1800 }\n        ]\n    }\n]\n\nfunction generateNumberOptionArray(start: number, end: number, productId: string, priceForOption: number): PremiumTypeOption[] {\n    return (Array(end - start + 1) as any)\n        .fill()\n        .map((_, idx) => start + idx)\n        .map(number => {\n            return {\n                value: number,\n                label: number,\n                productId: productId,\n                price: priceForOption\n            }\n        })\n}\n\nexport function getHighestPriorityPremiumProduct(premiumProducts: PremiumProduct[] = []) {\n    let results = premiumProducts.map(product => {\n        let type = getPremiumType(product)\n        return {\n            productSlug: product.productSlug,\n            productId: type?.productId,\n            priority: type?.priority\n        }\n    })\n\n    let result = results.sort((a, b) => b.priority - a.priority)[0]\n    return premiumProducts.find(product => product.productSlug === result.productSlug && product.expires > new Date())\n}\n\nexport function getPremiumType(product: PremiumProduct) {\n    return [...PREMIUM_TYPES].sort((a, b) => b.productId.localeCompare(a.productId)).find(type => product.productSlug.startsWith(type.productId))\n}\n\nexport function getPremiumLabelForSubscription(subscription: PremiumSubscription) {\n    if (!subscription.productName) {\n        return 'Unknown'\n    }\n    if (subscription.productName.includes('premium_plus')) {\n        return 'Premium+'\n    }\n    if (subscription.productName.includes('premium')) {\n        return 'Premium'\n    }\n    return subscription.productName\n}\n\nexport function hasHighEnoughPremium(products: PremiumProduct[], minPremiumType: PREMIUM_RANK) {\n    let hasHighEnoughPremium = false\n    products.forEach(product => {\n        let type = getPremiumType(product)\n        if (type && type.priority >= minPremiumType && product.expires > new Date()) {\n            hasHighEnoughPremium = true\n        }\n    })\n    return hasHighEnoughPremium\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAO,IAAA,AAAK,sCAAA;;;;WAAA;;AAML,MAAM,oBAAoB;AAE1B,MAAM,gBAA+B;IACxC;QACI,WAAW;QACX,OAAO;QACP,gBAAgB;QAChB,QAAQ;QACR,SAAS,0BAA0B,GAAG,IAAI,WAAW;IACzD;IACA;QACI,WAAW;QACX,OAAO;QACP,gBAAgB;QAChB,QAAQ;QACR,SAAS;YACL;gBAAE,OAAO;gBAAG,OAAO;gBAAmB,WAAW;gBAAgB,OAAO;YAAK;YAC7E;gBAAE,OAAO;gBAAG,OAAO;gBAAW,WAAW;gBAAgB,OAAO;YAAK;YACrE;gBAAE,OAAO;gBAAG,OAAO;gBAAuB,WAAW;gBAAsB,OAAO;YAAK;YACvF;gBAAE,OAAO;gBAAG,OAAO;gBAAqB,WAAW;gBAAuB,OAAO;YAAM;YACvF;gBAAE,OAAO;gBAAG,OAAO;gBAAU,WAAW;gBAAqB,OAAO;YAAI;YACxE;gBAAE,OAAO;gBAAG,OAAO;gBAAS,WAAW;gBAAoB,OAAO;YAAI;SACzE;IACL;IACA;QACI,WAAW;QACX,OAAO;QACP,gBAAgB;QAChB,QAAQ;QACR,SAAS;YACL;gBAAE,OAAO;gBAAG,OAAO;gBAAS,WAAW;gBAAuB,OAAO;YAAG;YACxE;gBAAE,OAAO;gBAAG,OAAO;gBAAU,WAAW;gBAAwB,OAAO;YAAI;YAC3E;gBAAE,OAAO;gBAAG,OAAO;gBAAW,WAAW;gBAAwB,OAAO;YAAI;YAC5E;gBAAE,OAAO;gBAAG,OAAO;gBAAY,WAAW;gBAAmB,OAAO;YAAK;YACzE;gBAAE,OAAO;gBAAG,OAAO;gBAAa,WAAW;gBAAmB,OAAO;YAAK;SAC7E;IACL;CACH;AAED,SAAS,0BAA0B,KAAa,EAAE,GAAW,EAAE,SAAiB,EAAE,cAAsB;IACpG,OAAO,AAAC,MAAM,MAAM,QAAQ,GACvB,IAAI,GACJ,GAAG,CAAC,CAAC,GAAG,MAAQ,QAAQ,KACxB,GAAG,CAAC,CAAA;QACD,OAAO;YACH,OAAO;YACP,OAAO;YACP,WAAW;YACX,OAAO;QACX;IACJ;AACR;AAEO,SAAS,iCAAiC,kBAAoC,EAAE;IACnF,IAAI,UAAU,gBAAgB,GAAG,CAAC,CAAA;QAC9B,IAAI,OAAO,eAAe;QAC1B,OAAO;YACH,aAAa,QAAQ,WAAW;YAChC,WAAW,MAAM;YACjB,UAAU,MAAM;QACpB;IACJ;IAEA,IAAI,SAAS,QAAQ,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,QAAQ,GAAG,EAAE,QAAQ,CAAC,CAAC,EAAE;IAC/D,OAAO,gBAAgB,IAAI,CAAC,CAAA,UAAW,QAAQ,WAAW,KAAK,OAAO,WAAW,IAAI,QAAQ,OAAO,GAAG,IAAI;AAC/G;AAEO,SAAS,eAAe,OAAuB;IAClD,OAAO;WAAI;KAAc,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,SAAS,CAAC,aAAa,CAAC,EAAE,SAAS,GAAG,IAAI,CAAC,CAAA,OAAQ,QAAQ,WAAW,CAAC,UAAU,CAAC,KAAK,SAAS;AAC/I;AAEO,SAAS,+BAA+B,YAAiC;IAC5E,IAAI,CAAC,aAAa,WAAW,EAAE;QAC3B,OAAO;IACX;IACA,IAAI,aAAa,WAAW,CAAC,QAAQ,CAAC,iBAAiB;QACnD,OAAO;IACX;IACA,IAAI,aAAa,WAAW,CAAC,QAAQ,CAAC,YAAY;QAC9C,OAAO;IACX;IACA,OAAO,aAAa,WAAW;AACnC;AAEO,SAAS,qBAAqB,QAA0B,EAAE,cAA4B;IACzF,IAAI,uBAAuB;IAC3B,SAAS,OAAO,CAAC,CAAA;QACb,IAAI,OAAO,eAAe;QAC1B,IAAI,QAAQ,KAAK,QAAQ,IAAI,kBAAkB,QAAQ,OAAO,GAAG,IAAI,QAAQ;YACzE,uBAAuB;QAC3B;IACJ;IACA,OAAO;AACX", "debugId": null}}, {"offset": {"line": 2884, "column": 0}, "map": {"version": 3, "sources": ["file:///app/properties.js"], "sourcesContent": ["let properties = {}\nlet isSSR = typeof window === 'undefined'\n\n// Temporary fallback to production for working demo while local services are being set up\n// TODO: Switch back to local once all services are properly configured\nconst useLocalServices = false; // Set to true once local APIs are working\n\nproperties = {\n    commandEndpoint:\n        isSSR || window.location.host.startsWith('localhost') || window.location.hostname.includes('pr-env-sky-')\n            ? (useLocalServices ? 'http://localhost:1234/command' : 'https://sky.coflnet.com/command')\n            : '/command',\n    apiEndpoint:\n        isSSR || window.location.host.startsWith('localhost') || window.location.hostname.includes('pr-env-sky-')\n            ? (useLocalServices ? 'http://localhost:1234/api' : 'https://sky.coflnet.com/api')\n            : '/api',\n    websocketEndpoint: isSSR || window.location.host === 'localhost:8008'\n            ? 'ws://localhost:8008/skyblock'\n            : (useLocalServices ? 'ws://localhost:8021/skyblock' : 'wss://sky.coflnet.com/skyblock'),\n    refLink: useLocalServices ? 'http://localhost:3000/refed' : 'https://sky.coflnet.com/refed',\n    websocketOldEndpoint: useLocalServices ? 'ws://localhost:8021/skyblock' : 'wss://skyblock-backend.coflnet.com/skyblock',\n    feedbackEndpoint: useLocalServices ? 'http://localhost:1234/api/feedback/' : 'https://feedback.coflnet.com/api/',\n    isTestRunner: process.env.TEST_RUNNER === 'true' || false\n}\n\nexport default properties\n"], "names": [], "mappings": ";;;AAsBkB;AAtBlB,IAAI,aAAa,CAAC;AAClB,IAAI,QAAQ,aAAkB;AAE9B,0FAA0F;AAC1F,uEAAuE;AACvE,MAAM,mBAAmB,OAAO,0CAA0C;AAE1E,aAAa;IACT,iBACI,SAAS,OAAO,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,gBAAgB,OAAO,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,iBACpF,6EAAqD,oCACtD;IACV,aACI,SAAS,OAAO,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,gBAAgB,OAAO,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,iBACpF,6EAAiD,gCAClD;IACV,mBAAmB,SAAS,OAAO,QAAQ,CAAC,IAAI,KAAK,mBAC3C,iCACC,6EAAoD;IAC/D,SAAS,6EAAmD;IAC5D,sBAAsB,6EAAoD;IAC1E,kBAAkB,6EAA2D;IAC7E,cAAc,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,WAAW,KAAK,UAAU;AACxD;uCAEe", "debugId": null}}, {"offset": {"line": 2912, "column": 0}, "map": {"version": 3, "sources": ["file:///app/utils/PropertiesUtils.tsx"], "sourcesContent": ["import properties from '../properties'\nimport { isClientSideRendering } from './SSRUtils'\n\nexport function getProperty(propertyName: string) {\n    // Dynamicly change properties\n    if (isClientSideRendering() && (window as any).dynamicProps && (window as any).dynamicProps[propertyName]) {\n        return (window as any).dynamicProps[propertyName]\n    }\n\n    return properties[propertyName]\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,YAAY,YAAoB;IAC5C,8BAA8B;IAC9B,IAAI,CAAA,GAAA,qHAAA,CAAA,wBAAqB,AAAD,OAAO,AAAC,OAAe,YAAY,IAAI,AAAC,OAAe,YAAY,CAAC,aAAa,EAAE;QACvG,OAAO,AAAC,OAAe,YAAY,CAAC,aAAa;IACrD;IAEA,OAAO,6GAAA,CAAA,UAAU,CAAC,aAAa;AACnC", "debugId": null}}, {"offset": {"line": 2935, "column": 0}, "map": {"version": 3, "sources": ["file:///app/utils/MessageIdUtils.tsx"], "sourcesContent": ["let messageId = 0\n\nexport function getNextMessageId(): number {\n    return ++messageId\n}\n"], "names": [], "mappings": ";;;AAAA,IAAI,YAAY;AAET,SAAS;IACZ,OAAO,EAAE;AACb", "debugId": null}}, {"offset": {"line": 2951, "column": 0}, "map": {"version": 3, "sources": ["file:///app/api/HttpHelper.tsx"], "sourcesContent": ["import { ApiRequest, HttpApi } from './ApiTypes.d'\nimport cacheUtils from '../utils/CacheUtils'\nimport { getProperty } from '../utils/PropertiesUtils'\nimport { getNextMessageId } from '../utils/MessageIdUtils'\nimport { isClientSideRendering } from '../utils/SSRUtils'\nimport { atobUnicode, btoaUnicode } from '../utils/Base64Utils'\n\nexport function initHttpHelper(customCommandEndpoint?: string, customApiEndpoint?: string): HttpApi {\n    const commandEndpoint = customCommandEndpoint || getProperty('commandEndpoint')\n    const apiEndpoint = customApiEndpoint || getProperty('apiEndpoint')\n    let requests: ApiRequest[] = []\n\n    /**\n     * @deprecated\n     * Sends http-Request to the backend\n     * @param request The request-Object\n     * @param cacheInvalidationGrouping A number which is appended to the url to be able to invalidate the cache\n     * @returns A emty promise (the resolve/reject Method of the request-Object is called)\n     */\n    function sendRequest(request: ApiRequest, cacheInvalidationGrouping?: number): Promise<void> {\n        request.mId = getNextMessageId()\n        let requestString = JSON.stringify(request.data)\n        var url = `${commandEndpoint}/${request.type}/${btoaUnicode(requestString)}`\n\n        if (cacheInvalidationGrouping) {\n            url += `/${cacheInvalidationGrouping}`\n        }\n\n        return cacheUtils.getFromCache(request.type, requestString).then(cacheValue => {\n            if (cacheValue) {\n                request.resolve(cacheValue)\n                return\n            }\n\n            try {\n                request.data = btoaUnicode(requestString)\n            } catch (error) {\n                throw new Error('couldnt btoa this data: ' + request.data)\n            }\n\n            // don't resend in progress requests\n            let equals = findForEqualSentRequest(request)\n            if (equals.length > 0) {\n                requests.push(request)\n                return\n            }\n\n            requests.push(request)\n            return handleServerRequest(request, url)\n        })\n    }\n\n    /**\n     * Sends API-Request to the backend\n     * @param request The request-Object\n     * @returns A emty promise (the resolve/reject Method of the request-Object is called)\n     */\n    function sendApiRequest(request: ApiRequest, body?: any): Promise<void> {\n        request.mId = getNextMessageId()\n        let requestString = request.data\n        var url = `${apiEndpoint}/${request.type}`\n\n        if (requestString) {\n            url += `/${requestString}`\n        }\n\n        if (request.customRequestURL) {\n            url = request.customRequestURL\n        }\n\n        if (url.endsWith('&') || url.endsWith('?')) {\n            url = url.substring(0, url.length - 1)\n        }\n\n        // don't resend in progress requests\n        // ignore requests with a body, as they are currently not stored and therefore not checked\n        if (!body) {\n            let equals = findForEqualSentRequest(request)\n            if (equals.length > 0) {\n                requests.push(request)\n                return Promise.resolve()\n            }\n        }\n\n        requests.push(request)\n        return handleServerRequest(request, url, body)\n    }\n\n    function handleServerRequest(request: ApiRequest, url: string, body?: any): Promise<void> {\n        let parsedResponse\n        try {\n            return fetch(url, {\n                body: body,\n                method: request.requestMethod,\n                headers: request.requestHeader\n            })\n                .then(response => {\n                    if (!response.ok && response.status !== 304) {\n                        return Promise.reject(response.text())\n                    }\n\n                    return response.text()\n                })\n                .then(responseText => {\n                    parsedResponse = parseResponseText(responseText)\n                    if ((!parsedResponse && parsedResponse !== false) || parsedResponse.slug !== undefined) {\n                        request.resolve()\n                        return\n                    }\n                    request.resolve(parsedResponse)\n                    let equals = findForEqualSentRequest(request)\n                    equals.forEach(equal => {\n                        equal.resolve(parsedResponse)\n                    })\n                    // all http answers are valid for 60 sec\n                    let maxAge = 60\n\n                    let data = request.data\n                    try {\n                        data = atobUnicode(request.data)\n                    } catch {}\n                    cacheUtils.setIntoCache(request.customRequestURL || request.type, data, parsedResponse, maxAge)\n                    removeSentRequests([...equals, request])\n                })\n                .catch(responseTextPromise => {\n                    if (!responseTextPromise || typeof responseTextPromise.then !== 'function') {\n                        request.reject(responseTextPromise || 'no responseTextPromise')\n                        return\n                    }\n                    responseTextPromise.then(responseText => {\n                        request.reject(parseResponseText(responseText) || 'no responseTextPromise after parse')\n                    })\n                })\n                .finally(() => {\n                    // when there are still matching request remove them\n                    let equals = findForEqualSentRequest(request)\n                    equals.forEach(equal => {\n                        equal.resolve(parsedResponse)\n                    })\n                    removeSentRequests([...equals, request])\n                })\n        } catch (e) {\n            console.log('Fetch threw exception...')\n            console.log('URL: ' + url)\n            console.log('Request: ' + JSON.stringify(request))\n            console.log('Body: ' + JSON.stringify(body))\n            console.log(JSON.stringify(e))\n            console.log('------------------------')\n\n            return request.reject('Fetch threw exception: ' + JSON.stringify(e))\n        }\n    }\n\n    function sendRequestLimitCache(request: ApiRequest, grouping = 1): Promise<void> {\n        let group = Math.round(new Date().getMinutes() / grouping)\n        return sendRequest(request, group)\n    }\n\n    function removeSentRequests(toDelete: ApiRequest[]) {\n        requests = requests.filter(request => {\n            for (let i = 0; i < toDelete.length; i++) {\n                if (toDelete[i].mId === request.mId) {\n                    return false\n                }\n            }\n            return true\n        })\n    }\n\n    function findForEqualSentRequest(request: ApiRequest) {\n        return requests.filter(r => {\n            return (\n                r.type === request.type &&\n                JSON.stringify(r.data) === JSON.stringify(request.data) &&\n                r.customRequestURL === request.customRequestURL &&\n                r.mId !== request.mId\n            )\n        })\n    }\n\n    function parseResponseText(responseText) {\n        let parsedResponse: any\n        try {\n            if (!isNaN(responseText)) {\n                return responseText\n            }\n            parsedResponse = JSON.parse(responseText)\n        } catch {\n            parsedResponse = responseText\n        }\n        return parsedResponse\n    }\n\n    return {\n        sendRequest: sendRequest,\n        sendLimitedCacheRequest: sendRequestLimitCache,\n        sendApiRequest: sendApiRequest\n    }\n}\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;AAEA;;;;;AAEO,SAAS,eAAe,qBAA8B,EAAE,iBAA0B;IACrF,MAAM,kBAAkB,yBAAyB,CAAA,GAAA,4HAAA,CAAA,cAAW,AAAD,EAAE;IAC7D,MAAM,cAAc,qBAAqB,CAAA,GAAA,4HAAA,CAAA,cAAW,AAAD,EAAE;IACrD,IAAI,WAAyB,EAAE;IAE/B;;;;;;KAMC,GACD,SAAS,YAAY,OAAmB,EAAE,yBAAkC;QACxE,QAAQ,GAAG,GAAG,CAAA,GAAA,2HAAA,CAAA,mBAAgB,AAAD;QAC7B,IAAI,gBAAgB,KAAK,SAAS,CAAC,QAAQ,IAAI;QAC/C,IAAI,MAAM,GAAG,gBAAgB,CAAC,EAAE,QAAQ,IAAI,CAAC,CAAC,EAAE,CAAA,GAAA,wHAAA,CAAA,cAAW,AAAD,EAAE,gBAAgB;QAE5E,IAAI,2BAA2B;YAC3B,OAAO,CAAC,CAAC,EAAE,2BAA2B;QAC1C;QAEA,OAAO,uHAAA,CAAA,UAAU,CAAC,YAAY,CAAC,QAAQ,IAAI,EAAE,eAAe,IAAI,CAAC,CAAA;YAC7D,IAAI,YAAY;gBACZ,QAAQ,OAAO,CAAC;gBAChB;YACJ;YAEA,IAAI;gBACA,QAAQ,IAAI,GAAG,CAAA,GAAA,wHAAA,CAAA,cAAW,AAAD,EAAE;YAC/B,EAAE,OAAO,OAAO;gBACZ,MAAM,IAAI,MAAM,6BAA6B,QAAQ,IAAI;YAC7D;YAEA,oCAAoC;YACpC,IAAI,SAAS,wBAAwB;YACrC,IAAI,OAAO,MAAM,GAAG,GAAG;gBACnB,SAAS,IAAI,CAAC;gBACd;YACJ;YAEA,SAAS,IAAI,CAAC;YACd,OAAO,oBAAoB,SAAS;QACxC;IACJ;IAEA;;;;KAIC,GACD,SAAS,eAAe,OAAmB,EAAE,IAAU;QACnD,QAAQ,GAAG,GAAG,CAAA,GAAA,2HAAA,CAAA,mBAAgB,AAAD;QAC7B,IAAI,gBAAgB,QAAQ,IAAI;QAChC,IAAI,MAAM,GAAG,YAAY,CAAC,EAAE,QAAQ,IAAI,EAAE;QAE1C,IAAI,eAAe;YACf,OAAO,CAAC,CAAC,EAAE,eAAe;QAC9B;QAEA,IAAI,QAAQ,gBAAgB,EAAE;YAC1B,MAAM,QAAQ,gBAAgB;QAClC;QAEA,IAAI,IAAI,QAAQ,CAAC,QAAQ,IAAI,QAAQ,CAAC,MAAM;YACxC,MAAM,IAAI,SAAS,CAAC,GAAG,IAAI,MAAM,GAAG;QACxC;QAEA,oCAAoC;QACpC,0FAA0F;QAC1F,IAAI,CAAC,MAAM;YACP,IAAI,SAAS,wBAAwB;YACrC,IAAI,OAAO,MAAM,GAAG,GAAG;gBACnB,SAAS,IAAI,CAAC;gBACd,OAAO,QAAQ,OAAO;YAC1B;QACJ;QAEA,SAAS,IAAI,CAAC;QACd,OAAO,oBAAoB,SAAS,KAAK;IAC7C;IAEA,SAAS,oBAAoB,OAAmB,EAAE,GAAW,EAAE,IAAU;QACrE,IAAI;QACJ,IAAI;YACA,OAAO,MAAM,KAAK;gBACd,MAAM;gBACN,QAAQ,QAAQ,aAAa;gBAC7B,SAAS,QAAQ,aAAa;YAClC,GACK,IAAI,CAAC,CAAA;gBACF,IAAI,CAAC,SAAS,EAAE,IAAI,SAAS,MAAM,KAAK,KAAK;oBACzC,OAAO,QAAQ,MAAM,CAAC,SAAS,IAAI;gBACvC;gBAEA,OAAO,SAAS,IAAI;YACxB,GACC,IAAI,CAAC,CAAA;gBACF,iBAAiB,kBAAkB;gBACnC,IAAI,AAAC,CAAC,kBAAkB,mBAAmB,SAAU,eAAe,IAAI,KAAK,WAAW;oBACpF,QAAQ,OAAO;oBACf;gBACJ;gBACA,QAAQ,OAAO,CAAC;gBAChB,IAAI,SAAS,wBAAwB;gBACrC,OAAO,OAAO,CAAC,CAAA;oBACX,MAAM,OAAO,CAAC;gBAClB;gBACA,wCAAwC;gBACxC,IAAI,SAAS;gBAEb,IAAI,OAAO,QAAQ,IAAI;gBACvB,IAAI;oBACA,OAAO,CAAA,GAAA,wHAAA,CAAA,cAAW,AAAD,EAAE,QAAQ,IAAI;gBACnC,EAAE,OAAM,CAAC;gBACT,uHAAA,CAAA,UAAU,CAAC,YAAY,CAAC,QAAQ,gBAAgB,IAAI,QAAQ,IAAI,EAAE,MAAM,gBAAgB;gBACxF,mBAAmB;uBAAI;oBAAQ;iBAAQ;YAC3C,GACC,KAAK,CAAC,CAAA;gBACH,IAAI,CAAC,uBAAuB,OAAO,oBAAoB,IAAI,KAAK,YAAY;oBACxE,QAAQ,MAAM,CAAC,uBAAuB;oBACtC;gBACJ;gBACA,oBAAoB,IAAI,CAAC,CAAA;oBACrB,QAAQ,MAAM,CAAC,kBAAkB,iBAAiB;gBACtD;YACJ,GACC,OAAO,CAAC;gBACL,oDAAoD;gBACpD,IAAI,SAAS,wBAAwB;gBACrC,OAAO,OAAO,CAAC,CAAA;oBACX,MAAM,OAAO,CAAC;gBAClB;gBACA,mBAAmB;uBAAI;oBAAQ;iBAAQ;YAC3C;QACR,EAAE,OAAO,GAAG;YACR,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC,UAAU;YACtB,QAAQ,GAAG,CAAC,cAAc,KAAK,SAAS,CAAC;YACzC,QAAQ,GAAG,CAAC,WAAW,KAAK,SAAS,CAAC;YACtC,QAAQ,GAAG,CAAC,KAAK,SAAS,CAAC;YAC3B,QAAQ,GAAG,CAAC;YAEZ,OAAO,QAAQ,MAAM,CAAC,4BAA4B,KAAK,SAAS,CAAC;QACrE;IACJ;IAEA,SAAS,sBAAsB,OAAmB,EAAE,WAAW,CAAC;QAC5D,IAAI,QAAQ,KAAK,KAAK,CAAC,IAAI,OAAO,UAAU,KAAK;QACjD,OAAO,YAAY,SAAS;IAChC;IAEA,SAAS,mBAAmB,QAAsB;QAC9C,WAAW,SAAS,MAAM,CAAC,CAAA;YACvB,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,IAAK;gBACtC,IAAI,QAAQ,CAAC,EAAE,CAAC,GAAG,KAAK,QAAQ,GAAG,EAAE;oBACjC,OAAO;gBACX;YACJ;YACA,OAAO;QACX;IACJ;IAEA,SAAS,wBAAwB,OAAmB;QAChD,OAAO,SAAS,MAAM,CAAC,CAAA;YACnB,OACI,EAAE,IAAI,KAAK,QAAQ,IAAI,IACvB,KAAK,SAAS,CAAC,EAAE,IAAI,MAAM,KAAK,SAAS,CAAC,QAAQ,IAAI,KACtD,EAAE,gBAAgB,KAAK,QAAQ,gBAAgB,IAC/C,EAAE,GAAG,KAAK,QAAQ,GAAG;QAE7B;IACJ;IAEA,SAAS,kBAAkB,YAAY;QACnC,IAAI;QACJ,IAAI;YACA,IAAI,CAAC,MAAM,eAAe;gBACtB,OAAO;YACX;YACA,iBAAiB,KAAK,KAAK,CAAC;QAChC,EAAE,OAAM;YACJ,iBAAiB;QACrB;QACA,OAAO;IACX;IAEA,OAAO;QACH,aAAa;QACb,yBAAyB;QACzB,gBAAgB;IACpB;AACJ", "debugId": null}}, {"offset": {"line": 3137, "column": 0}, "map": {"version": 3, "sources": ["file:///app/api/WebsocketHelper.tsx"], "sourcesContent": ["import { Api<PERSON>e<PERSON>, WebsocketHelper, ApiSubscription, RequestType } from './ApiTypes.d'\nimport cacheUtils from '../utils/CacheUtils'\nimport api from './ApiHelper'\nimport { toast } from 'react-toastify'\nimport { getProperty } from '../utils/PropertiesUtils'\nimport { getNextMessageId } from '../utils/MessageIdUtils'\nimport { atobUnicode, btoaUnicode } from '../utils/Base64Utils'\n\nlet requests: ApiRequest[] = []\nlet websocket: WebSocket\n\nlet isConnectionIdSet: boolean = false\n\nlet apiSubscriptions: ApiSubscription[] = []\n\nfunction initWebsocket(): void {\n    let onWebsocketClose = (): void => {\n        var timeout = Math.random() * (5000 - 0) + 0\n        setTimeout(() => {\n            websocket = getNewWebsocket(true)\n        }, timeout)\n    }\n\n    // dont show a toast message on websocket errors as this gets spammed when the user for example locks their computer\n    let onWebsocketError = (e: Event): void => {\n        console.error(e)\n    }\n\n    let onOpen = (e: Event, isReconnecting: boolean): void => {\n        let _reconnect = function () {\n            let toReconnect = [...apiSubscriptions]\n            apiSubscriptions = []\n\n            toReconnect.forEach(subscription => {\n                subscription.resubscribe(subscription)\n            })\n        }\n\n        // set the connection id first\n        api.setConnectionId().then(() => {\n            isConnectionIdSet = true\n            if (isReconnecting && sessionStorage.getItem('googleId') !== null) {\n                api.loginWithToken(sessionStorage.getItem('googleId')!).then(token => {\n                    sessionStorage.setItem('googleId', token)\n                    localStorage.setItem('googleId', token)\n                    _reconnect()\n                })\n            }\n        })\n    }\n\n    let _handleRequestOnMessage = function (response: ApiResponse, request: ApiRequest) {\n        let equals = findForEqualSentRequest(request)\n\n        if (response.type === 'display') {\n            let parsedData = JSON.parse(response.data)\n            if (typeof toast[parsedData.type] === 'function') {\n                toast[parsedData.type](parsedData.message)\n            } else {\n                toast.info(parsedData.message)\n            }\n            return\n        }\n\n        if (response.type.includes('error')) {\n            request.reject(JSON.parse(response.data))\n            equals.forEach(equal => equal.reject(JSON.parse(response.data)))\n        } else {\n            if (response.data === '') {\n                response.data = '\"\"'\n            }\n            let parsedResponse = JSON.parse(response.data)\n            request.resolve(parsedResponse)\n            equals.forEach(equal => equal.resolve(parsedResponse))\n            // cache the response\n            let maxAge = response.maxAge\n            cacheUtils.setIntoCache(request.type, atobUnicode(request.data), parsedResponse, maxAge)\n        }\n\n        removeSentRequests([...equals, request])\n    }\n\n    let _handleSubscriptionOnMessage = function (response: ApiResponse, subscription: ApiSubscription) {\n        try {\n            response.data = JSON.parse(response.data)\n        } catch (e) {}\n\n        if (response.type === 'error') {\n            subscription.onError(response.data.message)\n        } else {\n            subscription.callback(response)\n        }\n    }\n\n    let onWebsocketMessage = (e: MessageEvent): void => {\n        let response: ApiResponse = JSON.parse(e.data)\n        let request: ApiRequest | undefined = requests.find(e => e.mId === response.mId)\n        let subscription: ApiSubscription | undefined = apiSubscriptions.find(e => e.mId === response.mId)\n\n        if (!request && !subscription) {\n            return\n        }\n\n        if (request) {\n            _handleRequestOnMessage(response, request)\n        }\n        if (subscription) {\n            _handleSubscriptionOnMessage(response, subscription)\n        }\n    }\n\n    let getNewWebsocket = (isReconnecting: boolean): WebSocket => {\n        websocket = new WebSocket(getProperty('websocketEndpoint'))\n        websocket.onclose = onWebsocketClose\n        websocket.onerror = onWebsocketError\n        websocket.onmessage = onWebsocketMessage\n        websocket.onopen = e => {\n            onOpen(e, isReconnecting)\n        }\n        ;(window as any).websocket = websocket\n        return websocket\n    }\n\n    websocket = getNewWebsocket(false)\n}\n\nfunction sendRequest(request: ApiRequest): Promise<void> {\n    if (!websocket) {\n        initWebsocket()\n    }\n    let requestString = JSON.stringify(request.data)\n    return cacheUtils.getFromCache(request.type, requestString).then(cacheValue => {\n        if (cacheValue) {\n            request.resolve(cacheValue)\n            return\n        }\n\n        if (_isWebsocketReady(request.type, websocket)) {\n            request.mId = getNextMessageId()\n            let equals = findForEqualSentRequest(request)\n            if (equals.length > 0) {\n                requests.push(request)\n                return\n            }\n            requests.push(request)\n            prepareDataBeforeSend(request)\n            websocket.send(JSON.stringify(request))\n        } else {\n            setTimeout(() => {\n                sendRequest(request)\n            }, 500)\n            return\n        }\n    })\n}\n\nfunction prepareDataBeforeSend(request: ApiRequest) {\n    try {\n        request.data = btoaUnicode(JSON.stringify(request.data))\n    } catch (error) {\n        throw new Error('couldnt btoa this data: ' + request.data)\n    }\n}\n\nfunction removeOldSubscriptionByType(type: RequestType) {\n    for (let i = apiSubscriptions.length - 1; i >= 0; i--) {\n        let subscription = apiSubscriptions[i]\n        if (subscription.type === type) {\n            apiSubscriptions.splice(i, 1)\n        }\n    }\n}\n\nfunction subscribe(subscription: ApiSubscription): void {\n    if (!websocket) {\n        initWebsocket()\n    }\n    let requestString = JSON.stringify(subscription.data)\n    if (_isWebsocketReady(subscription.type, websocket)) {\n        subscription.mId = getNextMessageId()\n        try {\n            subscription.data = btoaUnicode(requestString)\n        } catch (error) {\n            throw new Error('couldnt btoa this data: ' + subscription.data)\n        }\n        apiSubscriptions.push(subscription)\n        websocket.send(JSON.stringify(subscription))\n    } else {\n        setTimeout(() => {\n            subscribe(subscription)\n        }, 500)\n    }\n}\n\nfunction findForEqualSentRequest(request: ApiRequest) {\n    return requests.filter(r => {\n        return r.type === request.type && r.data === request.data && r.mId !== request.mId\n    })\n}\n\nfunction removeSentRequests(toDelete: ApiRequest[]) {\n    requests = requests.filter(request => {\n        for (let i = 0; i < toDelete.length; i++) {\n            if (toDelete[i].mId === request.mId) {\n                return false\n            }\n        }\n        return true\n    })\n}\n\nfunction _isWebsocketReady(requestType: string, websocket: WebSocket) {\n    return websocket && websocket.readyState === WebSocket.OPEN && (isConnectionIdSet || requestType === RequestType.SET_CONNECTION_ID)\n}\n\nexport let websocketHelper: WebsocketHelper = {\n    sendRequest: sendRequest,\n    subscribe: subscribe,\n    removeOldSubscriptionByType: removeOldSubscriptionByType\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAEA,IAAI,WAAyB,EAAE;AAC/B,IAAI;AAEJ,IAAI,oBAA6B;AAEjC,IAAI,mBAAsC,EAAE;AAE5C,SAAS;IACL,IAAI,mBAAmB;QACnB,IAAI,UAAU,KAAK,MAAM,KAAK,CAAC,OAAO,CAAC,IAAI;QAC3C,WAAW;YACP,YAAY,gBAAgB;QAChC,GAAG;IACP;IAEA,oHAAoH;IACpH,IAAI,mBAAmB,CAAC;QACpB,QAAQ,KAAK,CAAC;IAClB;IAEA,IAAI,SAAS,CAAC,GAAU;QACpB,IAAI,aAAa;YACb,IAAI,cAAc;mBAAI;aAAiB;YACvC,mBAAmB,EAAE;YAErB,YAAY,OAAO,CAAC,CAAA;gBAChB,aAAa,WAAW,CAAC;YAC7B;QACJ;QAEA,8BAA8B;QAC9B,oHAAA,CAAA,UAAG,CAAC,eAAe,GAAG,IAAI,CAAC;YACvB,oBAAoB;YACpB,IAAI,kBAAkB,eAAe,OAAO,CAAC,gBAAgB,MAAM;gBAC/D,oHAAA,CAAA,UAAG,CAAC,cAAc,CAAC,eAAe,OAAO,CAAC,aAAc,IAAI,CAAC,CAAA;oBACzD,eAAe,OAAO,CAAC,YAAY;oBACnC,aAAa,OAAO,CAAC,YAAY;oBACjC;gBACJ;YACJ;QACJ;IACJ;IAEA,IAAI,0BAA0B,SAAU,QAAqB,EAAE,OAAmB;QAC9E,IAAI,SAAS,wBAAwB;QAErC,IAAI,SAAS,IAAI,KAAK,WAAW;YAC7B,IAAI,aAAa,KAAK,KAAK,CAAC,SAAS,IAAI;YACzC,IAAI,OAAO,sJAAA,CAAA,QAAK,CAAC,WAAW,IAAI,CAAC,KAAK,YAAY;gBAC9C,sJAAA,CAAA,QAAK,CAAC,WAAW,IAAI,CAAC,CAAC,WAAW,OAAO;YAC7C,OAAO;gBACH,sJAAA,CAAA,QAAK,CAAC,IAAI,CAAC,WAAW,OAAO;YACjC;YACA;QACJ;QAEA,IAAI,SAAS,IAAI,CAAC,QAAQ,CAAC,UAAU;YACjC,QAAQ,MAAM,CAAC,KAAK,KAAK,CAAC,SAAS,IAAI;YACvC,OAAO,OAAO,CAAC,CAAA,QAAS,MAAM,MAAM,CAAC,KAAK,KAAK,CAAC,SAAS,IAAI;QACjE,OAAO;YACH,IAAI,SAAS,IAAI,KAAK,IAAI;gBACtB,SAAS,IAAI,GAAG;YACpB;YACA,IAAI,iBAAiB,KAAK,KAAK,CAAC,SAAS,IAAI;YAC7C,QAAQ,OAAO,CAAC;YAChB,OAAO,OAAO,CAAC,CAAA,QAAS,MAAM,OAAO,CAAC;YACtC,qBAAqB;YACrB,IAAI,SAAS,SAAS,MAAM;YAC5B,uHAAA,CAAA,UAAU,CAAC,YAAY,CAAC,QAAQ,IAAI,EAAE,CAAA,GAAA,wHAAA,CAAA,cAAW,AAAD,EAAE,QAAQ,IAAI,GAAG,gBAAgB;QACrF;QAEA,mBAAmB;eAAI;YAAQ;SAAQ;IAC3C;IAEA,IAAI,+BAA+B,SAAU,QAAqB,EAAE,YAA6B;QAC7F,IAAI;YACA,SAAS,IAAI,GAAG,KAAK,KAAK,CAAC,SAAS,IAAI;QAC5C,EAAE,OAAO,GAAG,CAAC;QAEb,IAAI,SAAS,IAAI,KAAK,SAAS;YAC3B,aAAa,OAAO,CAAC,SAAS,IAAI,CAAC,OAAO;QAC9C,OAAO;YACH,aAAa,QAAQ,CAAC;QAC1B;IACJ;IAEA,IAAI,qBAAqB,CAAC;QACtB,IAAI,WAAwB,KAAK,KAAK,CAAC,EAAE,IAAI;QAC7C,IAAI,UAAkC,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK,SAAS,GAAG;QAC/E,IAAI,eAA4C,iBAAiB,IAAI,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK,SAAS,GAAG;QAEjG,IAAI,CAAC,WAAW,CAAC,cAAc;YAC3B;QACJ;QAEA,IAAI,SAAS;YACT,wBAAwB,UAAU;QACtC;QACA,IAAI,cAAc;YACd,6BAA6B,UAAU;QAC3C;IACJ;IAEA,IAAI,kBAAkB,CAAC;QACnB,YAAY,IAAI,UAAU,CAAA,GAAA,4HAAA,CAAA,cAAW,AAAD,EAAE;QACtC,UAAU,OAAO,GAAG;QACpB,UAAU,OAAO,GAAG;QACpB,UAAU,SAAS,GAAG;QACtB,UAAU,MAAM,GAAG,CAAA;YACf,OAAO,GAAG;QACd;QACE,OAAe,SAAS,GAAG;QAC7B,OAAO;IACX;IAEA,YAAY,gBAAgB;AAChC;AAEA,SAAS,YAAY,OAAmB;IACpC,IAAI,CAAC,WAAW;QACZ;IACJ;IACA,IAAI,gBAAgB,KAAK,SAAS,CAAC,QAAQ,IAAI;IAC/C,OAAO,uHAAA,CAAA,UAAU,CAAC,YAAY,CAAC,QAAQ,IAAI,EAAE,eAAe,IAAI,CAAC,CAAA;QAC7D,IAAI,YAAY;YACZ,QAAQ,OAAO,CAAC;YAChB;QACJ;QAEA,IAAI,kBAAkB,QAAQ,IAAI,EAAE,YAAY;YAC5C,QAAQ,GAAG,GAAG,CAAA,GAAA,2HAAA,CAAA,mBAAgB,AAAD;YAC7B,IAAI,SAAS,wBAAwB;YACrC,IAAI,OAAO,MAAM,GAAG,GAAG;gBACnB,SAAS,IAAI,CAAC;gBACd;YACJ;YACA,SAAS,IAAI,CAAC;YACd,sBAAsB;YACtB,UAAU,IAAI,CAAC,KAAK,SAAS,CAAC;QAClC,OAAO;YACH,WAAW;gBACP,YAAY;YAChB,GAAG;YACH;QACJ;IACJ;AACJ;AAEA,SAAS,sBAAsB,OAAmB;IAC9C,IAAI;QACA,QAAQ,IAAI,GAAG,CAAA,GAAA,wHAAA,CAAA,cAAW,AAAD,EAAE,KAAK,SAAS,CAAC,QAAQ,IAAI;IAC1D,EAAE,OAAO,OAAO;QACZ,MAAM,IAAI,MAAM,6BAA6B,QAAQ,IAAI;IAC7D;AACJ;AAEA,SAAS,4BAA4B,IAAiB;IAClD,IAAK,IAAI,IAAI,iBAAiB,MAAM,GAAG,GAAG,KAAK,GAAG,IAAK;QACnD,IAAI,eAAe,gBAAgB,CAAC,EAAE;QACtC,IAAI,aAAa,IAAI,KAAK,MAAM;YAC5B,iBAAiB,MAAM,CAAC,GAAG;QAC/B;IACJ;AACJ;AAEA,SAAS,UAAU,YAA6B;IAC5C,IAAI,CAAC,WAAW;QACZ;IACJ;IACA,IAAI,gBAAgB,KAAK,SAAS,CAAC,aAAa,IAAI;IACpD,IAAI,kBAAkB,aAAa,IAAI,EAAE,YAAY;QACjD,aAAa,GAAG,GAAG,CAAA,GAAA,2HAAA,CAAA,mBAAgB,AAAD;QAClC,IAAI;YACA,aAAa,IAAI,GAAG,CAAA,GAAA,wHAAA,CAAA,cAAW,AAAD,EAAE;QACpC,EAAE,OAAO,OAAO;YACZ,MAAM,IAAI,MAAM,6BAA6B,aAAa,IAAI;QAClE;QACA,iBAAiB,IAAI,CAAC;QACtB,UAAU,IAAI,CAAC,KAAK,SAAS,CAAC;IAClC,OAAO;QACH,WAAW;YACP,UAAU;QACd,GAAG;IACP;AACJ;AAEA,SAAS,wBAAwB,OAAmB;IAChD,OAAO,SAAS,MAAM,CAAC,CAAA;QACnB,OAAO,EAAE,IAAI,KAAK,QAAQ,IAAI,IAAI,EAAE,IAAI,KAAK,QAAQ,IAAI,IAAI,EAAE,GAAG,KAAK,QAAQ,GAAG;IACtF;AACJ;AAEA,SAAS,mBAAmB,QAAsB;IAC9C,WAAW,SAAS,MAAM,CAAC,CAAA;QACvB,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,IAAK;YACtC,IAAI,QAAQ,CAAC,EAAE,CAAC,GAAG,KAAK,QAAQ,GAAG,EAAE;gBACjC,OAAO;YACX;QACJ;QACA,OAAO;IACX;AACJ;AAEA,SAAS,kBAAkB,WAAmB,EAAE,SAAoB;IAChE,OAAO,aAAa,UAAU,UAAU,KAAK,UAAU,IAAI,IAAI,CAAC,qBAAqB,gBAAgB,wHAAA,CAAA,cAAW,CAAC,iBAAiB;AACtI;AAEO,IAAI,kBAAmC;IAC1C,aAAa;IACb,WAAW;IACX,6BAA6B;AACjC", "debugId": null}}, {"offset": {"line": 3353, "column": 0}, "map": {"version": 3, "sources": ["file:///app/utils/ClipboardUtils.tsx"], "sourcesContent": ["// DuckDuckgo on Android doesnt support the clipboard API, but the old execCommand\nfunction isAndroidDuckDuckGo() {\n    return navigator.userAgent.includes('DuckDuckGo') && navigator.userAgent.includes('Android')\n}\n\nexport function canUseClipBoard() {\n    if (typeof window === 'undefined') {\n        return false\n    }\n    return window.navigator.clipboard || isAndroidDuckDuckGo()\n}\n\nexport function writeToClipboard(text: string) {\n    if (isAndroidDuckDuckGo() || !window.navigator.clipboard) {\n        let textarea = document.createElement('textarea')\n        textarea.style.position = 'fixed'\n        textarea.style.width = '1px'\n        textarea.style.height = '1px'\n        textarea.style.padding = '0'\n        textarea.style.border = 'none'\n        textarea.style.outline = 'none'\n        textarea.style.boxShadow = 'none'\n        textarea.style.background = 'transparent'\n        document.body.appendChild(textarea)\n        textarea.textContent = text\n        textarea.focus()\n        textarea.select()\n        document.execCommand('copy')\n        textarea.remove()\n    } else {\n        window.navigator.clipboard.writeText(text)\n    }\n}\n"], "names": [], "mappings": "AAAA,kFAAkF;;;;;AAClF,SAAS;IACL,OAAO,UAAU,SAAS,CAAC,QAAQ,CAAC,iBAAiB,UAAU,SAAS,CAAC,QAAQ,CAAC;AACtF;AAEO,SAAS;IACZ,uCAAmC;;IAEnC;IACA,OAAO,OAAO,SAAS,CAAC,SAAS,IAAI;AACzC;AAEO,SAAS,iBAAiB,IAAY;IACzC,IAAI,yBAAyB,CAAC,OAAO,SAAS,CAAC,SAAS,EAAE;QACtD,IAAI,WAAW,SAAS,aAAa,CAAC;QACtC,SAAS,KAAK,CAAC,QAAQ,GAAG;QAC1B,SAAS,KAAK,CAAC,KAAK,GAAG;QACvB,SAAS,KAAK,CAAC,MAAM,GAAG;QACxB,SAAS,KAAK,CAAC,OAAO,GAAG;QACzB,SAAS,KAAK,CAAC,MAAM,GAAG;QACxB,SAAS,KAAK,CAAC,OAAO,GAAG;QACzB,SAAS,KAAK,CAAC,SAAS,GAAG;QAC3B,SAAS,KAAK,CAAC,UAAU,GAAG;QAC5B,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,SAAS,WAAW,GAAG;QACvB,SAAS,KAAK;QACd,SAAS,MAAM;QACf,SAAS,WAAW,CAAC;QACrB,SAAS,MAAM;IACnB,OAAO;QACH,OAAO,SAAS,CAAC,SAAS,CAAC,SAAS,CAAC;IACzC;AACJ", "debugId": null}}, {"offset": {"line": 3397, "column": 0}, "map": {"version": 3, "sources": ["file:///app/utils/CoflCoinsUtils.tsx"], "sourcesContent": ["import api from '../api/ApiHelper'\nimport { CUSTOM_EVENTS } from '../api/ApiTypes.d'\nimport { isClientSideRendering } from './SSRUtils'\nimport { v4 as generateUUID } from 'uuid'\n\ninterface RegisteredCallback {\n    uuid: string\n    callback(coflCoins: number)\n}\n\nlet registeredCallbacks: RegisteredCallback[] = []\nlet currentCoflCoins = -1\n\n/**\n * Registers a callback if the amound of coflcoins changes\n * @param callback The callback that will be called\n * @returns A unsubscribe function\n */\nexport function subscribeToCoflcoinChange(callback: (n: number) => void): Function {\n    let uuid = generateUUID()\n\n    registeredCallbacks.push({\n        uuid: uuid,\n        callback: callback\n    })\n\n    return () => {\n        let index = registeredCallbacks.findIndex(registeredCallback => {\n            return registeredCallback.uuid === uuid\n        })\n        registeredCallbacks.splice(index, 1)\n    }\n}\n\nexport function getCurrentCoflCoins() {\n    return currentCoflCoins\n}\n\nexport function initCoflCoinManager() {\n    if (!isClientSideRendering()) {\n        return\n    }\n\n    function notifyAboutCoflCoinUpdate(coflCoins: number) {\n        registeredCallbacks.forEach(registeredCallback => {\n            registeredCallback.callback(coflCoins)\n        })\n    }\n\n    function initCoflCoinBalanceAndSubscriptions() {\n        api.subscribeCoflCoinChange()\n        api.getCoflcoinBalance().then(coflCoins => {\n            currentCoflCoins = coflCoins\n            notifyAboutCoflCoinUpdate(coflCoins)\n        })\n        document.removeEventListener(CUSTOM_EVENTS.GOOGLE_LOGIN, initCoflCoinBalanceAndSubscriptions)\n    }\n\n    document.addEventListener(CUSTOM_EVENTS.GOOGLE_LOGIN, initCoflCoinBalanceAndSubscriptions)\n    document.addEventListener(CUSTOM_EVENTS.COFLCOIN_UPDATE, e => {\n        let coflCoins = (e as any).detail?.coflCoins\n        currentCoflCoins = coflCoins\n        notifyAboutCoflCoinUpdate(coflCoins)\n    })\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;;;;;AAOA,IAAI,sBAA4C,EAAE;AAClD,IAAI,mBAAmB,CAAC;AAOjB,SAAS,0BAA0B,QAA6B;IACnE,IAAI,OAAO,CAAA,GAAA,wLAAA,CAAA,KAAY,AAAD;IAEtB,oBAAoB,IAAI,CAAC;QACrB,MAAM;QACN,UAAU;IACd;IAEA,OAAO;QACH,IAAI,QAAQ,oBAAoB,SAAS,CAAC,CAAA;YACtC,OAAO,mBAAmB,IAAI,KAAK;QACvC;QACA,oBAAoB,MAAM,CAAC,OAAO;IACtC;AACJ;AAEO,SAAS;IACZ,OAAO;AACX;AAEO,SAAS;IACZ,IAAI,CAAC,CAAA,GAAA,qHAAA,CAAA,wBAAqB,AAAD,KAAK;QAC1B;IACJ;IAEA,SAAS,0BAA0B,SAAiB;QAChD,oBAAoB,OAAO,CAAC,CAAA;YACxB,mBAAmB,QAAQ,CAAC;QAChC;IACJ;IAEA,SAAS;QACL,oHAAA,CAAA,UAAG,CAAC,uBAAuB;QAC3B,oHAAA,CAAA,UAAG,CAAC,kBAAkB,GAAG,IAAI,CAAC,CAAA;YAC1B,mBAAmB;YACnB,0BAA0B;QAC9B;QACA,SAAS,mBAAmB,CAAC,wHAAA,CAAA,gBAAa,CAAC,YAAY,EAAE;IAC7D;IAEA,SAAS,gBAAgB,CAAC,wHAAA,CAAA,gBAAa,CAAC,YAAY,EAAE;IACtD,SAAS,gBAAgB,CAAC,wHAAA,CAAA,gBAAa,CAAC,eAAe,EAAE,CAAA;QACrD,IAAI,YAAY,AAAC,EAAU,MAAM,EAAE;QACnC,mBAAmB;QACnB,0BAA0B;IAC9B;AACJ", "debugId": null}}, {"offset": {"line": 3461, "column": 0}, "map": {"version": 3, "sources": ["file:///app/api/ApiHelper.tsx"], "sourcesContent": ["import { toast } from 'react-toastify'\nimport { v4 as generateUUID } from 'uuid'\nimport { atobUnicode } from '../utils/Base64Utils'\nimport cacheUtils from '../utils/CacheUtils'\nimport { getFlipCustomizeSettings } from '../utils/FlipUtils'\nimport { enchantmentAndReforgeCompare } from '../utils/Formatter'\nimport {\n    parseAccountInfo,\n    parseArchivedAuctions,\n    parseAuction,\n    parseAuctionDetails,\n    parseBazaarPrice,\n    parseBazaarSnapshot,\n    parseCraftingInstructions,\n    parseCraftingRecipe,\n    parseEnchantment,\n    parseFilterOption,\n    parseFlipAuction,\n    parseFlipTrackingResponse,\n    parseInventoryData,\n    parseItem,\n    parseItemBidForList,\n    parseItemPrice,\n    parseItemSummary,\n    parseKatFlip,\n    parseLowSupplyItem,\n    parseMayorData,\n    parseMinecraftConnectionInfo,\n    parseOwnerHistory,\n    parsePaymentResponse,\n    parsePlayer,\n    parsePopularSearch,\n    parsePremiumProducts,\n    parsePremiumSubscription,\n    parsePrivacySettings,\n    parseProfitableCrafts,\n    parseRecentAuction,\n    parseRefInfo,\n    parseSearchResultItem,\n    parseSkyblockProfile,\n    parseSubscription,\n    parseTradeObject,\n    parseTransaction\n} from '../utils/Parser/APIResponseParser'\nimport { PREMIUM_TYPES } from '../utils/PremiumTypeUtils'\nimport { getProperty } from '../utils/PropertiesUtils'\nimport {\n    FLIPPER_FILTER_KEY,\n    getSetting,\n    getSettingsObject,\n    ITEM_ICON_TYPE,\n    LAST_PREMIUM_PRODUCTS,\n    mapSettingsToApiFormat,\n    RESTRICTIONS_SETTINGS_KEY,\n    setSettingsFromServerSide,\n    storeUsedTagsInLocalStorage\n} from '../utils/SettingsUtils'\nimport { isClientSideRendering } from '../utils/SSRUtils'\nimport { HttpApi, RequestType, SubscriptionType, CUSTOM_EVENTS, NotificationListener } from './ApiTypes.d'\nimport { initHttpHelper } from './HttpHelper'\nimport { websocketHelper } from './WebsocketHelper'\nimport { canUseClipBoard, writeToClipboard } from '../utils/ClipboardUtils'\nimport properties from '../properties'\nimport { getCurrentCoflCoins } from '../utils/CoflCoinsUtils'\n\nfunction getApiEndpoint() {\n    return isClientSideRendering() ? getProperty('apiEndpoint') : process.env.API_ENDPOINT || getProperty('apiEndpoint')\n}\n\nexport function initAPI(returnSSRResponse: boolean = false): API {\n    let httpApi: HttpApi\n    if (isClientSideRendering()) {\n        httpApi = initHttpHelper()\n    } else {\n        let commandEndpoint = process.env.COMMAND_ENDPOINT\n        let apiEndpoint = getApiEndpoint()\n        httpApi = initHttpHelper(commandEndpoint, apiEndpoint)\n    }\n\n    setTimeout(() => {\n        if (isClientSideRendering()) {\n            cacheUtils.checkForCacheClear()\n        }\n    }, 20000)\n\n    let apiErrorHandler = (requestType: RequestType, error: any, requestData: any = null) => {\n        if (!error || !error.message) {\n            return\n        }\n        if (isClientSideRendering()) {\n            toast.error(error.message, {\n                onClick: () => {\n                    if (error.traceId && canUseClipBoard()) {\n                        writeToClipboard(error.traceId)\n                        toast.success(\n                            <span>\n                                Copied the error trace to the clipboard. Please use this to ask for help on our{' '}\n                                <a target=\"_blank\" rel=\"noreferrer\" href=\"https://discord.gg/wvKXfTgCfb\">\n                                    Discord\n                                </a>\n                                .\n                            </span>\n                        )\n                    }\n                }\n            })\n        }\n        console.log('RequestType: ' + requestType)\n        console.log('ErrorMessage: ' + error.message)\n        console.log('RequestData: ')\n        console.log(requestData)\n        console.log('------------------------------\\n')\n    }\n\n    let search = (searchText: string): Promise<SearchResultItem[]> => {\n        return new Promise((resolve, reject) => {\n            httpApi.sendApiRequest({\n                type: RequestType.SEARCH,\n                data: searchText,\n                resolve: (items: any) => {\n                    resolve(\n                        !items\n                            ? []\n                            : items.map((item: any) => {\n                                  return parseSearchResultItem(item)\n                              })\n                    )\n                },\n                reject: (error: any) => {\n                    apiErrorHandler(RequestType.SEARCH, error, searchText)\n                    reject(error)\n                }\n            })\n        })\n    }\n\n    let getItemImageUrl = (item: Item): string => {\n        let type = getSetting(ITEM_ICON_TYPE, 'default')\n\n        let iconURL = item.iconUrl || (item as any).icon\n        if (iconURL) {\n            // Temporary: Keep using production icons while local services are being set up\n            // TODO: Switch to local icons once static service is running\n            if (type === 'vanilla' && !iconURL.endsWith('/vanilla') && iconURL.includes('sky.coflnet.com/static/icon')) {\n                return iconURL + '/vanilla'\n            }\n            return iconURL\n        }\n\n        // Temporary: Use production icons while local static service is being set up\n        let r = `https://sky.coflnet.com/static/icon/${item.tag}${type === 'vanilla' ? '/vanilla' : ''}`\n        return r\n    }\n\n    let getItemDetails = (itemTag: string): Promise<Item> => {\n        return new Promise((resolve, reject) => {\n            httpApi.sendApiRequest({\n                type: RequestType.ITEM_DETAILS,\n                customRequestURL: `${getApiEndpoint()}/item/${itemTag}/details`,\n                data: '',\n                resolve: (item: any) => {\n                    returnSSRResponse ? resolve(item) : resolve(parseItem(item))\n                },\n                reject: (error: any) => {\n                    apiErrorHandler(RequestType.ITEM_DETAILS, error, itemTag)\n                    reject(error)\n                }\n            })\n        })\n    }\n\n    let getItemPrices = (itemTag: string, fetchSpan: DateRange, itemFilter?: ItemFilter): Promise<ItemPrice[]> => {\n        return new Promise((resolve, reject) => {\n            let params = new URLSearchParams()\n            if (itemFilter && Object.keys(itemFilter).length > 0) {\n                params = new URLSearchParams(itemFilter)\n            }\n\n            httpApi.sendApiRequest({\n                type: RequestType.ITEM_PRICES,\n                data: '',\n                customRequestURL: getApiEndpoint() + `/item/price/${itemTag}/history/${fetchSpan}?${params.toString()}`,\n                requestMethod: 'GET',\n                requestHeader: {\n                    'Content-Type': 'application/json'\n                },\n                resolve: (data: any) => {\n                    if (returnSSRResponse) {\n                        resolve(data)\n                        return\n                    }\n                    resolve(data ? data.map(parseItemPrice).sort((a: ItemPrice, b: ItemPrice) => a.time.getTime() - b.time.getTime()) : [])\n                },\n                reject: (error: any) => {\n                    apiErrorHandler(RequestType.ITEM_PRICES, error, {\n                        itemTag,\n                        fetchSpan,\n                        itemFilter\n                    })\n                    reject(error)\n                }\n            })\n        })\n    }\n\n    let getBazaarPrices = (itemTag: string, fetchSpan: DateRange): Promise<BazaarPrice[]> => {\n        return new Promise((resolve, reject) => {\n            httpApi.sendApiRequest({\n                type: RequestType.BAZAAR_PRICES,\n                data: '',\n                customRequestURL: getProperty('apiEndpoint') + `/bazaar/${itemTag}/history/${fetchSpan}`,\n                requestMethod: 'GET',\n                resolve: (data: any) => {\n                    resolve(data ? data.map(parseBazaarPrice).sort((a: BazaarPrice, b: BazaarPrice) => a.timestamp.getTime() - b.timestamp.getTime()) : [])\n                },\n                reject: (error: any) => {\n                    apiErrorHandler(RequestType.BAZAAR_PRICES, error, {\n                        itemTag,\n                        fetchSpan\n                    })\n                    reject(error)\n                }\n            })\n        })\n    }\n\n    let getBazaarPricesByRange = (itemTag: string, startDate: Date | string | number, endDate: Date | string | number): Promise<BazaarPrice[]> => {\n        return new Promise((resolve, reject) => {\n            let startDateIso = new Date(startDate).toISOString()\n            let endDateIso = new Date(endDate).toISOString()\n\n            httpApi.sendApiRequest({\n                type: RequestType.BAZAAR_PRICES,\n                data: '',\n                customRequestURL: getProperty('apiEndpoint') + `/bazaar/${itemTag}/history/?start=${startDateIso}&end=${endDateIso}`,\n                requestMethod: 'GET',\n                resolve: (data: any) => {\n                    data = data.filter(d => d.sell !== undefined && d.buy !== undefined)\n\n                    let buySort = [...data].sort((a, b) => a.buy - b.buy)\n                    let sellSort = [...data].sort((a, b) => a.sell - b.sell)\n\n                    let medianBuy = buySort.length > 0 ? buySort[Math.floor(buySort.length / 2)].buy : 0\n                    let medianSell = sellSort.length > 0 ? sellSort[Math.floor(sellSort.length / 2)].sell : 0\n\n                    let bazaarData: BazaarPrice[] = data\n                        .map(parseBazaarPrice)\n                        .sort((a: BazaarPrice, b: BazaarPrice) => a.timestamp.getTime() - b.timestamp.getTime())\n                    let normalizer = 8\n                    resolve(\n                        bazaarData.filter(\n                            b =>\n                                b.buyData.max < medianBuy * normalizer &&\n                                b.sellData.max < medianSell * normalizer &&\n                                b.buyData.min > medianBuy / normalizer &&\n                                b.sellData.min > medianSell / normalizer\n                        )\n                    )\n                },\n                reject: (error: any) => {\n                    apiErrorHandler(RequestType.BAZAAR_PRICES, error, {\n                        itemTag,\n                        startDateIso,\n                        endDateIso\n                    })\n                    reject(error)\n                }\n            })\n        })\n    }\n\n    let getAuctions = (uuid: string, page: number = 0, itemFilter?: ItemFilter): Promise<Auction[]> => {\n        return new Promise((resolve, reject) => {\n            let params = new URLSearchParams()\n            params.append('page', page.toString())\n\n            if (itemFilter && Object.keys(itemFilter).length > 0) {\n                Object.keys(itemFilter).forEach(key => {\n                    params.append(key, itemFilter[key])\n                })\n            }\n\n            httpApi.sendApiRequest({\n                type: RequestType.PLAYER_AUCTION,\n                customRequestURL: `${getApiEndpoint()}/player/${uuid}/auctions?${params.toString()}`,\n                data: '',\n                resolve: (auctions: any) => {\n                    returnSSRResponse\n                        ? resolve(auctions)\n                        : resolve(\n                              auctions.map((auction: any) => {\n                                  return parseAuction(auction)\n                              })\n                          )\n                },\n                reject: (error: any) => {\n                    apiErrorHandler(RequestType.PLAYER_AUCTION, error, { uuid, page })\n                    reject(error)\n                }\n            })\n        })\n    }\n\n    let getBids = (uuid: string, page: number = 0, itemFilter?: ItemFilter): Promise<BidForList[]> => {\n        return new Promise((resolve, reject) => {\n            let params = new URLSearchParams()\n            params.append('page', page.toString())\n\n            if (itemFilter && Object.keys(itemFilter).length > 0) {\n                Object.keys(itemFilter).forEach(key => {\n                    params.append(key, itemFilter[key])\n                })\n            }\n\n            httpApi.sendApiRequest({\n                type: RequestType.PLAYER_BIDS,\n                customRequestURL: `${getApiEndpoint()}/player/${uuid}/bids?${params.toString()}`,\n                data: '',\n                resolve: (bids: any) => {\n                    resolve(\n                        bids.map((bid: any) => {\n                            return parseItemBidForList(bid)\n                        })\n                    )\n                },\n                reject: (error: any) => {\n                    apiErrorHandler(RequestType.PLAYER_BIDS, error, { uuid, page })\n                    reject(error)\n                }\n            })\n        })\n    }\n\n    let getEnchantments = (): Promise<Enchantment[]> => {\n        return new Promise((resolve, reject) => {\n            httpApi.sendRequest({\n                type: RequestType.ALL_ENCHANTMENTS,\n                data: '',\n                resolve: (enchantments: any) => {\n                    let parsedEnchantments: Enchantment[] = enchantments.map(enchantment => {\n                        return parseEnchantment({\n                            type: enchantment.label,\n                            id: enchantment.id\n                        })\n                    })\n                    parsedEnchantments = parsedEnchantments\n                        .filter(enchantment => {\n                            return enchantment.name!.toLowerCase() !== 'unknown'\n                        })\n                        .sort(enchantmentAndReforgeCompare)\n                    resolve(parsedEnchantments)\n                },\n                reject: (error: any) => {\n                    apiErrorHandler(RequestType.ALL_ENCHANTMENTS, error, '')\n                    reject(error)\n                }\n            })\n        })\n    }\n\n    let trackSearch = (fullSearchId: string, fullSearchType: string): void => {\n        let requestData = {\n            id: fullSearchId,\n            type: fullSearchType\n        }\n        websocketHelper.sendRequest({\n            type: RequestType.TRACK_SEARCH,\n            data: requestData,\n            resolve: () => {},\n            reject: (error: any) => {\n                apiErrorHandler(RequestType.TRACK_SEARCH, error, requestData)\n            }\n        })\n    }\n\n    let getAuctionDetails = (auctionUUID: string): Promise<{ parsed: AuctionDetails; original: any }> => {\n        return new Promise((resolve, reject) => {\n            httpApi.sendApiRequest({\n                type: RequestType.AUCTION_DETAILS,\n                data: auctionUUID,\n                resolve: auctionDetails => {\n                    if (!auctionDetails) {\n                        reject()\n                        return\n                    }\n                    if (!auctionDetails.auctioneer) {\n                        api.getPlayerName(auctionDetails.auctioneerId)\n                            .then(name => {\n                                auctionDetails.auctioneer = {\n                                    name,\n                                    uuid: auctionDetails.auctioneerId\n                                }\n                            })\n                            .catch(e => {\n                                console.error(`Error fetching playername for ${auctionDetails.auctioneerId}. ${JSON.stringify(e)}`)\n                                auctionDetails.auctioneer = {\n                                    name: '',\n                                    uuid: auctionDetails.auctioneerId\n                                }\n                            })\n                            .finally(() => {\n                                resolve({ parsed: parseAuctionDetails(auctionDetails), original: auctionDetails })\n                            })\n                    } else {\n                        resolve({ parsed: parseAuctionDetails(auctionDetails), original: auctionDetails })\n                    }\n                },\n                reject: (error: any) => {\n                    reject(error)\n                }\n            })\n        })\n    }\n\n    let getPlayerName = (uuid: string): Promise<string> => {\n        // Reduce amount of API calls during test runs\n        if (properties.isTestRunner) {\n            return Promise.resolve('TestRunnerUser')\n        }\n        return new Promise((resolve, reject) => {\n            if (!uuid) {\n                resolve('')\n                return\n            }\n            httpApi.sendApiRequest({\n                type: RequestType.PLAYER_NAME,\n                customRequestURL: `${getApiEndpoint()}/player/${uuid}/name`,\n                data: '',\n                resolve: name => {\n                    resolve(name)\n                },\n                reject: (error: any) => {\n                    apiErrorHandler(RequestType.PLAYER_NAME, error, uuid)\n                    reject(error)\n                }\n            })\n        })\n    }\n\n    let getPlayerNames = (uuids: string[]): Promise<{ [key: string]: string }> => {\n        // Reduce amount of API calls during test runs\n        if (properties.isTestRunner) {\n            let result = {}\n            uuids.forEach(uuid => {\n                result[uuid] = 'TestRunnerUser'\n            })\n            return Promise.resolve(result)\n        }\n        return new Promise((resolve, reject) => {\n            httpApi.sendApiRequest(\n                {\n                    type: RequestType.PLAYER_NAMES,\n                    customRequestURL: `${getApiEndpoint()}/player/names`,\n                    requestMethod: 'POST',\n                    requestHeader: {\n                        'Content-Type': 'application/json'\n                    },\n                    data: '',\n                    resolve: names => {\n                        resolve(names)\n                    },\n                    reject: (error: any) => {\n                        apiErrorHandler(RequestType.PLAYER_NAMES, error, '')\n                        reject(error)\n                    }\n                },\n                JSON.stringify(uuids)\n            )\n        })\n    }\n\n    let connectionId = null\n\n    let setConnectionId = (): Promise<void> => {\n        return new Promise((resolve, reject) => {\n            connectionId = connectionId || generateUUID()\n\n            websocketHelper.sendRequest({\n                type: RequestType.SET_CONNECTION_ID,\n                data: connectionId,\n                resolve: () => {\n                    resolve()\n                },\n                reject: (error: any) => {\n                    apiErrorHandler(RequestType.SET_CONNECTION_ID, error, connectionId)\n                    reject(error)\n                }\n            })\n        })\n    }\n\n    let getVersion = (): Promise<string> => {\n        return new Promise((resolve, reject) => {\n            httpApi.sendRequest({\n                type: RequestType.GET_VERSION,\n                data: '',\n                resolve: (response: any) => {\n                    resolve(response.toString())\n                },\n                reject: (error: any) => {\n                    apiErrorHandler(RequestType.GET_VERSION, error, '')\n                    reject(error)\n                }\n            })\n        })\n    }\n\n    let subscribe = (topic: string, types: SubscriptionType[], targets: NotificationTarget[], price?: number, filter?: ItemFilter): Promise<void> => {\n        return new Promise((resolve, reject) => {\n            let googleId = sessionStorage.getItem('googleId')\n            if (!googleId) {\n                toast.error('You need to be logged in to create a notification listeners')\n                reject()\n                return\n            }\n\n            let typesToSend: SubscriptionType[] = [...types]\n            typesToSend.push(SubscriptionType.NONE)\n\n            if (filter) {\n                filter._hide = undefined\n                filter._sellerName = undefined\n            }\n\n            let requestData = {\n                topicId: topic,\n                price: price || undefined,\n                type: typesToSend.reduce((a, b) => {\n                    let aNum: number = typeof a === 'number' ? (a as number) : parseInt(SubscriptionType[a])\n                    let bNum: number = typeof b === 'number' ? (b as number) : parseInt(SubscriptionType[b])\n                    return aNum + bNum\n                }),\n                filter: filter ? JSON.stringify(filter) : undefined\n            }\n\n            httpApi.sendApiRequest(\n                {\n                    type: RequestType.SUBSCRIBE,\n                    customRequestURL: `${getApiEndpoint()}/notifications/listeners`,\n                    data: '',\n                    requestMethod: 'POST',\n                    requestHeader: {\n                        GoogleToken: googleId,\n                        'Content-Type': 'application/json'\n                    },\n                    resolve: (listener: NotificationListener) => {\n                        createNotificationSubscription({\n                            id: undefined,\n                            sourceSubIdRegex: listener.id?.toString() || '',\n                            sourceType: 'Subscription',\n                            targets: targets.map(t => {\n                                return {\n                                    id: t.id || 0,\n                                    name: t.name || '',\n                                    isDisabled: false,\n                                    priority: 0\n                                }\n                            })\n                        })\n                            .then(() => {\n                                resolve()\n                            })\n                            .catch(e => {\n                                apiErrorHandler(RequestType.SUBSCRIBE, e)\n                                reject(e)\n                            })\n                    },\n                    reject: (error: any) => {\n                        apiErrorHandler(RequestType.SUBSCRIBE, error)\n                        reject(error)\n                    }\n                },\n                JSON.stringify(requestData)\n            )\n        })\n    }\n\n    let unsubscribe = (subscription: NotificationListener): Promise<void> => {\n        return new Promise((resolve, reject) => {\n            let googleId = sessionStorage.getItem('googleId')\n            if (!googleId) {\n                toast.error('You need to be logged in to delete notification listeners')\n                reject()\n                return\n            }\n\n            let typesToSend: SubscriptionType[] = [...subscription.types]\n            typesToSend.push(SubscriptionType.NONE)\n\n            let filterToSend = { ...subscription.filter }\n\n            if (subscription.filter) {\n                filterToSend._hide = undefined\n                filterToSend._sellerName = undefined\n            }\n\n            let requestData = {\n                id: subscription.id,\n                topicId: subscription.topicId,\n                price: subscription.price || undefined,\n                type: typesToSend.reduce((a, b) => {\n                    let aNum: number = typeof a === 'number' ? (a as number) : parseInt(SubscriptionType[a])\n                    let bNum: number = typeof b === 'number' ? (b as number) : parseInt(SubscriptionType[b])\n                    return aNum + bNum\n                }),\n                filter: filterToSend ? JSON.stringify(filterToSend) : undefined\n            }\n\n            httpApi.sendApiRequest(\n                {\n                    type: RequestType.UNSUBSCRIBE,\n                    customRequestURL: `${getApiEndpoint()}/notifications/listeners`,\n                    data: '',\n                    requestMethod: 'DELETE',\n                    requestHeader: {\n                        GoogleToken: googleId,\n                        'Content-Type': 'application/json'\n                    },\n                    resolve: () => {\n                        resolve()\n                    },\n                    reject: (error: any) => {\n                        apiErrorHandler(RequestType.UNSUBSCRIBE, error)\n                        reject(error)\n                    }\n                },\n                JSON.stringify(requestData)\n            )\n        })\n    }\n\n    let getNotificationListener = (): Promise<NotificationListener[]> => {\n        return new Promise((resolve, reject) => {\n            let googleId = sessionStorage.getItem('googleId')\n            if (!googleId) {\n                toast.error('You need to be logged in to get notification listeners')\n                reject()\n                return\n            }\n\n            httpApi.sendApiRequest({\n                type: RequestType.GET_SUBSCRIPTIONS,\n                customRequestURL: `${getApiEndpoint()}/notifications/listeners`,\n                data: '',\n                requestHeader: {\n                    GoogleToken: googleId,\n                    'Content-Type': 'application/json'\n                },\n                resolve: data => {\n                    resolve(data ? data.map(parseSubscription) : [])\n                },\n                reject: (error: any) => {\n                    apiErrorHandler(RequestType.GET_SUBSCRIPTIONS, error)\n                    reject(error)\n                }\n            })\n        })\n    }\n\n    let loginWithToken = (id: string): Promise<string> => {\n        return new Promise((resolve, reject) => {\n            websocketHelper.sendRequest({\n                type: RequestType.LOGIN_WITH_TOKEN,\n                data: id,\n                resolve: token => {\n                    resolve(token)\n                },\n                reject: (error: any) => {\n                    apiErrorHandler(RequestType.LOGIN_WITH_TOKEN, error)\n                    reject(error)\n                }\n            })\n        })\n    }\n\n    let setToken = (token: string): Promise<void> => {\n        return new Promise((resolve, reject) => {\n            websocketHelper.sendRequest({\n                type: RequestType.FCM_TOKEN,\n                data: {\n                    name: '',\n                    token: token\n                },\n                resolve: () => {\n                    resolve()\n                },\n                reject: (error: any) => {\n                    apiErrorHandler(RequestType.FCM_TOKEN, error, token)\n                    reject(error)\n                }\n            })\n        })\n    }\n\n    let getRecentAuctions = (itemTag: string, itemFilter: ItemFilter): Promise<RecentAuction[]> => {\n        return new Promise((resolve, reject) => {\n            let params = new URLSearchParams()\n            if (itemFilter && Object.keys(itemFilter).length > 0) {\n                params = new URLSearchParams(itemFilter)\n            }\n\n            httpApi.sendApiRequest({\n                type: RequestType.RECENT_AUCTIONS,\n                customRequestURL: getApiEndpoint() + `/auctions/tag/${itemTag}/recent/overview?${params.toString()}`,\n                data: '',\n                resolve: (data: any) => {\n                    resolve(data ? data.map(a => parseRecentAuction(a)) : [])\n                },\n                reject: (error: any) => {\n                    apiErrorHandler(RequestType.RECENT_AUCTIONS, error, itemTag)\n                    reject(error)\n                }\n            })\n        })\n    }\n\n    let getFlips = (): Promise<FlipAuction[]> => {\n        return new Promise((resolve, reject) => {\n            websocketHelper.sendRequest({\n                type: RequestType.GET_FLIPS,\n                data: '',\n                resolve: (data: any) => {\n                    resolve(data.map(a => parseFlipAuction(a)))\n                },\n                reject: (error: any) => {\n                    apiErrorHandler(RequestType.RECENT_AUCTIONS, error, '')\n                    reject(error)\n                }\n            })\n        })\n    }\n\n    let getPreloadFlips = (): Promise<FlipAuction[]> => {\n        return new Promise((resolve, reject) => {\n            httpApi.sendRequest({\n                type: RequestType.GET_FLIPS,\n                data: '',\n                resolve: (data: any) => {\n                    returnSSRResponse ? resolve(data) : resolve(data.map(parseFlipAuction))\n                },\n                reject: (error: any) => {\n                    apiErrorHandler(RequestType.GET_FLIPS, error, '')\n                }\n            })\n        })\n    }\n\n    let subscribeFlips = (\n        restrictionList: FlipRestriction[],\n        filter: FlipperFilter,\n        flipSettings: FlipCustomizeSettings,\n        flipCallback?: Function,\n        soldCallback?: Function,\n        nextUpdateNotificationCallback?: Function,\n        onSubscribeSuccessCallback?: Function,\n        onErrorCallback?: Function,\n        forceSettingsUpdate: boolean = false\n    ) => {\n        websocketHelper.removeOldSubscriptionByType(RequestType.SUBSCRIBE_FLIPS)\n\n        storeUsedTagsInLocalStorage(restrictionList)\n\n        let requestData = mapSettingsToApiFormat(filter, flipSettings, restrictionList)\n\n        websocketHelper.subscribe({\n            type: RequestType.SUBSCRIBE_FLIPS,\n            data: forceSettingsUpdate ? requestData : null,\n            callback: function (response) {\n                switch (response.type) {\n                    case 'flip':\n                        if (flipCallback) {\n                            flipCallback(parseFlipAuction(response.data))\n                        }\n                        break\n                    case 'nextUpdate':\n                        if (nextUpdateNotificationCallback) {\n                            nextUpdateNotificationCallback()\n                        }\n                        break\n                    case 'sold':\n                        if (soldCallback) {\n                            soldCallback(response.data)\n                        }\n                        break\n                    case 'flipSettings':\n                        if (!response.data) {\n                            api.subscribeFlips(\n                                restrictionList,\n                                filter,\n                                flipSettings,\n                                flipCallback,\n                                soldCallback,\n                                nextUpdateNotificationCallback,\n                                undefined,\n                                onErrorCallback,\n                                true\n                            )\n                        } else {\n                            setSettingsFromServerSide(response.data)\n                        }\n                        break\n                    case 'settingsUpdate':\n                        let data = response.data as any\n                        if (data.changer === window.sessionStorage.getItem('sessionId')) {\n                            return\n                        }\n                        setSettingsFromServerSide(response.data)\n                        break\n                    case 'ok':\n                        if (onSubscribeSuccessCallback) {\n                            onSubscribeSuccessCallback()\n                        }\n                        break\n                    default:\n                        break\n                }\n            },\n            resubscribe: function (subscription) {\n                let filter = getSettingsObject<FlipperFilter>(FLIPPER_FILTER_KEY, {})\n                let restrictions = getSettingsObject<FlipRestriction[]>(RESTRICTIONS_SETTINGS_KEY, [])\n                subscribeFlips(\n                    restrictions,\n                    filter,\n                    getFlipCustomizeSettings(),\n                    flipCallback,\n                    soldCallback,\n                    nextUpdateNotificationCallback,\n                    undefined,\n                    onErrorCallback,\n                    false\n                )\n            },\n            onError: function (message) {\n                toast.error(message)\n                if (onErrorCallback) {\n                    onErrorCallback()\n                }\n            }\n        })\n    }\n\n    const debounceSubFlipAnonymFunction = (function () {\n        let timerId\n\n        return (\n            restrictionList: FlipRestriction[],\n            filter: FlipperFilter,\n            flipSettings: FlipCustomizeSettings,\n            flipCallback?: Function,\n            soldCallback?: Function,\n            nextUpdateNotificationCallback?: Function,\n            onSubscribeSuccessCallback?: Function\n        ) => {\n            clearTimeout(timerId)\n            timerId = setTimeout(() => {\n                websocketHelper.removeOldSubscriptionByType(RequestType.SUBSCRIBE_FLIPS)\n\n                let requestData = mapSettingsToApiFormat(filter, flipSettings, restrictionList)\n\n                websocketHelper.subscribe({\n                    type: RequestType.SUBSCRIBE_FLIPS_ANONYM,\n                    data: requestData,\n                    callback: function (response) {\n                        switch (response.type) {\n                            case 'flip':\n                                if (flipCallback) {\n                                    flipCallback(parseFlipAuction(response.data))\n                                }\n                                break\n                            case 'nextUpdate':\n                                if (nextUpdateNotificationCallback) {\n                                    nextUpdateNotificationCallback()\n                                }\n                                break\n                            case 'sold':\n                                if (soldCallback) {\n                                    soldCallback(response.data)\n                                }\n                                break\n                            case 'ok':\n                                if (onSubscribeSuccessCallback) {\n                                    onSubscribeSuccessCallback()\n                                }\n                                break\n                            default:\n                                break\n                        }\n                    },\n                    resubscribe: function (subscription) {\n                        let filter = getSettingsObject<FlipperFilter>(FLIPPER_FILTER_KEY, {})\n                        let restrictions = getSettingsObject<FlipRestriction[]>(RESTRICTIONS_SETTINGS_KEY, [])\n                        subscribeFlipsAnonym(\n                            restrictions,\n                            filter,\n                            getFlipCustomizeSettings(),\n                            flipCallback,\n                            soldCallback,\n                            nextUpdateNotificationCallback,\n                            undefined\n                        )\n                    },\n                    onError: function (message) {\n                        toast.error(message)\n                    }\n                })\n            }, 2000)\n        }\n    })()\n\n    let subscribeFlipsAnonym = (\n        restrictionList: FlipRestriction[],\n        filter: FlipperFilter,\n        flipSettings: FlipCustomizeSettings,\n        flipCallback?: Function,\n        soldCallback?: Function,\n        nextUpdateNotificationCallback?: Function,\n        onSubscribeSuccessCallback?: Function\n    ) => {\n        debounceSubFlipAnonymFunction(\n            restrictionList,\n            filter,\n            flipSettings,\n            flipCallback,\n            soldCallback,\n            nextUpdateNotificationCallback,\n            onSubscribeSuccessCallback\n        )\n    }\n\n    let unsubscribeFlips = (): Promise<void> => {\n        return new Promise((resolve, reject) => {\n            websocketHelper.sendRequest({\n                type: RequestType.UNSUBSCRIBE_FLIPS,\n                data: '',\n                resolve: function (data) {\n                    resolve()\n                },\n                reject: function (error) {\n                    apiErrorHandler(RequestType.ACTIVE_AUCTIONS, error, '')\n                    reject(error)\n                }\n            })\n        })\n    }\n\n    let getFilters = (tag: string): Promise<FilterOptions[]> => {\n        return new Promise((resolve, reject) => {\n            httpApi.sendApiRequest({\n                type: RequestType.GET_FILTER,\n                customRequestURL: `${getApiEndpoint()}/filter/options?itemTag=${tag}`,\n                data: '',\n                resolve: (data: any) => {\n                    resolve(data.map(a => parseFilterOption(a)))\n                },\n                reject: (error: any) => {\n                    apiErrorHandler(RequestType.GET_FILTER, error, tag)\n                }\n            })\n        })\n    }\n\n    let getNewPlayers = (): Promise<Player[]> => {\n        return new Promise((resolve, reject) => {\n            httpApi.sendLimitedCacheRequest(\n                {\n                    type: RequestType.NEW_PLAYERS,\n                    data: '',\n                    resolve: function (data) {\n                        returnSSRResponse ? resolve(data) : resolve(data.map(p => parsePlayer(p)))\n                    },\n                    reject: function (error) {\n                        apiErrorHandler(RequestType.NEW_PLAYERS, error, '')\n                        reject(error)\n                    }\n                },\n                5\n            )\n        })\n    }\n\n    let getNewItems = (): Promise<Item[]> => {\n        return new Promise((resolve, reject) => {\n            httpApi.sendLimitedCacheRequest(\n                {\n                    type: RequestType.NEW_ITEMS,\n                    data: '',\n                    resolve: function (data) {\n                        returnSSRResponse ? resolve(data) : resolve(data.map(i => parseItem(i)))\n                    },\n                    reject: function (error) {\n                        apiErrorHandler(RequestType.NEW_ITEMS, error, '')\n                        reject(error)\n                    }\n                },\n                15\n            )\n        })\n    }\n\n    let getPopularSearches = (): Promise<PopularSearch[]> => {\n        return new Promise((resolve, reject) => {\n            httpApi.sendLimitedCacheRequest(\n                {\n                    type: RequestType.POPULAR_SEARCHES,\n                    data: '',\n                    resolve: function (data) {\n                        returnSSRResponse ? resolve(data) : resolve(data.map(s => parsePopularSearch(s)))\n                    },\n                    reject: function (error) {\n                        apiErrorHandler(RequestType.POPULAR_SEARCHES, error, '')\n                        reject(error)\n                    }\n                },\n                5\n            )\n        })\n    }\n\n    let getEndedAuctions = (): Promise<Auction[]> => {\n        return new Promise((resolve, reject) => {\n            httpApi.sendLimitedCacheRequest(\n                {\n                    type: RequestType.ENDED_AUCTIONS,\n                    data: '',\n                    resolve: function (data) {\n                        returnSSRResponse ? resolve(data) : resolve(data.map(a => parseAuction(a)))\n                    },\n                    reject: function (error) {\n                        apiErrorHandler(RequestType.ENDED_AUCTIONS, error, '')\n                        reject(error)\n                    }\n                },\n                1\n            )\n        })\n    }\n\n    let getNewAuctions = (): Promise<Auction[]> => {\n        return new Promise((resolve, reject) => {\n            httpApi.sendLimitedCacheRequest(\n                {\n                    type: RequestType.NEW_AUCTIONS,\n                    data: '',\n                    resolve: function (data) {\n                        returnSSRResponse ? resolve(data) : resolve(data.map(a => parseAuction(a)))\n                    },\n                    reject: function (error) {\n                        apiErrorHandler(RequestType.NEW_AUCTIONS, error, '')\n                        reject(error)\n                    }\n                },\n                1\n            )\n        })\n    }\n\n    let getFlipBasedAuctions = (flipUUID: string): Promise<Auction[]> => {\n        return new Promise((resolve, reject) => {\n            httpApi.sendRequest({\n                type: RequestType.GET_FLIP_BASED_AUCTIONS,\n                data: flipUUID,\n                resolve: (data: any) => {\n                    resolve(data.map(a => parseAuction(a)))\n                },\n                reject: (error: any) => {\n                    apiErrorHandler(RequestType.GET_FLIP_BASED_AUCTIONS, error, flipUUID)\n                    reject(error)\n                }\n            })\n        })\n    }\n\n    let stripePurchase = (productId: string, coinAmount?: number): Promise<PaymentResponse> => {\n        return new Promise((resolve, reject) => {\n            let googleId = sessionStorage.getItem('googleId')\n            if (!googleId) {\n                toast.error('You need to be logged in to purchase something.')\n                reject()\n                return\n            }\n\n            let data = {\n                userId: googleId,\n                productId: productId\n            }\n\n            httpApi.sendApiRequest(\n                {\n                    type: RequestType.STRIPE_PAYMENT_SESSION,\n                    requestMethod: 'POST',\n                    requestHeader: {\n                        GoogleToken: data.userId,\n                        'Content-Type': 'application/json'\n                    },\n                    data: data.productId,\n                    resolve: (data: any) => {\n                        resolve(parsePaymentResponse(data))\n                    },\n                    reject: (error: any) => {\n                        apiErrorHandler(RequestType.STRIPE_PAYMENT_SESSION, error, data)\n                        reject(error)\n                    }\n                },\n                JSON.stringify({\n                    coinAmount\n                })\n            )\n        })\n    }\n\n    let paypalPurchase = (productId: string, coinAmount?: number): Promise<PaymentResponse> => {\n        return new Promise((resolve, reject) => {\n            let googleId = sessionStorage.getItem('googleId')\n            if (!googleId) {\n                toast.error('You need to be logged in to purchase something.')\n                reject()\n                return\n            }\n\n            let data = {\n                userId: googleId,\n                productId: productId\n            }\n\n            httpApi.sendApiRequest(\n                {\n                    type: RequestType.PAYPAL_PAYMENT,\n                    requestMethod: 'POST',\n                    data: data.productId,\n                    requestHeader: {\n                        GoogleToken: data.userId,\n                        'Content-Type': 'application/json'\n                    },\n                    resolve: (response: any) => {\n                        resolve(parsePaymentResponse(response))\n                    },\n                    reject: (error: any) => {\n                        apiErrorHandler(RequestType.PAYPAL_PAYMENT, error, data)\n                        reject(error)\n                    }\n                },\n                JSON.stringify({\n                    coinAmount\n                })\n            )\n        })\n    }\n\n    let lemonsqueezyPurchase = (productId: string, coinAmount?: number): Promise<PaymentResponse> => {\n        return new Promise((resolve, reject) => {\n            let googleId = sessionStorage.getItem('googleId')\n            if (!googleId) {\n                toast.error('You need to be logged in to purchase something.')\n                reject()\n                return\n            }\n\n            let data = {\n                userId: googleId,\n                productId: productId\n            }\n\n            httpApi.sendApiRequest(\n                {\n                    type: RequestType.LEMONSQUEEZY_PAYMENT,\n                    requestMethod: 'POST',\n                    data: data.productId,\n                    requestHeader: {\n                        GoogleToken: data.userId,\n                        'Content-Type': 'application/json'\n                    },\n                    resolve: (response: any) => {\n                        resolve(parsePaymentResponse(response))\n                    },\n                    reject: (error: any) => {\n                        apiErrorHandler(RequestType.LEMONSQUEEZY_PAYMENT, error, data)\n                        reject(error)\n                    }\n                },\n                JSON.stringify({\n                    coinAmount\n                })\n            )\n        })\n    }\n\n    let purchaseWithCoflcoins = (productId: string, googleToken: string, count?: number): Promise<void> => {\n        return new Promise((resolve, reject) => {\n            let data = {\n                userId: googleToken,\n                productId: productId\n            }\n\n            httpApi.sendApiRequest(\n                {\n                    type: RequestType.PURCHASE_WITH_COFLCOiNS,\n                    data: '',\n                    requestMethod: 'POST',\n                    requestHeader: {\n                        GoogleToken: data.userId,\n                        'Content-Type': 'application/json'\n                    },\n                    resolve: function () {\n                        resolve()\n                    },\n                    reject: function (error) {\n                        apiErrorHandler(RequestType.PURCHASE_WITH_COFLCOiNS, error, data)\n                        reject(error)\n                    }\n                },\n                JSON.stringify({\n                    count: count,\n                    slug: productId\n                })\n            )\n        })\n    }\n\n    let subscribeCoflCoinChange = () => {\n        websocketHelper.subscribe({\n            type: RequestType.SUBSCRIBE_EVENTS,\n            data: '',\n            resubscribe: function (subscription) {\n                subscribeCoflCoinChange()\n            },\n            onError: function (message) {\n                toast.error(message)\n            },\n            callback: function (response) {\n                if (response.data.sourceType === 'purchase' || response.data.sourceType === 'topup') {\n\n                    // CoflCoins shouldnt change below 0 with a purchase or topup change\n                    let newCoflCoinAmount = getCurrentCoflCoins() + Math.round(response.data.data.amount);\n                    if (newCoflCoinAmount < 0) {\n                        newCoflCoinAmount = 0\n                    }\n\n                    document.dispatchEvent(\n                        new CustomEvent(CUSTOM_EVENTS.COFLCOIN_UPDATE, { detail: { coflCoins: newCoflCoinAmount } })\n                    )\n                }\n            }\n        })\n    }\n\n    let getCoflcoinBalance = (): Promise<number> => {\n        return new Promise((resolve, reject) => {\n            websocketHelper.sendRequest({\n                type: RequestType.GET_COFLCOIN_BALANCE,\n                data: '',\n                resolve: function (response) {\n                    resolve(parseInt(response))\n                },\n                reject: function (error) {\n                    apiErrorHandler(RequestType.GET_COFLCOIN_BALANCE, error, '')\n                    reject(error)\n                }\n            })\n        })\n    }\n\n    let getRefInfo = (): Promise<RefInfo> => {\n        return new Promise((resolve, reject) => {\n            let googleId = sessionStorage.getItem('googleId')\n            if (!googleId) {\n                toast.error('You need to be logged in to use the ref system.')\n                reject()\n                return\n            }\n\n            httpApi.sendApiRequest({\n                type: RequestType.GET_REF_INFO,\n                data: '',\n                requestHeader: {\n                    GoogleToken: googleId\n                },\n                resolve: (response: any) => {\n                    resolve(parseRefInfo(response))\n                },\n                reject: (error: any) => {\n                    apiErrorHandler(RequestType.GET_REF_INFO, error, '')\n                    reject(error)\n                }\n            })\n        })\n    }\n\n    let setRef = (refId: string): Promise<void> => {\n        return new Promise((resolve, reject) => {\n            let googleId = sessionStorage.getItem('googleId')\n            if (!googleId) {\n                toast.error('You need to be logged in to use the ref system.')\n                reject()\n                return\n            }\n\n            httpApi.sendApiRequest(\n                {\n                    type: RequestType.SET_REF,\n                    data: '',\n                    requestMethod: 'POST',\n                    requestHeader: {\n                        GoogleToken: googleId,\n                        'Content-Type': 'application/json'\n                    },\n                    resolve: () => {\n                        resolve()\n                    },\n                    reject: (error: any) => {\n                        apiErrorHandler(RequestType.SET_REF, error, '')\n                        reject(error)\n                    }\n                },\n                JSON.stringify({\n                    refCode: refId\n                })\n            )\n        })\n    }\n\n    let getActiveAuctions = (item: Item, order: string, filter: ItemFilter = {}): Promise<RecentAuction[]> => {\n        return new Promise((resolve, reject) => {\n            let params = {\n                orderBy: order\n            }\n            Object.keys(filter).forEach(key => {\n                params[key] = filter[key].toString()\n            })\n\n            httpApi.sendApiRequest({\n                type: RequestType.ACTIVE_AUCTIONS,\n                customRequestURL: `${getApiEndpoint()}/auctions/tag/${item.tag}/active/overview?${new URLSearchParams(params).toString()}`,\n                data: '',\n                resolve: function (data) {\n                    resolve(data.map(a => parseRecentAuction(a)))\n                },\n                reject: function (error) {\n                    apiErrorHandler(RequestType.ACTIVE_AUCTIONS, error, {\n                        tag: item.tag,\n                        filter,\n                        order\n                    })\n                    reject(error)\n                }\n            })\n        })\n    }\n\n    let connectMinecraftAccount = (playerUUID: string): Promise<MinecraftConnectionInfo> => {\n        return new Promise((resolve, reject) => {\n            websocketHelper.sendRequest({\n                type: RequestType.CONNECT_MINECRAFT_ACCOUNT,\n                data: playerUUID,\n                resolve: function (data) {\n                    resolve(parseMinecraftConnectionInfo(data))\n                },\n                reject: function (error) {\n                    apiErrorHandler(RequestType.CONNECT_MINECRAFT_ACCOUNT, error, playerUUID)\n                    reject(error)\n                }\n            })\n        })\n    }\n\n    let accountInfo\n    let getAccountInfo = (): Promise<AccountInfo> => {\n        return new Promise((resolve, reject) => {\n            if (accountInfo) {\n                resolve(accountInfo)\n                return\n            }\n\n            websocketHelper.sendRequest({\n                type: RequestType.GET_ACCOUNT_INFO,\n                data: '',\n                resolve: function (accountInfo) {\n                    let info = parseAccountInfo(accountInfo)\n                    accountInfo = info\n                    resolve(info)\n                },\n                reject: function (error) {\n                    apiErrorHandler(RequestType.GET_ACCOUNT_INFO, error, '')\n                }\n            })\n        })\n    }\n\n    let itemSearch = (searchText: string): Promise<SearchResultItem[]> => {\n        return new Promise((resolve, reject) => {\n            httpApi.sendApiRequest({\n                type: RequestType.ITEM_SEARCH,\n                data: searchText,\n                resolve: function (data) {\n                    resolve(data.map(a => parseSearchResultItem(a)))\n                },\n                reject: function (error) {\n                    apiErrorHandler(RequestType.ITEM_SEARCH, error, searchText)\n                    reject(error)\n                }\n            })\n        })\n    }\n\n    let authenticateModConnection = async (conId: string, googleToken: string): Promise<void> => {\n        let timeout = setTimeout(() => {\n            toast.warn(\n                <span>\n                    The login seems to take longer that expected. Are you using Kaspersky? If so, the \"Secure Browsing\" feature seems to interfere with the\n                    login\n                </span>\n            )\n        }, 10000)\n        return new Promise((resolve, reject) => {\n            httpApi.sendApiRequest({\n                type: RequestType.AUTHENTICATE_MOD_CONNECTION,\n                requestMethod: 'POST',\n                data: '',\n                requestHeader: {\n                    GoogleToken: googleToken,\n                    'Content-Type': 'application/json'\n                },\n                customRequestURL: `${getApiEndpoint()}/mod/auth?newId=${encodeURIComponent(conId)}`,\n                resolve: function () {\n                    clearTimeout(timeout)\n                    resolve()\n                },\n                reject: function (error) {\n                    clearTimeout(timeout)\n                    apiErrorHandler(RequestType.AUTHENTICATE_MOD_CONNECTION, error, conId)\n                    reject(error)\n                }\n            })\n        })\n    }\n\n    let getFlipUpdateTime = (): Promise<Date> => {\n        return new Promise((resolve, reject) => {\n            httpApi.sendApiRequest({\n                type: RequestType.FLIP_UPDATE_TIME,\n                data: '',\n                resolve: function (data) {\n                    resolve(new Date(data))\n                },\n                reject: function (error) {\n                    apiErrorHandler(RequestType.FLIP_UPDATE_TIME, error, '')\n                }\n            })\n        })\n    }\n    let playerSearch = (playerName: string): Promise<Player[]> => {\n        return new Promise((resolve, reject) => {\n            httpApi.sendApiRequest({\n                type: RequestType.PLAYER_SEARCH,\n                data: playerName,\n                resolve: function (players) {\n                    resolve(players ? players.map(parsePlayer) : [])\n                },\n                reject: function (error) {\n                    apiErrorHandler(RequestType.PLAYER_SEARCH, error, playerName)\n                    reject(error)\n                }\n            })\n        })\n    }\n\n    let getLowSupplyItems = (): Promise<LowSupplyItem[]> => {\n        return new Promise((resolve, reject) => {\n            httpApi.sendApiRequest({\n                type: RequestType.GET_LOW_SUPPLY_ITEMS,\n                data: '',\n                resolve: function (items) {\n                    returnSSRResponse\n                        ? resolve(items)\n                        : resolve(\n                              items.map(item => {\n                                  let lowSupplyItem = parseLowSupplyItem(item)\n                                  return lowSupplyItem\n                              })\n                          )\n                },\n                reject: function (error) {\n                    apiErrorHandler(RequestType.GET_LOW_SUPPLY_ITEMS, error, '')\n                    reject(error)\n                }\n            })\n        })\n    }\n\n    let sendFeedback = (feedbackKey: string, feedback: any): Promise<void> => {\n        return new Promise((resolve, reject) => {\n            let googleId = sessionStorage.getItem('googleId')\n            let user\n            if (googleId) {\n                let parts = googleId.split('.')\n                if (parts.length > 2) {\n                    let obj = JSON.parse(atobUnicode(parts[1]))\n                    user = obj.sub\n                }\n            }\n\n            let requestData = {\n                Context: 'Skyblock',\n                User: user || '',\n                Feedback: JSON.stringify(feedback),\n                FeedbackName: feedbackKey\n            }\n\n            httpApi.sendApiRequest(\n                {\n                    type: RequestType.SEND_FEEDBACK,\n                    data: '',\n                    customRequestURL: getProperty('feedbackEndpoint'),\n                    requestMethod: 'POST',\n                    requestHeader: {\n                        'Content-Type': 'application/json'\n                    },\n                    resolve: function () {\n                        resolve()\n                    },\n                    reject: function (error) {\n                        apiErrorHandler(RequestType.SEND_FEEDBACK, error, feedback)\n                        reject(error)\n                    }\n                },\n                JSON.stringify(requestData)\n            )\n        })\n    }\n\n    let getProfitableCrafts = (): Promise<ProfitableCraft[]> => {\n        return new Promise((resolve, reject) => {\n            httpApi.sendApiRequest({\n                type: RequestType.GET_PROFITABLE_CRAFTS,\n                customRequestURL: getApiEndpoint() + '/' + RequestType.GET_PROFITABLE_CRAFTS,\n                data: '',\n                resolve: function (crafts) {\n                    returnSSRResponse ? resolve(crafts) : resolve(parseProfitableCrafts(crafts))\n                },\n                reject: function (error) {\n                    apiErrorHandler(RequestType.GET_PROFITABLE_CRAFTS, error, '')\n                    reject(error)\n                }\n            })\n        })\n    }\n\n    let triggerPlayerNameCheck = (playerUUID: string): Promise<void> => {\n        return new Promise((resolve, reject) => {\n            httpApi.sendApiRequest({\n                type: RequestType.TRIGGER_PLAYER_NAME_CHECK,\n                data: '',\n                customRequestURL: getApiEndpoint() + '/player/' + playerUUID + '/name',\n                requestMethod: 'POST',\n                resolve: function () {\n                    resolve()\n                },\n                reject: function (error) {\n                    apiErrorHandler(RequestType.TRIGGER_PLAYER_NAME_CHECK, error, '')\n                    reject(error)\n                }\n            })\n        })\n    }\n\n    let getPlayerProfiles = (playerUUID): Promise<SkyblockProfile[]> => {\n        return new Promise((resolve, reject) => {\n            httpApi.sendApiRequest({\n                type: RequestType.GET_PLAYER_PROFILES,\n                data: playerUUID,\n                resolve: function (result) {\n                    resolve(\n                        Object.keys(result.profiles).map(key => {\n                            return parseSkyblockProfile(result.profiles[key])\n                        })\n                    )\n                },\n                reject: function (error) {\n                    apiErrorHandler(RequestType.TRIGGER_PLAYER_NAME_CHECK, error, playerUUID)\n                }\n            })\n        })\n    }\n\n    let getCraftingRecipe = (itemTag: string): Promise<CraftingRecipe> => {\n        return new Promise((resolve, reject) => {\n            httpApi.sendApiRequest({\n                type: RequestType.GET_CRAFTING_RECIPE,\n                data: itemTag,\n                resolve: function (data) {\n                    resolve(parseCraftingRecipe(data))\n                },\n                reject: function (error) {\n                    apiErrorHandler(RequestType.GET_CRAFTING_RECIPE, error, itemTag)\n                    reject(error)\n                }\n            })\n        })\n    }\n\n    let getLowestBin = (itemTag: string): Promise<LowestBin> => {\n        return new Promise((resolve, reject) => {\n            httpApi.sendApiRequest({\n                type: RequestType.GET_LOWEST_BIN,\n                customRequestURL: 'item/price/' + itemTag + '/bin',\n                data: itemTag,\n                resolve: function (data) {\n                    resolve({\n                        lowest: data.lowest,\n                        secondLowest: data.secondLowest\n                    })\n                },\n                reject: function (error) {\n                    apiErrorHandler(RequestType.GET_LOWEST_BIN, error, itemTag)\n                    reject(error)\n                }\n            })\n        })\n    }\n\n    let flipFilters = (tag: string): Promise<FilterOptions[]> => {\n        return new Promise((resolve, reject) => {\n            httpApi.sendLimitedCacheRequest(\n                {\n                    type: RequestType.FLIP_FILTERS,\n                    data: tag,\n                    resolve: function (data) {\n                        resolve(data.map(a => parseFilterOption(a)))\n                    },\n                    reject: function (error) {\n                        apiErrorHandler(RequestType.FLIP_FILTERS, error, tag)\n                        reject(error)\n                    }\n                },\n                1\n            )\n        })\n    }\n\n    let getBazaarTags = (): Promise<string[]> => {\n        return new Promise((resolve, reject) => {\n            httpApi.sendApiRequest({\n                type: RequestType.GET_BAZAAR_TAGS,\n                data: '',\n                resolve: function (data) {\n                    resolve(data)\n                },\n                reject: function (error) {\n                    apiErrorHandler(RequestType.GET_BAZAAR_TAGS, error, '')\n                    reject(error)\n                }\n            })\n        })\n    }\n\n    let getItemPriceSummary = (itemTag: string, filter: ItemFilter): Promise<ItemPriceSummary> => {\n        let getParams = new URLSearchParams(filter).toString()\n\n        return new Promise((resolve, reject) => {\n            httpApi.sendApiRequest({\n                type: RequestType.ITEM_PRICE_SUMMARY,\n                customRequestURL: `${getApiEndpoint()}/${RequestType.ITEM_PRICE_SUMMARY}/${itemTag}?${getParams}`,\n                data: '',\n                resolve: function (data) {\n                    returnSSRResponse ? resolve(data) : resolve(parseItemSummary(data))\n                },\n                reject: function (error) {\n                    apiErrorHandler(RequestType.ITEM_PRICE_SUMMARY, error, '')\n                    reject(error)\n                }\n            })\n        })\n    }\n\n    let setFlipSetting = (key: string, value: any): Promise<void> => {\n        if (sessionStorage.getItem('googleId') === null) {\n            return Promise.resolve()\n        }\n\n        storeUsedTagsInLocalStorage(getSettingsObject<FlipRestriction[]>(RESTRICTIONS_SETTINGS_KEY, []))\n\n        return new Promise((resolve, reject) => {\n            let data = {\n                key,\n                value: typeof value === 'object' ? JSON.stringify(value) : value.toString(),\n                changer: window.sessionStorage.getItem('sessionId')\n            }\n\n            websocketHelper.sendRequest({\n                type: RequestType.SET_FLIP_SETTING,\n                data: data,\n                resolve: () => {\n                    resolve()\n                },\n                reject: (error: any) => {\n                    apiErrorHandler(RequestType.SET_FLIP_SETTING, error, data)\n                }\n            })\n        })\n    }\n\n    let getKatFlips = (): Promise<KatFlip[]> => {\n        return new Promise((resolve, reject) => {\n            httpApi.sendApiRequest({\n                type: RequestType.GET_KAT_FLIPS,\n                data: '',\n                resolve: function (data) {\n                    returnSSRResponse ? resolve(data) : resolve(data.map(parseKatFlip))\n                },\n                reject: function (error) {\n                    apiErrorHandler(RequestType.GET_KAT_FLIPS, error, '')\n                }\n            })\n        })\n    }\n\n    let getTrackedFlipsForPlayer = (playerUUID: string, from?: Date, to?: Date): Promise<FlipTrackingResponse> => {\n        return new Promise((resolve, reject) => {\n            let params = new URLSearchParams()\n            if (from && to) {\n                params.set('start', from.toISOString())\n                params.set('end', to.toISOString())\n            }\n\n            let googleId = isClientSideRendering() ? sessionStorage.getItem('googleId') : null\n            let requestHeader = googleId ? { GoogleToken: googleId } : {}\n\n            httpApi.sendApiRequest({\n                customRequestURL: `${getApiEndpoint()}/flip/stats/player/${playerUUID}?${params.toString()}`,\n                type: RequestType.GET_TRACKED_FLIPS_FOR_PLAYER,\n                requestHeader: requestHeader,\n                data: playerUUID,\n                resolve: function (data) {\n                    returnSSRResponse ? resolve(data) : resolve(parseFlipTrackingResponse(data))\n                },\n                reject: function (error) {\n                    apiErrorHandler(RequestType.GET_TRACKED_FLIPS_FOR_PLAYER, error, playerUUID)\n                    reject(error)\n                }\n            })\n        })\n    }\n\n    let transferCoflCoins = (email: string | undefined, mcId: string | undefined, amount: number, reference: string): Promise<void> => {\n        return new Promise((resolve, reject) => {\n            let data = {\n                email: email,\n                mcId: mcId,\n                amount: amount,\n                reference: reference\n            }\n\n            websocketHelper.sendRequest({\n                type: RequestType.TRASFER_COFLCOINS,\n                data: data,\n                resolve: function () {\n                    resolve()\n                },\n                reject: function (error) {\n                    apiErrorHandler(RequestType.TRASFER_COFLCOINS, error, data)\n                    reject(error)\n                }\n            })\n        })\n    }\n\n    let getBazaarSnapshot = (itemTag: string, timestamp: string | number | Date): Promise<BazaarSnapshot> => {\n        return new Promise((resolve, reject) => {\n            let isoTimestamp = new Date(timestamp).toISOString()\n\n            httpApi.sendApiRequest({\n                type: RequestType.GET_BAZAAR_SNAPSHOT,\n                customRequestURL: getProperty('apiEndpoint') + `/bazaar/${itemTag}/snapshot${isoTimestamp ? `?timestamp=${isoTimestamp}` : ''}`,\n                data: '',\n                resolve: function (data) {\n                    if (!data) {\n                        resolve({\n                            item: {\n                                tag: ''\n                            },\n                            buyData: {\n                                moving: 0,\n                                orderCount: 0,\n                                price: 0,\n                                volume: 0\n                            },\n                            sellData: {\n                                moving: 0,\n                                orderCount: 0,\n                                price: 0,\n                                volume: 0\n                            },\n                            sellOrders: [],\n                            buyOrders: [],\n                            timeStamp: new Date()\n                        })\n                        return\n                    }\n                    resolve(parseBazaarSnapshot(data))\n                },\n                reject: function (error) {\n                    apiErrorHandler(RequestType.GET_BAZAAR_SNAPSHOT, error, { itemTag, timestamp: isoTimestamp })\n                    reject(error)\n                }\n            })\n        })\n    }\n\n    let getPrivacySettings = (): Promise<PrivacySettings> => {\n        return new Promise((resolve, reject) => {\n            let googleId = sessionStorage.getItem('googleId')\n            if (!googleId) {\n                toast.error('You need to be logged in to configure privacy settings.')\n                reject()\n                return\n            }\n\n            httpApi.sendApiRequest({\n                type: RequestType.GET_PRIVACY_SETTINGS,\n                data: '',\n                customRequestURL: `${getApiEndpoint()}/user/privacy`,\n                requestHeader: {\n                    GoogleToken: googleId\n                },\n                resolve: (data: any) => {\n                    resolve(parsePrivacySettings(data))\n                },\n                reject: (error: any) => {\n                    apiErrorHandler(RequestType.GET_PRIVACY_SETTINGS, error, '')\n                    reject(error)\n                }\n            })\n        })\n    }\n\n    let setPrivacySettings = (settings: PrivacySettings): Promise<void> => {\n        return new Promise((resolve, reject) => {\n            let googleId = sessionStorage.getItem('googleId')\n            if (!googleId) {\n                toast.error('You need to be logged in to save privacy settings.')\n                reject()\n                return\n            }\n\n            httpApi.sendApiRequest(\n                {\n                    type: RequestType.SET_PRIVACY_SETTINGS,\n                    data: '',\n                    requestMethod: 'POST',\n                    customRequestURL: `${getApiEndpoint()}/user/privacy`,\n                    requestHeader: {\n                        GoogleToken: googleId,\n                        'Content-Type': 'application/json'\n                    },\n                    resolve: () => {\n                        resolve()\n                    },\n                    reject: (error: any) => {\n                        apiErrorHandler(RequestType.SET_PRIVACY_SETTINGS, error, settings)\n                        reject(error)\n                    }\n                },\n                JSON.stringify(settings)\n            )\n        })\n    }\n\n    let checkRat = (hash: string): Promise<RatCheckingResponse> => {\n        return new Promise((resolve, reject) => {\n            httpApi.sendApiRequest({\n                type: RequestType.CHECK_FOR_RAT,\n                data: '',\n                customRequestURL: `https://isthisarat.com/api/signature/${hash}`,\n                resolve: (data: RatCheckingResponse) => {\n                    resolve(data)\n                },\n                reject: (error: any) => {\n                    apiErrorHandler(RequestType.CHECK_FOR_RAT, error, hash)\n                    reject(error)\n                }\n            })\n        })\n    }\n\n    let getPremiumProducts = (): Promise<PremiumProduct[]> => {\n        return new Promise((resolve, reject) => {\n            let googleId = sessionStorage.getItem('googleId')\n            if (!googleId) {\n                toast.error('You need to be logged in to load premium products.')\n                reject()\n                return\n            }\n\n            httpApi.sendApiRequest(\n                {\n                    type: RequestType.GET_PREMIUM_PRODUCTS,\n                    data: '',\n                    requestMethod: 'POST',\n                    customRequestURL: `${getApiEndpoint()}/premium/user/owns`,\n                    requestHeader: {\n                        GoogleToken: googleId,\n                        'Content-Type': 'application/json'\n                    },\n                    resolve: products => {\n                        localStorage.setItem(LAST_PREMIUM_PRODUCTS, JSON.stringify(products))\n                        resolve(parsePremiumProducts(products))\n                    },\n                    reject: (error: any) => {\n                        apiErrorHandler(RequestType.GET_PREMIUM_PRODUCTS, error, '')\n                        reject(error)\n                    }\n                },\n                JSON.stringify(PREMIUM_TYPES.map(type => type.productId))\n            )\n        })\n    }\n\n    /**\n     * Uses the last loaded premium products (if available) to instantly call the callback function\n     * The newest premium products are loaded after that and the callback is executed again\n     */\n    let refreshLoadPremiumProducts = (callback: (products: PremiumProduct[]) => void, onError: () => void) => {\n        let lastPremiumProducts = localStorage.getItem(LAST_PREMIUM_PRODUCTS)\n        if (lastPremiumProducts) {\n            try {\n                callback(parsePremiumProducts(JSON.parse(lastPremiumProducts)))\n            } catch {\n                callback([])\n            }\n        }\n        getPremiumProducts().then(prodcuts => {\n            callback(prodcuts)\n        }).catch(() => {\n            onError()\n        })\n    }\n\n    let unsubscribeAll = (): Promise<void> => {\n        return new Promise((resolve, reject) => {\n            websocketHelper.sendRequest({\n                type: RequestType.UNSUBSCRIBE_ALL,\n                data: '',\n                resolve: () => {\n                    resolve()\n                },\n                reject: (error: any) => {\n                    apiErrorHandler(RequestType.UNSUBSCRIBE_ALL, error, '')\n                    reject(error)\n                }\n            })\n        })\n    }\n\n    let getItemNames = (items: Item[]): Promise<{ [key: string]: string }> => {\n        return new Promise((resolve, reject) => {\n            httpApi.sendApiRequest(\n                {\n                    type: RequestType.GET_ITEM_NAMES,\n                    requestMethod: 'POST',\n                    requestHeader: {\n                        'Content-Type': 'application/json'\n                    },\n                    data: '',\n                    resolve: data => {\n                        resolve(data)\n                    },\n                    reject: (error: any) => {\n                        apiErrorHandler(RequestType.GET_ITEM_NAMES, error, items)\n                        reject(error)\n                    }\n                },\n                JSON.stringify(items.map(item => item.tag))\n            )\n        })\n    }\n\n    let checkFilter = (auction: AuctionDetails, filter: ItemFilter): Promise<boolean> => {\n        return new Promise((resolve, reject) => {\n            httpApi.sendApiRequest(\n                {\n                    type: RequestType.CHECK_FILTER,\n                    requestMethod: 'POST',\n                    customRequestURL: `${getApiEndpoint()}/Filter`,\n                    requestHeader: {\n                        'Content-Type': 'application/json'\n                    },\n                    data: '',\n                    resolve: data => {\n                        resolve(data)\n                    },\n                    reject: (error: any) => {\n                        apiErrorHandler(RequestType.CHECK_FILTER, error, { auction, filter })\n                        reject(error)\n                    }\n                },\n                JSON.stringify({ filters: filter, auction: auction })\n            )\n        })\n    }\n\n    let getRelatedItems = (tag: string): Promise<Item[]> => {\n        return new Promise((resolve, reject) => {\n            httpApi.sendApiRequest({\n                type: RequestType.RELATED_ITEMS,\n                customRequestURL: `${getApiEndpoint()}/item/${tag}/similar`,\n                data: '',\n                resolve: data => {\n                    resolve(data.map(item => parseItem(item)))\n                },\n                reject: (error: any) => {\n                    apiErrorHandler(RequestType.RELATED_ITEMS, error, tag)\n                    reject(error)\n                }\n            })\n        })\n    }\n\n    let getOwnerHistory = (uid: string): Promise<OwnerHistory[]> => {\n        return new Promise((resolve, reject) => {\n            httpApi.sendApiRequest({\n                type: RequestType.OWNER_HISOTRY,\n                customRequestURL: `${getApiEndpoint()}/auctions/uid/${uid}/sold`,\n                data: '',\n                resolve: data => {\n                    resolve(data.map(parseOwnerHistory))\n                },\n                reject: (error: any) => {\n                    apiErrorHandler(RequestType.OWNER_HISOTRY, error, uid)\n                    reject(error)\n                }\n            })\n        })\n    }\n\n    let getMayorData = (start: Date, end: Date): Promise<MayorData[]> => {\n        let params = new URLSearchParams()\n        params.set('from', start.toISOString())\n        params.set('to', end.toISOString())\n\n        return new Promise((resolve, reject) => {\n            httpApi.sendApiRequest({\n                type: RequestType.MAYOR_DATA,\n                customRequestURL: `${getApiEndpoint()}/mayor?${params.toString()}`,\n                data: '',\n                resolve: data => {\n                    resolve(data.map(parseMayorData))\n                },\n                reject: (error: any) => {\n                    // temporarly don't show mayor errors\n                    //apiErrorHandler(RequestType.MAYOR_DATA, error, { start, end })\n                    reject(error)\n                }\n            })\n        })\n    }\n\n    let getTransactions = (): Promise<Transaction[]> => {\n        return new Promise((resolve, reject) => {\n            let googleId = sessionStorage.getItem('googleId')\n            if (!googleId) {\n                toast.error('You need to be logged in to load transactions.')\n                reject()\n                return\n            }\n\n            httpApi.sendApiRequest({\n                type: RequestType.GET_TRANSACTIONS,\n                requestHeader: {\n                    GoogleToken: googleId,\n                    'Content-Type': 'application/json'\n                },\n                customRequestURL: `${getApiEndpoint()}/premium/transactions`,\n                data: '',\n                resolve: (data: any) => {\n                    if (!data) {\n                        return []\n                    }\n                    resolve(data.map(parseTransaction))\n                },\n                reject: (error: any) => {\n                    apiErrorHandler(RequestType.STRIPE_PAYMENT_SESSION, error, '')\n                    reject(error)\n                }\n            })\n        })\n    }\n\n    let getPlayerInventory = (): Promise<InventoryData[]> => {\n        return new Promise((resolve, reject) => {\n            let googleId = sessionStorage.getItem('googleId')\n            if (!googleId) {\n                toast.error('You need to be logged in to load the inventory.')\n                reject()\n                return\n            }\n            httpApi.sendApiRequest({\n                type: RequestType.INVENTORY_DATA,\n                customRequestURL: `${getApiEndpoint()}/inventory`,\n                requestHeader: {\n                    GoogleToken: googleId,\n                    'Content-Type': 'application/json'\n                },\n                data: '',\n                resolve: data => {\n                    resolve(data ? (data as TradeObject[]).slice(Math.max(data.length - 36, 0)).map(parseInventoryData) : [])\n                },\n                reject: (error: any) => {\n                    apiErrorHandler(RequestType.INVENTORY_DATA, error)\n                    reject(error)\n                }\n            })\n        })\n    }\n\n    let createTradeOffer = (playerUUID: string, offer?: InventoryData, wantedItems: WantedItem[] = [], offeredCoins?: number): Promise<void> => {\n        return new Promise((resolve, reject) => {\n            let googleId = sessionStorage.getItem('googleId')\n            if (!googleId) {\n                toast.error('You need to be logged in to load the inventory.')\n                reject()\n                return\n            }\n            httpApi.sendApiRequest(\n                {\n                    type: RequestType.CREATE_TRADE_OFFER,\n                    requestMethod: 'POST',\n                    customRequestURL: `${getApiEndpoint()}/trades`,\n                    requestHeader: {\n                        GoogleToken: googleId,\n                        'Content-Type': 'application/json'\n                    },\n                    data: '',\n                    resolve: () => {\n                        resolve()\n                    },\n                    reject: (error: any) => {\n                        apiErrorHandler(RequestType.CREATE_TRADE_OFFER, error)\n                        reject(error)\n                    }\n                },\n                JSON.stringify([\n                    {\n                        playerUuid: playerUUID,\n                        item: offer,\n                        coins: offeredCoins,\n                        wantedItems: wantedItems\n                    }\n                ])\n            )\n        })\n    }\n\n    let deleteTradeOffer = (tradeId: string): Promise<void> => {\n        return new Promise((resolve, reject) => {\n            let googleId = sessionStorage.getItem('googleId')\n            if (!googleId) {\n                toast.error('You need to be logged in to delete your trades.')\n                reject()\n                return\n            }\n            httpApi.sendApiRequest({\n                type: RequestType.DELETE_TRADE_OFFER,\n                requestMethod: 'DELETE',\n                customRequestURL: `${getApiEndpoint()}/trades/${tradeId}`,\n                requestHeader: {\n                    GoogleToken: googleId,\n                    'Content-Type': 'application/json'\n                },\n                data: '',\n                resolve: () => {\n                    resolve()\n                },\n                reject: (error: any) => {\n                    apiErrorHandler(RequestType.DELETE_TRADE_OFFER, error, tradeId)\n                    reject(error)\n                }\n            })\n        })\n    }\n\n    let getTradeOffers = (onlyOwn: boolean, filter?: ItemFilter): Promise<TradeObject[]> => {\n        return new Promise((resolve, reject) => {\n            let googleId = sessionStorage.getItem('googleId')\n            if (!googleId) {\n                toast.error('You need to be logged in to use the trade feature.')\n                reject()\n                return\n            }\n            let params = new URLSearchParams()\n            if (filter) {\n                params = new URLSearchParams({\n                    filters: JSON.stringify(filter)\n                })\n            }\n\n            httpApi.sendApiRequest({\n                type: RequestType.GET_TRADES,\n                customRequestURL: `${getApiEndpoint()}/trades${onlyOwn ? '/own' : ''}?${filter ? `${params.toString()}` : ''}`,\n                data: '',\n                requestHeader: {\n                    GoogleToken: googleId,\n                    'Content-Type': 'application/json'\n                },\n                resolve: data => {\n                    resolve(data ? data.map(parseTradeObject) : [])\n                },\n                reject: (error: any) => {\n                    apiErrorHandler(RequestType.GET_TRADES, error)\n                    reject(error)\n                }\n            })\n        })\n    }\n\n    let getNotificationTargets = (): Promise<NotificationTarget[]> => {\n        return new Promise((resolve, reject) => {\n            let googleId = sessionStorage.getItem('googleId')\n            if (!googleId) {\n                toast.error('You need to be logged in to load notification targets')\n                reject()\n                return\n            }\n\n            httpApi.sendApiRequest({\n                type: RequestType.GET_NOTIFICATION_TARGETS,\n                customRequestURL: `${getApiEndpoint()}/notifications/targets`,\n                data: '',\n                requestHeader: {\n                    GoogleToken: googleId,\n                    'Content-Type': 'application/json'\n                },\n                resolve: data => {\n                    resolve(data ? data : [])\n                },\n                reject: (error: any) => {\n                    apiErrorHandler(RequestType.GET_NOTIFICATION_TARGETS, error)\n                    reject(error)\n                }\n            })\n        })\n    }\n\n    let addNotificationTarget = (target: NotificationTarget): Promise<NotificationTarget> => {\n        return new Promise((resolve, reject) => {\n            let googleId = sessionStorage.getItem('googleId')\n            if (!googleId) {\n                toast.error('You need to be logged in to add a notification targets')\n                reject()\n                return\n            }\n\n            httpApi.sendApiRequest(\n                {\n                    type: RequestType.ADD_NOTIFICATION_TARGETS,\n                    customRequestURL: `${getApiEndpoint()}/notifications/targets`,\n                    requestMethod: 'POST',\n                    data: '',\n                    requestHeader: {\n                        GoogleToken: googleId,\n                        'Content-Type': 'application/json'\n                    },\n                    resolve: data => {\n                        resolve(data ? data : [])\n                    },\n                    reject: (error: any) => {\n                        apiErrorHandler(RequestType.ADD_NOTIFICATION_TARGETS, error)\n                        reject(error)\n                    }\n                },\n                JSON.stringify(target)\n            )\n        })\n    }\n\n    let deleteNotificationTarget = (target: NotificationTarget): Promise<void> => {\n        return new Promise((resolve, reject) => {\n            let googleId = sessionStorage.getItem('googleId')\n            if (!googleId) {\n                toast.error('You need to be logged in to delete a notification targets')\n                reject()\n                return\n            }\n\n            httpApi.sendApiRequest(\n                {\n                    type: RequestType.ADD_NOTIFICATION_TARGETS,\n                    customRequestURL: `${getApiEndpoint()}/notifications/targets`,\n                    requestMethod: 'DELETE',\n                    data: '',\n                    requestHeader: {\n                        GoogleToken: googleId,\n                        'Content-Type': 'application/json'\n                    },\n                    resolve: data => {\n                        resolve(data ? data : [])\n                    },\n                    reject: (error: any) => {\n                        apiErrorHandler(RequestType.ADD_NOTIFICATION_TARGETS, error)\n                        reject(error)\n                    }\n                },\n                JSON.stringify(target)\n            )\n        })\n    }\n\n    let updateNotificationTarget = (target: NotificationTarget): Promise<void> => {\n        return new Promise((resolve, reject) => {\n            let googleId = sessionStorage.getItem('googleId')\n            if (!googleId) {\n                toast.error('You need to be logged in to update a notification targets')\n                reject()\n                return\n            }\n\n            httpApi.sendApiRequest(\n                {\n                    type: RequestType.UPDATE_NOTIFICATION_TARGET,\n                    customRequestURL: `${getApiEndpoint()}/notifications/targets`,\n                    requestMethod: 'PUT',\n                    data: '',\n                    requestHeader: {\n                        GoogleToken: googleId,\n                        'Content-Type': 'application/json'\n                    },\n                    resolve: data => {\n                        resolve(data ? data : [])\n                    },\n                    reject: (error: any) => {\n                        apiErrorHandler(RequestType.ADD_NOTIFICATION_TARGETS, error)\n                        reject(error)\n                    }\n                },\n                JSON.stringify(target)\n            )\n        })\n    }\n\n    let sendTestNotification = (target: NotificationTarget): Promise<void> => {\n        return new Promise((resolve, reject) => {\n            let googleId = sessionStorage.getItem('googleId')\n            if (!googleId) {\n                toast.error('You need to be logged in to send a test notification')\n                reject()\n                return\n            }\n\n            httpApi.sendApiRequest(\n                {\n                    type: RequestType.SEND_TEST_NOTIFICATION,\n                    customRequestURL: `${getApiEndpoint()}/notifications/targets/test`,\n                    data: '',\n                    requestMethod: 'POST',\n                    requestHeader: {\n                        GoogleToken: googleId,\n                        'Content-Type': 'application/json'\n                    },\n                    resolve: () => {\n                        resolve()\n                    },\n                    reject: (error: any) => {\n                        apiErrorHandler(RequestType.SEND_TEST_NOTIFICATION, error)\n                        reject(error)\n                    }\n                },\n                JSON.stringify(target)\n            )\n        })\n    }\n\n    let getNotificationSubscriptions = (): Promise<NotificationSubscription[]> => {\n        return new Promise((resolve, reject) => {\n            let googleId = sessionStorage.getItem('googleId')\n            if (!googleId) {\n                toast.error('You need to be logged in to load notification subscriptions')\n                reject()\n                return\n            }\n\n            httpApi.sendApiRequest({\n                type: RequestType.GET_NOTIFICATION_SUBSCRIPTION,\n                customRequestURL: `${getApiEndpoint()}/notifications/subscriptions`,\n                requestMethod: 'GET',\n                data: '',\n                requestHeader: {\n                    GoogleToken: googleId,\n                    'Content-Type': 'application/json'\n                },\n                resolve: data => {\n                    resolve(data ? data : [])\n                },\n                reject: (error: any) => {\n                    apiErrorHandler(RequestType.GET_NOTIFICATION_SUBSCRIPTION, error)\n                    reject(error)\n                }\n            })\n        })\n    }\n\n    let createNotificationSubscription = (subscription: NotificationSubscription): Promise<NotificationSubscription> => {\n        return new Promise((resolve, reject) => {\n            let googleId = sessionStorage.getItem('googleId')\n            if (!googleId) {\n                toast.error('You need to be logged in to create a notification subscription')\n                reject()\n                return\n            }\n\n            httpApi.sendApiRequest(\n                {\n                    type: RequestType.ADD_NOTIFICATION_SUBSCRIPTION,\n                    customRequestURL: `${getApiEndpoint()}/notifications/subscriptions`,\n                    requestMethod: 'POST',\n                    data: '',\n                    requestHeader: {\n                        GoogleToken: googleId,\n                        'Content-Type': 'application/json'\n                    },\n                    resolve: data => {\n                        resolve(data)\n                    },\n                    reject: (error: any) => {\n                        apiErrorHandler(RequestType.ADD_NOTIFICATION_SUBSCRIPTION, error)\n                        reject(error)\n                    }\n                },\n                JSON.stringify(subscription)\n            )\n        })\n    }\n\n    let deleteNotificationSubscription = (subscription: NotificationSubscription): Promise<void> => {\n        return new Promise((resolve, reject) => {\n            let googleId = sessionStorage.getItem('googleId')\n            if (!googleId) {\n                toast.error('You need to be logged in to delete a notification subscription')\n                reject()\n                return\n            }\n\n            httpApi.sendApiRequest(\n                {\n                    type: RequestType.DELETE_NOTIFICATION_SUBSCRIPTION,\n                    customRequestURL: `${getApiEndpoint()}/notifications/subscriptions`,\n                    requestMethod: 'DELETE',\n                    data: '',\n                    requestHeader: {\n                        GoogleToken: googleId,\n                        'Content-Type': 'application/json'\n                    },\n                    resolve: () => {\n                        resolve()\n                    },\n                    reject: (error: any) => {\n                        apiErrorHandler(RequestType.DELETE_NOTIFICATION_SUBSCRIPTION, error)\n                        reject(error)\n                    }\n                },\n                JSON.stringify(subscription)\n            )\n        })\n    }\n\n    let getPublishedConfigs = (): Promise<string[]> => {\n        return new Promise((resolve, reject) => {\n            websocketHelper.sendRequest({\n                type: RequestType.GET_PUBLISHED_CONFIGS,\n                data: '',\n                resolve: (configs: any) => {\n                    resolve(configs)\n                },\n                reject: (error: any) => {\n                    apiErrorHandler(RequestType.GET_PUBLISHED_CONFIGS, error, '')\n                    reject(error)\n                }\n            })\n        })\n    }\n\n    let loadConfig = (configName: string): Promise<void> => {\n        return new Promise((resolve, reject) => {\n            websocketHelper.sendRequest({\n                type: RequestType.LOAD_CONFIG,\n                data: { configName },\n                resolve: () => {\n                    resolve()\n                },\n                reject: (error: any) => {\n                    apiErrorHandler(RequestType.LOAD_CONFIG, error, configName)\n                    reject(error)\n                }\n            })\n        })\n    }\n\n    let updateConfig = (configName: string, updateNotes: string = ''): Promise<void> => {\n        return new Promise((resolve, reject) => {\n            websocketHelper.sendRequest({\n                type: RequestType.UPDATE_CONFIG,\n                data: { configName, updateNotes },\n                resolve: (configs: any) => {\n                    resolve(configs)\n                },\n                reject: (error: any) => {\n                    apiErrorHandler(RequestType.UPDATE_CONFIG, error, '')\n                    reject(error)\n                }\n            })\n        })\n    }\n\n    let requestArchivedAuctions = (itemTag: string, itemFilter?: ItemFilter): Promise<ArchivedAuctionResponse> => {\n        return new Promise((resolve, reject) => {\n            let googleId = sessionStorage.getItem('googleId')\n            if (!googleId) {\n                toast.error('You need to be logged in to request archived auctions.')\n                reject()\n                return\n            }\n\n            let params = new URLSearchParams()\n            if (itemFilter && Object.keys(itemFilter).length > 0) {\n                params = new URLSearchParams(itemFilter)\n            }\n\n            httpApi.sendApiRequest({\n                type: RequestType.ARCHIVED_AUCTIONS,\n                customRequestURL: `${getApiEndpoint()}/auctions/tag/${itemTag}/archive/overview?${params.toString()}`,\n                requestMethod: 'GET',\n                data: '',\n                requestHeader: {\n                    GoogleToken: googleId,\n                    'Content-Type': 'application/json'\n                },\n                resolve: result => {\n                    resolve(parseArchivedAuctions(result))\n                },\n                reject: (error: any) => {\n                    apiErrorHandler(RequestType.ARCHIVED_AUCTIONS, error, { itemTag, itemFilter })\n                    reject(error)\n                }\n            })\n        })\n    }\n\n    let exportArchivedAuctionsData = (itemTag: string, itemFilter: ItemFilter, discordWebhookUrl: string, flags: string[]): Promise<void> => {\n        return new Promise((resolve, reject) => {\n            let googleId = sessionStorage.getItem('googleId')\n            if (!googleId) {\n                toast.error('You need to be logged in to export archived auctions.')\n                reject()\n                return\n            }\n\n            httpApi.sendApiRequest(\n                {\n                    type: RequestType.EXPORT_ARCHIVED_AUCTIONS,\n                    customRequestURL: `${getApiEndpoint()}/auctions/tag/${itemTag}/archive/export`,\n                    requestMethod: 'POST',\n                    data: '',\n                    requestHeader: {\n                        GoogleToken: googleId,\n                        'Content-Type': 'application/json'\n                    },\n                    resolve: () => {\n                        resolve()\n                    },\n                    reject: (error: any) => {\n                        apiErrorHandler(RequestType.EXPORT_ARCHIVED_AUCTIONS, error, { itemTag, itemFilter })\n                        reject(error)\n                    }\n                },\n                JSON.stringify({\n                    filters: itemFilter,\n                    discordWebhookUrl: discordWebhookUrl,\n                    flags: flags.length > 0 ? flags.toString() : undefined\n                })\n            )\n        })\n    }\n\n    let getLinkvertiseLink = (): Promise<string> => {\n        return new Promise((resolve, reject) => {\n            let googleId = sessionStorage.getItem('googleId')\n            if (!googleId) {\n                toast.error('You need to be logged in to do linkvertise tasks.')\n                reject()\n                return\n            }\n\n            httpApi.sendApiRequest({\n                type: RequestType.GET_LINKVERTISE_LINK,\n                customRequestURL: `${getApiEndpoint()}/linkvertise`,\n                requestMethod: 'GET',\n                data: '',\n                requestHeader: {\n                    GoogleToken: googleId,\n                    'Content-Type': 'application/json'\n                },\n                resolve: link => {\n                    resolve(link)\n                },\n                reject: (error: any) => {\n                    apiErrorHandler(RequestType.GET_LINKVERTISE_LINK, error)\n                    reject(error)\n                }\n            })\n        })\n    }\n\n    let purchasePremiumSubscription = (productSlug: string, googleToken: string): Promise<PaymentResponse> => {\n        return new Promise((resolve, reject) => {\n            httpApi.sendApiRequest({\n                type: RequestType.PURCHASE_PREMIUM_SUBSCRIPTION,\n                customRequestURL: `${getApiEndpoint()}/premium/subscription/${productSlug}`,\n                requestMethod: 'POST',\n                data: '',\n                requestHeader: {\n                    GoogleToken: googleToken,\n                    'Content-Type': 'application/json'\n                },\n                resolve: data => {\n                    resolve(parsePaymentResponse(data))\n                },\n                reject: (error: any) => {\n                    apiErrorHandler(RequestType.PURCHASE_PREMIUM_SUBSCRIPTION, error)\n                    reject(error)\n                }\n            })\n        })\n    }\n\n    let getPremiumSubscriptions = (): Promise<PremiumSubscription[]> => {\n        return new Promise((resolve, reject) => {\n            let googleId = sessionStorage.getItem('googleId')\n            if (!googleId) {\n                toast.error('You need to be logged in to create a premium subscription.')\n                reject()\n                return\n            }\n\n            httpApi.sendApiRequest({\n                type: RequestType.CREATE_PREMIUM_SUBSCRIPTION,\n                customRequestURL: `${getApiEndpoint()}/premium/subscription`,\n                requestMethod: 'GET',\n                data: '',\n                requestHeader: {\n                    GoogleToken: googleId,\n                    'Content-Type': 'application/json'\n                },\n                resolve: (subscriptions: any = []) => {\n                    resolve(subscriptions.map(parsePremiumSubscription))\n                },\n                reject: (error: any) => {\n                    apiErrorHandler(RequestType.CREATE_PREMIUM_SUBSCRIPTION, error)\n                    reject(error)\n                }\n            })\n        })\n    }\n\n    let cancelPremiumSubscription = (id: string): Promise<void> => {\n        return new Promise((resolve, reject) => {\n            let googleId = sessionStorage.getItem('googleId')\n            if (!googleId) {\n                toast.error('You need to be logged in to cancel a premium subscription.')\n                reject()\n                return\n            }\n\n            httpApi.sendApiRequest({\n                type: RequestType.DELETE_PREMIUM_SUBSCRIPTION,\n                customRequestURL: `${getApiEndpoint()}/premium/subscription/${id}`,\n                requestMethod: 'DELETE',\n                data: '',\n                requestHeader: {\n                    GoogleToken: googleId,\n                    'Content-Type': 'application/json'\n                },\n                resolve: () => {\n                    resolve()\n                },\n                reject: (error: any) => {\n                    apiErrorHandler(RequestType.DELETE_PREMIUM_SUBSCRIPTION, error)\n                    reject(error)\n                }\n            })\n        })\n    }\n\n    let getCraftInstructions = (itemTag: string): Promise<CraftingInstructions> => {\n        return new Promise((resolve, reject) => {\n            httpApi.sendApiRequest({\n                type: RequestType.GET_CRAFTING_INSTRUCTIONS,\n                customRequestURL: `${getApiEndpoint()}/craft/${itemTag}/instructions`,\n                data: '',\n                resolve: data => {\n                    resolve(parseCraftingInstructions(data))\n                },\n                reject: (error: any) => {\n                    apiErrorHandler(RequestType.GET_CRAFTING_INSTRUCTIONS, error, itemTag)\n                    reject(error)\n                }\n            })\n        })\n    }\n\n    return {\n        search,\n        trackSearch,\n        getItemDetails,\n        getItemPrices,\n        getAuctions,\n        getBids,\n        getEnchantments,\n        getAuctionDetails,\n        getItemImageUrl,\n        getPlayerName,\n        getPlayerNames,\n        setConnectionId,\n        getVersion,\n        subscribe,\n        unsubscribe,\n        getNotificationListener,\n        loginWithToken,\n        stripePurchase,\n        setToken,\n        getRecentAuctions,\n        getFlips,\n        subscribeFlips,\n        getFilters,\n        getNewPlayers,\n        getNewItems,\n        getPopularSearches,\n        getEndedAuctions,\n        getNewAuctions,\n        getFlipBasedAuctions,\n        paypalPurchase,\n        lemonsqueezyPurchase,\n        getRefInfo,\n        setRef,\n        getActiveAuctions,\n        connectMinecraftAccount,\n        getAccountInfo,\n        unsubscribeFlips,\n        itemSearch,\n        authenticateModConnection,\n        getFlipUpdateTime,\n        playerSearch,\n        getProfitableCrafts,\n        getLowSupplyItems,\n        sendFeedback,\n        triggerPlayerNameCheck,\n        getPlayerProfiles,\n        getCraftingRecipe,\n        getLowestBin,\n        flipFilters,\n        getBazaarTags,\n        getPreloadFlips,\n        getItemPriceSummary,\n        purchaseWithCoflcoins,\n        subscribeCoflCoinChange,\n        getCoflcoinBalance,\n        setFlipSetting,\n        getKatFlips,\n        getTrackedFlipsForPlayer,\n        transferCoflCoins,\n        getBazaarSnapshot,\n        getBazaarPrices,\n        getBazaarPricesByRange,\n        subscribeFlipsAnonym,\n        getPrivacySettings,\n        setPrivacySettings,\n        checkRat,\n        getPremiumProducts,\n        unsubscribeAll,\n        getItemNames,\n        checkFilter,\n        refreshLoadPremiumProducts,\n        getRelatedItems,\n        getOwnerHistory,\n        getMayorData,\n        getPlayerInventory,\n        createTradeOffer,\n        getTradeOffers,\n        deleteTradeOffer,\n        getTransactions,\n        getNotificationTargets,\n        addNotificationTarget,\n        deleteNotificationTarget,\n        updateNotificationTarget,\n        sendTestNotification,\n        createNotificationSubscription,\n        deleteNotificationSubscription,\n        getNotificationSubscriptions,\n        getPublishedConfigs,\n        loadConfig,\n        updateConfig,\n        requestArchivedAuctions,\n        exportArchivedAuctionsData,\n        getLinkvertiseLink,\n        getPremiumSubscriptions,\n        cancelPremiumSubscription,\n        purchasePremiumSubscription,\n        getCraftInstructions\n    }\n}\n\nlet api = initAPI()\n\nexport default api\n"], "names": [], "mappings": ";;;;AAkEkE;;AAlElE;AACA;AACA;AACA;AACA;AACA;AACA;AAsCA;AACA;AACA;AAWA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;AAEA,SAAS;IACL,OAAO,CAAA,GAAA,qHAAA,CAAA,wBAAqB,AAAD,MAAM,CAAA,GAAA,4HAAA,CAAA,cAAW,AAAD,EAAE,iBAAiB,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,YAAY,IAAI,CAAA,GAAA,4HAAA,CAAA,cAAW,AAAD,EAAE;AAC1G;AAEO,SAAS,QAAQ,oBAA6B,KAAK;IACtD,IAAI;IACJ,IAAI,CAAA,GAAA,qHAAA,CAAA,wBAAqB,AAAD,KAAK;QACzB,UAAU,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;IAC3B,OAAO;QACH,IAAI,kBAAkB,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,gBAAgB;QAClD,IAAI,cAAc;QAClB,UAAU,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD,EAAE,iBAAiB;IAC9C;IAEA,WAAW;QACP,IAAI,CAAA,GAAA,qHAAA,CAAA,wBAAqB,AAAD,KAAK;YACzB,uHAAA,CAAA,UAAU,CAAC,kBAAkB;QACjC;IACJ,GAAG;IAEH,IAAI,kBAAkB,CAAC,aAA0B,OAAY,cAAmB,IAAI;QAChF,IAAI,CAAC,SAAS,CAAC,MAAM,OAAO,EAAE;YAC1B;QACJ;QACA,IAAI,CAAA,GAAA,qHAAA,CAAA,wBAAqB,AAAD,KAAK;YACzB,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,OAAO,EAAE;gBACvB,SAAS;oBACL,IAAI,MAAM,OAAO,IAAI,CAAA,GAAA,2HAAA,CAAA,kBAAe,AAAD,KAAK;wBACpC,CAAA,GAAA,2HAAA,CAAA,mBAAgB,AAAD,EAAE,MAAM,OAAO;wBAC9B,sJAAA,CAAA,QAAK,CAAC,OAAO,eACT,6LAAC;;gCAAK;gCAC8E;8CAChF,6LAAC;oCAAE,QAAO;oCAAS,KAAI;oCAAa,MAAK;8CAAgC;;;;;;gCAErE;;;;;;;oBAIhB;gBACJ;YACJ;QACJ;QACA,QAAQ,GAAG,CAAC,kBAAkB;QAC9B,QAAQ,GAAG,CAAC,mBAAmB,MAAM,OAAO;QAC5C,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC;IAChB;IAEA,IAAI,SAAS,CAAC;QACV,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,QAAQ,cAAc,CAAC;gBACnB,MAAM,wHAAA,CAAA,cAAW,CAAC,MAAM;gBACxB,MAAM;gBACN,SAAS,CAAC;oBACN,QACI,CAAC,QACK,EAAE,GACF,MAAM,GAAG,CAAC,CAAC;wBACP,OAAO,CAAA,GAAA,wIAAA,CAAA,wBAAqB,AAAD,EAAE;oBACjC;gBAEd;gBACA,QAAQ,CAAC;oBACL,gBAAgB,wHAAA,CAAA,cAAW,CAAC,MAAM,EAAE,OAAO;oBAC3C,OAAO;gBACX;YACJ;QACJ;IACJ;IAEA,IAAI,kBAAkB,CAAC;QACnB,IAAI,OAAO,CAAA,GAAA,0HAAA,CAAA,aAAU,AAAD,EAAE,0HAAA,CAAA,iBAAc,EAAE;QAEtC,IAAI,UAAU,KAAK,OAAO,IAAI,AAAC,KAAa,IAAI;QAChD,IAAI,SAAS;YACT,+EAA+E;YAC/E,6DAA6D;YAC7D,IAAI,SAAS,aAAa,CAAC,QAAQ,QAAQ,CAAC,eAAe,QAAQ,QAAQ,CAAC,gCAAgC;gBACxG,OAAO,UAAU;YACrB;YACA,OAAO;QACX;QAEA,6EAA6E;QAC7E,IAAI,IAAI,CAAC,oCAAoC,EAAE,KAAK,GAAG,GAAG,SAAS,YAAY,aAAa,IAAI;QAChG,OAAO;IACX;IAEA,IAAI,iBAAiB,CAAC;QAClB,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,QAAQ,cAAc,CAAC;gBACnB,MAAM,wHAAA,CAAA,cAAW,CAAC,YAAY;gBAC9B,kBAAkB,GAAG,iBAAiB,MAAM,EAAE,QAAQ,QAAQ,CAAC;gBAC/D,MAAM;gBACN,SAAS,CAAC;oBACN,oBAAoB,QAAQ,QAAQ,QAAQ,CAAA,GAAA,wIAAA,CAAA,YAAS,AAAD,EAAE;gBAC1D;gBACA,QAAQ,CAAC;oBACL,gBAAgB,wHAAA,CAAA,cAAW,CAAC,YAAY,EAAE,OAAO;oBACjD,OAAO;gBACX;YACJ;QACJ;IACJ;IAEA,IAAI,gBAAgB,CAAC,SAAiB,WAAsB;QACxD,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,IAAI,SAAS,IAAI;YACjB,IAAI,cAAc,OAAO,IAAI,CAAC,YAAY,MAAM,GAAG,GAAG;gBAClD,SAAS,IAAI,gBAAgB;YACjC;YAEA,QAAQ,cAAc,CAAC;gBACnB,MAAM,wHAAA,CAAA,cAAW,CAAC,WAAW;gBAC7B,MAAM;gBACN,kBAAkB,mBAAmB,CAAC,YAAY,EAAE,QAAQ,SAAS,EAAE,UAAU,CAAC,EAAE,OAAO,QAAQ,IAAI;gBACvG,eAAe;gBACf,eAAe;oBACX,gBAAgB;gBACpB;gBACA,SAAS,CAAC;oBACN,IAAI,mBAAmB;wBACnB,QAAQ;wBACR;oBACJ;oBACA,QAAQ,OAAO,KAAK,GAAG,CAAC,wIAAA,CAAA,iBAAc,EAAE,IAAI,CAAC,CAAC,GAAc,IAAiB,EAAE,IAAI,CAAC,OAAO,KAAK,EAAE,IAAI,CAAC,OAAO,MAAM,EAAE;gBAC1H;gBACA,QAAQ,CAAC;oBACL,gBAAgB,wHAAA,CAAA,cAAW,CAAC,WAAW,EAAE,OAAO;wBAC5C;wBACA;wBACA;oBACJ;oBACA,OAAO;gBACX;YACJ;QACJ;IACJ;IAEA,IAAI,kBAAkB,CAAC,SAAiB;QACpC,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,QAAQ,cAAc,CAAC;gBACnB,MAAM,wHAAA,CAAA,cAAW,CAAC,aAAa;gBAC/B,MAAM;gBACN,kBAAkB,CAAA,GAAA,4HAAA,CAAA,cAAW,AAAD,EAAE,iBAAiB,CAAC,QAAQ,EAAE,QAAQ,SAAS,EAAE,WAAW;gBACxF,eAAe;gBACf,SAAS,CAAC;oBACN,QAAQ,OAAO,KAAK,GAAG,CAAC,wIAAA,CAAA,mBAAgB,EAAE,IAAI,CAAC,CAAC,GAAgB,IAAmB,EAAE,SAAS,CAAC,OAAO,KAAK,EAAE,SAAS,CAAC,OAAO,MAAM,EAAE;gBAC1I;gBACA,QAAQ,CAAC;oBACL,gBAAgB,wHAAA,CAAA,cAAW,CAAC,aAAa,EAAE,OAAO;wBAC9C;wBACA;oBACJ;oBACA,OAAO;gBACX;YACJ;QACJ;IACJ;IAEA,IAAI,yBAAyB,CAAC,SAAiB,WAAmC;QAC9E,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,IAAI,eAAe,IAAI,KAAK,WAAW,WAAW;YAClD,IAAI,aAAa,IAAI,KAAK,SAAS,WAAW;YAE9C,QAAQ,cAAc,CAAC;gBACnB,MAAM,wHAAA,CAAA,cAAW,CAAC,aAAa;gBAC/B,MAAM;gBACN,kBAAkB,CAAA,GAAA,4HAAA,CAAA,cAAW,AAAD,EAAE,iBAAiB,CAAC,QAAQ,EAAE,QAAQ,gBAAgB,EAAE,aAAa,KAAK,EAAE,YAAY;gBACpH,eAAe;gBACf,SAAS,CAAC;oBACN,OAAO,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,aAAa,EAAE,GAAG,KAAK;oBAE1D,IAAI,UAAU;2BAAI;qBAAK,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,GAAG,GAAG,EAAE,GAAG;oBACpD,IAAI,WAAW;2BAAI;qBAAK,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,IAAI,GAAG,EAAE,IAAI;oBAEvD,IAAI,YAAY,QAAQ,MAAM,GAAG,IAAI,OAAO,CAAC,KAAK,KAAK,CAAC,QAAQ,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG;oBACnF,IAAI,aAAa,SAAS,MAAM,GAAG,IAAI,QAAQ,CAAC,KAAK,KAAK,CAAC,SAAS,MAAM,GAAG,GAAG,CAAC,IAAI,GAAG;oBAExF,IAAI,aAA4B,KAC3B,GAAG,CAAC,wIAAA,CAAA,mBAAgB,EACpB,IAAI,CAAC,CAAC,GAAgB,IAAmB,EAAE,SAAS,CAAC,OAAO,KAAK,EAAE,SAAS,CAAC,OAAO;oBACzF,IAAI,aAAa;oBACjB,QACI,WAAW,MAAM,CACb,CAAA,IACI,EAAE,OAAO,CAAC,GAAG,GAAG,YAAY,cAC5B,EAAE,QAAQ,CAAC,GAAG,GAAG,aAAa,cAC9B,EAAE,OAAO,CAAC,GAAG,GAAG,YAAY,cAC5B,EAAE,QAAQ,CAAC,GAAG,GAAG,aAAa;gBAG9C;gBACA,QAAQ,CAAC;oBACL,gBAAgB,wHAAA,CAAA,cAAW,CAAC,aAAa,EAAE,OAAO;wBAC9C;wBACA;wBACA;oBACJ;oBACA,OAAO;gBACX;YACJ;QACJ;IACJ;IAEA,IAAI,cAAc,CAAC,MAAc,OAAe,CAAC,EAAE;QAC/C,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,IAAI,SAAS,IAAI;YACjB,OAAO,MAAM,CAAC,QAAQ,KAAK,QAAQ;YAEnC,IAAI,cAAc,OAAO,IAAI,CAAC,YAAY,MAAM,GAAG,GAAG;gBAClD,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,CAAA;oBAC5B,OAAO,MAAM,CAAC,KAAK,UAAU,CAAC,IAAI;gBACtC;YACJ;YAEA,QAAQ,cAAc,CAAC;gBACnB,MAAM,wHAAA,CAAA,cAAW,CAAC,cAAc;gBAChC,kBAAkB,GAAG,iBAAiB,QAAQ,EAAE,KAAK,UAAU,EAAE,OAAO,QAAQ,IAAI;gBACpF,MAAM;gBACN,SAAS,CAAC;oBACN,oBACM,QAAQ,YACR,QACI,SAAS,GAAG,CAAC,CAAC;wBACV,OAAO,CAAA,GAAA,wIAAA,CAAA,eAAY,AAAD,EAAE;oBACxB;gBAEd;gBACA,QAAQ,CAAC;oBACL,gBAAgB,wHAAA,CAAA,cAAW,CAAC,cAAc,EAAE,OAAO;wBAAE;wBAAM;oBAAK;oBAChE,OAAO;gBACX;YACJ;QACJ;IACJ;IAEA,IAAI,UAAU,CAAC,MAAc,OAAe,CAAC,EAAE;QAC3C,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,IAAI,SAAS,IAAI;YACjB,OAAO,MAAM,CAAC,QAAQ,KAAK,QAAQ;YAEnC,IAAI,cAAc,OAAO,IAAI,CAAC,YAAY,MAAM,GAAG,GAAG;gBAClD,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,CAAA;oBAC5B,OAAO,MAAM,CAAC,KAAK,UAAU,CAAC,IAAI;gBACtC;YACJ;YAEA,QAAQ,cAAc,CAAC;gBACnB,MAAM,wHAAA,CAAA,cAAW,CAAC,WAAW;gBAC7B,kBAAkB,GAAG,iBAAiB,QAAQ,EAAE,KAAK,MAAM,EAAE,OAAO,QAAQ,IAAI;gBAChF,MAAM;gBACN,SAAS,CAAC;oBACN,QACI,KAAK,GAAG,CAAC,CAAC;wBACN,OAAO,CAAA,GAAA,wIAAA,CAAA,sBAAmB,AAAD,EAAE;oBAC/B;gBAER;gBACA,QAAQ,CAAC;oBACL,gBAAgB,wHAAA,CAAA,cAAW,CAAC,WAAW,EAAE,OAAO;wBAAE;wBAAM;oBAAK;oBAC7D,OAAO;gBACX;YACJ;QACJ;IACJ;IAEA,IAAI,kBAAkB;QAClB,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,QAAQ,WAAW,CAAC;gBAChB,MAAM,wHAAA,CAAA,cAAW,CAAC,gBAAgB;gBAClC,MAAM;gBACN,SAAS,CAAC;oBACN,IAAI,qBAAoC,aAAa,GAAG,CAAC,CAAA;wBACrD,OAAO,CAAA,GAAA,wIAAA,CAAA,mBAAgB,AAAD,EAAE;4BACpB,MAAM,YAAY,KAAK;4BACvB,IAAI,YAAY,EAAE;wBACtB;oBACJ;oBACA,qBAAqB,mBAChB,MAAM,CAAC,CAAA;wBACJ,OAAO,YAAY,IAAI,CAAE,WAAW,OAAO;oBAC/C,GACC,IAAI,CAAC,sHAAA,CAAA,+BAA4B;oBACtC,QAAQ;gBACZ;gBACA,QAAQ,CAAC;oBACL,gBAAgB,wHAAA,CAAA,cAAW,CAAC,gBAAgB,EAAE,OAAO;oBACrD,OAAO;gBACX;YACJ;QACJ;IACJ;IAEA,IAAI,cAAc,CAAC,cAAsB;QACrC,IAAI,cAAc;YACd,IAAI;YACJ,MAAM;QACV;QACA,0HAAA,CAAA,kBAAe,CAAC,WAAW,CAAC;YACxB,MAAM,wHAAA,CAAA,cAAW,CAAC,YAAY;YAC9B,MAAM;YACN,SAAS,KAAO;YAChB,QAAQ,CAAC;gBACL,gBAAgB,wHAAA,CAAA,cAAW,CAAC,YAAY,EAAE,OAAO;YACrD;QACJ;IACJ;IAEA,IAAI,oBAAoB,CAAC;QACrB,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,QAAQ,cAAc,CAAC;gBACnB,MAAM,wHAAA,CAAA,cAAW,CAAC,eAAe;gBACjC,MAAM;gBACN,SAAS,CAAA;oBACL,IAAI,CAAC,gBAAgB;wBACjB;wBACA;oBACJ;oBACA,IAAI,CAAC,eAAe,UAAU,EAAE;wBAC5B,IAAI,aAAa,CAAC,eAAe,YAAY,EACxC,IAAI,CAAC,CAAA;4BACF,eAAe,UAAU,GAAG;gCACxB;gCACA,MAAM,eAAe,YAAY;4BACrC;wBACJ,GACC,KAAK,CAAC,CAAA;4BACH,QAAQ,KAAK,CAAC,CAAC,8BAA8B,EAAE,eAAe,YAAY,CAAC,EAAE,EAAE,KAAK,SAAS,CAAC,IAAI;4BAClG,eAAe,UAAU,GAAG;gCACxB,MAAM;gCACN,MAAM,eAAe,YAAY;4BACrC;wBACJ,GACC,OAAO,CAAC;4BACL,QAAQ;gCAAE,QAAQ,CAAA,GAAA,wIAAA,CAAA,sBAAmB,AAAD,EAAE;gCAAiB,UAAU;4BAAe;wBACpF;oBACR,OAAO;wBACH,QAAQ;4BAAE,QAAQ,CAAA,GAAA,wIAAA,CAAA,sBAAmB,AAAD,EAAE;4BAAiB,UAAU;wBAAe;oBACpF;gBACJ;gBACA,QAAQ,CAAC;oBACL,OAAO;gBACX;YACJ;QACJ;IACJ;IAEA,IAAI,gBAAgB,CAAC;QACjB,8CAA8C;QAC9C,IAAI,6GAAA,CAAA,UAAU,CAAC,YAAY,EAAE;YACzB,OAAO,QAAQ,OAAO,CAAC;QAC3B;QACA,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,IAAI,CAAC,MAAM;gBACP,QAAQ;gBACR;YACJ;YACA,QAAQ,cAAc,CAAC;gBACnB,MAAM,wHAAA,CAAA,cAAW,CAAC,WAAW;gBAC7B,kBAAkB,GAAG,iBAAiB,QAAQ,EAAE,KAAK,KAAK,CAAC;gBAC3D,MAAM;gBACN,SAAS,CAAA;oBACL,QAAQ;gBACZ;gBACA,QAAQ,CAAC;oBACL,gBAAgB,wHAAA,CAAA,cAAW,CAAC,WAAW,EAAE,OAAO;oBAChD,OAAO;gBACX;YACJ;QACJ;IACJ;IAEA,IAAI,iBAAiB,CAAC;QAClB,8CAA8C;QAC9C,IAAI,6GAAA,CAAA,UAAU,CAAC,YAAY,EAAE;YACzB,IAAI,SAAS,CAAC;YACd,MAAM,OAAO,CAAC,CAAA;gBACV,MAAM,CAAC,KAAK,GAAG;YACnB;YACA,OAAO,QAAQ,OAAO,CAAC;QAC3B;QACA,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,QAAQ,cAAc,CAClB;gBACI,MAAM,wHAAA,CAAA,cAAW,CAAC,YAAY;gBAC9B,kBAAkB,GAAG,iBAAiB,aAAa,CAAC;gBACpD,eAAe;gBACf,eAAe;oBACX,gBAAgB;gBACpB;gBACA,MAAM;gBACN,SAAS,CAAA;oBACL,QAAQ;gBACZ;gBACA,QAAQ,CAAC;oBACL,gBAAgB,wHAAA,CAAA,cAAW,CAAC,YAAY,EAAE,OAAO;oBACjD,OAAO;gBACX;YACJ,GACA,KAAK,SAAS,CAAC;QAEvB;IACJ;IAEA,IAAI,eAAe;IAEnB,IAAI,kBAAkB;QAClB,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,eAAe,gBAAgB,CAAA,GAAA,wLAAA,CAAA,KAAY,AAAD;YAE1C,0HAAA,CAAA,kBAAe,CAAC,WAAW,CAAC;gBACxB,MAAM,wHAAA,CAAA,cAAW,CAAC,iBAAiB;gBACnC,MAAM;gBACN,SAAS;oBACL;gBACJ;gBACA,QAAQ,CAAC;oBACL,gBAAgB,wHAAA,CAAA,cAAW,CAAC,iBAAiB,EAAE,OAAO;oBACtD,OAAO;gBACX;YACJ;QACJ;IACJ;IAEA,IAAI,aAAa;QACb,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,QAAQ,WAAW,CAAC;gBAChB,MAAM,wHAAA,CAAA,cAAW,CAAC,WAAW;gBAC7B,MAAM;gBACN,SAAS,CAAC;oBACN,QAAQ,SAAS,QAAQ;gBAC7B;gBACA,QAAQ,CAAC;oBACL,gBAAgB,wHAAA,CAAA,cAAW,CAAC,WAAW,EAAE,OAAO;oBAChD,OAAO;gBACX;YACJ;QACJ;IACJ;IAEA,IAAI,YAAY,CAAC,OAAe,OAA2B,SAA+B,OAAgB;QACtG,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,IAAI,WAAW,eAAe,OAAO,CAAC;YACtC,IAAI,CAAC,UAAU;gBACX,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;gBACA;YACJ;YAEA,IAAI,cAAkC;mBAAI;aAAM;YAChD,YAAY,IAAI,CAAC,wHAAA,CAAA,mBAAgB,CAAC,IAAI;YAEtC,IAAI,QAAQ;gBACR,OAAO,KAAK,GAAG;gBACf,OAAO,WAAW,GAAG;YACzB;YAEA,IAAI,cAAc;gBACd,SAAS;gBACT,OAAO,SAAS;gBAChB,MAAM,YAAY,MAAM,CAAC,CAAC,GAAG;oBACzB,IAAI,OAAe,OAAO,MAAM,WAAY,IAAe,SAAS,wHAAA,CAAA,mBAAgB,CAAC,EAAE;oBACvF,IAAI,OAAe,OAAO,MAAM,WAAY,IAAe,SAAS,wHAAA,CAAA,mBAAgB,CAAC,EAAE;oBACvF,OAAO,OAAO;gBAClB;gBACA,QAAQ,SAAS,KAAK,SAAS,CAAC,UAAU;YAC9C;YAEA,QAAQ,cAAc,CAClB;gBACI,MAAM,wHAAA,CAAA,cAAW,CAAC,SAAS;gBAC3B,kBAAkB,GAAG,iBAAiB,wBAAwB,CAAC;gBAC/D,MAAM;gBACN,eAAe;gBACf,eAAe;oBACX,aAAa;oBACb,gBAAgB;gBACpB;gBACA,SAAS,CAAC;oBACN,+BAA+B;wBAC3B,IAAI;wBACJ,kBAAkB,SAAS,EAAE,EAAE,cAAc;wBAC7C,YAAY;wBACZ,SAAS,QAAQ,GAAG,CAAC,CAAA;4BACjB,OAAO;gCACH,IAAI,EAAE,EAAE,IAAI;gCACZ,MAAM,EAAE,IAAI,IAAI;gCAChB,YAAY;gCACZ,UAAU;4BACd;wBACJ;oBACJ,GACK,IAAI,CAAC;wBACF;oBACJ,GACC,KAAK,CAAC,CAAA;wBACH,gBAAgB,wHAAA,CAAA,cAAW,CAAC,SAAS,EAAE;wBACvC,OAAO;oBACX;gBACR;gBACA,QAAQ,CAAC;oBACL,gBAAgB,wHAAA,CAAA,cAAW,CAAC,SAAS,EAAE;oBACvC,OAAO;gBACX;YACJ,GACA,KAAK,SAAS,CAAC;QAEvB;IACJ;IAEA,IAAI,cAAc,CAAC;QACf,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,IAAI,WAAW,eAAe,OAAO,CAAC;YACtC,IAAI,CAAC,UAAU;gBACX,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;gBACA;YACJ;YAEA,IAAI,cAAkC;mBAAI,aAAa,KAAK;aAAC;YAC7D,YAAY,IAAI,CAAC,wHAAA,CAAA,mBAAgB,CAAC,IAAI;YAEtC,IAAI,eAAe;gBAAE,GAAG,aAAa,MAAM;YAAC;YAE5C,IAAI,aAAa,MAAM,EAAE;gBACrB,aAAa,KAAK,GAAG;gBACrB,aAAa,WAAW,GAAG;YAC/B;YAEA,IAAI,cAAc;gBACd,IAAI,aAAa,EAAE;gBACnB,SAAS,aAAa,OAAO;gBAC7B,OAAO,aAAa,KAAK,IAAI;gBAC7B,MAAM,YAAY,MAAM,CAAC,CAAC,GAAG;oBACzB,IAAI,OAAe,OAAO,MAAM,WAAY,IAAe,SAAS,wHAAA,CAAA,mBAAgB,CAAC,EAAE;oBACvF,IAAI,OAAe,OAAO,MAAM,WAAY,IAAe,SAAS,wHAAA,CAAA,mBAAgB,CAAC,EAAE;oBACvF,OAAO,OAAO;gBAClB;gBACA,QAAQ,uCAAe,KAAK,SAAS,CAAC;YAC1C;YAEA,QAAQ,cAAc,CAClB;gBACI,MAAM,wHAAA,CAAA,cAAW,CAAC,WAAW;gBAC7B,kBAAkB,GAAG,iBAAiB,wBAAwB,CAAC;gBAC/D,MAAM;gBACN,eAAe;gBACf,eAAe;oBACX,aAAa;oBACb,gBAAgB;gBACpB;gBACA,SAAS;oBACL;gBACJ;gBACA,QAAQ,CAAC;oBACL,gBAAgB,wHAAA,CAAA,cAAW,CAAC,WAAW,EAAE;oBACzC,OAAO;gBACX;YACJ,GACA,KAAK,SAAS,CAAC;QAEvB;IACJ;IAEA,IAAI,0BAA0B;QAC1B,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,IAAI,WAAW,eAAe,OAAO,CAAC;YACtC,IAAI,CAAC,UAAU;gBACX,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;gBACA;YACJ;YAEA,QAAQ,cAAc,CAAC;gBACnB,MAAM,wHAAA,CAAA,cAAW,CAAC,iBAAiB;gBACnC,kBAAkB,GAAG,iBAAiB,wBAAwB,CAAC;gBAC/D,MAAM;gBACN,eAAe;oBACX,aAAa;oBACb,gBAAgB;gBACpB;gBACA,SAAS,CAAA;oBACL,QAAQ,OAAO,KAAK,GAAG,CAAC,wIAAA,CAAA,oBAAiB,IAAI,EAAE;gBACnD;gBACA,QAAQ,CAAC;oBACL,gBAAgB,wHAAA,CAAA,cAAW,CAAC,iBAAiB,EAAE;oBAC/C,OAAO;gBACX;YACJ;QACJ;IACJ;IAEA,IAAI,iBAAiB,CAAC;QAClB,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,0HAAA,CAAA,kBAAe,CAAC,WAAW,CAAC;gBACxB,MAAM,wHAAA,CAAA,cAAW,CAAC,gBAAgB;gBAClC,MAAM;gBACN,SAAS,CAAA;oBACL,QAAQ;gBACZ;gBACA,QAAQ,CAAC;oBACL,gBAAgB,wHAAA,CAAA,cAAW,CAAC,gBAAgB,EAAE;oBAC9C,OAAO;gBACX;YACJ;QACJ;IACJ;IAEA,IAAI,WAAW,CAAC;QACZ,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,0HAAA,CAAA,kBAAe,CAAC,WAAW,CAAC;gBACxB,MAAM,wHAAA,CAAA,cAAW,CAAC,SAAS;gBAC3B,MAAM;oBACF,MAAM;oBACN,OAAO;gBACX;gBACA,SAAS;oBACL;gBACJ;gBACA,QAAQ,CAAC;oBACL,gBAAgB,wHAAA,CAAA,cAAW,CAAC,SAAS,EAAE,OAAO;oBAC9C,OAAO;gBACX;YACJ;QACJ;IACJ;IAEA,IAAI,oBAAoB,CAAC,SAAiB;QACtC,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,IAAI,SAAS,IAAI;YACjB,IAAI,cAAc,OAAO,IAAI,CAAC,YAAY,MAAM,GAAG,GAAG;gBAClD,SAAS,IAAI,gBAAgB;YACjC;YAEA,QAAQ,cAAc,CAAC;gBACnB,MAAM,wHAAA,CAAA,cAAW,CAAC,eAAe;gBACjC,kBAAkB,mBAAmB,CAAC,cAAc,EAAE,QAAQ,iBAAiB,EAAE,OAAO,QAAQ,IAAI;gBACpG,MAAM;gBACN,SAAS,CAAC;oBACN,QAAQ,OAAO,KAAK,GAAG,CAAC,CAAA,IAAK,CAAA,GAAA,wIAAA,CAAA,qBAAkB,AAAD,EAAE,MAAM,EAAE;gBAC5D;gBACA,QAAQ,CAAC;oBACL,gBAAgB,wHAAA,CAAA,cAAW,CAAC,eAAe,EAAE,OAAO;oBACpD,OAAO;gBACX;YACJ;QACJ;IACJ;IAEA,IAAI,WAAW;QACX,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,0HAAA,CAAA,kBAAe,CAAC,WAAW,CAAC;gBACxB,MAAM,wHAAA,CAAA,cAAW,CAAC,SAAS;gBAC3B,MAAM;gBACN,SAAS,CAAC;oBACN,QAAQ,KAAK,GAAG,CAAC,CAAA,IAAK,CAAA,GAAA,wIAAA,CAAA,mBAAgB,AAAD,EAAE;gBAC3C;gBACA,QAAQ,CAAC;oBACL,gBAAgB,wHAAA,CAAA,cAAW,CAAC,eAAe,EAAE,OAAO;oBACpD,OAAO;gBACX;YACJ;QACJ;IACJ;IAEA,IAAI,kBAAkB;QAClB,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,QAAQ,WAAW,CAAC;gBAChB,MAAM,wHAAA,CAAA,cAAW,CAAC,SAAS;gBAC3B,MAAM;gBACN,SAAS,CAAC;oBACN,oBAAoB,QAAQ,QAAQ,QAAQ,KAAK,GAAG,CAAC,wIAAA,CAAA,mBAAgB;gBACzE;gBACA,QAAQ,CAAC;oBACL,gBAAgB,wHAAA,CAAA,cAAW,CAAC,SAAS,EAAE,OAAO;gBAClD;YACJ;QACJ;IACJ;IAEA,IAAI,iBAAiB,CACjB,iBACA,QACA,cACA,cACA,cACA,gCACA,4BACA,iBACA,sBAA+B,KAAK;QAEpC,0HAAA,CAAA,kBAAe,CAAC,2BAA2B,CAAC,wHAAA,CAAA,cAAW,CAAC,eAAe;QAEvE,CAAA,GAAA,0HAAA,CAAA,8BAA2B,AAAD,EAAE;QAE5B,IAAI,cAAc,CAAA,GAAA,0HAAA,CAAA,yBAAsB,AAAD,EAAE,QAAQ,cAAc;QAE/D,0HAAA,CAAA,kBAAe,CAAC,SAAS,CAAC;YACtB,MAAM,wHAAA,CAAA,cAAW,CAAC,eAAe;YACjC,MAAM,sBAAsB,cAAc;YAC1C,UAAU,SAAU,QAAQ;gBACxB,OAAQ,SAAS,IAAI;oBACjB,KAAK;wBACD,IAAI,cAAc;4BACd,aAAa,CAAA,GAAA,wIAAA,CAAA,mBAAgB,AAAD,EAAE,SAAS,IAAI;wBAC/C;wBACA;oBACJ,KAAK;wBACD,IAAI,gCAAgC;4BAChC;wBACJ;wBACA;oBACJ,KAAK;wBACD,IAAI,cAAc;4BACd,aAAa,SAAS,IAAI;wBAC9B;wBACA;oBACJ,KAAK;wBACD,IAAI,CAAC,SAAS,IAAI,EAAE;4BAChB,IAAI,cAAc,CACd,iBACA,QACA,cACA,cACA,cACA,gCACA,WACA,iBACA;wBAER,OAAO;4BACH,CAAA,GAAA,0HAAA,CAAA,4BAAyB,AAAD,EAAE,SAAS,IAAI;wBAC3C;wBACA;oBACJ,KAAK;wBACD,IAAI,OAAO,SAAS,IAAI;wBACxB,IAAI,KAAK,OAAO,KAAK,OAAO,cAAc,CAAC,OAAO,CAAC,cAAc;4BAC7D;wBACJ;wBACA,CAAA,GAAA,0HAAA,CAAA,4BAAyB,AAAD,EAAE,SAAS,IAAI;wBACvC;oBACJ,KAAK;wBACD,IAAI,4BAA4B;4BAC5B;wBACJ;wBACA;oBACJ;wBACI;gBACR;YACJ;YACA,aAAa,SAAU,YAAY;gBAC/B,IAAI,SAAS,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD,EAAiB,0HAAA,CAAA,qBAAkB,EAAE,CAAC;gBACnE,IAAI,eAAe,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD,EAAqB,0HAAA,CAAA,4BAAyB,EAAE,EAAE;gBACrF,eACI,cACA,QACA,CAAA,GAAA,sHAAA,CAAA,2BAAwB,AAAD,KACvB,cACA,cACA,gCACA,WACA,iBACA;YAER;YACA,SAAS,SAAU,OAAO;gBACtB,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,IAAI,iBAAiB;oBACjB;gBACJ;YACJ;QACJ;IACJ;IAEA,MAAM,gCAAgC,AAAC;QACnC,IAAI;QAEJ,OAAO,CACH,iBACA,QACA,cACA,cACA,cACA,gCACA;YAEA,aAAa;YACb,UAAU,WAAW;gBACjB,0HAAA,CAAA,kBAAe,CAAC,2BAA2B,CAAC,wHAAA,CAAA,cAAW,CAAC,eAAe;gBAEvE,IAAI,cAAc,CAAA,GAAA,0HAAA,CAAA,yBAAsB,AAAD,EAAE,QAAQ,cAAc;gBAE/D,0HAAA,CAAA,kBAAe,CAAC,SAAS,CAAC;oBACtB,MAAM,wHAAA,CAAA,cAAW,CAAC,sBAAsB;oBACxC,MAAM;oBACN,UAAU,SAAU,QAAQ;wBACxB,OAAQ,SAAS,IAAI;4BACjB,KAAK;gCACD,IAAI,cAAc;oCACd,aAAa,CAAA,GAAA,wIAAA,CAAA,mBAAgB,AAAD,EAAE,SAAS,IAAI;gCAC/C;gCACA;4BACJ,KAAK;gCACD,IAAI,gCAAgC;oCAChC;gCACJ;gCACA;4BACJ,KAAK;gCACD,IAAI,cAAc;oCACd,aAAa,SAAS,IAAI;gCAC9B;gCACA;4BACJ,KAAK;gCACD,IAAI,4BAA4B;oCAC5B;gCACJ;gCACA;4BACJ;gCACI;wBACR;oBACJ;oBACA,aAAa,SAAU,YAAY;wBAC/B,IAAI,SAAS,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD,EAAiB,0HAAA,CAAA,qBAAkB,EAAE,CAAC;wBACnE,IAAI,eAAe,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD,EAAqB,0HAAA,CAAA,4BAAyB,EAAE,EAAE;wBACrF,qBACI,cACA,QACA,CAAA,GAAA,sHAAA,CAAA,2BAAwB,AAAD,KACvB,cACA,cACA,gCACA;oBAER;oBACA,SAAS,SAAU,OAAO;wBACtB,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oBAChB;gBACJ;YACJ,GAAG;QACP;IACJ;IAEA,IAAI,uBAAuB,CACvB,iBACA,QACA,cACA,cACA,cACA,gCACA;QAEA,8BACI,iBACA,QACA,cACA,cACA,cACA,gCACA;IAER;IAEA,IAAI,mBAAmB;QACnB,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,0HAAA,CAAA,kBAAe,CAAC,WAAW,CAAC;gBACxB,MAAM,wHAAA,CAAA,cAAW,CAAC,iBAAiB;gBACnC,MAAM;gBACN,SAAS,SAAU,IAAI;oBACnB;gBACJ;gBACA,QAAQ,SAAU,KAAK;oBACnB,gBAAgB,wHAAA,CAAA,cAAW,CAAC,eAAe,EAAE,OAAO;oBACpD,OAAO;gBACX;YACJ;QACJ;IACJ;IAEA,IAAI,aAAa,CAAC;QACd,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,QAAQ,cAAc,CAAC;gBACnB,MAAM,wHAAA,CAAA,cAAW,CAAC,UAAU;gBAC5B,kBAAkB,GAAG,iBAAiB,wBAAwB,EAAE,KAAK;gBACrE,MAAM;gBACN,SAAS,CAAC;oBACN,QAAQ,KAAK,GAAG,CAAC,CAAA,IAAK,CAAA,GAAA,wIAAA,CAAA,oBAAiB,AAAD,EAAE;gBAC5C;gBACA,QAAQ,CAAC;oBACL,gBAAgB,wHAAA,CAAA,cAAW,CAAC,UAAU,EAAE,OAAO;gBACnD;YACJ;QACJ;IACJ;IAEA,IAAI,gBAAgB;QAChB,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,QAAQ,uBAAuB,CAC3B;gBACI,MAAM,wHAAA,CAAA,cAAW,CAAC,WAAW;gBAC7B,MAAM;gBACN,SAAS,SAAU,IAAI;oBACnB,oBAAoB,QAAQ,QAAQ,QAAQ,KAAK,GAAG,CAAC,CAAA,IAAK,CAAA,GAAA,wIAAA,CAAA,cAAW,AAAD,EAAE;gBAC1E;gBACA,QAAQ,SAAU,KAAK;oBACnB,gBAAgB,wHAAA,CAAA,cAAW,CAAC,WAAW,EAAE,OAAO;oBAChD,OAAO;gBACX;YACJ,GACA;QAER;IACJ;IAEA,IAAI,cAAc;QACd,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,QAAQ,uBAAuB,CAC3B;gBACI,MAAM,wHAAA,CAAA,cAAW,CAAC,SAAS;gBAC3B,MAAM;gBACN,SAAS,SAAU,IAAI;oBACnB,oBAAoB,QAAQ,QAAQ,QAAQ,KAAK,GAAG,CAAC,CAAA,IAAK,CAAA,GAAA,wIAAA,CAAA,YAAS,AAAD,EAAE;gBACxE;gBACA,QAAQ,SAAU,KAAK;oBACnB,gBAAgB,wHAAA,CAAA,cAAW,CAAC,SAAS,EAAE,OAAO;oBAC9C,OAAO;gBACX;YACJ,GACA;QAER;IACJ;IAEA,IAAI,qBAAqB;QACrB,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,QAAQ,uBAAuB,CAC3B;gBACI,MAAM,wHAAA,CAAA,cAAW,CAAC,gBAAgB;gBAClC,MAAM;gBACN,SAAS,SAAU,IAAI;oBACnB,oBAAoB,QAAQ,QAAQ,QAAQ,KAAK,GAAG,CAAC,CAAA,IAAK,CAAA,GAAA,wIAAA,CAAA,qBAAkB,AAAD,EAAE;gBACjF;gBACA,QAAQ,SAAU,KAAK;oBACnB,gBAAgB,wHAAA,CAAA,cAAW,CAAC,gBAAgB,EAAE,OAAO;oBACrD,OAAO;gBACX;YACJ,GACA;QAER;IACJ;IAEA,IAAI,mBAAmB;QACnB,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,QAAQ,uBAAuB,CAC3B;gBACI,MAAM,wHAAA,CAAA,cAAW,CAAC,cAAc;gBAChC,MAAM;gBACN,SAAS,SAAU,IAAI;oBACnB,oBAAoB,QAAQ,QAAQ,QAAQ,KAAK,GAAG,CAAC,CAAA,IAAK,CAAA,GAAA,wIAAA,CAAA,eAAY,AAAD,EAAE;gBAC3E;gBACA,QAAQ,SAAU,KAAK;oBACnB,gBAAgB,wHAAA,CAAA,cAAW,CAAC,cAAc,EAAE,OAAO;oBACnD,OAAO;gBACX;YACJ,GACA;QAER;IACJ;IAEA,IAAI,iBAAiB;QACjB,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,QAAQ,uBAAuB,CAC3B;gBACI,MAAM,wHAAA,CAAA,cAAW,CAAC,YAAY;gBAC9B,MAAM;gBACN,SAAS,SAAU,IAAI;oBACnB,oBAAoB,QAAQ,QAAQ,QAAQ,KAAK,GAAG,CAAC,CAAA,IAAK,CAAA,GAAA,wIAAA,CAAA,eAAY,AAAD,EAAE;gBAC3E;gBACA,QAAQ,SAAU,KAAK;oBACnB,gBAAgB,wHAAA,CAAA,cAAW,CAAC,YAAY,EAAE,OAAO;oBACjD,OAAO;gBACX;YACJ,GACA;QAER;IACJ;IAEA,IAAI,uBAAuB,CAAC;QACxB,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,QAAQ,WAAW,CAAC;gBAChB,MAAM,wHAAA,CAAA,cAAW,CAAC,uBAAuB;gBACzC,MAAM;gBACN,SAAS,CAAC;oBACN,QAAQ,KAAK,GAAG,CAAC,CAAA,IAAK,CAAA,GAAA,wIAAA,CAAA,eAAY,AAAD,EAAE;gBACvC;gBACA,QAAQ,CAAC;oBACL,gBAAgB,wHAAA,CAAA,cAAW,CAAC,uBAAuB,EAAE,OAAO;oBAC5D,OAAO;gBACX;YACJ;QACJ;IACJ;IAEA,IAAI,iBAAiB,CAAC,WAAmB;QACrC,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,IAAI,WAAW,eAAe,OAAO,CAAC;YACtC,IAAI,CAAC,UAAU;gBACX,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;gBACA;YACJ;YAEA,IAAI,OAAO;gBACP,QAAQ;gBACR,WAAW;YACf;YAEA,QAAQ,cAAc,CAClB;gBACI,MAAM,wHAAA,CAAA,cAAW,CAAC,sBAAsB;gBACxC,eAAe;gBACf,eAAe;oBACX,aAAa,KAAK,MAAM;oBACxB,gBAAgB;gBACpB;gBACA,MAAM,KAAK,SAAS;gBACpB,SAAS,CAAC;oBACN,QAAQ,CAAA,GAAA,wIAAA,CAAA,uBAAoB,AAAD,EAAE;gBACjC;gBACA,QAAQ,CAAC;oBACL,gBAAgB,wHAAA,CAAA,cAAW,CAAC,sBAAsB,EAAE,OAAO;oBAC3D,OAAO;gBACX;YACJ,GACA,KAAK,SAAS,CAAC;gBACX;YACJ;QAER;IACJ;IAEA,IAAI,iBAAiB,CAAC,WAAmB;QACrC,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,IAAI,WAAW,eAAe,OAAO,CAAC;YACtC,IAAI,CAAC,UAAU;gBACX,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;gBACA;YACJ;YAEA,IAAI,OAAO;gBACP,QAAQ;gBACR,WAAW;YACf;YAEA,QAAQ,cAAc,CAClB;gBACI,MAAM,wHAAA,CAAA,cAAW,CAAC,cAAc;gBAChC,eAAe;gBACf,MAAM,KAAK,SAAS;gBACpB,eAAe;oBACX,aAAa,KAAK,MAAM;oBACxB,gBAAgB;gBACpB;gBACA,SAAS,CAAC;oBACN,QAAQ,CAAA,GAAA,wIAAA,CAAA,uBAAoB,AAAD,EAAE;gBACjC;gBACA,QAAQ,CAAC;oBACL,gBAAgB,wHAAA,CAAA,cAAW,CAAC,cAAc,EAAE,OAAO;oBACnD,OAAO;gBACX;YACJ,GACA,KAAK,SAAS,CAAC;gBACX;YACJ;QAER;IACJ;IAEA,IAAI,uBAAuB,CAAC,WAAmB;QAC3C,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,IAAI,WAAW,eAAe,OAAO,CAAC;YACtC,IAAI,CAAC,UAAU;gBACX,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;gBACA;YACJ;YAEA,IAAI,OAAO;gBACP,QAAQ;gBACR,WAAW;YACf;YAEA,QAAQ,cAAc,CAClB;gBACI,MAAM,wHAAA,CAAA,cAAW,CAAC,oBAAoB;gBACtC,eAAe;gBACf,MAAM,KAAK,SAAS;gBACpB,eAAe;oBACX,aAAa,KAAK,MAAM;oBACxB,gBAAgB;gBACpB;gBACA,SAAS,CAAC;oBACN,QAAQ,CAAA,GAAA,wIAAA,CAAA,uBAAoB,AAAD,EAAE;gBACjC;gBACA,QAAQ,CAAC;oBACL,gBAAgB,wHAAA,CAAA,cAAW,CAAC,oBAAoB,EAAE,OAAO;oBACzD,OAAO;gBACX;YACJ,GACA,KAAK,SAAS,CAAC;gBACX;YACJ;QAER;IACJ;IAEA,IAAI,wBAAwB,CAAC,WAAmB,aAAqB;QACjE,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,IAAI,OAAO;gBACP,QAAQ;gBACR,WAAW;YACf;YAEA,QAAQ,cAAc,CAClB;gBACI,MAAM,wHAAA,CAAA,cAAW,CAAC,uBAAuB;gBACzC,MAAM;gBACN,eAAe;gBACf,eAAe;oBACX,aAAa,KAAK,MAAM;oBACxB,gBAAgB;gBACpB;gBACA,SAAS;oBACL;gBACJ;gBACA,QAAQ,SAAU,KAAK;oBACnB,gBAAgB,wHAAA,CAAA,cAAW,CAAC,uBAAuB,EAAE,OAAO;oBAC5D,OAAO;gBACX;YACJ,GACA,KAAK,SAAS,CAAC;gBACX,OAAO;gBACP,MAAM;YACV;QAER;IACJ;IAEA,IAAI,0BAA0B;QAC1B,0HAAA,CAAA,kBAAe,CAAC,SAAS,CAAC;YACtB,MAAM,wHAAA,CAAA,cAAW,CAAC,gBAAgB;YAClC,MAAM;YACN,aAAa,SAAU,YAAY;gBAC/B;YACJ;YACA,SAAS,SAAU,OAAO;gBACtB,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YAChB;YACA,UAAU,SAAU,QAAQ;gBACxB,IAAI,SAAS,IAAI,CAAC,UAAU,KAAK,cAAc,SAAS,IAAI,CAAC,UAAU,KAAK,SAAS;oBAEjF,oEAAoE;oBACpE,IAAI,oBAAoB,CAAA,GAAA,2HAAA,CAAA,sBAAmB,AAAD,MAAM,KAAK,KAAK,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,MAAM;oBACpF,IAAI,oBAAoB,GAAG;wBACvB,oBAAoB;oBACxB;oBAEA,SAAS,aAAa,CAClB,IAAI,YAAY,wHAAA,CAAA,gBAAa,CAAC,eAAe,EAAE;wBAAE,QAAQ;4BAAE,WAAW;wBAAkB;oBAAE;gBAElG;YACJ;QACJ;IACJ;IAEA,IAAI,qBAAqB;QACrB,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,0HAAA,CAAA,kBAAe,CAAC,WAAW,CAAC;gBACxB,MAAM,wHAAA,CAAA,cAAW,CAAC,oBAAoB;gBACtC,MAAM;gBACN,SAAS,SAAU,QAAQ;oBACvB,QAAQ,SAAS;gBACrB;gBACA,QAAQ,SAAU,KAAK;oBACnB,gBAAgB,wHAAA,CAAA,cAAW,CAAC,oBAAoB,EAAE,OAAO;oBACzD,OAAO;gBACX;YACJ;QACJ;IACJ;IAEA,IAAI,aAAa;QACb,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,IAAI,WAAW,eAAe,OAAO,CAAC;YACtC,IAAI,CAAC,UAAU;gBACX,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;gBACA;YACJ;YAEA,QAAQ,cAAc,CAAC;gBACnB,MAAM,wHAAA,CAAA,cAAW,CAAC,YAAY;gBAC9B,MAAM;gBACN,eAAe;oBACX,aAAa;gBACjB;gBACA,SAAS,CAAC;oBACN,QAAQ,CAAA,GAAA,wIAAA,CAAA,eAAY,AAAD,EAAE;gBACzB;gBACA,QAAQ,CAAC;oBACL,gBAAgB,wHAAA,CAAA,cAAW,CAAC,YAAY,EAAE,OAAO;oBACjD,OAAO;gBACX;YACJ;QACJ;IACJ;IAEA,IAAI,SAAS,CAAC;QACV,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,IAAI,WAAW,eAAe,OAAO,CAAC;YACtC,IAAI,CAAC,UAAU;gBACX,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;gBACA;YACJ;YAEA,QAAQ,cAAc,CAClB;gBACI,MAAM,wHAAA,CAAA,cAAW,CAAC,OAAO;gBACzB,MAAM;gBACN,eAAe;gBACf,eAAe;oBACX,aAAa;oBACb,gBAAgB;gBACpB;gBACA,SAAS;oBACL;gBACJ;gBACA,QAAQ,CAAC;oBACL,gBAAgB,wHAAA,CAAA,cAAW,CAAC,OAAO,EAAE,OAAO;oBAC5C,OAAO;gBACX;YACJ,GACA,KAAK,SAAS,CAAC;gBACX,SAAS;YACb;QAER;IACJ;IAEA,IAAI,oBAAoB,CAAC,MAAY,OAAe,SAAqB,CAAC,CAAC;QACvE,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,IAAI,SAAS;gBACT,SAAS;YACb;YACA,OAAO,IAAI,CAAC,QAAQ,OAAO,CAAC,CAAA;gBACxB,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,QAAQ;YACtC;YAEA,QAAQ,cAAc,CAAC;gBACnB,MAAM,wHAAA,CAAA,cAAW,CAAC,eAAe;gBACjC,kBAAkB,GAAG,iBAAiB,cAAc,EAAE,KAAK,GAAG,CAAC,iBAAiB,EAAE,IAAI,gBAAgB,QAAQ,QAAQ,IAAI;gBAC1H,MAAM;gBACN,SAAS,SAAU,IAAI;oBACnB,QAAQ,KAAK,GAAG,CAAC,CAAA,IAAK,CAAA,GAAA,wIAAA,CAAA,qBAAkB,AAAD,EAAE;gBAC7C;gBACA,QAAQ,SAAU,KAAK;oBACnB,gBAAgB,wHAAA,CAAA,cAAW,CAAC,eAAe,EAAE,OAAO;wBAChD,KAAK,KAAK,GAAG;wBACb;wBACA;oBACJ;oBACA,OAAO;gBACX;YACJ;QACJ;IACJ;IAEA,IAAI,0BAA0B,CAAC;QAC3B,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,0HAAA,CAAA,kBAAe,CAAC,WAAW,CAAC;gBACxB,MAAM,wHAAA,CAAA,cAAW,CAAC,yBAAyB;gBAC3C,MAAM;gBACN,SAAS,SAAU,IAAI;oBACnB,QAAQ,CAAA,GAAA,wIAAA,CAAA,+BAA4B,AAAD,EAAE;gBACzC;gBACA,QAAQ,SAAU,KAAK;oBACnB,gBAAgB,wHAAA,CAAA,cAAW,CAAC,yBAAyB,EAAE,OAAO;oBAC9D,OAAO;gBACX;YACJ;QACJ;IACJ;IAEA,IAAI;IACJ,IAAI,iBAAiB;QACjB,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,IAAI,aAAa;gBACb,QAAQ;gBACR;YACJ;YAEA,0HAAA,CAAA,kBAAe,CAAC,WAAW,CAAC;gBACxB,MAAM,wHAAA,CAAA,cAAW,CAAC,gBAAgB;gBAClC,MAAM;gBACN,SAAS,SAAU,WAAW;oBAC1B,IAAI,OAAO,CAAA,GAAA,wIAAA,CAAA,mBAAgB,AAAD,EAAE;oBAC5B,cAAc;oBACd,QAAQ;gBACZ;gBACA,QAAQ,SAAU,KAAK;oBACnB,gBAAgB,wHAAA,CAAA,cAAW,CAAC,gBAAgB,EAAE,OAAO;gBACzD;YACJ;QACJ;IACJ;IAEA,IAAI,aAAa,CAAC;QACd,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,QAAQ,cAAc,CAAC;gBACnB,MAAM,wHAAA,CAAA,cAAW,CAAC,WAAW;gBAC7B,MAAM;gBACN,SAAS,SAAU,IAAI;oBACnB,QAAQ,KAAK,GAAG,CAAC,CAAA,IAAK,CAAA,GAAA,wIAAA,CAAA,wBAAqB,AAAD,EAAE;gBAChD;gBACA,QAAQ,SAAU,KAAK;oBACnB,gBAAgB,wHAAA,CAAA,cAAW,CAAC,WAAW,EAAE,OAAO;oBAChD,OAAO;gBACX;YACJ;QACJ;IACJ;IAEA,IAAI,4BAA4B,OAAO,OAAe;QAClD,IAAI,UAAU,WAAW;YACrB,sJAAA,CAAA,QAAK,CAAC,IAAI,eACN,6LAAC;0BAAK;;;;;;QAKd,GAAG;QACH,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,QAAQ,cAAc,CAAC;gBACnB,MAAM,wHAAA,CAAA,cAAW,CAAC,2BAA2B;gBAC7C,eAAe;gBACf,MAAM;gBACN,eAAe;oBACX,aAAa;oBACb,gBAAgB;gBACpB;gBACA,kBAAkB,GAAG,iBAAiB,gBAAgB,EAAE,mBAAmB,QAAQ;gBACnF,SAAS;oBACL,aAAa;oBACb;gBACJ;gBACA,QAAQ,SAAU,KAAK;oBACnB,aAAa;oBACb,gBAAgB,wHAAA,CAAA,cAAW,CAAC,2BAA2B,EAAE,OAAO;oBAChE,OAAO;gBACX;YACJ;QACJ;IACJ;IAEA,IAAI,oBAAoB;QACpB,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,QAAQ,cAAc,CAAC;gBACnB,MAAM,wHAAA,CAAA,cAAW,CAAC,gBAAgB;gBAClC,MAAM;gBACN,SAAS,SAAU,IAAI;oBACnB,QAAQ,IAAI,KAAK;gBACrB;gBACA,QAAQ,SAAU,KAAK;oBACnB,gBAAgB,wHAAA,CAAA,cAAW,CAAC,gBAAgB,EAAE,OAAO;gBACzD;YACJ;QACJ;IACJ;IACA,IAAI,eAAe,CAAC;QAChB,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,QAAQ,cAAc,CAAC;gBACnB,MAAM,wHAAA,CAAA,cAAW,CAAC,aAAa;gBAC/B,MAAM;gBACN,SAAS,SAAU,OAAO;oBACtB,QAAQ,UAAU,QAAQ,GAAG,CAAC,wIAAA,CAAA,cAAW,IAAI,EAAE;gBACnD;gBACA,QAAQ,SAAU,KAAK;oBACnB,gBAAgB,wHAAA,CAAA,cAAW,CAAC,aAAa,EAAE,OAAO;oBAClD,OAAO;gBACX;YACJ;QACJ;IACJ;IAEA,IAAI,oBAAoB;QACpB,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,QAAQ,cAAc,CAAC;gBACnB,MAAM,wHAAA,CAAA,cAAW,CAAC,oBAAoB;gBACtC,MAAM;gBACN,SAAS,SAAU,KAAK;oBACpB,oBACM,QAAQ,SACR,QACI,MAAM,GAAG,CAAC,CAAA;wBACN,IAAI,gBAAgB,CAAA,GAAA,wIAAA,CAAA,qBAAkB,AAAD,EAAE;wBACvC,OAAO;oBACX;gBAEd;gBACA,QAAQ,SAAU,KAAK;oBACnB,gBAAgB,wHAAA,CAAA,cAAW,CAAC,oBAAoB,EAAE,OAAO;oBACzD,OAAO;gBACX;YACJ;QACJ;IACJ;IAEA,IAAI,eAAe,CAAC,aAAqB;QACrC,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,IAAI,WAAW,eAAe,OAAO,CAAC;YACtC,IAAI;YACJ,IAAI,UAAU;gBACV,IAAI,QAAQ,SAAS,KAAK,CAAC;gBAC3B,IAAI,MAAM,MAAM,GAAG,GAAG;oBAClB,IAAI,MAAM,KAAK,KAAK,CAAC,CAAA,GAAA,wHAAA,CAAA,cAAW,AAAD,EAAE,KAAK,CAAC,EAAE;oBACzC,OAAO,IAAI,GAAG;gBAClB;YACJ;YAEA,IAAI,cAAc;gBACd,SAAS;gBACT,MAAM,QAAQ;gBACd,UAAU,KAAK,SAAS,CAAC;gBACzB,cAAc;YAClB;YAEA,QAAQ,cAAc,CAClB;gBACI,MAAM,wHAAA,CAAA,cAAW,CAAC,aAAa;gBAC/B,MAAM;gBACN,kBAAkB,CAAA,GAAA,4HAAA,CAAA,cAAW,AAAD,EAAE;gBAC9B,eAAe;gBACf,eAAe;oBACX,gBAAgB;gBACpB;gBACA,SAAS;oBACL;gBACJ;gBACA,QAAQ,SAAU,KAAK;oBACnB,gBAAgB,wHAAA,CAAA,cAAW,CAAC,aAAa,EAAE,OAAO;oBAClD,OAAO;gBACX;YACJ,GACA,KAAK,SAAS,CAAC;QAEvB;IACJ;IAEA,IAAI,sBAAsB;QACtB,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,QAAQ,cAAc,CAAC;gBACnB,MAAM,wHAAA,CAAA,cAAW,CAAC,qBAAqB;gBACvC,kBAAkB,mBAAmB,MAAM,wHAAA,CAAA,cAAW,CAAC,qBAAqB;gBAC5E,MAAM;gBACN,SAAS,SAAU,MAAM;oBACrB,oBAAoB,QAAQ,UAAU,QAAQ,CAAA,GAAA,wIAAA,CAAA,wBAAqB,AAAD,EAAE;gBACxE;gBACA,QAAQ,SAAU,KAAK;oBACnB,gBAAgB,wHAAA,CAAA,cAAW,CAAC,qBAAqB,EAAE,OAAO;oBAC1D,OAAO;gBACX;YACJ;QACJ;IACJ;IAEA,IAAI,yBAAyB,CAAC;QAC1B,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,QAAQ,cAAc,CAAC;gBACnB,MAAM,wHAAA,CAAA,cAAW,CAAC,yBAAyB;gBAC3C,MAAM;gBACN,kBAAkB,mBAAmB,aAAa,aAAa;gBAC/D,eAAe;gBACf,SAAS;oBACL;gBACJ;gBACA,QAAQ,SAAU,KAAK;oBACnB,gBAAgB,wHAAA,CAAA,cAAW,CAAC,yBAAyB,EAAE,OAAO;oBAC9D,OAAO;gBACX;YACJ;QACJ;IACJ;IAEA,IAAI,oBAAoB,CAAC;QACrB,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,QAAQ,cAAc,CAAC;gBACnB,MAAM,wHAAA,CAAA,cAAW,CAAC,mBAAmB;gBACrC,MAAM;gBACN,SAAS,SAAU,MAAM;oBACrB,QACI,OAAO,IAAI,CAAC,OAAO,QAAQ,EAAE,GAAG,CAAC,CAAA;wBAC7B,OAAO,CAAA,GAAA,wIAAA,CAAA,uBAAoB,AAAD,EAAE,OAAO,QAAQ,CAAC,IAAI;oBACpD;gBAER;gBACA,QAAQ,SAAU,KAAK;oBACnB,gBAAgB,wHAAA,CAAA,cAAW,CAAC,yBAAyB,EAAE,OAAO;gBAClE;YACJ;QACJ;IACJ;IAEA,IAAI,oBAAoB,CAAC;QACrB,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,QAAQ,cAAc,CAAC;gBACnB,MAAM,wHAAA,CAAA,cAAW,CAAC,mBAAmB;gBACrC,MAAM;gBACN,SAAS,SAAU,IAAI;oBACnB,QAAQ,CAAA,GAAA,wIAAA,CAAA,sBAAmB,AAAD,EAAE;gBAChC;gBACA,QAAQ,SAAU,KAAK;oBACnB,gBAAgB,wHAAA,CAAA,cAAW,CAAC,mBAAmB,EAAE,OAAO;oBACxD,OAAO;gBACX;YACJ;QACJ;IACJ;IAEA,IAAI,eAAe,CAAC;QAChB,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,QAAQ,cAAc,CAAC;gBACnB,MAAM,wHAAA,CAAA,cAAW,CAAC,cAAc;gBAChC,kBAAkB,gBAAgB,UAAU;gBAC5C,MAAM;gBACN,SAAS,SAAU,IAAI;oBACnB,QAAQ;wBACJ,QAAQ,KAAK,MAAM;wBACnB,cAAc,KAAK,YAAY;oBACnC;gBACJ;gBACA,QAAQ,SAAU,KAAK;oBACnB,gBAAgB,wHAAA,CAAA,cAAW,CAAC,cAAc,EAAE,OAAO;oBACnD,OAAO;gBACX;YACJ;QACJ;IACJ;IAEA,IAAI,cAAc,CAAC;QACf,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,QAAQ,uBAAuB,CAC3B;gBACI,MAAM,wHAAA,CAAA,cAAW,CAAC,YAAY;gBAC9B,MAAM;gBACN,SAAS,SAAU,IAAI;oBACnB,QAAQ,KAAK,GAAG,CAAC,CAAA,IAAK,CAAA,GAAA,wIAAA,CAAA,oBAAiB,AAAD,EAAE;gBAC5C;gBACA,QAAQ,SAAU,KAAK;oBACnB,gBAAgB,wHAAA,CAAA,cAAW,CAAC,YAAY,EAAE,OAAO;oBACjD,OAAO;gBACX;YACJ,GACA;QAER;IACJ;IAEA,IAAI,gBAAgB;QAChB,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,QAAQ,cAAc,CAAC;gBACnB,MAAM,wHAAA,CAAA,cAAW,CAAC,eAAe;gBACjC,MAAM;gBACN,SAAS,SAAU,IAAI;oBACnB,QAAQ;gBACZ;gBACA,QAAQ,SAAU,KAAK;oBACnB,gBAAgB,wHAAA,CAAA,cAAW,CAAC,eAAe,EAAE,OAAO;oBACpD,OAAO;gBACX;YACJ;QACJ;IACJ;IAEA,IAAI,sBAAsB,CAAC,SAAiB;QACxC,IAAI,YAAY,IAAI,gBAAgB,QAAQ,QAAQ;QAEpD,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,QAAQ,cAAc,CAAC;gBACnB,MAAM,wHAAA,CAAA,cAAW,CAAC,kBAAkB;gBACpC,kBAAkB,GAAG,iBAAiB,CAAC,EAAE,wHAAA,CAAA,cAAW,CAAC,kBAAkB,CAAC,CAAC,EAAE,QAAQ,CAAC,EAAE,WAAW;gBACjG,MAAM;gBACN,SAAS,SAAU,IAAI;oBACnB,oBAAoB,QAAQ,QAAQ,QAAQ,CAAA,GAAA,wIAAA,CAAA,mBAAgB,AAAD,EAAE;gBACjE;gBACA,QAAQ,SAAU,KAAK;oBACnB,gBAAgB,wHAAA,CAAA,cAAW,CAAC,kBAAkB,EAAE,OAAO;oBACvD,OAAO;gBACX;YACJ;QACJ;IACJ;IAEA,IAAI,iBAAiB,CAAC,KAAa;QAC/B,IAAI,eAAe,OAAO,CAAC,gBAAgB,MAAM;YAC7C,OAAO,QAAQ,OAAO;QAC1B;QAEA,CAAA,GAAA,0HAAA,CAAA,8BAA2B,AAAD,EAAE,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD,EAAqB,0HAAA,CAAA,4BAAyB,EAAE,EAAE;QAE9F,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,IAAI,OAAO;gBACP;gBACA,OAAO,OAAO,UAAU,WAAW,KAAK,SAAS,CAAC,SAAS,MAAM,QAAQ;gBACzE,SAAS,OAAO,cAAc,CAAC,OAAO,CAAC;YAC3C;YAEA,0HAAA,CAAA,kBAAe,CAAC,WAAW,CAAC;gBACxB,MAAM,wHAAA,CAAA,cAAW,CAAC,gBAAgB;gBAClC,MAAM;gBACN,SAAS;oBACL;gBACJ;gBACA,QAAQ,CAAC;oBACL,gBAAgB,wHAAA,CAAA,cAAW,CAAC,gBAAgB,EAAE,OAAO;gBACzD;YACJ;QACJ;IACJ;IAEA,IAAI,cAAc;QACd,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,QAAQ,cAAc,CAAC;gBACnB,MAAM,wHAAA,CAAA,cAAW,CAAC,aAAa;gBAC/B,MAAM;gBACN,SAAS,SAAU,IAAI;oBACnB,oBAAoB,QAAQ,QAAQ,QAAQ,KAAK,GAAG,CAAC,wIAAA,CAAA,eAAY;gBACrE;gBACA,QAAQ,SAAU,KAAK;oBACnB,gBAAgB,wHAAA,CAAA,cAAW,CAAC,aAAa,EAAE,OAAO;gBACtD;YACJ;QACJ;IACJ;IAEA,IAAI,2BAA2B,CAAC,YAAoB,MAAa;QAC7D,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,IAAI,SAAS,IAAI;YACjB,IAAI,QAAQ,IAAI;gBACZ,OAAO,GAAG,CAAC,SAAS,KAAK,WAAW;gBACpC,OAAO,GAAG,CAAC,OAAO,GAAG,WAAW;YACpC;YAEA,IAAI,WAAW,CAAA,GAAA,qHAAA,CAAA,wBAAqB,AAAD,MAAM,eAAe,OAAO,CAAC,cAAc;YAC9E,IAAI,gBAAgB,WAAW;gBAAE,aAAa;YAAS,IAAI,CAAC;YAE5D,QAAQ,cAAc,CAAC;gBACnB,kBAAkB,GAAG,iBAAiB,mBAAmB,EAAE,WAAW,CAAC,EAAE,OAAO,QAAQ,IAAI;gBAC5F,MAAM,wHAAA,CAAA,cAAW,CAAC,4BAA4B;gBAC9C,eAAe;gBACf,MAAM;gBACN,SAAS,SAAU,IAAI;oBACnB,oBAAoB,QAAQ,QAAQ,QAAQ,CAAA,GAAA,wIAAA,CAAA,4BAAyB,AAAD,EAAE;gBAC1E;gBACA,QAAQ,SAAU,KAAK;oBACnB,gBAAgB,wHAAA,CAAA,cAAW,CAAC,4BAA4B,EAAE,OAAO;oBACjE,OAAO;gBACX;YACJ;QACJ;IACJ;IAEA,IAAI,oBAAoB,CAAC,OAA2B,MAA0B,QAAgB;QAC1F,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,IAAI,OAAO;gBACP,OAAO;gBACP,MAAM;gBACN,QAAQ;gBACR,WAAW;YACf;YAEA,0HAAA,CAAA,kBAAe,CAAC,WAAW,CAAC;gBACxB,MAAM,wHAAA,CAAA,cAAW,CAAC,iBAAiB;gBACnC,MAAM;gBACN,SAAS;oBACL;gBACJ;gBACA,QAAQ,SAAU,KAAK;oBACnB,gBAAgB,wHAAA,CAAA,cAAW,CAAC,iBAAiB,EAAE,OAAO;oBACtD,OAAO;gBACX;YACJ;QACJ;IACJ;IAEA,IAAI,oBAAoB,CAAC,SAAiB;QACtC,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,IAAI,eAAe,IAAI,KAAK,WAAW,WAAW;YAElD,QAAQ,cAAc,CAAC;gBACnB,MAAM,wHAAA,CAAA,cAAW,CAAC,mBAAmB;gBACrC,kBAAkB,CAAA,GAAA,4HAAA,CAAA,cAAW,AAAD,EAAE,iBAAiB,CAAC,QAAQ,EAAE,QAAQ,SAAS,EAAE,eAAe,CAAC,WAAW,EAAE,cAAc,GAAG,IAAI;gBAC/H,MAAM;gBACN,SAAS,SAAU,IAAI;oBACnB,IAAI,CAAC,MAAM;wBACP,QAAQ;4BACJ,MAAM;gCACF,KAAK;4BACT;4BACA,SAAS;gCACL,QAAQ;gCACR,YAAY;gCACZ,OAAO;gCACP,QAAQ;4BACZ;4BACA,UAAU;gCACN,QAAQ;gCACR,YAAY;gCACZ,OAAO;gCACP,QAAQ;4BACZ;4BACA,YAAY,EAAE;4BACd,WAAW,EAAE;4BACb,WAAW,IAAI;wBACnB;wBACA;oBACJ;oBACA,QAAQ,CAAA,GAAA,wIAAA,CAAA,sBAAmB,AAAD,EAAE;gBAChC;gBACA,QAAQ,SAAU,KAAK;oBACnB,gBAAgB,wHAAA,CAAA,cAAW,CAAC,mBAAmB,EAAE,OAAO;wBAAE;wBAAS,WAAW;oBAAa;oBAC3F,OAAO;gBACX;YACJ;QACJ;IACJ;IAEA,IAAI,qBAAqB;QACrB,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,IAAI,WAAW,eAAe,OAAO,CAAC;YACtC,IAAI,CAAC,UAAU;gBACX,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;gBACA;YACJ;YAEA,QAAQ,cAAc,CAAC;gBACnB,MAAM,wHAAA,CAAA,cAAW,CAAC,oBAAoB;gBACtC,MAAM;gBACN,kBAAkB,GAAG,iBAAiB,aAAa,CAAC;gBACpD,eAAe;oBACX,aAAa;gBACjB;gBACA,SAAS,CAAC;oBACN,QAAQ,CAAA,GAAA,wIAAA,CAAA,uBAAoB,AAAD,EAAE;gBACjC;gBACA,QAAQ,CAAC;oBACL,gBAAgB,wHAAA,CAAA,cAAW,CAAC,oBAAoB,EAAE,OAAO;oBACzD,OAAO;gBACX;YACJ;QACJ;IACJ;IAEA,IAAI,qBAAqB,CAAC;QACtB,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,IAAI,WAAW,eAAe,OAAO,CAAC;YACtC,IAAI,CAAC,UAAU;gBACX,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;gBACA;YACJ;YAEA,QAAQ,cAAc,CAClB;gBACI,MAAM,wHAAA,CAAA,cAAW,CAAC,oBAAoB;gBACtC,MAAM;gBACN,eAAe;gBACf,kBAAkB,GAAG,iBAAiB,aAAa,CAAC;gBACpD,eAAe;oBACX,aAAa;oBACb,gBAAgB;gBACpB;gBACA,SAAS;oBACL;gBACJ;gBACA,QAAQ,CAAC;oBACL,gBAAgB,wHAAA,CAAA,cAAW,CAAC,oBAAoB,EAAE,OAAO;oBACzD,OAAO;gBACX;YACJ,GACA,KAAK,SAAS,CAAC;QAEvB;IACJ;IAEA,IAAI,WAAW,CAAC;QACZ,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,QAAQ,cAAc,CAAC;gBACnB,MAAM,wHAAA,CAAA,cAAW,CAAC,aAAa;gBAC/B,MAAM;gBACN,kBAAkB,CAAC,qCAAqC,EAAE,MAAM;gBAChE,SAAS,CAAC;oBACN,QAAQ;gBACZ;gBACA,QAAQ,CAAC;oBACL,gBAAgB,wHAAA,CAAA,cAAW,CAAC,aAAa,EAAE,OAAO;oBAClD,OAAO;gBACX;YACJ;QACJ;IACJ;IAEA,IAAI,qBAAqB;QACrB,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,IAAI,WAAW,eAAe,OAAO,CAAC;YACtC,IAAI,CAAC,UAAU;gBACX,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;gBACA;YACJ;YAEA,QAAQ,cAAc,CAClB;gBACI,MAAM,wHAAA,CAAA,cAAW,CAAC,oBAAoB;gBACtC,MAAM;gBACN,eAAe;gBACf,kBAAkB,GAAG,iBAAiB,kBAAkB,CAAC;gBACzD,eAAe;oBACX,aAAa;oBACb,gBAAgB;gBACpB;gBACA,SAAS,CAAA;oBACL,aAAa,OAAO,CAAC,0HAAA,CAAA,wBAAqB,EAAE,KAAK,SAAS,CAAC;oBAC3D,QAAQ,CAAA,GAAA,wIAAA,CAAA,uBAAoB,AAAD,EAAE;gBACjC;gBACA,QAAQ,CAAC;oBACL,gBAAgB,wHAAA,CAAA,cAAW,CAAC,oBAAoB,EAAE,OAAO;oBACzD,OAAO;gBACX;YACJ,GACA,KAAK,SAAS,CAAC,6HAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAA,OAAQ,KAAK,SAAS;QAE/D;IACJ;IAEA;;;KAGC,GACD,IAAI,6BAA6B,CAAC,UAAgD;QAC9E,IAAI,sBAAsB,aAAa,OAAO,CAAC,0HAAA,CAAA,wBAAqB;QACpE,IAAI,qBAAqB;YACrB,IAAI;gBACA,SAAS,CAAA,GAAA,wIAAA,CAAA,uBAAoB,AAAD,EAAE,KAAK,KAAK,CAAC;YAC7C,EAAE,OAAM;gBACJ,SAAS,EAAE;YACf;QACJ;QACA,qBAAqB,IAAI,CAAC,CAAA;YACtB,SAAS;QACb,GAAG,KAAK,CAAC;YACL;QACJ;IACJ;IAEA,IAAI,iBAAiB;QACjB,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,0HAAA,CAAA,kBAAe,CAAC,WAAW,CAAC;gBACxB,MAAM,wHAAA,CAAA,cAAW,CAAC,eAAe;gBACjC,MAAM;gBACN,SAAS;oBACL;gBACJ;gBACA,QAAQ,CAAC;oBACL,gBAAgB,wHAAA,CAAA,cAAW,CAAC,eAAe,EAAE,OAAO;oBACpD,OAAO;gBACX;YACJ;QACJ;IACJ;IAEA,IAAI,eAAe,CAAC;QAChB,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,QAAQ,cAAc,CAClB;gBACI,MAAM,wHAAA,CAAA,cAAW,CAAC,cAAc;gBAChC,eAAe;gBACf,eAAe;oBACX,gBAAgB;gBACpB;gBACA,MAAM;gBACN,SAAS,CAAA;oBACL,QAAQ;gBACZ;gBACA,QAAQ,CAAC;oBACL,gBAAgB,wHAAA,CAAA,cAAW,CAAC,cAAc,EAAE,OAAO;oBACnD,OAAO;gBACX;YACJ,GACA,KAAK,SAAS,CAAC,MAAM,GAAG,CAAC,CAAA,OAAQ,KAAK,GAAG;QAEjD;IACJ;IAEA,IAAI,cAAc,CAAC,SAAyB;QACxC,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,QAAQ,cAAc,CAClB;gBACI,MAAM,wHAAA,CAAA,cAAW,CAAC,YAAY;gBAC9B,eAAe;gBACf,kBAAkB,GAAG,iBAAiB,OAAO,CAAC;gBAC9C,eAAe;oBACX,gBAAgB;gBACpB;gBACA,MAAM;gBACN,SAAS,CAAA;oBACL,QAAQ;gBACZ;gBACA,QAAQ,CAAC;oBACL,gBAAgB,wHAAA,CAAA,cAAW,CAAC,YAAY,EAAE,OAAO;wBAAE;wBAAS;oBAAO;oBACnE,OAAO;gBACX;YACJ,GACA,KAAK,SAAS,CAAC;gBAAE,SAAS;gBAAQ,SAAS;YAAQ;QAE3D;IACJ;IAEA,IAAI,kBAAkB,CAAC;QACnB,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,QAAQ,cAAc,CAAC;gBACnB,MAAM,wHAAA,CAAA,cAAW,CAAC,aAAa;gBAC/B,kBAAkB,GAAG,iBAAiB,MAAM,EAAE,IAAI,QAAQ,CAAC;gBAC3D,MAAM;gBACN,SAAS,CAAA;oBACL,QAAQ,KAAK,GAAG,CAAC,CAAA,OAAQ,CAAA,GAAA,wIAAA,CAAA,YAAS,AAAD,EAAE;gBACvC;gBACA,QAAQ,CAAC;oBACL,gBAAgB,wHAAA,CAAA,cAAW,CAAC,aAAa,EAAE,OAAO;oBAClD,OAAO;gBACX;YACJ;QACJ;IACJ;IAEA,IAAI,kBAAkB,CAAC;QACnB,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,QAAQ,cAAc,CAAC;gBACnB,MAAM,wHAAA,CAAA,cAAW,CAAC,aAAa;gBAC/B,kBAAkB,GAAG,iBAAiB,cAAc,EAAE,IAAI,KAAK,CAAC;gBAChE,MAAM;gBACN,SAAS,CAAA;oBACL,QAAQ,KAAK,GAAG,CAAC,wIAAA,CAAA,oBAAiB;gBACtC;gBACA,QAAQ,CAAC;oBACL,gBAAgB,wHAAA,CAAA,cAAW,CAAC,aAAa,EAAE,OAAO;oBAClD,OAAO;gBACX;YACJ;QACJ;IACJ;IAEA,IAAI,eAAe,CAAC,OAAa;QAC7B,IAAI,SAAS,IAAI;QACjB,OAAO,GAAG,CAAC,QAAQ,MAAM,WAAW;QACpC,OAAO,GAAG,CAAC,MAAM,IAAI,WAAW;QAEhC,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,QAAQ,cAAc,CAAC;gBACnB,MAAM,wHAAA,CAAA,cAAW,CAAC,UAAU;gBAC5B,kBAAkB,GAAG,iBAAiB,OAAO,EAAE,OAAO,QAAQ,IAAI;gBAClE,MAAM;gBACN,SAAS,CAAA;oBACL,QAAQ,KAAK,GAAG,CAAC,wIAAA,CAAA,iBAAc;gBACnC;gBACA,QAAQ,CAAC;oBACL,qCAAqC;oBACrC,gEAAgE;oBAChE,OAAO;gBACX;YACJ;QACJ;IACJ;IAEA,IAAI,kBAAkB;QAClB,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,IAAI,WAAW,eAAe,OAAO,CAAC;YACtC,IAAI,CAAC,UAAU;gBACX,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;gBACA;YACJ;YAEA,QAAQ,cAAc,CAAC;gBACnB,MAAM,wHAAA,CAAA,cAAW,CAAC,gBAAgB;gBAClC,eAAe;oBACX,aAAa;oBACb,gBAAgB;gBACpB;gBACA,kBAAkB,GAAG,iBAAiB,qBAAqB,CAAC;gBAC5D,MAAM;gBACN,SAAS,CAAC;oBACN,IAAI,CAAC,MAAM;wBACP,OAAO,EAAE;oBACb;oBACA,QAAQ,KAAK,GAAG,CAAC,wIAAA,CAAA,mBAAgB;gBACrC;gBACA,QAAQ,CAAC;oBACL,gBAAgB,wHAAA,CAAA,cAAW,CAAC,sBAAsB,EAAE,OAAO;oBAC3D,OAAO;gBACX;YACJ;QACJ;IACJ;IAEA,IAAI,qBAAqB;QACrB,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,IAAI,WAAW,eAAe,OAAO,CAAC;YACtC,IAAI,CAAC,UAAU;gBACX,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;gBACA;YACJ;YACA,QAAQ,cAAc,CAAC;gBACnB,MAAM,wHAAA,CAAA,cAAW,CAAC,cAAc;gBAChC,kBAAkB,GAAG,iBAAiB,UAAU,CAAC;gBACjD,eAAe;oBACX,aAAa;oBACb,gBAAgB;gBACpB;gBACA,MAAM;gBACN,SAAS,CAAA;oBACL,QAAQ,OAAO,AAAC,KAAuB,KAAK,CAAC,KAAK,GAAG,CAAC,KAAK,MAAM,GAAG,IAAI,IAAI,GAAG,CAAC,wIAAA,CAAA,qBAAkB,IAAI,EAAE;gBAC5G;gBACA,QAAQ,CAAC;oBACL,gBAAgB,wHAAA,CAAA,cAAW,CAAC,cAAc,EAAE;oBAC5C,OAAO;gBACX;YACJ;QACJ;IACJ;IAEA,IAAI,mBAAmB,CAAC,YAAoB,OAAuB,cAA4B,EAAE,EAAE;QAC/F,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,IAAI,WAAW,eAAe,OAAO,CAAC;YACtC,IAAI,CAAC,UAAU;gBACX,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;gBACA;YACJ;YACA,QAAQ,cAAc,CAClB;gBACI,MAAM,wHAAA,CAAA,cAAW,CAAC,kBAAkB;gBACpC,eAAe;gBACf,kBAAkB,GAAG,iBAAiB,OAAO,CAAC;gBAC9C,eAAe;oBACX,aAAa;oBACb,gBAAgB;gBACpB;gBACA,MAAM;gBACN,SAAS;oBACL;gBACJ;gBACA,QAAQ,CAAC;oBACL,gBAAgB,wHAAA,CAAA,cAAW,CAAC,kBAAkB,EAAE;oBAChD,OAAO;gBACX;YACJ,GACA,KAAK,SAAS,CAAC;gBACX;oBACI,YAAY;oBACZ,MAAM;oBACN,OAAO;oBACP,aAAa;gBACjB;aACH;QAET;IACJ;IAEA,IAAI,mBAAmB,CAAC;QACpB,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,IAAI,WAAW,eAAe,OAAO,CAAC;YACtC,IAAI,CAAC,UAAU;gBACX,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;gBACA;YACJ;YACA,QAAQ,cAAc,CAAC;gBACnB,MAAM,wHAAA,CAAA,cAAW,CAAC,kBAAkB;gBACpC,eAAe;gBACf,kBAAkB,GAAG,iBAAiB,QAAQ,EAAE,SAAS;gBACzD,eAAe;oBACX,aAAa;oBACb,gBAAgB;gBACpB;gBACA,MAAM;gBACN,SAAS;oBACL;gBACJ;gBACA,QAAQ,CAAC;oBACL,gBAAgB,wHAAA,CAAA,cAAW,CAAC,kBAAkB,EAAE,OAAO;oBACvD,OAAO;gBACX;YACJ;QACJ;IACJ;IAEA,IAAI,iBAAiB,CAAC,SAAkB;QACpC,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,IAAI,WAAW,eAAe,OAAO,CAAC;YACtC,IAAI,CAAC,UAAU;gBACX,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;gBACA;YACJ;YACA,IAAI,SAAS,IAAI;YACjB,IAAI,QAAQ;gBACR,SAAS,IAAI,gBAAgB;oBACzB,SAAS,KAAK,SAAS,CAAC;gBAC5B;YACJ;YAEA,QAAQ,cAAc,CAAC;gBACnB,MAAM,wHAAA,CAAA,cAAW,CAAC,UAAU;gBAC5B,kBAAkB,GAAG,iBAAiB,OAAO,EAAE,UAAU,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,OAAO,QAAQ,IAAI,GAAG,IAAI;gBAC9G,MAAM;gBACN,eAAe;oBACX,aAAa;oBACb,gBAAgB;gBACpB;gBACA,SAAS,CAAA;oBACL,QAAQ,OAAO,KAAK,GAAG,CAAC,wIAAA,CAAA,mBAAgB,IAAI,EAAE;gBAClD;gBACA,QAAQ,CAAC;oBACL,gBAAgB,wHAAA,CAAA,cAAW,CAAC,UAAU,EAAE;oBACxC,OAAO;gBACX;YACJ;QACJ;IACJ;IAEA,IAAI,yBAAyB;QACzB,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,IAAI,WAAW,eAAe,OAAO,CAAC;YACtC,IAAI,CAAC,UAAU;gBACX,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;gBACA;YACJ;YAEA,QAAQ,cAAc,CAAC;gBACnB,MAAM,wHAAA,CAAA,cAAW,CAAC,wBAAwB;gBAC1C,kBAAkB,GAAG,iBAAiB,sBAAsB,CAAC;gBAC7D,MAAM;gBACN,eAAe;oBACX,aAAa;oBACb,gBAAgB;gBACpB;gBACA,SAAS,CAAA;oBACL,QAAQ,OAAO,OAAO,EAAE;gBAC5B;gBACA,QAAQ,CAAC;oBACL,gBAAgB,wHAAA,CAAA,cAAW,CAAC,wBAAwB,EAAE;oBACtD,OAAO;gBACX;YACJ;QACJ;IACJ;IAEA,IAAI,wBAAwB,CAAC;QACzB,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,IAAI,WAAW,eAAe,OAAO,CAAC;YACtC,IAAI,CAAC,UAAU;gBACX,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;gBACA;YACJ;YAEA,QAAQ,cAAc,CAClB;gBACI,MAAM,wHAAA,CAAA,cAAW,CAAC,wBAAwB;gBAC1C,kBAAkB,GAAG,iBAAiB,sBAAsB,CAAC;gBAC7D,eAAe;gBACf,MAAM;gBACN,eAAe;oBACX,aAAa;oBACb,gBAAgB;gBACpB;gBACA,SAAS,CAAA;oBACL,QAAQ,OAAO,OAAO,EAAE;gBAC5B;gBACA,QAAQ,CAAC;oBACL,gBAAgB,wHAAA,CAAA,cAAW,CAAC,wBAAwB,EAAE;oBACtD,OAAO;gBACX;YACJ,GACA,KAAK,SAAS,CAAC;QAEvB;IACJ;IAEA,IAAI,2BAA2B,CAAC;QAC5B,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,IAAI,WAAW,eAAe,OAAO,CAAC;YACtC,IAAI,CAAC,UAAU;gBACX,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;gBACA;YACJ;YAEA,QAAQ,cAAc,CAClB;gBACI,MAAM,wHAAA,CAAA,cAAW,CAAC,wBAAwB;gBAC1C,kBAAkB,GAAG,iBAAiB,sBAAsB,CAAC;gBAC7D,eAAe;gBACf,MAAM;gBACN,eAAe;oBACX,aAAa;oBACb,gBAAgB;gBACpB;gBACA,SAAS,CAAA;oBACL,QAAQ,OAAO,OAAO,EAAE;gBAC5B;gBACA,QAAQ,CAAC;oBACL,gBAAgB,wHAAA,CAAA,cAAW,CAAC,wBAAwB,EAAE;oBACtD,OAAO;gBACX;YACJ,GACA,KAAK,SAAS,CAAC;QAEvB;IACJ;IAEA,IAAI,2BAA2B,CAAC;QAC5B,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,IAAI,WAAW,eAAe,OAAO,CAAC;YACtC,IAAI,CAAC,UAAU;gBACX,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;gBACA;YACJ;YAEA,QAAQ,cAAc,CAClB;gBACI,MAAM,wHAAA,CAAA,cAAW,CAAC,0BAA0B;gBAC5C,kBAAkB,GAAG,iBAAiB,sBAAsB,CAAC;gBAC7D,eAAe;gBACf,MAAM;gBACN,eAAe;oBACX,aAAa;oBACb,gBAAgB;gBACpB;gBACA,SAAS,CAAA;oBACL,QAAQ,OAAO,OAAO,EAAE;gBAC5B;gBACA,QAAQ,CAAC;oBACL,gBAAgB,wHAAA,CAAA,cAAW,CAAC,wBAAwB,EAAE;oBACtD,OAAO;gBACX;YACJ,GACA,KAAK,SAAS,CAAC;QAEvB;IACJ;IAEA,IAAI,uBAAuB,CAAC;QACxB,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,IAAI,WAAW,eAAe,OAAO,CAAC;YACtC,IAAI,CAAC,UAAU;gBACX,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;gBACA;YACJ;YAEA,QAAQ,cAAc,CAClB;gBACI,MAAM,wHAAA,CAAA,cAAW,CAAC,sBAAsB;gBACxC,kBAAkB,GAAG,iBAAiB,2BAA2B,CAAC;gBAClE,MAAM;gBACN,eAAe;gBACf,eAAe;oBACX,aAAa;oBACb,gBAAgB;gBACpB;gBACA,SAAS;oBACL;gBACJ;gBACA,QAAQ,CAAC;oBACL,gBAAgB,wHAAA,CAAA,cAAW,CAAC,sBAAsB,EAAE;oBACpD,OAAO;gBACX;YACJ,GACA,KAAK,SAAS,CAAC;QAEvB;IACJ;IAEA,IAAI,+BAA+B;QAC/B,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,IAAI,WAAW,eAAe,OAAO,CAAC;YACtC,IAAI,CAAC,UAAU;gBACX,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;gBACA;YACJ;YAEA,QAAQ,cAAc,CAAC;gBACnB,MAAM,wHAAA,CAAA,cAAW,CAAC,6BAA6B;gBAC/C,kBAAkB,GAAG,iBAAiB,4BAA4B,CAAC;gBACnE,eAAe;gBACf,MAAM;gBACN,eAAe;oBACX,aAAa;oBACb,gBAAgB;gBACpB;gBACA,SAAS,CAAA;oBACL,QAAQ,OAAO,OAAO,EAAE;gBAC5B;gBACA,QAAQ,CAAC;oBACL,gBAAgB,wHAAA,CAAA,cAAW,CAAC,6BAA6B,EAAE;oBAC3D,OAAO;gBACX;YACJ;QACJ;IACJ;IAEA,IAAI,iCAAiC,CAAC;QAClC,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,IAAI,WAAW,eAAe,OAAO,CAAC;YACtC,IAAI,CAAC,UAAU;gBACX,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;gBACA;YACJ;YAEA,QAAQ,cAAc,CAClB;gBACI,MAAM,wHAAA,CAAA,cAAW,CAAC,6BAA6B;gBAC/C,kBAAkB,GAAG,iBAAiB,4BAA4B,CAAC;gBACnE,eAAe;gBACf,MAAM;gBACN,eAAe;oBACX,aAAa;oBACb,gBAAgB;gBACpB;gBACA,SAAS,CAAA;oBACL,QAAQ;gBACZ;gBACA,QAAQ,CAAC;oBACL,gBAAgB,wHAAA,CAAA,cAAW,CAAC,6BAA6B,EAAE;oBAC3D,OAAO;gBACX;YACJ,GACA,KAAK,SAAS,CAAC;QAEvB;IACJ;IAEA,IAAI,iCAAiC,CAAC;QAClC,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,IAAI,WAAW,eAAe,OAAO,CAAC;YACtC,IAAI,CAAC,UAAU;gBACX,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;gBACA;YACJ;YAEA,QAAQ,cAAc,CAClB;gBACI,MAAM,wHAAA,CAAA,cAAW,CAAC,gCAAgC;gBAClD,kBAAkB,GAAG,iBAAiB,4BAA4B,CAAC;gBACnE,eAAe;gBACf,MAAM;gBACN,eAAe;oBACX,aAAa;oBACb,gBAAgB;gBACpB;gBACA,SAAS;oBACL;gBACJ;gBACA,QAAQ,CAAC;oBACL,gBAAgB,wHAAA,CAAA,cAAW,CAAC,gCAAgC,EAAE;oBAC9D,OAAO;gBACX;YACJ,GACA,KAAK,SAAS,CAAC;QAEvB;IACJ;IAEA,IAAI,sBAAsB;QACtB,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,0HAAA,CAAA,kBAAe,CAAC,WAAW,CAAC;gBACxB,MAAM,wHAAA,CAAA,cAAW,CAAC,qBAAqB;gBACvC,MAAM;gBACN,SAAS,CAAC;oBACN,QAAQ;gBACZ;gBACA,QAAQ,CAAC;oBACL,gBAAgB,wHAAA,CAAA,cAAW,CAAC,qBAAqB,EAAE,OAAO;oBAC1D,OAAO;gBACX;YACJ;QACJ;IACJ;IAEA,IAAI,aAAa,CAAC;QACd,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,0HAAA,CAAA,kBAAe,CAAC,WAAW,CAAC;gBACxB,MAAM,wHAAA,CAAA,cAAW,CAAC,WAAW;gBAC7B,MAAM;oBAAE;gBAAW;gBACnB,SAAS;oBACL;gBACJ;gBACA,QAAQ,CAAC;oBACL,gBAAgB,wHAAA,CAAA,cAAW,CAAC,WAAW,EAAE,OAAO;oBAChD,OAAO;gBACX;YACJ;QACJ;IACJ;IAEA,IAAI,eAAe,CAAC,YAAoB,cAAsB,EAAE;QAC5D,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,0HAAA,CAAA,kBAAe,CAAC,WAAW,CAAC;gBACxB,MAAM,wHAAA,CAAA,cAAW,CAAC,aAAa;gBAC/B,MAAM;oBAAE;oBAAY;gBAAY;gBAChC,SAAS,CAAC;oBACN,QAAQ;gBACZ;gBACA,QAAQ,CAAC;oBACL,gBAAgB,wHAAA,CAAA,cAAW,CAAC,aAAa,EAAE,OAAO;oBAClD,OAAO;gBACX;YACJ;QACJ;IACJ;IAEA,IAAI,0BAA0B,CAAC,SAAiB;QAC5C,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,IAAI,WAAW,eAAe,OAAO,CAAC;YACtC,IAAI,CAAC,UAAU;gBACX,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;gBACA;YACJ;YAEA,IAAI,SAAS,IAAI;YACjB,IAAI,cAAc,OAAO,IAAI,CAAC,YAAY,MAAM,GAAG,GAAG;gBAClD,SAAS,IAAI,gBAAgB;YACjC;YAEA,QAAQ,cAAc,CAAC;gBACnB,MAAM,wHAAA,CAAA,cAAW,CAAC,iBAAiB;gBACnC,kBAAkB,GAAG,iBAAiB,cAAc,EAAE,QAAQ,kBAAkB,EAAE,OAAO,QAAQ,IAAI;gBACrG,eAAe;gBACf,MAAM;gBACN,eAAe;oBACX,aAAa;oBACb,gBAAgB;gBACpB;gBACA,SAAS,CAAA;oBACL,QAAQ,CAAA,GAAA,wIAAA,CAAA,wBAAqB,AAAD,EAAE;gBAClC;gBACA,QAAQ,CAAC;oBACL,gBAAgB,wHAAA,CAAA,cAAW,CAAC,iBAAiB,EAAE,OAAO;wBAAE;wBAAS;oBAAW;oBAC5E,OAAO;gBACX;YACJ;QACJ;IACJ;IAEA,IAAI,6BAA6B,CAAC,SAAiB,YAAwB,mBAA2B;QAClG,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,IAAI,WAAW,eAAe,OAAO,CAAC;YACtC,IAAI,CAAC,UAAU;gBACX,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;gBACA;YACJ;YAEA,QAAQ,cAAc,CAClB;gBACI,MAAM,wHAAA,CAAA,cAAW,CAAC,wBAAwB;gBAC1C,kBAAkB,GAAG,iBAAiB,cAAc,EAAE,QAAQ,eAAe,CAAC;gBAC9E,eAAe;gBACf,MAAM;gBACN,eAAe;oBACX,aAAa;oBACb,gBAAgB;gBACpB;gBACA,SAAS;oBACL;gBACJ;gBACA,QAAQ,CAAC;oBACL,gBAAgB,wHAAA,CAAA,cAAW,CAAC,wBAAwB,EAAE,OAAO;wBAAE;wBAAS;oBAAW;oBACnF,OAAO;gBACX;YACJ,GACA,KAAK,SAAS,CAAC;gBACX,SAAS;gBACT,mBAAmB;gBACnB,OAAO,MAAM,MAAM,GAAG,IAAI,MAAM,QAAQ,KAAK;YACjD;QAER;IACJ;IAEA,IAAI,qBAAqB;QACrB,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,IAAI,WAAW,eAAe,OAAO,CAAC;YACtC,IAAI,CAAC,UAAU;gBACX,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;gBACA;YACJ;YAEA,QAAQ,cAAc,CAAC;gBACnB,MAAM,wHAAA,CAAA,cAAW,CAAC,oBAAoB;gBACtC,kBAAkB,GAAG,iBAAiB,YAAY,CAAC;gBACnD,eAAe;gBACf,MAAM;gBACN,eAAe;oBACX,aAAa;oBACb,gBAAgB;gBACpB;gBACA,SAAS,CAAA;oBACL,QAAQ;gBACZ;gBACA,QAAQ,CAAC;oBACL,gBAAgB,wHAAA,CAAA,cAAW,CAAC,oBAAoB,EAAE;oBAClD,OAAO;gBACX;YACJ;QACJ;IACJ;IAEA,IAAI,8BAA8B,CAAC,aAAqB;QACpD,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,QAAQ,cAAc,CAAC;gBACnB,MAAM,wHAAA,CAAA,cAAW,CAAC,6BAA6B;gBAC/C,kBAAkB,GAAG,iBAAiB,sBAAsB,EAAE,aAAa;gBAC3E,eAAe;gBACf,MAAM;gBACN,eAAe;oBACX,aAAa;oBACb,gBAAgB;gBACpB;gBACA,SAAS,CAAA;oBACL,QAAQ,CAAA,GAAA,wIAAA,CAAA,uBAAoB,AAAD,EAAE;gBACjC;gBACA,QAAQ,CAAC;oBACL,gBAAgB,wHAAA,CAAA,cAAW,CAAC,6BAA6B,EAAE;oBAC3D,OAAO;gBACX;YACJ;QACJ;IACJ;IAEA,IAAI,0BAA0B;QAC1B,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,IAAI,WAAW,eAAe,OAAO,CAAC;YACtC,IAAI,CAAC,UAAU;gBACX,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;gBACA;YACJ;YAEA,QAAQ,cAAc,CAAC;gBACnB,MAAM,wHAAA,CAAA,cAAW,CAAC,2BAA2B;gBAC7C,kBAAkB,GAAG,iBAAiB,qBAAqB,CAAC;gBAC5D,eAAe;gBACf,MAAM;gBACN,eAAe;oBACX,aAAa;oBACb,gBAAgB;gBACpB;gBACA,SAAS,CAAC,gBAAqB,EAAE;oBAC7B,QAAQ,cAAc,GAAG,CAAC,wIAAA,CAAA,2BAAwB;gBACtD;gBACA,QAAQ,CAAC;oBACL,gBAAgB,wHAAA,CAAA,cAAW,CAAC,2BAA2B,EAAE;oBACzD,OAAO;gBACX;YACJ;QACJ;IACJ;IAEA,IAAI,4BAA4B,CAAC;QAC7B,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,IAAI,WAAW,eAAe,OAAO,CAAC;YACtC,IAAI,CAAC,UAAU;gBACX,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;gBACA;YACJ;YAEA,QAAQ,cAAc,CAAC;gBACnB,MAAM,wHAAA,CAAA,cAAW,CAAC,2BAA2B;gBAC7C,kBAAkB,GAAG,iBAAiB,sBAAsB,EAAE,IAAI;gBAClE,eAAe;gBACf,MAAM;gBACN,eAAe;oBACX,aAAa;oBACb,gBAAgB;gBACpB;gBACA,SAAS;oBACL;gBACJ;gBACA,QAAQ,CAAC;oBACL,gBAAgB,wHAAA,CAAA,cAAW,CAAC,2BAA2B,EAAE;oBACzD,OAAO;gBACX;YACJ;QACJ;IACJ;IAEA,IAAI,uBAAuB,CAAC;QACxB,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,QAAQ,cAAc,CAAC;gBACnB,MAAM,wHAAA,CAAA,cAAW,CAAC,yBAAyB;gBAC3C,kBAAkB,GAAG,iBAAiB,OAAO,EAAE,QAAQ,aAAa,CAAC;gBACrE,MAAM;gBACN,SAAS,CAAA;oBACL,QAAQ,CAAA,GAAA,wIAAA,CAAA,4BAAyB,AAAD,EAAE;gBACtC;gBACA,QAAQ,CAAC;oBACL,gBAAgB,wHAAA,CAAA,cAAW,CAAC,yBAAyB,EAAE,OAAO;oBAC9D,OAAO;gBACX;YACJ;QACJ;IACJ;IAEA,OAAO;QACH;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACJ;AACJ;AAEA,IAAI,MAAM;uCAEK", "debugId": null}}, {"offset": {"line": 5962, "column": 0}, "map": {"version": 3, "sources": ["file:///app/utils/CacheUtils.tsx"], "sourcesContent": ["import { get, set, clear } from 'idb-keyval'\nimport api from '../api/ApiHelper'\nimport { isClientSideRendering } from './SSRUtils'\n\nlet cacheUtils: CacheUtils = {\n    getFromCache: function (type: string, data: string) {\n        return new Promise((resolve, reject) => {\n            if (!isClientSideRendering()) {\n                resolve(null)\n            }\n            get(type + data)\n                .then(response => {\n                    if (response) {\n                        let parsed = JSON.parse(response as string) as CacheEntry\n                        if (parsed.expireTimeStamp && new Date().getTime() < parsed.expireTimeStamp) {\n                            resolve(parsed.response)\n                            return\n                        }\n                    }\n                    resolve(null)\n                })\n                .catch(() => {\n                    resolve(null)\n                })\n        })\n    },\n    setIntoCache: function (type: string, data: string, response: ApiResponse, maxAge: number = 0): void {\n        if (!isClientSideRendering()) {\n            return\n        }\n        let entry: CacheEntry = {\n            expireTimeStamp: new Date().getTime() + maxAge * 1000,\n            response: response\n        }\n        set(type + data, JSON.stringify(entry)).catch(() => {})\n    },\n    checkForCacheClear: function () {\n        if (!isClientSideRendering()) {\n            return\n        }\n        api.getVersion().then(version => {\n            let localVersion = window.localStorage.getItem('version')\n            if (window.caches !== undefined && localVersion !== version) {\n                // clear workbox caches\n                caches\n                    .keys()\n                    .then(keys => {\n                        keys.forEach(key => {\n                            caches.delete(key)\n                        })\n                    })\n                    .catch(() => {})\n                // clear index db\n                clear()\n            }\n            window.localStorage.setItem('version', version)\n        })\n    },\n    clearAll: function () {\n        if (!isClientSideRendering()) {\n            return\n        }\n        if (window.caches !== undefined) {\n            caches.keys().then(keys => {\n                keys.forEach(key => {\n                    caches.delete(key)\n                })\n            })\n            clear()\n        }\n    }\n}\nexport default cacheUtils\n\nexport function getCacheControlHeader() {\n    return 'public, max-age=60, s-maxage=20'\n}\n\ninterface CacheEntry {\n    expireTimeStamp: number\n    response: ApiResponse\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAEA,IAAI,aAAyB;IACzB,cAAc,SAAU,IAAY,EAAE,IAAY;QAC9C,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,IAAI,CAAC,CAAA,GAAA,qHAAA,CAAA,wBAAqB,AAAD,KAAK;gBAC1B,QAAQ;YACZ;YACA,CAAA,GAAA,iJAAA,CAAA,MAAG,AAAD,EAAE,OAAO,MACN,IAAI,CAAC,CAAA;gBACF,IAAI,UAAU;oBACV,IAAI,SAAS,KAAK,KAAK,CAAC;oBACxB,IAAI,OAAO,eAAe,IAAI,IAAI,OAAO,OAAO,KAAK,OAAO,eAAe,EAAE;wBACzE,QAAQ,OAAO,QAAQ;wBACvB;oBACJ;gBACJ;gBACA,QAAQ;YACZ,GACC,KAAK,CAAC;gBACH,QAAQ;YACZ;QACR;IACJ;IACA,cAAc,SAAU,IAAY,EAAE,IAAY,EAAE,QAAqB,EAAE,SAAiB,CAAC;QACzF,IAAI,CAAC,CAAA,GAAA,qHAAA,CAAA,wBAAqB,AAAD,KAAK;YAC1B;QACJ;QACA,IAAI,QAAoB;YACpB,iBAAiB,IAAI,OAAO,OAAO,KAAK,SAAS;YACjD,UAAU;QACd;QACA,CAAA,GAAA,iJAAA,CAAA,MAAG,AAAD,EAAE,OAAO,MAAM,KAAK,SAAS,CAAC,QAAQ,KAAK,CAAC,KAAO;IACzD;IACA,oBAAoB;QAChB,IAAI,CAAC,CAAA,GAAA,qHAAA,CAAA,wBAAqB,AAAD,KAAK;YAC1B;QACJ;QACA,oHAAA,CAAA,UAAG,CAAC,UAAU,GAAG,IAAI,CAAC,CAAA;YAClB,IAAI,eAAe,OAAO,YAAY,CAAC,OAAO,CAAC;YAC/C,IAAI,OAAO,MAAM,KAAK,aAAa,iBAAiB,SAAS;gBACzD,uBAAuB;gBACvB,OACK,IAAI,GACJ,IAAI,CAAC,CAAA;oBACF,KAAK,OAAO,CAAC,CAAA;wBACT,OAAO,MAAM,CAAC;oBAClB;gBACJ,GACC,KAAK,CAAC,KAAO;gBAClB,iBAAiB;gBACjB,CAAA,GAAA,iJAAA,CAAA,QAAK,AAAD;YACR;YACA,OAAO,YAAY,CAAC,OAAO,CAAC,WAAW;QAC3C;IACJ;IACA,UAAU;QACN,IAAI,CAAC,CAAA,GAAA,qHAAA,CAAA,wBAAqB,AAAD,KAAK;YAC1B;QACJ;QACA,IAAI,OAAO,MAAM,KAAK,WAAW;YAC7B,OAAO,IAAI,GAAG,IAAI,CAAC,CAAA;gBACf,KAAK,OAAO,CAAC,CAAA;oBACT,OAAO,MAAM,CAAC;gBAClB;YACJ;YACA,CAAA,GAAA,iJAAA,CAAA,QAAK,AAAD;QACR;IACJ;AACJ;uCACe;AAER,SAAS;IACZ,OAAO;AACX", "debugId": null}}, {"offset": {"line": 6048, "column": 0}, "map": {"version": 3, "sources": ["file:///app/utils/NotificationUtils.tsx"], "sourcesContent": ["import { toast } from 'react-toastify'\nimport cacheUtils from './CacheUtils'\nimport { canUseClipBoard, writeToClipboard } from './ClipboardUtils'\n\nexport default function registerNotificationCallback(router) {\n    let interval = setInterval(function () {\n        // wait until messaging is definded\n        let messaging = (window as any).messaging\n        if (typeof messaging == 'undefined') return\n        clearInterval(interval)\n\n        messaging.onMessage(function (payload) {\n            let notification = payload.notification\n            if (payload.data?.type === 'auction') {\n                savePayloadIntoCache(payload)\n            }\n            displayNotification(notification)\n        })\n    }, 10)\n\n    function displayNotification(notification: any) {\n        toast.info(notification.title + '\\n' + notification.body, {\n            onClick: () => {\n                if (canUseClipBoard()) {\n                    writeToClipboard('/viewauction ' + notification.click_action.split(/\\/auction\\//)[1])\n                }\n                router.push('/' + notification.click_action.match(/\\/\\/[^/]+\\/([^.]+)/)[1])\n            },\n            autoClose: 20000\n        })\n    }\n\n    function savePayloadIntoCache(payload: any) {\n        let auction = JSON.parse(payload.data.auction)\n        cacheUtils.setIntoCache('auctionDetails', JSON.stringify(auction.uuid), auction, 60)\n    }\n}\n\nexport function getNotificationTypeAsString(type: NotificationType | number): string {\n    switch (type) {\n        case 1:\n        case 'WEBHOOK':\n            return 'Webhook'\n        case 2:\n        case 'DISCORD':\n            return 'Discord'\n        case 3:\n        case 'DiscordWebhook':\n            return 'Discord Webhook'\n        case 4:\n        case 'FIREBASE':\n            return 'Push-Notification'\n        case 5:\n        case 'EMAIL':\n            return 'E-Mail'\n        case 6:\n        case 'InGame':\n            return 'InGame'\n        default:\n            return 'Unknown'\n    }\n}\n\nexport function getNotficationWhenEnumAsString(when: NotificationWhen | number): string {\n    switch (when) {\n        case 0:\n        case 'NEVER':\n            return 'Never'\n        case 1:\n        case 'AfterFail':\n            return 'After fail'\n        case 2:\n        case 'ALWAYS':\n            return 'Always'\n        default:\n            return 'Never'\n    }\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;AAEe,SAAS,6BAA6B,MAAM;IACvD,IAAI,WAAW,YAAY;QACvB,mCAAmC;QACnC,IAAI,YAAY,AAAC,OAAe,SAAS;QACzC,IAAI,OAAO,aAAa,aAAa;QACrC,cAAc;QAEd,UAAU,SAAS,CAAC,SAAU,OAAO;YACjC,IAAI,eAAe,QAAQ,YAAY;YACvC,IAAI,QAAQ,IAAI,EAAE,SAAS,WAAW;gBAClC,qBAAqB;YACzB;YACA,oBAAoB;QACxB;IACJ,GAAG;IAEH,SAAS,oBAAoB,YAAiB;QAC1C,sJAAA,CAAA,QAAK,CAAC,IAAI,CAAC,aAAa,KAAK,GAAG,OAAO,aAAa,IAAI,EAAE;YACtD,SAAS;gBACL,IAAI,CAAA,GAAA,2HAAA,CAAA,kBAAe,AAAD,KAAK;oBACnB,CAAA,GAAA,2HAAA,CAAA,mBAAgB,AAAD,EAAE,kBAAkB,aAAa,YAAY,CAAC,KAAK,CAAC,cAAc,CAAC,EAAE;gBACxF;gBACA,OAAO,IAAI,CAAC,MAAM,aAAa,YAAY,CAAC,KAAK,CAAC,qBAAqB,CAAC,EAAE;YAC9E;YACA,WAAW;QACf;IACJ;IAEA,SAAS,qBAAqB,OAAY;QACtC,IAAI,UAAU,KAAK,KAAK,CAAC,QAAQ,IAAI,CAAC,OAAO;QAC7C,uHAAA,CAAA,UAAU,CAAC,YAAY,CAAC,kBAAkB,KAAK,SAAS,CAAC,QAAQ,IAAI,GAAG,SAAS;IACrF;AACJ;AAEO,SAAS,4BAA4B,IAA+B;IACvE,OAAQ;QACJ,KAAK;QACL,KAAK;YACD,OAAO;QACX,KAAK;QACL,KAAK;YACD,OAAO;QACX,KAAK;QACL,KAAK;YACD,OAAO;QACX,KAAK;QACL,KAAK;YACD,OAAO;QACX,KAAK;QACL,KAAK;YACD,OAAO;QACX,KAAK;QACL,KAAK;YACD,OAAO;QACX;YACI,OAAO;IACf;AACJ;AAEO,SAAS,+BAA+B,IAA+B;IAC1E,OAAQ;QACJ,KAAK;QACL,KAAK;YACD,OAAO;QACX,KAAK;QACL,KAAK;YACD,OAAO;QACX,KAAK;QACL,KAAK;YACD,OAAO;QACX;YACI,OAAO;IACf;AACJ", "debugId": null}}, {"offset": {"line": 6137, "column": 0}, "map": {"version": 3, "sources": ["file:///app/utils/Parser/URLParser.tsx"], "sourcesContent": ["import { atobUnicode } from '../Base64Utils'\nimport { isClientSideRendering } from '../SSRUtils'\nimport { AppRouterInstance } from 'next/dist/shared/lib/app-router-context.shared-runtime'\n\nexport function parseItemFilter(itemFilterBase64: string): ItemFilter {\n    let itemFilter: any = JSON.parse(atobUnicode(itemFilterBase64))\n    if (!itemFilter) {\n        return {}\n    }\n    return itemFilter\n}\nexport function getItemFilterFromUrl(): ItemFilter {\n    if (!isClientSideRendering()) {\n        return {}\n    }\n\n    // backwards compatibility for old itemFilter\n    let itemFilterBase64 = getURLSearchParam('itemFilter')\n    if (itemFilterBase64) {\n        return parseItemFilter(itemFilterBase64)\n    }\n\n    let nonFilterParams = ['range', 'refId', 'conId']\n    let searchParams = new URLSearchParams(window.location.search)\n    let itemFilter: ItemFilter = {}\n    searchParams.forEach((value, key) => {\n        if (nonFilterParams.indexOf(key) === -1) {\n            itemFilter[key] = value\n        }\n    })\n    return itemFilter\n}\n\nexport function getURLSearchParam(key: string): string | null {\n    if (!isClientSideRendering()) {\n        return null\n    }\n    let searchParams = new URLSearchParams(window.location.search)\n    return searchParams.get(key)\n}\n\nexport function setFilterIntoUrlParams(router: AppRouterInstance, pathname: string, itemFilter: ItemFilter) {\n    if (isClientSideRendering()) {\n        let searchParams = new URLSearchParams()\n        Object.keys(itemFilter).forEach(key => {\n            searchParams.set(key, itemFilter[key])\n        })\n        router.replace(`${pathname}?${searchParams.toString()}`)\n        window.history.replaceState(null, '', `${pathname}?${searchParams.toString()}`)\n    } else {\n        console.error('Tried to update url query \"itemFilter\" during serverside rendering')\n    }\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAGO,SAAS,gBAAgB,gBAAwB;IACpD,IAAI,aAAkB,KAAK,KAAK,CAAC,CAAA,GAAA,wHAAA,CAAA,cAAW,AAAD,EAAE;IAC7C,IAAI,CAAC,YAAY;QACb,OAAO,CAAC;IACZ;IACA,OAAO;AACX;AACO,SAAS;IACZ,IAAI,CAAC,CAAA,GAAA,qHAAA,CAAA,wBAAqB,AAAD,KAAK;QAC1B,OAAO,CAAC;IACZ;IAEA,6CAA6C;IAC7C,IAAI,mBAAmB,kBAAkB;IACzC,IAAI,kBAAkB;QAClB,OAAO,gBAAgB;IAC3B;IAEA,IAAI,kBAAkB;QAAC;QAAS;QAAS;KAAQ;IACjD,IAAI,eAAe,IAAI,gBAAgB,OAAO,QAAQ,CAAC,MAAM;IAC7D,IAAI,aAAyB,CAAC;IAC9B,aAAa,OAAO,CAAC,CAAC,OAAO;QACzB,IAAI,gBAAgB,OAAO,CAAC,SAAS,CAAC,GAAG;YACrC,UAAU,CAAC,IAAI,GAAG;QACtB;IACJ;IACA,OAAO;AACX;AAEO,SAAS,kBAAkB,GAAW;IACzC,IAAI,CAAC,CAAA,GAAA,qHAAA,CAAA,wBAAqB,AAAD,KAAK;QAC1B,OAAO;IACX;IACA,IAAI,eAAe,IAAI,gBAAgB,OAAO,QAAQ,CAAC,MAAM;IAC7D,OAAO,aAAa,GAAG,CAAC;AAC5B;AAEO,SAAS,uBAAuB,MAAyB,EAAE,QAAgB,EAAE,UAAsB;IACtG,IAAI,CAAA,GAAA,qHAAA,CAAA,wBAAqB,AAAD,KAAK;QACzB,IAAI,eAAe,IAAI;QACvB,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,CAAA;YAC5B,aAAa,GAAG,CAAC,KAAK,UAAU,CAAC,IAAI;QACzC;QACA,OAAO,OAAO,CAAC,GAAG,SAAS,CAAC,EAAE,aAAa,QAAQ,IAAI;QACvD,OAAO,OAAO,CAAC,YAAY,CAAC,MAAM,IAAI,GAAG,SAAS,CAAC,EAAE,aAAa,QAAQ,IAAI;IAClF,OAAO;QACH,QAAQ,KAAK,CAAC;IAClB;AACJ", "debugId": null}}, {"offset": {"line": 6205, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/Tooltip/Tooltip.tsx"], "sourcesContent": ["'use client'\nimport { useState, type JSX } from 'react'\nimport { Modal, OverlayTrigger, Tooltip as BootstrapTooltip } from 'react-bootstrap'\nimport { v4 as generateUUID } from 'uuid'\n\n/**\n * type: if the tooltip is a regular tooltip or should be clicked at (popup)\n * content: the content that is initally displayed (e.g. a ?-Symbol)\n * tooltipContent: the content that is displayed after the activation (onHover or onClick)\n * tooltipTitle: only used as the title in the popup-Dilog\n */\ninterface Props {\n    type: 'hover' | 'click'\n    content: JSX.Element\n    tooltipContent?: JSX.Element\n    tooltipTitle?: JSX.Element\n    size?: 'sm' | 'lg' | 'xl'\n    onClick?: Function\n    className?: string\n    id?: any\n    hoverPlacement?: any\n}\n\nfunction Tooltip(props: Props) {\n    let [showDialog, setShowDialog] = useState(false)\n\n    function getHoverElement() {\n        return props.tooltipContent ? (\n            <OverlayTrigger\n                overlay={\n                    <BootstrapTooltip id={props.id || generateUUID()} className={props.className}>\n                        {props.tooltipContent}\n                    </BootstrapTooltip>\n                }\n                placement={props.hoverPlacement}\n            >\n                {props.content}\n            </OverlayTrigger>\n        ) : (\n            props.content\n        )\n    }\n\n    function onClick() {\n        setShowDialog(true)\n        if (props.onClick) {\n            props.onClick()\n        }\n    }\n\n    function getClickElement() {\n        return props.tooltipContent || props.tooltipTitle ? (\n            <span className={`tooltipWrapper ${props.className}`}>\n                <span style={{ cursor: 'pointer' }} onClick={onClick}>\n                    {props.content}\n                </span>\n                <Modal\n                    size={props.size || 'lg'}\n                    show={showDialog}\n                    onHide={() => {\n                        setShowDialog(false)\n                    }}\n                >\n                    <Modal.Header closeButton>\n                        <Modal.Title>{props.tooltipTitle}</Modal.Title>\n                    </Modal.Header>\n                    <Modal.Body>{props.tooltipContent}</Modal.Body>\n                </Modal>\n            </span>\n        ) : (\n            props.content\n        )\n    }\n\n    return props.type === 'hover' ? getHoverElement() : getClickElement()\n}\n\nexport default Tooltip\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AAAA;AACA;;;AAHA;;;;AAuBA,SAAS,QAAQ,KAAY;;IACzB,IAAI,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,SAAS;QACL,OAAO,MAAM,cAAc,iBACvB,6LAAC,2MAAA,CAAA,iBAAc;YACX,uBACI,6LAAC,6LAAA,CAAA,UAAgB;gBAAC,IAAI,MAAM,EAAE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAY,AAAD;gBAAK,WAAW,MAAM,SAAS;0BACvE,MAAM,cAAc;;;;;;YAG7B,WAAW,MAAM,cAAc;sBAE9B,MAAM,OAAO;;;;;mBAGlB,MAAM,OAAO;IAErB;IAEA,SAAS;QACL,cAAc;QACd,IAAI,MAAM,OAAO,EAAE;YACf,MAAM,OAAO;QACjB;IACJ;IAEA,SAAS;QACL,OAAO,MAAM,cAAc,IAAI,MAAM,YAAY,iBAC7C,6LAAC;YAAK,WAAW,CAAC,eAAe,EAAE,MAAM,SAAS,EAAE;;8BAChD,6LAAC;oBAAK,OAAO;wBAAE,QAAQ;oBAAU;oBAAG,SAAS;8BACxC,MAAM,OAAO;;;;;;8BAElB,6LAAC,yLAAA,CAAA,QAAK;oBACF,MAAM,MAAM,IAAI,IAAI;oBACpB,MAAM;oBACN,QAAQ;wBACJ,cAAc;oBAClB;;sCAEA,6LAAC,yLAAA,CAAA,QAAK,CAAC,MAAM;4BAAC,WAAW;sCACrB,cAAA,6LAAC,yLAAA,CAAA,QAAK,CAAC,KAAK;0CAAE,MAAM,YAAY;;;;;;;;;;;sCAEpC,6LAAC,yLAAA,CAAA,QAAK,CAAC,IAAI;sCAAE,MAAM,cAAc;;;;;;;;;;;;;;;;;mBAIzC,MAAM,OAAO;IAErB;IAEA,OAAO,MAAM,IAAI,KAAK,UAAU,oBAAoB;AACxD;GApDS;KAAA;uCAsDM", "debugId": null}}, {"offset": {"line": 6319, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/components/ReloadDialog/ReloadDialog.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"checkbox\": \"ReloadDialog-module__HyoM8G__checkbox\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA"}}, {"offset": {"line": 6328, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/ReloadDialog/ReloadDialog.tsx"], "sourcesContent": ["import { useState, ChangeEvent } from 'react'\nimport { <PERSON>, Button } from 'react-bootstrap'\nimport { toast } from 'react-toastify'\nimport api from '../../api/ApiHelper'\nimport Tooltip from '../Tooltip/Tooltip'\nimport styles from './ReloadDialog.module.css'\nimport { errorLog } from '../MainApp/MainApp'\n\ninterface Props {\n    onClose()\n}\n\nfunction ReloadDialog(props: Props) {\n    let [feedback, setFeedback] = useState<ReloadFeedback>({\n        loadNewInformation: false,\n        otherIssue: false,\n        somethingBroke: false,\n        additionalInformation: ''\n    })\n    let [hasUserInput, setHasUserInput] = useState(false)\n    let [showMissingAdditionalInformation, setShowMissingAdditionalInformation] = useState(false)\n\n    function onClose() {\n        props.onClose()\n    }\n\n    function onSubmit() {\n        if (!feedback.additionalInformation) {\n            setShowMissingAdditionalInformation(true)\n            return\n        }\n        let feedbackToSend: any = { ...feedback }\n        feedbackToSend.errorLog = errorLog\n        feedbackToSend.href = location.href\n\n        api.sendFeedback('reload', feedbackToSend)\n            .then(() => {\n                toast.success('Thank you for your feedback!')\n                props.onClose()\n            })\n            .catch(() => {\n                toast.error('Feedback could not be sent.')\n                props.onClose()\n            })\n    }\n\n    function onRememberHideDialogChange(e: ChangeEvent<HTMLInputElement>) {\n        localStorage.setItem('rememberHideReloadDialog', e.target.checked.toString())\n        setHasUserInput(true)\n    }\n\n    function onLoadNewInformationChange(e: ChangeEvent<HTMLInputElement>) {\n        let newFeedback = { ...feedback }\n        newFeedback.loadNewInformation = e.target.checked\n        setFeedback(newFeedback)\n        setHasUserInput(true)\n    }\n\n    function onSomethingBrokeChange(e: ChangeEvent<HTMLInputElement>) {\n        let newFeedback = { ...feedback }\n        newFeedback.somethingBroke = e.target.checked\n        setFeedback(newFeedback)\n        setHasUserInput(true)\n    }\n\n    function onOtherIssueChange(e: ChangeEvent<HTMLInputElement>) {\n        let newFeedback = { ...feedback }\n        newFeedback.otherIssue = e.target.checked\n        setFeedback(newFeedback)\n        setHasUserInput(true)\n    }\n\n    function onAdditionalInformationChange(e: ChangeEvent<HTMLInputElement>) {\n        let newFeedback = { ...feedback }\n        newFeedback.additionalInformation = e.target.value\n        if (newFeedback.additionalInformation) {\n            setShowMissingAdditionalInformation(false)\n        }\n        setFeedback(newFeedback)\n        setHasUserInput(true)\n    }\n\n    return (\n        <div>\n            <p>\n                We noticed that you reloaded the page multiple times. We try to constantly improve our service. To do so we need to know if and what went wrong.\n            </p>\n            <p>Please tell us, why you reloaded the page:</p>\n            <Form>\n                <hr />\n                <Form.Group>\n                    <Form.Label htmlFor=\"loaddNewInformation\">I tried to load new information</Form.Label>\n                    <Form.Check\n                        id=\"loaddNewInformation\"\n                        className={styles.checkbox}\n                        defaultChecked={feedback.loadNewInformation}\n                        onChange={onLoadNewInformationChange}\n                    />\n                    <p style={{ fontStyle: 'italic' }}>\n                        If you need the most recent information on something you could try to create a notification for it. Currently you can create\n                        notifications for Items, Auctions and Players. Do you need something else? Please dont hesitate to ask us.\n                    </p>\n                </Form.Group>\n                <hr />\n                <Form.Group>\n                    <Form.Label htmlFor=\"somethingBroke\">Something broke</Form.Label>\n                    <Form.Check id=\"somethingBroke\" className={styles.checkbox} defaultChecked={feedback.somethingBroke} onChange={onSomethingBrokeChange} />\n                    <p style={{ fontStyle: 'italic' }}>You found an bug? Please tell us about it so that we can fix it as soon as possible.</p>\n                </Form.Group>\n\n                <hr />\n                <Form.Group>\n                    <Form.Label htmlFor=\"otherIssues\">Other issue</Form.Label>\n                    <Form.Check id=\"otherIssues\" className={styles.checkbox} defaultChecked={feedback.otherIssue} onChange={onOtherIssueChange} />\n                    <p style={{ fontStyle: 'italic' }}>If you need any kind of help please join our discord or write us an E-Mail. We are happy to help.</p>\n                </Form.Group>\n\n                <hr />\n                <Form.Group>\n                    <Form.Label htmlFor=\"additionalInformations\">*Additional information</Form.Label>\n                    <Form.Control\n                        isInvalid={showMissingAdditionalInformation}\n                        style={{ height: '75px', resize: 'none' }}\n                        id=\"additionalInformations\"\n                        as=\"textarea\"\n                        onChange={onAdditionalInformationChange}\n                    />\n                    {showMissingAdditionalInformation ? (\n                        <div>\n                            <span style={{ color: 'red' }}>Please enter some additional information</span>\n                        </div>\n                    ) : null}\n                </Form.Group>\n\n                <hr />\n                <Form.Group>\n                    <Form.Label htmlFor=\"rememberHideDialog\">\n                        <b>Please dont show this dialog again.</b>\n                    </Form.Label>\n                    <Form.Check id=\"rememberHideDialog\" className={styles.checkbox} onChange={onRememberHideDialogChange} />\n                </Form.Group>\n            </Form>\n            <div style={{ display: 'flex', justifyContent: 'flex-end' }}>\n                <Button variant=\"danger\" onClick={onClose}>\n                    Close\n                </Button>\n                <Tooltip\n                    type={'hover'}\n                    content={\n                        <div>\n                            <Button variant=\"success\" onClick={onSubmit} disabled={!hasUserInput} style={{ marginLeft: '15px' }}>\n                                Submit\n                            </Button>\n                        </div>\n                    }\n                    tooltipContent={!hasUserInput ? <span>Please enter some information before submitting feedback</span> : undefined}\n                />\n            </div>\n        </div>\n    )\n}\n\nexport default ReloadDialog\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;AAMA,SAAS,aAAa,KAAY;;IAC9B,IAAI,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;QACnD,oBAAoB;QACpB,YAAY;QACZ,gBAAgB;QAChB,uBAAuB;IAC3B;IACA,IAAI,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,IAAI,CAAC,kCAAkC,oCAAoC,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvF,SAAS;QACL,MAAM,OAAO;IACjB;IAEA,SAAS;QACL,IAAI,CAAC,SAAS,qBAAqB,EAAE;YACjC,oCAAoC;YACpC;QACJ;QACA,IAAI,iBAAsB;YAAE,GAAG,QAAQ;QAAC;QACxC,eAAe,QAAQ,GAAG,oIAAA,CAAA,WAAQ;QAClC,eAAe,IAAI,GAAG,SAAS,IAAI;QAEnC,oHAAA,CAAA,UAAG,CAAC,YAAY,CAAC,UAAU,gBACtB,IAAI,CAAC;YACF,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,MAAM,OAAO;QACjB,GACC,KAAK,CAAC;YACH,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,MAAM,OAAO;QACjB;IACR;IAEA,SAAS,2BAA2B,CAAgC;QAChE,aAAa,OAAO,CAAC,4BAA4B,EAAE,MAAM,CAAC,OAAO,CAAC,QAAQ;QAC1E,gBAAgB;IACpB;IAEA,SAAS,2BAA2B,CAAgC;QAChE,IAAI,cAAc;YAAE,GAAG,QAAQ;QAAC;QAChC,YAAY,kBAAkB,GAAG,EAAE,MAAM,CAAC,OAAO;QACjD,YAAY;QACZ,gBAAgB;IACpB;IAEA,SAAS,uBAAuB,CAAgC;QAC5D,IAAI,cAAc;YAAE,GAAG,QAAQ;QAAC;QAChC,YAAY,cAAc,GAAG,EAAE,MAAM,CAAC,OAAO;QAC7C,YAAY;QACZ,gBAAgB;IACpB;IAEA,SAAS,mBAAmB,CAAgC;QACxD,IAAI,cAAc;YAAE,GAAG,QAAQ;QAAC;QAChC,YAAY,UAAU,GAAG,EAAE,MAAM,CAAC,OAAO;QACzC,YAAY;QACZ,gBAAgB;IACpB;IAEA,SAAS,8BAA8B,CAAgC;QACnE,IAAI,cAAc;YAAE,GAAG,QAAQ;QAAC;QAChC,YAAY,qBAAqB,GAAG,EAAE,MAAM,CAAC,KAAK;QAClD,IAAI,YAAY,qBAAqB,EAAE;YACnC,oCAAoC;QACxC;QACA,YAAY;QACZ,gBAAgB;IACpB;IAEA,qBACI,6LAAC;;0BACG,6LAAC;0BAAE;;;;;;0BAGH,6LAAC;0BAAE;;;;;;0BACH,6LAAC,uLAAA,CAAA,OAAI;;kCACD,6LAAC;;;;;kCACD,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;;0CACP,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;gCAAC,SAAQ;0CAAsB;;;;;;0CAC1C,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;gCACP,IAAG;gCACH,WAAW,yJAAA,CAAA,UAAM,CAAC,QAAQ;gCAC1B,gBAAgB,SAAS,kBAAkB;gCAC3C,UAAU;;;;;;0CAEd,6LAAC;gCAAE,OAAO;oCAAE,WAAW;gCAAS;0CAAG;;;;;;;;;;;;kCAKvC,6LAAC;;;;;kCACD,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;;0CACP,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;gCAAC,SAAQ;0CAAiB;;;;;;0CACrC,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;gCAAC,IAAG;gCAAiB,WAAW,yJAAA,CAAA,UAAM,CAAC,QAAQ;gCAAE,gBAAgB,SAAS,cAAc;gCAAE,UAAU;;;;;;0CAC/G,6LAAC;gCAAE,OAAO;oCAAE,WAAW;gCAAS;0CAAG;;;;;;;;;;;;kCAGvC,6LAAC;;;;;kCACD,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;;0CACP,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;gCAAC,SAAQ;0CAAc;;;;;;0CAClC,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;gCAAC,IAAG;gCAAc,WAAW,yJAAA,CAAA,UAAM,CAAC,QAAQ;gCAAE,gBAAgB,SAAS,UAAU;gCAAE,UAAU;;;;;;0CACxG,6LAAC;gCAAE,OAAO;oCAAE,WAAW;gCAAS;0CAAG;;;;;;;;;;;;kCAGvC,6LAAC;;;;;kCACD,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;;0CACP,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;gCAAC,SAAQ;0CAAyB;;;;;;0CAC7C,6LAAC,uLAAA,CAAA,OAAI,CAAC,OAAO;gCACT,WAAW;gCACX,OAAO;oCAAE,QAAQ;oCAAQ,QAAQ;gCAAO;gCACxC,IAAG;gCACH,IAAG;gCACH,UAAU;;;;;;4BAEb,iDACG,6LAAC;0CACG,cAAA,6LAAC;oCAAK,OAAO;wCAAE,OAAO;oCAAM;8CAAG;;;;;;;;;;uCAEnC;;;;;;;kCAGR,6LAAC;;;;;kCACD,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;;0CACP,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;gCAAC,SAAQ;0CAChB,cAAA,6LAAC;8CAAE;;;;;;;;;;;0CAEP,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;gCAAC,IAAG;gCAAqB,WAAW,yJAAA,CAAA,UAAM,CAAC,QAAQ;gCAAE,UAAU;;;;;;;;;;;;;;;;;;0BAGlF,6LAAC;gBAAI,OAAO;oBAAE,SAAS;oBAAQ,gBAAgB;gBAAW;;kCACtD,6LAAC,2LAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAS,SAAS;kCAAS;;;;;;kCAG3C,6LAAC,oIAAA,CAAA,UAAO;wBACJ,MAAM;wBACN,uBACI,6LAAC;sCACG,cAAA,6LAAC,2LAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,SAAS;gCAAU,UAAU,CAAC;gCAAc,OAAO;oCAAE,YAAY;gCAAO;0CAAG;;;;;;;;;;;wBAK7G,gBAAgB,CAAC,6BAAe,6LAAC;sCAAK;;;;;qCAAkE;;;;;;;;;;;;;;;;;;AAK5H;GApJS;KAAA;uCAsJM", "debugId": null}}, {"offset": {"line": 6728, "column": 0}, "map": {"version": 3, "sources": ["file:///app/migrations/BackupFlipSettingsMigration.tsx"], "sourcesContent": ["export function startFlipSettingsBackupMigration() {\n    let currentSettings = localStorage.getItem('userSettings')\n    let backup = localStorage.getItem('backup_userSettings')\n    if (currentSettings && !backup) {\n        localStorage.setItem('backup_userSettings', currentSettings)\n    }\n}\n"], "names": [], "mappings": ";;;AAAO,SAAS;IACZ,IAAI,kBAAkB,aAAa,OAAO,CAAC;IAC3C,IAAI,SAAS,aAAa,OAAO,CAAC;IAClC,IAAI,mBAAmB,CAAC,QAAQ;QAC5B,aAAa,OAAO,CAAC,uBAAuB;IAChD;AACJ", "debugId": null}}, {"offset": {"line": 6747, "column": 0}, "map": {"version": 3, "sources": ["file:///app/migrations/EmptyFlipRestrictionMigration.tsx"], "sourcesContent": ["import { getSettingsObject, RESTRICTIONS_SETTINGS_KEY, setSetting } from '../utils/SettingsUtils'\n\nexport function startEmptyFlipRestrictionMigration() {\n    let restrictions = getSettingsObject<FlipRestriction[]>(RESTRICTIONS_SETTINGS_KEY, [])\n    let validRestrictions: FlipRestriction[] = []\n    for (let i = 0; i < restrictions.length; i++) {\n        const restriction = restrictions[i]\n        if (!restriction.item || (restriction.item && restriction.item.tag)) {\n            validRestrictions.push(restriction)\n        }\n    }\n    setSetting(RESTRICTIONS_SETTINGS_KEY, JSON.stringify(validRestrictions))\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEO,SAAS;IACZ,IAAI,eAAe,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD,EAAqB,0HAAA,CAAA,4BAAyB,EAAE,EAAE;IACrF,IAAI,oBAAuC,EAAE;IAC7C,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,MAAM,EAAE,IAAK;QAC1C,MAAM,cAAc,YAAY,CAAC,EAAE;QACnC,IAAI,CAAC,YAAY,IAAI,IAAK,YAAY,IAAI,IAAI,YAAY,IAAI,CAAC,GAAG,EAAG;YACjE,kBAAkB,IAAI,CAAC;QAC3B;IACJ;IACA,CAAA,GAAA,0HAAA,CAAA,aAAU,AAAD,EAAE,0HAAA,CAAA,4BAAyB,EAAE,KAAK,SAAS,CAAC;AACzD", "debugId": null}}, {"offset": {"line": 6772, "column": 0}, "map": {"version": 3, "sources": ["file:///app/migrations/MigrationUtils.tsx"], "sourcesContent": ["import { startFlipSettingsBackupMigration } from './BackupFlipSettingsMigration'\nimport { startEmptyFlipRestrictionMigration } from './EmptyFlipRestrictionMigration'\n\nexport function startMigrations() {\n    startFlipSettingsBackupMigration()\n    startEmptyFlipRestrictionMigration()\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS;IACZ,CAAA,GAAA,6IAAA,CAAA,mCAAgC,AAAD;IAC/B,CAAA,GAAA,+IAAA,CAAA,qCAAkC,AAAD;AACrC", "debugId": null}}, {"offset": {"line": 6792, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/TopLoader/TopLoadingAnimation.tsx"], "sourcesContent": ["'use client'\nimport * as NProgress from 'nprogress'\nimport { useEffect } from 'react'\nexport default function TopLoadingAnimation() {\n    const color = '#29d'\n    const height = 3\n\n    const styles = (\n        <style>\n            {`#nprogress{pointer-events:none}#nprogress .bar{background:${color};position:fixed;z-index:1031;top:0;left:0;width:100%;height:${height}px}#nprogress .peg{display:block;position:absolute;right:0;width:100px;height:100%;opacity:1;-webkit-transform:rotate(3deg) translate(0px,-4px);-ms-transform:rotate(3deg) translate(0px,-4px);transform:rotate(3deg) translate(0px,-4px)}#nprogress .spinner{display:block;position:fixed;z-index:1031;top:15px;right:15px}#nprogress .spinner-icon{width:18px;height:18px;box-sizing:border-box;border:2px solid transparent;border-top-color:${color};border-left-color:${color};border-radius:50%;-webkit-animation:nprogress-spinner 400ms linear infinite;animation:nprogress-spinner 400ms linear infinite}.nprogress-custom-parent{overflow:hidden;position:relative}.nprogress-custom-parent #nprogress .bar,.nprogress-custom-parent #nprogress .spinner{position:absolute}@-webkit-keyframes nprogress-spinner{0%{-webkit-transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg)}}@keyframes nprogress-spinner{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}`}\n        </style>\n    )\n\n    useEffect(() => {\n        NProgress.configure({\n            showSpinner: true,\n            trickle: true,\n            trickleSpeed: 200,\n            minimum: 0.08,\n            easing: 'ease',\n            speed: 200\n        })\n\n        function isNoSufficientNavigation(currentUrl: string, newUrl: string) {\n            const currentUrlObj = new URL(currentUrl)\n            const newUrlObj = new URL(newUrl)\n            // Compare hostname, pathname, and search parameters\n            return currentUrlObj.hostname === newUrlObj.hostname && currentUrlObj.pathname === newUrlObj.pathname\n        }\n\n        // eslint-disable-next-line no-var\n        var npgclass = document.querySelectorAll('html')\n        function findClosestAnchor(element: HTMLElement | null): HTMLAnchorElement | null {\n            while (element && element.tagName.toLowerCase() !== 'a') {\n                element = element.parentElement\n            }\n            return element as HTMLAnchorElement\n        }\n        function handleClick(event: MouseEvent) {\n            try {\n                const target = event.target as HTMLElement\n                const anchor = findClosestAnchor(target)\n                if (anchor) {\n                    const currentUrl = window.location.href\n                    const newUrl = (anchor as HTMLAnchorElement).href\n                    const isExternalLink = (anchor as HTMLAnchorElement).target === '_blank'\n                    const isAnchor = isNoSufficientNavigation(currentUrl, newUrl)\n                    if (newUrl === currentUrl || isAnchor || isExternalLink) {\n                        NProgress.start()\n                        NProgress.done()\n                        ;[].forEach.call(npgclass, function (el: Element) {\n                            el.classList.remove('nprogress-busy')\n                        })\n                    } else {\n                        NProgress.start()\n                        ;(function (history) {\n                            const pushState = history.pushState\n                            history.pushState = function () {\n                                NProgress.done()\n                                ;[].forEach.call(npgclass, function (el: Element) {\n                                    el.classList.remove('nprogress-busy')\n                                })\n                                // eslint-disable-next-line prefer-rest-params, @typescript-eslint/no-explicit-any\n                                return pushState.apply(history, arguments as any)\n                            }\n                        })(window.history)\n                    }\n                }\n            } catch (err) {\n                // Log the error in development only!\n                // console.log('NextTopLoader error: ', err);\n                NProgress.start()\n                NProgress.done()\n            }\n        }\n\n        // Add the global click event listener\n        document.addEventListener('click', handleClick)\n\n        // Clean up the global click event listener when the component is unmounted\n        return () => {\n            document.removeEventListener('click', handleClick)\n        }\n    }, [])\n\n    return styles\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;;;AAFA;;;AAGe,SAAS;;IACpB,MAAM,QAAQ;IACd,MAAM,SAAS;IAEf,MAAM,uBACF,6LAAC;kBACI,CAAC,0DAA0D,EAAE,MAAM,4DAA4D,EAAE,OAAO,gbAAgb,EAAE,MAAM,mBAAmB,EAAE,MAAM,seAAse,CAAC;;;;;;IAI3kC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACN,CAAA,GAAA,yIAAA,CAAA,YAAmB,AAAD,EAAE;gBAChB,aAAa;gBACb,SAAS;gBACT,cAAc;gBACd,SAAS;gBACT,QAAQ;gBACR,OAAO;YACX;YAEA,SAAS,yBAAyB,UAAkB,EAAE,MAAc;gBAChE,MAAM,gBAAgB,IAAI,IAAI;gBAC9B,MAAM,YAAY,IAAI,IAAI;gBAC1B,oDAAoD;gBACpD,OAAO,cAAc,QAAQ,KAAK,UAAU,QAAQ,IAAI,cAAc,QAAQ,KAAK,UAAU,QAAQ;YACzG;YAEA,kCAAkC;YAClC,IAAI,WAAW,SAAS,gBAAgB,CAAC;YACzC,SAAS,kBAAkB,OAA2B;gBAClD,MAAO,WAAW,QAAQ,OAAO,CAAC,WAAW,OAAO,IAAK;oBACrD,UAAU,QAAQ,aAAa;gBACnC;gBACA,OAAO;YACX;YACA,SAAS,YAAY,KAAiB;gBAClC,IAAI;oBACA,MAAM,SAAS,MAAM,MAAM;oBAC3B,MAAM,SAAS,kBAAkB;oBACjC,IAAI,QAAQ;wBACR,MAAM,aAAa,OAAO,QAAQ,CAAC,IAAI;wBACvC,MAAM,SAAS,AAAC,OAA6B,IAAI;wBACjD,MAAM,iBAAiB,AAAC,OAA6B,MAAM,KAAK;wBAChE,MAAM,WAAW,yBAAyB,YAAY;wBACtD,IAAI,WAAW,cAAc,YAAY,gBAAgB;4BACrD,CAAA,GAAA,yIAAA,CAAA,QAAe,AAAD;4BACd,CAAA,GAAA,yIAAA,CAAA,OAAc,AAAD;4BACZ,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;6EAAU,SAAU,EAAW;oCAC5C,GAAG,SAAS,CAAC,MAAM,CAAC;gCACxB;;wBACJ,OAAO;4BACH,CAAA,GAAA,yIAAA,CAAA,QAAe,AAAD;4BACb;6EAAC,SAAU,OAAO;oCACf,MAAM,YAAY,QAAQ,SAAS;oCACnC,QAAQ,SAAS,GAFpB;qFAEuB;4CAChB,CAAA,GAAA,yIAAA,CAAA,OAAc,AAAD;4CACZ,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;6FAAU,SAAU,EAAW;oDAC5C,GAAG,SAAS,CAAC,MAAM,CAAC;gDACxB;;4CACA,kFAAkF;4CAClF,OAAO,UAAU,KAAK,CAAC,SAAS;wCACpC;qCACH;gCAAD;4EAAG,OAAO,OAAO;wBACrB;oBACJ;gBACJ,EAAE,OAAO,KAAK;oBACV,qCAAqC;oBACrC,6CAA6C;oBAC7C,CAAA,GAAA,yIAAA,CAAA,QAAe,AAAD;oBACd,CAAA,GAAA,yIAAA,CAAA,OAAc,AAAD;gBACjB;YACJ;YAEA,sCAAsC;YACtC,SAAS,gBAAgB,CAAC,SAAS;YAEnC,2EAA2E;YAC3E;iDAAO;oBACH,SAAS,mBAAmB,CAAC,SAAS;gBAC1C;;QACJ;wCAAG,EAAE;IAEL,OAAO;AACX;GAnFwB;KAAA", "debugId": null}}, {"offset": {"line": 6908, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/MainApp/MainApp.tsx"], "sourcesContent": ["'use client'\nimport { useEffect, useState } from 'react'\nimport { useMatomo } from '@jonkoops/matomo-tracker-react'\nimport CookieConsent from 'react-cookie-consent'\nimport { ToastContainer } from 'react-toastify'\nimport { OfflineBanner } from '../OfflineBanner/OfflineBanner'\nimport registerNotificationCallback from '../../utils/NotificationUtils'\nimport { getURLSearchParam } from '../../utils/Parser/URLParser'\nimport Cookies from 'js-cookie'\nimport { Modal } from 'react-bootstrap'\nimport ReloadDialog from '../ReloadDialog/ReloadDialog'\nimport { startMigrations } from '../../migrations/MigrationUtils'\nimport { v4 as generateUUID } from 'uuid'\nimport { isClientSideRendering } from '../../utils/SSRUtils'\nimport { useRouter } from 'next/navigation'\nimport '../../styles/bootstrap-react.min.css'\nimport '../../styles/bootstrap-dark.min.css'\nimport '../../styles/globals.css'\nimport TopLoadingAnimation from '../TopLoader/TopLoadingAnimation'\nimport { initCoflCoinManager } from '../../utils/CoflCoinsUtils'\n\ninterface ErrorLog {\n    error: ErrorEvent\n    timestamp: Date\n}\n\nexport const errorLog: ErrorLog[] = []\n\ninitCoflCoinManager()\n\nexport function MainApp(props: any) {\n    const [showRefreshFeedbackDialog, setShowRefreshFeedbackDialog] = useState(false)\n    const [isReloadTracked, setIsReloadTracked] = useState(false)\n    const { trackPageView, trackEvent, pushInstruction } = useMatomo()\n    const router = useRouter()\n\n    useEffect(() => {\n        window.addEventListener('error', function (event) {\n            errorLog.push({\n                error: event,\n                timestamp: new Date()\n            })\n\n            if (event.error.name === 'ChunkLoadError') {\n                let chunkErrorLocalStorage = window.localStorage.getItem('chunkErrorReload')\n                if (chunkErrorLocalStorage && parseInt(chunkErrorLocalStorage) + 5000 > new Date().getTime()) {\n                    alert('There is something wrong with the website-chunks. Please try Control + F5 to hard refresh the page.')\n                    return\n                }\n                window.localStorage.setItem('chunkErrorReload', new Date().getTime().toString())\n                caches\n                    .keys()\n                    .then(keys => {\n                        keys.forEach(key => {\n                            caches.delete(key)\n                        })\n                    })\n                    .catch(() => {})\n                location.reload()\n            }\n        })\n    }, [])\n\n    useEffect(() => {\n        window.sessionStorage.setItem('sessionId', generateUUID())\n        checkForReload()\n        startMigrations()\n    }, [])\n\n    useEffect(() => {\n        pushInstruction('requireConsent')\n\n        // check for tracking of old users\n        let cookie = Cookies.get('nonEssentialCookiesAllowed')\n        if (cookie === 'true') {\n            pushInstruction('rememberConsentGiven')\n        }\n\n        let refId = getURLSearchParam('refId')\n        if (refId) {\n            ;(window as any).refId = refId\n        }\n\n        registerNotificationCallback(router)\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [isClientSideRendering() ? location : null])\n\n    useEffect(() => {\n        trackPageView({})\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [isClientSideRendering() ? document.title : null])\n\n    function checkForReload() {\n        let preventReloadDialog = localStorage.getItem('rememberHideReloadDialog') === 'true'\n        if (preventReloadDialog || isReloadTracked) {\n            return\n        }\n\n        // check if page was reloaded\n        if (\n            window.performance\n                .getEntriesByType('navigation')\n                .map(nav => (nav as PerformanceNavigationTiming).type)\n                .includes('reload')\n        ) {\n            let lastReloadTime = localStorage.getItem('lastReloadTime')\n            // Check if the last reload was less than 30 seconds ago\n            if (lastReloadTime && 30_000 > new Date().getTime() - Number(lastReloadTime)) {\n                setTimeout(() => {\n                    setShowRefreshFeedbackDialog(true)\n                }, 1000)\n            } else {\n                setIsReloadTracked(true)\n                localStorage.setItem('lastReloadTime', new Date().getTime().toString())\n            }\n        }\n    }\n\n    function setTrackingAllowed() {\n        pushInstruction('rememberConsentGiven')\n        trackPageView({})\n    }\n\n    let refreshFeedbackDialog = (\n        <Modal\n            size={'lg'}\n            show={showRefreshFeedbackDialog}\n            onHide={() => {\n                setShowRefreshFeedbackDialog(false)\n            }}\n        >\n            <Modal.Header closeButton>\n                <Modal.Title>Has an error occured?</Modal.Title>\n            </Modal.Header>\n            <Modal.Body>\n                <ReloadDialog\n                    onClose={() => {\n                        setShowRefreshFeedbackDialog(false)\n                    }}\n                />\n            </Modal.Body>\n        </Modal>\n    )\n\n    return (\n        <>\n            <OfflineBanner />\n            <TopLoadingAnimation />\n            {props.children}\n            <CookieConsent\n                enableDeclineButton\n                declineButtonStyle={{ backgroundColor: 'rgb(65, 65, 65)', borderRadius: '10px', color: 'lightgrey', fontSize: '14px' }}\n                buttonStyle={{ backgroundColor: 'green', borderRadius: '10px', color: 'white', fontSize: '20px' }}\n                contentStyle={{ marginBottom: '0px' }}\n                buttonText=\"Yes, I understand\"\n                declineButtonText=\"Decline\"\n                cookieName=\"nonEssentialCookiesAllowed\"\n                data-nosnippet\n                style={{ paddingLeft: '2vw' }}\n                onAccept={() => {\n                    setTrackingAllowed()\n                }}\n            >\n                <span data-nosnippet>\n                    <p style={{ margin: '0' }}>\n                        We use cookies for analytics. By clicking the \"Yes, I understand\" button, you consent our use of cookies. View our{' '}\n                        <a href=\"https://coflnet.com/privacy\" style={{ backgroundColor: 'white', textDecoration: 'none', color: 'black', borderRadius: '3px' }}>\n                            Privacy Policy ↗️\n                        </a>\n                    </p>\n                </span>\n            </CookieConsent>\n            {refreshFeedbackDialog}\n            <ToastContainer theme={'colored'} stacked />\n        </>\n    )\n}\n"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAIA;AACA;;;AAnBA;;;;;;;;;;;;;;;;;;;;AA0BO,MAAM,WAAuB,EAAE;AAEtC,CAAA,GAAA,2HAAA,CAAA,sBAAmB,AAAD;AAEX,SAAS,QAAQ,KAAU;;IAC9B,MAAM,CAAC,2BAA2B,6BAA6B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3E,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,EAAE,aAAa,EAAE,UAAU,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,sNAAA,CAAA,YAAS,AAAD;IAC/D,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACN,OAAO,gBAAgB,CAAC;qCAAS,SAAU,KAAK;oBAC5C,SAAS,IAAI,CAAC;wBACV,OAAO;wBACP,WAAW,IAAI;oBACnB;oBAEA,IAAI,MAAM,KAAK,CAAC,IAAI,KAAK,kBAAkB;wBACvC,IAAI,yBAAyB,OAAO,YAAY,CAAC,OAAO,CAAC;wBACzD,IAAI,0BAA0B,SAAS,0BAA0B,OAAO,IAAI,OAAO,OAAO,IAAI;4BAC1F,MAAM;4BACN;wBACJ;wBACA,OAAO,YAAY,CAAC,OAAO,CAAC,oBAAoB,IAAI,OAAO,OAAO,GAAG,QAAQ;wBAC7E,OACK,IAAI,GACJ,IAAI;iDAAC,CAAA;gCACF,KAAK,OAAO;yDAAC,CAAA;wCACT,OAAO,MAAM,CAAC;oCAClB;;4BACJ;gDACC,KAAK;iDAAC,KAAO;;wBAClB,SAAS,MAAM;oBACnB;gBACJ;;QACJ;4BAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACN,OAAO,cAAc,CAAC,OAAO,CAAC,aAAa,CAAA,GAAA,wLAAA,CAAA,KAAY,AAAD;YACtD;YACA,CAAA,GAAA,gIAAA,CAAA,kBAAe,AAAD;QAClB;4BAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACN,gBAAgB;YAEhB,kCAAkC;YAClC,IAAI,SAAS,wJAAA,CAAA,UAAO,CAAC,GAAG,CAAC;YACzB,IAAI,WAAW,QAAQ;gBACnB,gBAAgB;YACpB;YAEA,IAAI,QAAQ,CAAA,GAAA,gIAAA,CAAA,oBAAiB,AAAD,EAAE;YAC9B,IAAI,OAAO;;gBACL,OAAe,KAAK,GAAG;YAC7B;YAEA,CAAA,GAAA,8HAAA,CAAA,UAA4B,AAAD,EAAE;QAC7B,uDAAuD;QAC3D;4BAAG;QAAC,CAAA,GAAA,qHAAA,CAAA,wBAAqB,AAAD,MAAM,WAAW;KAAK;IAE9C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACN,cAAc,CAAC;QACf,uDAAuD;QAC3D;4BAAG;QAAC,CAAA,GAAA,qHAAA,CAAA,wBAAqB,AAAD,MAAM,SAAS,KAAK,GAAG;KAAK;IAEpD,SAAS;QACL,IAAI,sBAAsB,aAAa,OAAO,CAAC,gCAAgC;QAC/E,IAAI,uBAAuB,iBAAiB;YACxC;QACJ;QAEA,6BAA6B;QAC7B,IACI,OAAO,WAAW,CACb,gBAAgB,CAAC,cACjB,GAAG,CAAC,CAAA,MAAO,AAAC,IAAoC,IAAI,EACpD,QAAQ,CAAC,WAChB;YACE,IAAI,iBAAiB,aAAa,OAAO,CAAC;YAC1C,wDAAwD;YACxD,IAAI,kBAAkB,SAAS,IAAI,OAAO,OAAO,KAAK,OAAO,iBAAiB;gBAC1E,WAAW;oBACP,6BAA6B;gBACjC,GAAG;YACP,OAAO;gBACH,mBAAmB;gBACnB,aAAa,OAAO,CAAC,kBAAkB,IAAI,OAAO,OAAO,GAAG,QAAQ;YACxE;QACJ;IACJ;IAEA,SAAS;QACL,gBAAgB;QAChB,cAAc,CAAC;IACnB;IAEA,IAAI,sCACA,6LAAC,yLAAA,CAAA,QAAK;QACF,MAAM;QACN,MAAM;QACN,QAAQ;YACJ,6BAA6B;QACjC;;0BAEA,6LAAC,yLAAA,CAAA,QAAK,CAAC,MAAM;gBAAC,WAAW;0BACrB,cAAA,6LAAC,yLAAA,CAAA,QAAK,CAAC,KAAK;8BAAC;;;;;;;;;;;0BAEjB,6LAAC,yLAAA,CAAA,QAAK,CAAC,IAAI;0BACP,cAAA,6LAAC,8IAAA,CAAA,UAAY;oBACT,SAAS;wBACL,6BAA6B;oBACjC;;;;;;;;;;;;;;;;;IAMhB,qBACI;;0BACI,6LAAC,gJAAA,CAAA,gBAAa;;;;;0BACd,6LAAC,kJAAA,CAAA,UAAmB;;;;;YACnB,MAAM,QAAQ;0BACf,6LAAC,0MAAA,CAAA,UAAa;gBACV,mBAAmB;gBACnB,oBAAoB;oBAAE,iBAAiB;oBAAmB,cAAc;oBAAQ,OAAO;oBAAa,UAAU;gBAAO;gBACrH,aAAa;oBAAE,iBAAiB;oBAAS,cAAc;oBAAQ,OAAO;oBAAS,UAAU;gBAAO;gBAChG,cAAc;oBAAE,cAAc;gBAAM;gBACpC,YAAW;gBACX,mBAAkB;gBAClB,YAAW;gBACX,gBAAc;gBACd,OAAO;oBAAE,aAAa;gBAAM;gBAC5B,UAAU;oBACN;gBACJ;0BAEA,cAAA,6LAAC;oBAAK,gBAAc;8BAChB,cAAA,6LAAC;wBAAE,OAAO;4BAAE,QAAQ;wBAAI;;4BAAG;4BAC4F;0CACnH,6LAAC;gCAAE,MAAK;gCAA8B,OAAO;oCAAE,iBAAiB;oCAAS,gBAAgB;oCAAQ,OAAO;oCAAS,cAAc;gCAAM;0CAAG;;;;;;;;;;;;;;;;;;;;;;YAMnJ;0BACD,6LAAC,sJAAA,CAAA,iBAAc;gBAAC,OAAO;gBAAW,OAAO;;;;;;;;AAGrD;GAlJgB;;QAG2C,sNAAA,CAAA,YAAS;QACjD,qIAAA,CAAA,YAAS;;;KAJZ", "debugId": null}}]}