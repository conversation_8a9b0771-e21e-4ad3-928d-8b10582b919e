{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/components/NavBar/NavBar.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"hamburgerIcon\": \"NavBar-module__yBvhsG__hamburgerIcon\",\n  \"logo\": \"NavBar-module__yBvhsG__logo\",\n  \"menuItem\": \"NavBar-module__yBvhsG__menuItem\",\n  \"navBar\": \"NavBar-module__yBvhsG__navBar\",\n  \"navClosing\": \"NavBar-module__yBvhsG__navClosing\",\n  \"navOpen\": \"NavBar-module__yBvhsG__navOpen\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 20, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/NavBar/NavBar.tsx"], "sourcesContent": ["'use client'\nimport AccountBalanceIcon from '@mui/icons-material/AccountBalance'\nimport AccountIcon from '@mui/icons-material/AccountCircle'\nimport BuildIcon from '@mui/icons-material/Build'\nimport ChatIcon from '@mui/icons-material/Chat'\nimport DownloadIcon from '@mui/icons-material/Download'\nimport HomeIcon from '@mui/icons-material/Home'\nimport MenuIcon from '@mui/icons-material/Menu'\nimport NotificationIcon from '@mui/icons-material/NotificationsOutlined'\nimport PetsIcon from '@mui/icons-material/PetsOutlined'\nimport PolicyIcon from '@mui/icons-material/Policy'\nimport ShareIcon from '@mui/icons-material/ShareOutlined'\nimport StorefrontIcon from '@mui/icons-material/Storefront'\nimport CurrencyExchangeIcon from '@mui/icons-material/CurrencyExchange'\nimport Image from 'next/image'\nimport Link from 'next/link'\nimport React, { useEffect, useState } from 'react'\nimport { Menu, MenuItem, Sidebar } from 'react-pro-sidebar'\nimport { useForceUpdate } from '../../utils/Hooks'\nimport styles from './NavBar.module.css'\n\nlet resizePromise: NodeJS.Timeout | null = null\n\ninterface Props {\n    hamburgerIconStyle?: React.CSSProperties\n}\n\nfunction NavBar(props: Props) {\n    let [isWideOpen, setIsWideOpen] = useState(false)\n    let [isHovering, setIsHovering] = useState(false)\n    let [isSmall, setIsSmall] = useState(true)\n    let [collapsed, setCollapsed] = useState(true)\n    let forceUpdate = useForceUpdate()\n\n    useEffect(() => {\n        setIsSmall(document.body.clientWidth < 1500)\n\n        window.addEventListener('resize', resizeHandler)\n\n        return () => {\n            window.removeEventListener('resize', resizeHandler)\n        }\n    }, [])\n\n    useEffect(() => {\n        if (isWideOpen) {\n            document.addEventListener('click', outsideClickHandler, true)\n        } else {\n            document.removeEventListener('click', outsideClickHandler, true)\n        }\n\n        return () => {\n            document.removeEventListener('click', outsideClickHandler, true)\n        }\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [isWideOpen])\n\n    useEffect(() => {\n        setCollapsed(isCollapsed())\n    }, [isSmall, isWideOpen, isHovering])\n\n    function isCollapsed() {\n        if (isSmall) {\n            return false\n        }\n        return !isWideOpen && !isHovering\n    }\n\n    function outsideClickHandler(evt) {\n        const flyoutEl = document.getElementById('navBar')\n        const hamburgerEl = document.getElementById('hamburgerIcon')\n        let targetEl = evt.target\n\n        do {\n            if (targetEl === flyoutEl || targetEl === hamburgerEl) {\n                return\n            }\n            targetEl = (targetEl as any).parentNode\n        } while (targetEl)\n\n        if (isWideOpen) {\n            if (isSmall) {\n                let el = document.getElementById('pro-sidebar')\n                el?.classList.add(styles.navClosing)\n                el?.classList.remove(styles.navOpen)\n                setTimeout(() => {\n                    setIsWideOpen(false)\n                    el?.classList.remove(styles.navClosing)\n                }, 500)\n            } else {\n                setIsWideOpen(false)\n            }\n        }\n    }\n\n    function onMouseMove() {\n        setIsHovering(true)\n    }\n\n    function onMouseOut() {\n        setIsHovering(false)\n    }\n\n    function resizeHandler() {\n        if (resizePromise) {\n            return\n        }\n        resizePromise = setTimeout(() => {\n            setIsWideOpen(false)\n            setIsSmall(document.body.clientWidth < 1500)\n            forceUpdate()\n            resizePromise = null\n            let el = document.getElementById('pro-sidebar')\n            if (el) {\n                el.style.left = '0px'\n            }\n        }, 500)\n    }\n\n    function onHamburgerClick() {\n        if (isSmall && !isWideOpen) {\n            let el = document.getElementById('pro-sidebar')\n            if (el) {\n                el.hidden = false\n                el.style.left = '-270px'\n                setTimeout(() => {\n                    if (el) {\n                        el.classList.add(styles.navOpen)\n                    }\n                })\n                setTimeout(() => {\n                    setIsWideOpen(true)\n                }, 500)\n            }\n        } else {\n            setIsWideOpen(!isWideOpen)\n        }\n    }\n\n    return (\n        <span>\n            <aside className={styles.navBar} id=\"navBar\" onMouseEnter={onMouseMove} onMouseLeave={onMouseOut}>\n                <Sidebar id=\"pro-sidebar\" hidden={isSmall && !isWideOpen} backgroundColor=\"#1d1d1d\" collapsed={collapsed}>\n                    <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>\n                        <div>\n                            <div className={styles.logo}>\n                                <Image src=\"/logo512.png\" alt=\"Logo\" width={40} height={40} style={{ translate: '-5px' }} /> {!isCollapsed() ? 'Coflnet' : ''}\n                            </div>\n                        </div>\n                        <hr />\n                        <Menu>\n                            <MenuItem className={styles.menuItem} component={<Link href={'/'} />} icon={<HomeIcon />}>\n                                Home\n                            </MenuItem>\n                            <MenuItem className={styles.menuItem} component={<Link href={'/flipper'} />} icon={<StorefrontIcon />}>\n                                Item Flipper\n                            </MenuItem>\n                            <MenuItem className={styles.menuItem} component={<Link href={'/account'} />} icon={<AccountIcon />}>\n                                Account\n                            </MenuItem>\n                            <MenuItem className={styles.menuItem} component={<Link href={'/subscriptions'} />} icon={<NotificationIcon />}>\n                                Notifier\n                            </MenuItem>\n                            <MenuItem className={styles.menuItem} component={<Link href={'/crafts'} />} icon={<BuildIcon />}>\n                                Profitable Crafts\n                            </MenuItem>\n                            <MenuItem className={styles.menuItem} component={<Link href={'/premium'} />} icon={<AccountBalanceIcon />}>\n                                Premium / Shop\n                            </MenuItem>\n                            <MenuItem className={styles.menuItem} component={<Link href={'/trade'} />} icon={<CurrencyExchangeIcon />}>\n                                Trading\n                            </MenuItem>\n                            <MenuItem className={styles.menuItem} component={<Link href={'/kat'} />} icon={<PetsIcon />}>\n                                Kat Flips\n                            </MenuItem>\n                            <MenuItem className={styles.menuItem} component={<Link href={'/mod'} />} icon={<DownloadIcon />}>\n                                Mod\n                            </MenuItem>\n                            <MenuItem className={styles.menuItem} component={<Link href={'/ref'} />} icon={<ShareIcon />}>\n                                Referral\n                            </MenuItem>\n                            <MenuItem className={styles.menuItem} component={<Link href={'/about'} />} icon={<PolicyIcon />}>\n                                Links / Legal\n                            </MenuItem>\n                            <MenuItem className={styles.menuItem} component={<Link href={'/feedback'} />} icon={<ChatIcon />}>\n                                Feedback\n                            </MenuItem>\n                            <MenuItem\n                                className={styles.menuItem}\n                                component={<Link href={'https://discord.gg/wvKXfTgCfb'} target=\"_blank\" />}\n                                rel=\"noreferrer\"\n                                icon={<Image src=\"/discord_icon.svg\" alt=\"Discord icon\" height={24} width={32} />}\n                            >\n                                Discord\n                            </MenuItem>\n                        </Menu>\n                    </div>\n                </Sidebar>\n            </aside>\n            {isSmall ? (\n                <span onClick={onHamburgerClick} className={styles.hamburgerIcon} id=\"hamburgerIcon\" style={props.hamburgerIconStyle}>\n                    <MenuIcon fontSize=\"large\" />\n                </span>\n            ) : (\n                ''\n            )}\n        </span>\n    )\n}\n\nexport default NavBar\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAnBA;;;;;;;;;;;;;;;;;;;;AAqBA,IAAI,gBAAuC;AAM3C,SAAS,OAAO,KAAY;;IACxB,IAAI,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,IAAI,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,IAAI,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,IAAI,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,IAAI,cAAc,CAAA,GAAA,kHAAA,CAAA,iBAAc,AAAD;IAE/B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACN,WAAW,SAAS,IAAI,CAAC,WAAW,GAAG;YAEvC,OAAO,gBAAgB,CAAC,UAAU;YAElC;oCAAO;oBACH,OAAO,mBAAmB,CAAC,UAAU;gBACzC;;QACJ;2BAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACN,IAAI,YAAY;gBACZ,SAAS,gBAAgB,CAAC,SAAS,qBAAqB;YAC5D,OAAO;gBACH,SAAS,mBAAmB,CAAC,SAAS,qBAAqB;YAC/D;YAEA;oCAAO;oBACH,SAAS,mBAAmB,CAAC,SAAS,qBAAqB;gBAC/D;;QACA,uDAAuD;QAC3D;2BAAG;QAAC;KAAW;IAEf,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACN,aAAa;QACjB;2BAAG;QAAC;QAAS;QAAY;KAAW;IAEpC,SAAS;QACL,IAAI,SAAS;YACT,OAAO;QACX;QACA,OAAO,CAAC,cAAc,CAAC;IAC3B;IAEA,SAAS,oBAAoB,GAAG;QAC5B,MAAM,WAAW,SAAS,cAAc,CAAC;QACzC,MAAM,cAAc,SAAS,cAAc,CAAC;QAC5C,IAAI,WAAW,IAAI,MAAM;QAEzB,GAAG;YACC,IAAI,aAAa,YAAY,aAAa,aAAa;gBACnD;YACJ;YACA,WAAW,AAAC,SAAiB,UAAU;QAC3C,QAAS,SAAS;QAElB,IAAI,YAAY;YACZ,IAAI,SAAS;gBACT,IAAI,KAAK,SAAS,cAAc,CAAC;gBACjC,IAAI,UAAU,IAAI,6IAAA,CAAA,UAAM,CAAC,UAAU;gBACnC,IAAI,UAAU,OAAO,6IAAA,CAAA,UAAM,CAAC,OAAO;gBACnC,WAAW;oBACP,cAAc;oBACd,IAAI,UAAU,OAAO,6IAAA,CAAA,UAAM,CAAC,UAAU;gBAC1C,GAAG;YACP,OAAO;gBACH,cAAc;YAClB;QACJ;IACJ;IAEA,SAAS;QACL,cAAc;IAClB;IAEA,SAAS;QACL,cAAc;IAClB;IAEA,SAAS;QACL,IAAI,eAAe;YACf;QACJ;QACA,gBAAgB,WAAW;YACvB,cAAc;YACd,WAAW,SAAS,IAAI,CAAC,WAAW,GAAG;YACvC;YACA,gBAAgB;YAChB,IAAI,KAAK,SAAS,cAAc,CAAC;YACjC,IAAI,IAAI;gBACJ,GAAG,KAAK,CAAC,IAAI,GAAG;YACpB;QACJ,GAAG;IACP;IAEA,SAAS;QACL,IAAI,WAAW,CAAC,YAAY;YACxB,IAAI,KAAK,SAAS,cAAc,CAAC;YACjC,IAAI,IAAI;gBACJ,GAAG,MAAM,GAAG;gBACZ,GAAG,KAAK,CAAC,IAAI,GAAG;gBAChB,WAAW;oBACP,IAAI,IAAI;wBACJ,GAAG,SAAS,CAAC,GAAG,CAAC,6IAAA,CAAA,UAAM,CAAC,OAAO;oBACnC;gBACJ;gBACA,WAAW;oBACP,cAAc;gBAClB,GAAG;YACP;QACJ,OAAO;YACH,cAAc,CAAC;QACnB;IACJ;IAEA,qBACI,6LAAC;;0BACG,6LAAC;gBAAM,WAAW,6IAAA,CAAA,UAAM,CAAC,MAAM;gBAAE,IAAG;gBAAS,cAAc;gBAAa,cAAc;0BAClF,cAAA,6LAAC,iKAAA,CAAA,UAAO;oBAAC,IAAG;oBAAc,QAAQ,WAAW,CAAC;oBAAY,iBAAgB;oBAAU,WAAW;8BAC3F,cAAA,6LAAC;wBAAI,OAAO;4BAAE,QAAQ;4BAAQ,SAAS;4BAAQ,eAAe;wBAAS;;0CACnE,6LAAC;0CACG,cAAA,6LAAC;oCAAI,WAAW,6IAAA,CAAA,UAAM,CAAC,IAAI;;sDACvB,6LAAC,gIAAA,CAAA,UAAK;4CAAC,KAAI;4CAAe,KAAI;4CAAO,OAAO;4CAAI,QAAQ;4CAAI,OAAO;gDAAE,WAAW;4CAAO;;;;;;wCAAK;wCAAE,CAAC,gBAAgB,YAAY;;;;;;;;;;;;0CAGnI,6LAAC;;;;;0CACD,6LAAC,iKAAA,CAAA,OAAI;;kDACD,6LAAC,iKAAA,CAAA,WAAQ;wCAAC,WAAW,6IAAA,CAAA,UAAM,CAAC,QAAQ;wCAAE,yBAAW,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAM;;;;;;wCAAS,oBAAM,6LAAC,4JAAA,CAAA,UAAQ;;;;;kDAAK;;;;;;kDAG1F,6LAAC,iKAAA,CAAA,WAAQ;wCAAC,WAAW,6IAAA,CAAA,UAAM,CAAC,QAAQ;wCAAE,yBAAW,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAM;;;;;;wCAAgB,oBAAM,6LAAC,kKAAA,CAAA,UAAc;;;;;kDAAK;;;;;;kDAGvG,6LAAC,iKAAA,CAAA,WAAQ;wCAAC,WAAW,6IAAA,CAAA,UAAM,CAAC,QAAQ;wCAAE,yBAAW,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAM;;;;;;wCAAgB,oBAAM,6LAAC,qKAAA,CAAA,UAAW;;;;;kDAAK;;;;;;kDAGpG,6LAAC,iKAAA,CAAA,WAAQ;wCAAC,WAAW,6IAAA,CAAA,UAAM,CAAC,QAAQ;wCAAE,yBAAW,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAM;;;;;;wCAAsB,oBAAM,6LAAC,6KAAA,CAAA,UAAgB;;;;;kDAAK;;;;;;kDAG/G,6LAAC,iKAAA,CAAA,WAAQ;wCAAC,WAAW,6IAAA,CAAA,UAAM,CAAC,QAAQ;wCAAE,yBAAW,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAM;;;;;;wCAAe,oBAAM,6LAAC,6JAAA,CAAA,UAAS;;;;;kDAAK;;;;;;kDAGjG,6LAAC,iKAAA,CAAA,WAAQ;wCAAC,WAAW,6IAAA,CAAA,UAAM,CAAC,QAAQ;wCAAE,yBAAW,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAM;;;;;;wCAAgB,oBAAM,6LAAC,sKAAA,CAAA,UAAkB;;;;;kDAAK;;;;;;kDAG3G,6LAAC,iKAAA,CAAA,WAAQ;wCAAC,WAAW,6IAAA,CAAA,UAAM,CAAC,QAAQ;wCAAE,yBAAW,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAM;;;;;;wCAAc,oBAAM,6LAAC,wKAAA,CAAA,UAAoB;;;;;kDAAK;;;;;;kDAG3G,6LAAC,iKAAA,CAAA,WAAQ;wCAAC,WAAW,6IAAA,CAAA,UAAM,CAAC,QAAQ;wCAAE,yBAAW,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAM;;;;;;wCAAY,oBAAM,6LAAC,oKAAA,CAAA,UAAQ;;;;;kDAAK;;;;;;kDAG7F,6LAAC,iKAAA,CAAA,WAAQ;wCAAC,WAAW,6IAAA,CAAA,UAAM,CAAC,QAAQ;wCAAE,yBAAW,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAM;;;;;;wCAAY,oBAAM,6LAAC,gKAAA,CAAA,UAAY;;;;;kDAAK;;;;;;kDAGjG,6LAAC,iKAAA,CAAA,WAAQ;wCAAC,WAAW,6IAAA,CAAA,UAAM,CAAC,QAAQ;wCAAE,yBAAW,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAM;;;;;;wCAAY,oBAAM,6LAAC,qKAAA,CAAA,UAAS;;;;;kDAAK;;;;;;kDAG9F,6LAAC,iKAAA,CAAA,WAAQ;wCAAC,WAAW,6IAAA,CAAA,UAAM,CAAC,QAAQ;wCAAE,yBAAW,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAM;;;;;;wCAAc,oBAAM,6LAAC,8JAAA,CAAA,UAAU;;;;;kDAAK;;;;;;kDAGjG,6LAAC,iKAAA,CAAA,WAAQ;wCAAC,WAAW,6IAAA,CAAA,UAAM,CAAC,QAAQ;wCAAE,yBAAW,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAM;;;;;;wCAAiB,oBAAM,6LAAC,4JAAA,CAAA,UAAQ;;;;;kDAAK;;;;;;kDAGlG,6LAAC,iKAAA,CAAA,WAAQ;wCACL,WAAW,6IAAA,CAAA,UAAM,CAAC,QAAQ;wCAC1B,yBAAW,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAM;4CAAiC,QAAO;;;;;;wCAC/D,KAAI;wCACJ,oBAAM,6LAAC,gIAAA,CAAA,UAAK;4CAAC,KAAI;4CAAoB,KAAI;4CAAe,QAAQ;4CAAI,OAAO;;;;;;kDAC9E;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAOhB,wBACG,6LAAC;gBAAK,SAAS;gBAAkB,WAAW,6IAAA,CAAA,UAAM,CAAC,aAAa;gBAAE,IAAG;gBAAgB,OAAO,MAAM,kBAAkB;0BAChH,cAAA,6LAAC,4JAAA,CAAA,UAAQ;oBAAC,UAAS;;;;;;;;;;uBAGvB;;;;;;;AAIhB;GArLS;;QAKa,kHAAA,CAAA,iBAAc;;;KAL3B;uCAuLM", "debugId": null}}, {"offset": {"line": 566, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/components/OptionsMenu/OptionsMenu.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"buttonsWrapper\": \"OptionsMenu-module__BxCVkW__buttonsWrapper\",\n  \"dropdown\": \"OptionsMenu-module__BxCVkW__dropdown\",\n  \"optionsMenu\": \"OptionsMenu-module__BxCVkW__optionsMenu\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 577, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/OptionsMenu/OptionsMenu.tsx"], "sourcesContent": ["'use client'\nimport React from 'react'\nimport MoreVertIcon from '@mui/icons-material/MoreVert'\nimport styles from './OptionsMenu.module.css'\nimport { Button, Dropdown, DropdownButton } from 'react-bootstrap'\n\ninterface Props {\n    selected?: Player | Item\n}\ninterface AvailableLinks {\n    title: string\n    url: string\n}\n\nconst CustomToggle = React.forwardRef(({ children, onClick }: any, ref) => (\n    <span\n        ref={ref as any}\n        onClick={e => {\n            e.preventDefault()\n            onClick(e)\n        }}\n    >\n        {children}\n        <MoreVertIcon />\n    </span>\n))\n\nfunction OptionsMenu(props: Props) {\n    let available: AvailableLinks[] = []\n    const isItemPage = (props.selected as Item)?.tag !== undefined\n    const isPlayerPage = !isItemPage\n    if (isItemPage) {\n        let fandomName = props.selected?.name\n        let wikiName = props.selected?.name\n        let tag = (props.selected as Item).tag\n        if (tag.startsWith('ENCHANTMENT_')) {\n            fandomName = tag.replace('ENCHANTMENT_', '').replace('ULTIMATE_', '').replace(/_\\d/, '').toLowerCase()\n            fandomName = fandomName\n                .split('_')\n                .map(part => part.charAt(0).toUpperCase() + part.slice(1).toLowerCase())\n                .join('_')\n            wikiName = fandomName + '_Enchantment'\n        }\n        available.push({ title: 'Fandom', url: 'https://hypixel-skyblock.fandom.com/wiki/' + fandomName })\n        available.push({ title: 'Wiki', url: 'https://wiki.hypixel.net/' + wikiName })\n        if ((props.selected as Item).bazaar) {\n            available.push({ title: 'Skyblock.bz', url: 'https://Skyblock.bz/product/' + tag })\n        }\n    } else if (isPlayerPage) {\n        let player = props.selected as Player\n        available.push({ title: 'SkyCrypt', url: 'https://sky.shiiyu.moe/stats/' + player?.uuid })\n        available.push({ title: 'Plancke', url: 'https://plancke.io/hypixel/player/stats/' + player?.uuid })\n    }\n\n    const navigate = (url: string) => {\n        window.open(url, '_blank')\n    }\n\n    if (!props.selected || props.selected.name === undefined) {\n        return null\n    }\n\n    return (\n        <div className={styles.optionsMenu}>\n            <div className={styles.buttonsWrapper}>\n                {available.map((result, i) => (\n                    <a key={i} href={result.url} title={result.title} target=\"_blank\" rel=\"noreferrer\">\n                        <Button>{result.title}</Button>\n                    </a>\n                ))}\n            </div>\n\n            <Dropdown className={styles.dropdown}>\n                <Dropdown.Toggle as={CustomToggle}></Dropdown.Toggle>\n                <Dropdown.Menu id=\"dropdownMenuButton\">\n                    {available.map((result, i) => (\n                        <Dropdown.Item\n                            key={result.url}\n                            onClick={() => {\n                                navigate(result.url)\n                            }}\n                        >\n                            {result.title}\n                        </Dropdown.Item>\n                    ))}\n                </Dropdown.Menu>\n            </Dropdown>\n        </div>\n    )\n}\n\nexport default OptionsMenu\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AAAA;AAJA;;;;;;AAcA,MAAM,6BAAe,6JAAA,CAAA,UAAK,CAAC,UAAU,MAAC,CAAC,EAAE,QAAQ,EAAE,OAAO,EAAO,EAAE,oBAC/D,6LAAC;QACG,KAAK;QACL,SAAS,CAAA;YACL,EAAE,cAAc;YAChB,QAAQ;QACZ;;YAEC;0BACD,6LAAC,gKAAA,CAAA,UAAY;;;;;;;;;;;;AAIrB,SAAS,YAAY,KAAY;IAC7B,IAAI,YAA8B,EAAE;IACpC,MAAM,aAAa,AAAC,MAAM,QAAQ,EAAW,QAAQ;IACrD,MAAM,eAAe,CAAC;IACtB,IAAI,YAAY;QACZ,IAAI,aAAa,MAAM,QAAQ,EAAE;QACjC,IAAI,WAAW,MAAM,QAAQ,EAAE;QAC/B,IAAI,MAAM,AAAC,MAAM,QAAQ,CAAU,GAAG;QACtC,IAAI,IAAI,UAAU,CAAC,iBAAiB;YAChC,aAAa,IAAI,OAAO,CAAC,gBAAgB,IAAI,OAAO,CAAC,aAAa,IAAI,OAAO,CAAC,OAAO,IAAI,WAAW;YACpG,aAAa,WACR,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC,GAAG,WAAW,IACpE,IAAI,CAAC;YACV,WAAW,aAAa;QAC5B;QACA,UAAU,IAAI,CAAC;YAAE,OAAO;YAAU,KAAK,8CAA8C;QAAW;QAChG,UAAU,IAAI,CAAC;YAAE,OAAO;YAAQ,KAAK,8BAA8B;QAAS;QAC5E,IAAI,AAAC,MAAM,QAAQ,CAAU,MAAM,EAAE;YACjC,UAAU,IAAI,CAAC;gBAAE,OAAO;gBAAe,KAAK,iCAAiC;YAAI;QACrF;IACJ,OAAO,IAAI,cAAc;QACrB,IAAI,SAAS,MAAM,QAAQ;QAC3B,UAAU,IAAI,CAAC;YAAE,OAAO;YAAY,KAAK,kCAAkC,QAAQ;QAAK;QACxF,UAAU,IAAI,CAAC;YAAE,OAAO;YAAW,KAAK,6CAA6C,QAAQ;QAAK;IACtG;IAEA,MAAM,WAAW,CAAC;QACd,OAAO,IAAI,CAAC,KAAK;IACrB;IAEA,IAAI,CAAC,MAAM,QAAQ,IAAI,MAAM,QAAQ,CAAC,IAAI,KAAK,WAAW;QACtD,OAAO;IACX;IAEA,qBACI,6LAAC;QAAI,WAAW,uJAAA,CAAA,UAAM,CAAC,WAAW;;0BAC9B,6LAAC;gBAAI,WAAW,uJAAA,CAAA,UAAM,CAAC,cAAc;0BAChC,UAAU,GAAG,CAAC,CAAC,QAAQ,kBACpB,6LAAC;wBAAU,MAAM,OAAO,GAAG;wBAAE,OAAO,OAAO,KAAK;wBAAE,QAAO;wBAAS,KAAI;kCAClE,cAAA,6LAAC,2LAAA,CAAA,SAAM;sCAAE,OAAO,KAAK;;;;;;uBADjB;;;;;;;;;;0BAMhB,6LAAC,+LAAA,CAAA,WAAQ;gBAAC,WAAW,uJAAA,CAAA,UAAM,CAAC,QAAQ;;kCAChC,6LAAC,+LAAA,CAAA,WAAQ,CAAC,MAAM;wBAAC,IAAI;;;;;;kCACrB,6LAAC,+LAAA,CAAA,WAAQ,CAAC,IAAI;wBAAC,IAAG;kCACb,UAAU,GAAG,CAAC,CAAC,QAAQ,kBACpB,6LAAC,+LAAA,CAAA,WAAQ,CAAC,IAAI;gCAEV,SAAS;oCACL,SAAS,OAAO,GAAG;gCACvB;0CAEC,OAAO,KAAK;+BALR,OAAO,GAAG;;;;;;;;;;;;;;;;;;;;;;AAY3C;MA9DS;uCAgEM", "debugId": null}}, {"offset": {"line": 737, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/components/Search/Search.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"bar\": \"Search-module__Lg4wHG__bar\",\n  \"current\": \"Search-module__Lg4wHG__current\",\n  \"multiInputfield\": \"Search-module__Lg4wHG__multiInputfield\",\n  \"multiSearch\": \"Search-module__Lg4wHG__multiSearch\",\n  \"previousSearch\": \"Search-module__Lg4wHG__previousSearch\",\n  \"search\": \"Search-module__Lg4wHG__search\",\n  \"searchFormGroup\": \"Search-module__Lg4wHG__searchFormGroup\",\n  \"searchResultIcon\": \"Search-module__Lg4wHG__searchResultIcon\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 753, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/Search/Search.tsx"], "sourcesContent": ["'use client'\nimport React, { ChangeEvent, useEffect, useRef, useState, type JSX } from 'react'\nimport api from '../../api/ApiHelper'\nimport { Form, InputGroup, ListGroup, Spinner } from 'react-bootstrap'\nimport { convertTagToName, getStyleForTier } from '../../utils/Formatter'\nimport NavBar from '../NavBar/NavBar'\nimport OptionsMenu from '../OptionsMenu/OptionsMenu'\nimport SearchIcon from '@mui/icons-material/SearchOutlined'\nimport WrongIcon from '@mui/icons-material/Dangerous'\nimport Refresh from '@mui/icons-material/Refresh'\nimport ClearIcon from '@mui/icons-material/Clear'\nimport { Item, Menu, useContextMenu, Separator } from 'react-contexify'\nimport { toast } from 'react-toastify'\nimport { isClientSideRendering } from '../../utils/SSRUtils'\nimport styles from './Search.module.css'\nimport { useForceUpdate, useIsMobile } from '../../utils/Hooks'\nimport Image from 'next/image'\nimport { useRouter } from 'next/navigation'\nimport PushPinIcon from '@mui/icons-material/PushPin'\nimport {\n    addClickedSearchResultToPreviousSearches,\n    addPreviousSearchResultsToDisplay,\n    getFirstPreviousSearches,\n    pinSearchResult,\n    unpinSearchResult\n} from '../../utils/PreviousSearchUtils'\nimport { ITEM_ICON_TYPE, getSetting, setSetting } from '../../utils/SettingsUtils'\nimport ClientOnly from '../ClientOnly/ClientOnly'\n\ninterface Props {\n    selected?: Player | Item\n    currentElement?: JSX.Element\n    backgroundColor?: string\n    backgroundColorSelected?: string\n    searchFunction?(searchText: string)\n    onSearchresultClick?(item: SearchResultItem)\n    hideNavbar?: boolean\n    placeholder?: string\n    type?: 'player' | 'item'\n    preventDisplayOfPreviousSearches?: boolean\n    enableReset?: boolean\n    onResetClick?()\n    hideOptions?: boolean\n    keyForPinnedItems?: string\n}\n\nconst PLAYER_SEARCH_CONEXT_MENU_ID = 'player-search-context-menu'\nconst SEARCH_RESULT_CONTEXT_MENU_ID = 'search-result-context-menu'\n\nfunction Search(props: Props) {\n    let router = useRouter()\n    let [searchText, setSearchText] = useState('')\n    let [results, setResults] = useState<SearchResultItem[]>([])\n    let [isSearching, setIsSearching] = useState(false)\n    let [noResultsFound, setNoResultsFound] = useState(false)\n    let [isSmall, setIsSmall] = useState(true)\n    let [selectedIndex, setSelectedIndex] = useState(0)\n    const { show: showPlayerContextMenu } = useContextMenu({\n        id: PLAYER_SEARCH_CONEXT_MENU_ID\n    })\n    const { show: showSearchItemContextMenu, hideAll: hideSearchItemContextMenu } = useContextMenu({\n        id: SEARCH_RESULT_CONTEXT_MENU_ID\n    })\n    const isMobile = useIsMobile()\n\n    let rememberEnterPressRef = useRef(false)\n\n    let searchElement = useRef(null)\n    let forceUpdate = useForceUpdate()\n\n    useEffect(() => {\n        if (isClientSideRendering()) {\n            setIsSmall(isClientSideRendering() ? document.body.clientWidth < 1500 : false)\n        }\n        document.addEventListener('click', outsideClickHandler, true)\n        return () => {\n            document.removeEventListener('click', outsideClickHandler, true)\n        }\n    }, [])\n\n    useEffect(() => {\n        setSearchText('')\n        setResults([])\n    }, [props.selected])\n\n    let search = () => {\n        let searchFor = searchText\n        let searchFunction = props.searchFunction || api.search\n        searchFunction(searchFor).then(searchResults => {\n            // has the searchtext changed?\n            if (\n                searchElement.current !== null &&\n                searchFor === ((searchElement.current as HTMLDivElement).querySelector('#search-bar') as HTMLInputElement).value\n            ) {\n                let searchResultsToShow = [...searchResults]\n                if (!props.preventDisplayOfPreviousSearches) {\n                    searchResultsToShow = addPreviousSearchResultsToDisplay(searchFor, searchResults, props.keyForPinnedItems)\n                }\n\n                setSelectedIndex(0)\n                setNoResultsFound(searchResultsToShow.length === 0)\n                setResults(searchResultsToShow)\n                setIsSearching(false)\n\n                if (rememberEnterPressRef.current) {\n                    onItemClick(searchResultsToShow[0])\n                    rememberEnterPressRef.current = false\n                }\n            }\n        })\n    }\n\n    let onSearchChange = (e: ChangeEvent) => {\n        let newSearchText: string = (e.target as HTMLInputElement).value\n        searchText = newSearchText\n        setSearchText(newSearchText)\n        setIsSearching(true)\n\n        if (newSearchText === '') {\n            setResults([])\n            setIsSearching(false)\n            return\n        }\n\n        setNoResultsFound(false)\n        search()\n    }\n\n    function outsideClickHandler(evt) {\n        const flyoutEl = searchElement.current\n        let targetEl = evt.target\n\n        do {\n            if (targetEl === flyoutEl) {\n                return\n            }\n            targetEl = (targetEl as any).parentNode\n        } while (targetEl)\n\n        setResults([])\n    }\n\n    let onKeyPress = (e: KeyboardEvent) => {\n        switch (e.key) {\n            case 'Enter':\n                e.preventDefault()\n                if (isSearching) {\n                    rememberEnterPressRef.current = true\n                    return\n                }\n                if (!results || results.length === 0) {\n                    return\n                }\n                onItemClick(results[selectedIndex])\n                break\n            case 'ArrowDown':\n                if (selectedIndex < results.length - 1) {\n                    setSelectedIndex(selectedIndex + 1)\n                }\n                break\n            case 'ArrowUp':\n                if (selectedIndex > 0) {\n                    setSelectedIndex(selectedIndex - 1)\n                }\n                break\n        }\n    }\n\n    let onItemClick = (item: SearchResultItem) => {\n        if (props.onSearchresultClick) {\n            props.onSearchresultClick(item)\n            return\n        }\n\n        if (item.urlSearchParams && new URLSearchParams(window.location.search).toString() !== item.urlSearchParams.toString()) {\n            setSearchText('')\n            setResults([])\n        }\n\n        addClickedSearchResultToPreviousSearches(item, props.keyForPinnedItems)\n\n        api.trackSearch(item.id, item.type)\n\n        let searchParams = new URLSearchParams()\n        let itemFilter = item.urlSearchParams?.get('itemFilter')\n        let apply = item.urlSearchParams?.get('apply')\n        if (itemFilter) {\n            searchParams.set('itemFilter', itemFilter)\n        }\n        if (apply) {\n            searchParams.set('apply', apply)\n        }\n\n        router.push(`${item.route}?${searchParams.toString()}`)\n    }\n\n    let noResultsFoundElement = (\n        <ListGroup.Item\n            key={-1}\n            style={getListItemStyle(-1)}\n            onContextMenu={e => {\n                handleSearchContextMenuForCurrentElement(e)\n            }}\n        >\n            <Image className={styles.searchResultIcon} height={32} width={32} src=\"/Barrier.png\" alt=\"\" />\n            No search results\n        </ListGroup.Item>\n    )\n\n    let getSelectedElement = (): JSX.Element => {\n        if (props.currentElement) {\n            return (\n                <h1 onContextMenu={e => handleSearchContextMenuForCurrentElement(e)} className={styles.current}>\n                    {props.currentElement}\n                </h1>\n            )\n        }\n        if (!props.selected) {\n            return <div />\n        }\n        return (\n            <h1 onContextMenu={e => handleSearchContextMenuForCurrentElement(e)} className={styles.current}>\n                <ClientOnly>\n                    <img\n                        crossOrigin=\"anonymous\"\n                        className=\"playerHeadIcon\"\n                        src={props.type === 'player' ? props.selected.iconUrl : api.getItemImageUrl({ tag: (props.selected as Item).tag })}\n                        height=\"32\"\n                        width=\"32\"\n                        alt=\"\"\n                        style={{ marginRight: '10px', cursor: 'pointer' }}\n                        onClick={() => {\n                            let type = getSetting(ITEM_ICON_TYPE, 'default')\n                            if (type === 'default') {\n                                setSetting(ITEM_ICON_TYPE, 'vanilla')\n                            } else {\n                                setSetting(ITEM_ICON_TYPE, 'default')\n                            }\n                            window.location.reload()\n                        }}\n                    />\n                </ClientOnly>\n                {props.selected.name || convertTagToName((props.selected as Item).tag)}\n                {props.enableReset ? (\n                    <ClearIcon onClick={props.onResetClick} style={{ cursor: 'pointer', color: 'red', marginLeft: '10px', fontWeight: 'bold' }} />\n                ) : null}\n            </h1>\n        )\n    }\n\n    let searchStyle: React.CSSProperties = {\n        backgroundColor: props.backgroundColor,\n        borderRadius: results.length > 0 || noResultsFound ? '0px 10px 0 0' : '0px 10px 10px 0px',\n        borderLeftWidth: 0,\n        borderBottomColor: results.length > 0 || noResultsFound ? '#444' : undefined\n    }\n\n    let searchIconStyle: React.CSSProperties = {\n        width: isSmall ? 'auto' : '58px',\n        borderRadius: results.length > 0 || noResultsFound ? '10px 0 0 0' : '10px 0px 0px 10px',\n        backgroundColor: props.backgroundColor || '#303030',\n        borderBottomColor: results.length > 0 || noResultsFound ? '#444' : undefined,\n        padding: isSmall ? '0px' : undefined\n    }\n\n    function getListItemStyle(i: number): React.CSSProperties {\n        let style = {\n            backgroundColor: i === selectedIndex ? props.backgroundColorSelected || '#444' : props.backgroundColor,\n            borderRadius: i === results.length - 1 ? '0 0 10px 10px' : '',\n            border: 0,\n            borderTop: i === 0 ? '1px solid #444' : 0,\n            borderTopWidth: i === 0 ? 0 : undefined,\n            fontWeigth: 'normal',\n            fontFamily: 'inherit'\n        }\n        if (results[i]) {\n            let isDuplicate = results.findIndex((element, index) => element.dataItem.name === results[i].dataItem.name && index !== i) !== -1\n            if (isDuplicate) {\n                return {\n                    ...getStyleForTier(results[i]?.tier),\n                    ...style\n                }\n            }\n        }\n        return style\n    }\n\n    function checkNameChange(uuid: string) {\n        api.triggerPlayerNameCheck(uuid).then(() => {\n            toast.success('A name check for the player was triggered. This may take a few minutes.')\n        })\n    }\n\n    function handleSearchContextMenuForCurrentElement(event) {\n        if (props.selected && props.type === 'player') {\n            event.preventDefault()\n            showPlayerContextMenu({ event: event })\n        }\n    }\n\n    function handleSearchContextMenuForSearchResult(event: React.MouseEvent<HTMLElement, MouseEvent>, searchResultItem: SearchResultItem) {\n        event.preventDefault()\n        showSearchItemContextMenu({ event: event, props: { item: searchResultItem } })\n    }\n\n    let currentItemContextMenuElement = (\n        <div>\n            <Menu id={PLAYER_SEARCH_CONEXT_MENU_ID} theme={'dark'}>\n                <Item\n                    onClick={params => {\n                        checkNameChange((props.selected as Player).uuid)\n                    }}\n                >\n                    <Refresh style={{ marginRight: '5px' }} />\n                    Trigger check if name has changed\n                </Item>\n            </Menu>\n        </div>\n    )\n\n    let searchItemContextMenuElement = (\n        <div>\n            <Menu id={SEARCH_RESULT_CONTEXT_MENU_ID} theme={'dark'}>\n                <Item\n                    onClick={params => {\n                        let item: SearchResultItem = params.props.item\n                        pinSearchResult(item, props.keyForPinnedItems)\n                        let index = results.findIndex(r => r.dataItem.name === item.dataItem.name)\n                        if (index !== -1) {\n                            let newResults = [...results]\n                            newResults[index].pinned = true\n                            newResults[index].isPreviousSearch = true\n                            setResults(newResults)\n                        }\n                        hideSearchItemContextMenu()\n                    }}\n                    hidden={params => !!params.props.item.pinned}\n                    closeOnClick\n                >\n                    <PushPinIcon />\n                    Pin search result\n                </Item>\n                <Item\n                    onClick={params => {\n                        let item: SearchResultItem = params.props.item\n                        unpinSearchResult(item, props.keyForPinnedItems)\n                        let index = results.findIndex(r => r.dataItem.name === item.dataItem.name)\n                        if (index !== -1) {\n                            let newResults = [...results]\n                            newResults[index].pinned = false\n                            setResults(newResults)\n                        }\n                        hideSearchItemContextMenu()\n                    }}\n                    hidden={params => !params.props.item.pinned}\n                    closeOnClick\n                >\n                    <PushPinIcon />\n                    Unpin search result\n                </Item>\n                <Separator />\n                <Item\n                    onClick={() => {\n                        api.sendFeedback('badSearchResults', {\n                            searchText: searchText,\n                            results: results\n                        })\n                    }}\n                >\n                    <WrongIcon style={{ color: 'red', marginRight: '5px' }} />I didn't find the thing I was looking for!\n                </Item>\n            </Menu>\n        </div>\n    )\n\n    return (\n        <div ref={searchElement} className={styles.search} style={isSmall ? { marginLeft: '-5px', marginRight: '-5px' } : {}}>\n            <Form autoComplete=\"off\">\n                <Form.Group className={styles.searchFormGroup}>\n                    {!isSmall && !props.hideNavbar ? <NavBar /> : ''}\n                    <InputGroup id=\"search-input-group\">\n                        <InputGroup.Text style={searchIconStyle}>\n                            {isSmall && !props.hideNavbar ? (\n                                <div style={{ width: '56px' }}>\n                                    <NavBar hamburgerIconStyle={{ marginRight: '0px', width: '56px' }} />\n                                </div>\n                            ) : (\n                                <SearchIcon />\n                            )}\n                        </InputGroup.Text>\n                        <Form.Control\n                            key=\"search\"\n                            autoFocus={!isMobile}\n                            style={searchStyle}\n                            type=\"text\"\n                            placeholder={props.placeholder || 'Search player/item'}\n                            id={'search-bar'}\n                            className=\"searchBar\"\n                            value={searchText}\n                            onChange={onSearchChange}\n                            onKeyDown={(e: any) => {\n                                onKeyPress(e)\n                            }}\n                            onClick={() => {\n                                if (!props.preventDisplayOfPreviousSearches && !noResultsFound && results.length === 0 && !searchText) {\n                                    let previousResuls = getFirstPreviousSearches(5, props.keyForPinnedItems)\n                                    setResults(previousResuls)\n                                }\n                            }}\n                        />\n                    </InputGroup>\n                </Form.Group>\n            </Form>\n            <ListGroup className={styles.searchResutList}>\n                {noResultsFound\n                    ? noResultsFoundElement\n                    : results.map((result, i) => (\n                          <ListGroup.Item\n                              key={result.id}\n                              action\n                              onClick={(e: any) => {\n                                  onItemClick(result)\n                              }}\n                              style={getListItemStyle(i)}\n                              className={result.isPreviousSearch ? styles.previousSearch : undefined}\n                              onContextMenu={event => {\n                                  handleSearchContextMenuForSearchResult(event, result)\n                              }}\n                          >\n                              {result.dataItem.iconUrl ? (\n                                  <Image\n                                      className={`${styles.searchResultIcon} playerHeadIcon`}\n                                      crossOrigin=\"anonymous\"\n                                      width={32}\n                                      height={32}\n                                      src={\n                                          result.dataItem._imageLoaded\n                                              ? result.dataItem.iconUrl\n                                              : 'data:image/gif;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mPMqAcAAVUA6UpAAT4AAAAASUVORK5CYII='\n                                      }\n                                      alt=\"\"\n                                      onLoad={() => {\n                                          result.dataItem._imageLoaded = true\n                                          setResults(results)\n                                          forceUpdate()\n                                      }}\n                                  />\n                              ) : (\n                                  <Spinner animation=\"border\" role=\"status\" variant=\"primary\" />\n                              )}\n                              {result.pinned ? <PushPinIcon style={{ marginRight: '5px' }} /> : null}\n                              {result.dataItem.name}\n                          </ListGroup.Item>\n                      ))}\n            </ListGroup>\n            <div className={styles.bar} style={{ marginTop: '20px' }}>\n                {getSelectedElement()}\n                {!props.hideOptions ? <OptionsMenu selected={props.selected} /> : null}\n            </div>\n            {searchItemContextMenuElement}\n            {currentItemContextMenuElement}\n        </div>\n    )\n}\n\nexport default Search\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAOA;AACA;;;AA3BA;;;;;;;;;;;;;;;;;;;;;;AA8CA,MAAM,+BAA+B;AACrC,MAAM,gCAAgC;AAEtC,SAAS,OAAO,KAAY;;IACxB,IAAI,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACrB,IAAI,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,IAAI,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB,EAAE;IAC3D,IAAI,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,IAAI,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,IAAI,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,IAAI,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,EAAE,MAAM,qBAAqB,EAAE,GAAG,CAAA,GAAA,uJAAA,CAAA,iBAAc,AAAD,EAAE;QACnD,IAAI;IACR;IACA,MAAM,EAAE,MAAM,yBAAyB,EAAE,SAAS,yBAAyB,EAAE,GAAG,CAAA,GAAA,uJAAA,CAAA,iBAAc,AAAD,EAAE;QAC3F,IAAI;IACR;IACA,MAAM,WAAW,CAAA,GAAA,kHAAA,CAAA,cAAW,AAAD;IAE3B,IAAI,wBAAwB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAEnC,IAAI,gBAAgB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC3B,IAAI,cAAc,CAAA,GAAA,kHAAA,CAAA,iBAAc,AAAD;IAE/B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACN,IAAI,CAAA,GAAA,qHAAA,CAAA,wBAAqB,AAAD,KAAK;gBACzB,WAAW,CAAA,GAAA,qHAAA,CAAA,wBAAqB,AAAD,MAAM,SAAS,IAAI,CAAC,WAAW,GAAG,OAAO;YAC5E;YACA,SAAS,gBAAgB,CAAC,SAAS,qBAAqB;YACxD;oCAAO;oBACH,SAAS,mBAAmB,CAAC,SAAS,qBAAqB;gBAC/D;;QACJ;2BAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACN,cAAc;YACd,WAAW,EAAE;QACjB;2BAAG;QAAC,MAAM,QAAQ;KAAC;IAEnB,IAAI,SAAS;QACT,IAAI,YAAY;QAChB,IAAI,iBAAiB,MAAM,cAAc,IAAI,oHAAA,CAAA,UAAG,CAAC,MAAM;QACvD,eAAe,WAAW,IAAI,CAAC,CAAA;YAC3B,8BAA8B;YAC9B,IACI,cAAc,OAAO,KAAK,QAC1B,cAAc,AAAC,AAAC,cAAc,OAAO,CAAoB,aAAa,CAAC,eAAoC,KAAK,EAClH;gBACE,IAAI,sBAAsB;uBAAI;iBAAc;gBAC5C,IAAI,CAAC,MAAM,gCAAgC,EAAE;oBACzC,sBAAsB,CAAA,GAAA,gIAAA,CAAA,oCAAiC,AAAD,EAAE,WAAW,eAAe,MAAM,iBAAiB;gBAC7G;gBAEA,iBAAiB;gBACjB,kBAAkB,oBAAoB,MAAM,KAAK;gBACjD,WAAW;gBACX,eAAe;gBAEf,IAAI,sBAAsB,OAAO,EAAE;oBAC/B,YAAY,mBAAmB,CAAC,EAAE;oBAClC,sBAAsB,OAAO,GAAG;gBACpC;YACJ;QACJ;IACJ;IAEA,IAAI,iBAAiB,CAAC;QAClB,IAAI,gBAAwB,AAAC,EAAE,MAAM,CAAsB,KAAK;QAChE,aAAa;QACb,cAAc;QACd,eAAe;QAEf,IAAI,kBAAkB,IAAI;YACtB,WAAW,EAAE;YACb,eAAe;YACf;QACJ;QAEA,kBAAkB;QAClB;IACJ;IAEA,SAAS,oBAAoB,GAAG;QAC5B,MAAM,WAAW,cAAc,OAAO;QACtC,IAAI,WAAW,IAAI,MAAM;QAEzB,GAAG;YACC,IAAI,aAAa,UAAU;gBACvB;YACJ;YACA,WAAW,AAAC,SAAiB,UAAU;QAC3C,QAAS,SAAS;QAElB,WAAW,EAAE;IACjB;IAEA,IAAI,aAAa,CAAC;QACd,OAAQ,EAAE,GAAG;YACT,KAAK;gBACD,EAAE,cAAc;gBAChB,IAAI,aAAa;oBACb,sBAAsB,OAAO,GAAG;oBAChC;gBACJ;gBACA,IAAI,CAAC,WAAW,QAAQ,MAAM,KAAK,GAAG;oBAClC;gBACJ;gBACA,YAAY,OAAO,CAAC,cAAc;gBAClC;YACJ,KAAK;gBACD,IAAI,gBAAgB,QAAQ,MAAM,GAAG,GAAG;oBACpC,iBAAiB,gBAAgB;gBACrC;gBACA;YACJ,KAAK;gBACD,IAAI,gBAAgB,GAAG;oBACnB,iBAAiB,gBAAgB;gBACrC;gBACA;QACR;IACJ;IAEA,IAAI,cAAc,CAAC;QACf,IAAI,MAAM,mBAAmB,EAAE;YAC3B,MAAM,mBAAmB,CAAC;YAC1B;QACJ;QAEA,IAAI,KAAK,eAAe,IAAI,IAAI,gBAAgB,OAAO,QAAQ,CAAC,MAAM,EAAE,QAAQ,OAAO,KAAK,eAAe,CAAC,QAAQ,IAAI;YACpH,cAAc;YACd,WAAW,EAAE;QACjB;QAEA,CAAA,GAAA,gIAAA,CAAA,2CAAwC,AAAD,EAAE,MAAM,MAAM,iBAAiB;QAEtE,oHAAA,CAAA,UAAG,CAAC,WAAW,CAAC,KAAK,EAAE,EAAE,KAAK,IAAI;QAElC,IAAI,eAAe,IAAI;QACvB,IAAI,aAAa,KAAK,eAAe,EAAE,IAAI;QAC3C,IAAI,QAAQ,KAAK,eAAe,EAAE,IAAI;QACtC,IAAI,YAAY;YACZ,aAAa,GAAG,CAAC,cAAc;QACnC;QACA,IAAI,OAAO;YACP,aAAa,GAAG,CAAC,SAAS;QAC9B;QAEA,OAAO,IAAI,CAAC,GAAG,KAAK,KAAK,CAAC,CAAC,EAAE,aAAa,QAAQ,IAAI;IAC1D;IAEA,IAAI,sCACA,6LAAC,iMAAA,CAAA,YAAS,CAAC,IAAI;QAEX,OAAO,iBAAiB,CAAC;QACzB,eAAe,CAAA;YACX,yCAAyC;QAC7C;;0BAEA,6LAAC,gIAAA,CAAA,UAAK;gBAAC,WAAW,6IAAA,CAAA,UAAM,CAAC,gBAAgB;gBAAE,QAAQ;gBAAI,OAAO;gBAAI,KAAI;gBAAe,KAAI;;;;;;YAAK;;OANzF,CAAC;;;;;IAWd,IAAI,qBAAqB;QACrB,IAAI,MAAM,cAAc,EAAE;YACtB,qBACI,6LAAC;gBAAG,eAAe,CAAA,IAAK,yCAAyC;gBAAI,WAAW,6IAAA,CAAA,UAAM,CAAC,OAAO;0BACzF,MAAM,cAAc;;;;;;QAGjC;QACA,IAAI,CAAC,MAAM,QAAQ,EAAE;YACjB,qBAAO,6LAAC;;;;;QACZ;QACA,qBACI,6LAAC;YAAG,eAAe,CAAA,IAAK,yCAAyC;YAAI,WAAW,6IAAA,CAAA,UAAM,CAAC,OAAO;;8BAC1F,6LAAC,0IAAA,CAAA,UAAU;8BACP,cAAA,6LAAC;wBACG,aAAY;wBACZ,WAAU;wBACV,KAAK,MAAM,IAAI,KAAK,WAAW,MAAM,QAAQ,CAAC,OAAO,GAAG,oHAAA,CAAA,UAAG,CAAC,eAAe,CAAC;4BAAE,KAAK,AAAC,MAAM,QAAQ,CAAU,GAAG;wBAAC;wBAChH,QAAO;wBACP,OAAM;wBACN,KAAI;wBACJ,OAAO;4BAAE,aAAa;4BAAQ,QAAQ;wBAAU;wBAChD,SAAS;4BACL,IAAI,OAAO,CAAA,GAAA,0HAAA,CAAA,aAAU,AAAD,EAAE,0HAAA,CAAA,iBAAc,EAAE;4BACtC,IAAI,SAAS,WAAW;gCACpB,CAAA,GAAA,0HAAA,CAAA,aAAU,AAAD,EAAE,0HAAA,CAAA,iBAAc,EAAE;4BAC/B,OAAO;gCACH,CAAA,GAAA,0HAAA,CAAA,aAAU,AAAD,EAAE,0HAAA,CAAA,iBAAc,EAAE;4BAC/B;4BACA,OAAO,QAAQ,CAAC,MAAM;wBAC1B;;;;;;;;;;;gBAGP,MAAM,QAAQ,CAAC,IAAI,IAAI,CAAA,GAAA,sHAAA,CAAA,mBAAgB,AAAD,EAAE,AAAC,MAAM,QAAQ,CAAU,GAAG;gBACpE,MAAM,WAAW,iBACd,6LAAC,6JAAA,CAAA,UAAS;oBAAC,SAAS,MAAM,YAAY;oBAAE,OAAO;wBAAE,QAAQ;wBAAW,OAAO;wBAAO,YAAY;wBAAQ,YAAY;oBAAO;;;;;2BACzH;;;;;;;IAGhB;IAEA,IAAI,cAAmC;QACnC,iBAAiB,MAAM,eAAe;QACtC,cAAc,QAAQ,MAAM,GAAG,KAAK,iBAAiB,iBAAiB;QACtE,iBAAiB;QACjB,mBAAmB,QAAQ,MAAM,GAAG,KAAK,iBAAiB,SAAS;IACvE;IAEA,IAAI,kBAAuC;QACvC,OAAO,UAAU,SAAS;QAC1B,cAAc,QAAQ,MAAM,GAAG,KAAK,iBAAiB,eAAe;QACpE,iBAAiB,MAAM,eAAe,IAAI;QAC1C,mBAAmB,QAAQ,MAAM,GAAG,KAAK,iBAAiB,SAAS;QACnE,SAAS,UAAU,QAAQ;IAC/B;IAEA,SAAS,iBAAiB,CAAS;QAC/B,IAAI,QAAQ;YACR,iBAAiB,MAAM,gBAAgB,MAAM,uBAAuB,IAAI,SAAS,MAAM,eAAe;YACtG,cAAc,MAAM,QAAQ,MAAM,GAAG,IAAI,kBAAkB;YAC3D,QAAQ;YACR,WAAW,MAAM,IAAI,mBAAmB;YACxC,gBAAgB,MAAM,IAAI,IAAI;YAC9B,YAAY;YACZ,YAAY;QAChB;QACA,IAAI,OAAO,CAAC,EAAE,EAAE;YACZ,IAAI,cAAc,QAAQ,SAAS,CAAC,CAAC,SAAS,QAAU,QAAQ,QAAQ,CAAC,IAAI,KAAK,OAAO,CAAC,EAAE,CAAC,QAAQ,CAAC,IAAI,IAAI,UAAU,OAAO,CAAC;YAChI,IAAI,aAAa;gBACb,OAAO;oBACH,GAAG,CAAA,GAAA,sHAAA,CAAA,kBAAe,AAAD,EAAE,OAAO,CAAC,EAAE,EAAE,KAAK;oBACpC,GAAG,KAAK;gBACZ;YACJ;QACJ;QACA,OAAO;IACX;IAEA,SAAS,gBAAgB,IAAY;QACjC,oHAAA,CAAA,UAAG,CAAC,sBAAsB,CAAC,MAAM,IAAI,CAAC;YAClC,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAClB;IACJ;IAEA,SAAS,yCAAyC,KAAK;QACnD,IAAI,MAAM,QAAQ,IAAI,MAAM,IAAI,KAAK,UAAU;YAC3C,MAAM,cAAc;YACpB,sBAAsB;gBAAE,OAAO;YAAM;QACzC;IACJ;IAEA,SAAS,uCAAuC,KAAgD,EAAE,gBAAkC;QAChI,MAAM,cAAc;QACpB,0BAA0B;YAAE,OAAO;YAAO,OAAO;gBAAE,MAAM;YAAiB;QAAE;IAChF;IAEA,IAAI,8CACA,6LAAC;kBACG,cAAA,6LAAC,uJAAA,CAAA,OAAI;YAAC,IAAI;YAA8B,OAAO;sBAC3C,cAAA,6LAAC,uJAAA,CAAA,OAAI;gBACD,SAAS,CAAA;oBACL,gBAAgB,AAAC,MAAM,QAAQ,CAAY,IAAI;gBACnD;;kCAEA,6LAAC,+JAAA,CAAA,UAAO;wBAAC,OAAO;4BAAE,aAAa;wBAAM;;;;;;oBAAK;;;;;;;;;;;;;;;;;IAO1D,IAAI,6CACA,6LAAC;kBACG,cAAA,6LAAC,uJAAA,CAAA,OAAI;YAAC,IAAI;YAA+B,OAAO;;8BAC5C,6LAAC,uJAAA,CAAA,OAAI;oBACD,SAAS,CAAA;wBACL,IAAI,OAAyB,OAAO,KAAK,CAAC,IAAI;wBAC9C,CAAA,GAAA,gIAAA,CAAA,kBAAe,AAAD,EAAE,MAAM,MAAM,iBAAiB;wBAC7C,IAAI,QAAQ,QAAQ,SAAS,CAAC,CAAA,IAAK,EAAE,QAAQ,CAAC,IAAI,KAAK,KAAK,QAAQ,CAAC,IAAI;wBACzE,IAAI,UAAU,CAAC,GAAG;4BACd,IAAI,aAAa;mCAAI;6BAAQ;4BAC7B,UAAU,CAAC,MAAM,CAAC,MAAM,GAAG;4BAC3B,UAAU,CAAC,MAAM,CAAC,gBAAgB,GAAG;4BACrC,WAAW;wBACf;wBACA;oBACJ;oBACA,QAAQ,CAAA,SAAU,CAAC,CAAC,OAAO,KAAK,CAAC,IAAI,CAAC,MAAM;oBAC5C,YAAY;;sCAEZ,6LAAC,+JAAA,CAAA,UAAW;;;;;wBAAG;;;;;;;8BAGnB,6LAAC,uJAAA,CAAA,OAAI;oBACD,SAAS,CAAA;wBACL,IAAI,OAAyB,OAAO,KAAK,CAAC,IAAI;wBAC9C,CAAA,GAAA,gIAAA,CAAA,oBAAiB,AAAD,EAAE,MAAM,MAAM,iBAAiB;wBAC/C,IAAI,QAAQ,QAAQ,SAAS,CAAC,CAAA,IAAK,EAAE,QAAQ,CAAC,IAAI,KAAK,KAAK,QAAQ,CAAC,IAAI;wBACzE,IAAI,UAAU,CAAC,GAAG;4BACd,IAAI,aAAa;mCAAI;6BAAQ;4BAC7B,UAAU,CAAC,MAAM,CAAC,MAAM,GAAG;4BAC3B,WAAW;wBACf;wBACA;oBACJ;oBACA,QAAQ,CAAA,SAAU,CAAC,OAAO,KAAK,CAAC,IAAI,CAAC,MAAM;oBAC3C,YAAY;;sCAEZ,6LAAC,+JAAA,CAAA,UAAW;;;;;wBAAG;;;;;;;8BAGnB,6LAAC,uJAAA,CAAA,YAAS;;;;;8BACV,6LAAC,uJAAA,CAAA,OAAI;oBACD,SAAS;wBACL,oHAAA,CAAA,UAAG,CAAC,YAAY,CAAC,oBAAoB;4BACjC,YAAY;4BACZ,SAAS;wBACb;oBACJ;;sCAEA,6LAAC,iKAAA,CAAA,UAAS;4BAAC,OAAO;gCAAE,OAAO;gCAAO,aAAa;4BAAM;;;;;;wBAAK;;;;;;;;;;;;;;;;;;IAM1E,qBACI,6LAAC;QAAI,KAAK;QAAe,WAAW,6IAAA,CAAA,UAAM,CAAC,MAAM;QAAE,OAAO,UAAU;YAAE,YAAY;YAAQ,aAAa;QAAO,IAAI,CAAC;;0BAC/G,6LAAC,uLAAA,CAAA,OAAI;gBAAC,cAAa;0BACf,cAAA,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;oBAAC,WAAW,6IAAA,CAAA,UAAM,CAAC,eAAe;;wBACxC,CAAC,WAAW,CAAC,MAAM,UAAU,iBAAG,6LAAC,kIAAA,CAAA,UAAM;;;;mCAAM;sCAC9C,6LAAC,mMAAA,CAAA,aAAU;4BAAC,IAAG;;8CACX,6LAAC,mMAAA,CAAA,aAAU,CAAC,IAAI;oCAAC,OAAO;8CACnB,WAAW,CAAC,MAAM,UAAU,iBACzB,6LAAC;wCAAI,OAAO;4CAAE,OAAO;wCAAO;kDACxB,cAAA,6LAAC,kIAAA,CAAA,UAAM;4CAAC,oBAAoB;gDAAE,aAAa;gDAAO,OAAO;4CAAO;;;;;;;;;;6DAGpE,6LAAC,sKAAA,CAAA,UAAU;;;;;;;;;;8CAGnB,6LAAC,uLAAA,CAAA,OAAI,CAAC,OAAO;oCAET,WAAW,CAAC;oCACZ,OAAO;oCACP,MAAK;oCACL,aAAa,MAAM,WAAW,IAAI;oCAClC,IAAI;oCACJ,WAAU;oCACV,OAAO;oCACP,UAAU;oCACV,WAAW,CAAC;wCACR,WAAW;oCACf;oCACA,SAAS;wCACL,IAAI,CAAC,MAAM,gCAAgC,IAAI,CAAC,kBAAkB,QAAQ,MAAM,KAAK,KAAK,CAAC,YAAY;4CACnG,IAAI,iBAAiB,CAAA,GAAA,gIAAA,CAAA,2BAAwB,AAAD,EAAE,GAAG,MAAM,iBAAiB;4CACxE,WAAW;wCACf;oCACJ;mCAjBI;;;;;;;;;;;;;;;;;;;;;;0BAsBpB,6LAAC,iMAAA,CAAA,YAAS;gBAAC,WAAW,6IAAA,CAAA,UAAM,CAAC,eAAe;0BACvC,iBACK,wBACA,QAAQ,GAAG,CAAC,CAAC,QAAQ,kBACjB,6LAAC,iMAAA,CAAA,YAAS,CAAC,IAAI;wBAEX,MAAM;wBACN,SAAS,CAAC;4BACN,YAAY;wBAChB;wBACA,OAAO,iBAAiB;wBACxB,WAAW,OAAO,gBAAgB,GAAG,6IAAA,CAAA,UAAM,CAAC,cAAc,GAAG;wBAC7D,eAAe,CAAA;4BACX,uCAAuC,OAAO;wBAClD;;4BAEC,OAAO,QAAQ,CAAC,OAAO,iBACpB,6LAAC,gIAAA,CAAA,UAAK;gCACF,WAAW,GAAG,6IAAA,CAAA,UAAM,CAAC,gBAAgB,CAAC,eAAe,CAAC;gCACtD,aAAY;gCACZ,OAAO;gCACP,QAAQ;gCACR,KACI,OAAO,QAAQ,CAAC,YAAY,GACtB,OAAO,QAAQ,CAAC,OAAO,GACvB;gCAEV,KAAI;gCACJ,QAAQ;oCACJ,OAAO,QAAQ,CAAC,YAAY,GAAG;oCAC/B,WAAW;oCACX;gCACJ;;;;;qDAGJ,6LAAC,6LAAA,CAAA,UAAO;gCAAC,WAAU;gCAAS,MAAK;gCAAS,SAAQ;;;;;;4BAErD,OAAO,MAAM,iBAAG,6LAAC,+JAAA,CAAA,UAAW;gCAAC,OAAO;oCAAE,aAAa;gCAAM;;;;;uCAAQ;4BACjE,OAAO,QAAQ,CAAC,IAAI;;uBAjChB,OAAO,EAAE;;;;;;;;;;0BAqChC,6LAAC;gBAAI,WAAW,6IAAA,CAAA,UAAM,CAAC,GAAG;gBAAE,OAAO;oBAAE,WAAW;gBAAO;;oBAClD;oBACA,CAAC,MAAM,WAAW,iBAAG,6LAAC,4IAAA,CAAA,UAAW;wBAAC,UAAU,MAAM,QAAQ;;;;;+BAAO;;;;;;;YAErE;YACA;;;;;;;AAGb;GA9ZS;;QACQ,qIAAA,CAAA,YAAS;QAOkB,uJAAA,CAAA,iBAAc;QAG0B,uJAAA,CAAA,iBAAc;QAG7E,kHAAA,CAAA,cAAW;QAKV,kHAAA,CAAA,iBAAc;;;KAnB3B;uCAgaM", "debugId": null}}, {"offset": {"line": 1439, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/Search/ApiSearchField.tsx"], "sourcesContent": ["'use client'\nimport { forwardRef, Ref, useRef, useState } from 'react'\nimport { AsyncTypeahead } from 'react-bootstrap-typeahead'\nimport { v4 as generateUUID } from 'uuid'\nimport Typeahead from 'react-bootstrap-typeahead/types/core/Typeahead'\nimport api from '../../api/ApiHelper'\nimport { Option } from 'react-bootstrap-typeahead/types/types'\nimport styles from './Search.module.css'\nimport Image from 'next/image'\nimport { getStyleForTier } from '../../utils/Formatter'\nimport { Form } from 'react-bootstrap'\n\ninterface Props {\n    onChange(selected: SearchResultItem[], searchText?: string)\n    disabled?: boolean\n    placeholder?: string\n    defaultValue?: string\n    searchFunction?(searchText: string): Promise<SearchResultItem[]>\n    selected?: SearchResultItem[]\n    defaultSelected?: SearchResultItem[]\n    className?: string\n    multiple: boolean\n}\n\nexport default (props: Props) => {\n    let [uuid] = useState(generateUUID())\n    let [results, setResults] = useState<SearchResultItem[]>([])\n    let [isLoading, setIsLoading] = useState(false)\n    let ref = useRef<Typeahead>(null)\n\n    function _onChange(selected: Option[]) {\n        props.onChange(selected as SearchResultItem[], ref?.current?.getInput()?.value)\n    }\n\n    function handleSearch(query) {\n        setIsLoading(true)\n\n        let searchFunction = props.searchFunction || api.search\n\n        searchFunction(query).then(results => {\n            setResults(\n                results.map(r => {\n                    return {\n                        label: r.dataItem.name || '-',\n                        ...r\n                    }\n                })\n            )\n            setIsLoading(false)\n        })\n    }\n\n    return (\n        <AsyncTypeahead\n            id={uuid}\n            className={`${styles.multiSearch} ${props.className}`}\n            disabled={props.disabled}\n            inputProps={{ className: styles.multiInputfield }}\n            filterBy={() => true}\n            isLoading={isLoading}\n            key={uuid}\n            labelKey=\"label\"\n            renderMenuItemChildren={(option, { text }) => {\n                let o: any = option\n                let isDuplicate = results.filter((element, index) => element.dataItem.name === o.dataItem.name).length > 1\n                return (\n                    <>\n                        <Image\n                            className={`${styles.searchResultIcon} playerHeadIcon`}\n                            crossOrigin=\"anonymous\"\n                            width={32}\n                            height={32}\n                            src={o.dataItem.iconUrl}\n                            alt=\"\"\n                        />\n                        <span style={isDuplicate ? getStyleForTier(o.tier) : undefined}>{o.label}</span>\n                    </>\n                )\n            }}\n            defaultSelected={props.defaultSelected}\n            minLength={1}\n            onSearch={handleSearch}\n            defaultInputValue={props.defaultValue}\n            selected={props.selected}\n            options={results}\n            placeholder={props.placeholder || 'Search item...'}\n            onChange={_onChange}\n            ref={ref}\n            multiple={props.multiple}\n        />\n    )\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AACA;AAEA;AAEA;AACA;AACA;;;AATA;;;;;;;;0CAwBe,CAAC;;IACZ,IAAI,CAAC,KAAK,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,GAAA,wLAAA,CAAA,KAAY,AAAD;IACjC,IAAI,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB,EAAE;IAC3D,IAAI,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,IAAI,MAAM,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAa;IAE5B,SAAS,UAAU,QAAkB;QACjC,MAAM,QAAQ,CAAC,UAAgC,KAAK,SAAS,YAAY;IAC7E;IAEA,SAAS,aAAa,KAAK;QACvB,aAAa;QAEb,IAAI,iBAAiB,MAAM,cAAc,IAAI,oHAAA,CAAA,UAAG,CAAC,MAAM;QAEvD,eAAe,OAAO,IAAI,CAAC,CAAA;YACvB,WACI,QAAQ,GAAG,CAAC,CAAA;gBACR,OAAO;oBACH,OAAO,EAAE,QAAQ,CAAC,IAAI,IAAI;oBAC1B,GAAG,CAAC;gBACR;YACJ;YAEJ,aAAa;QACjB;IACJ;IAEA,qBACI,6LAAC,uPAAA,CAAA,iBAAc;QACX,IAAI;QACJ,WAAW,GAAG,6IAAA,CAAA,UAAM,CAAC,WAAW,CAAC,CAAC,EAAE,MAAM,SAAS,EAAE;QACrD,UAAU,MAAM,QAAQ;QACxB,YAAY;YAAE,WAAW,6IAAA,CAAA,UAAM,CAAC,eAAe;QAAC;QAChD,UAAU,IAAM;QAChB,WAAW;QAEX,UAAS;QACT,wBAAwB,CAAC,QAAQ,EAAE,IAAI,EAAE;YACrC,IAAI,IAAS;YACb,IAAI,cAAc,QAAQ,MAAM,CAAC,CAAC,SAAS,QAAU,QAAQ,QAAQ,CAAC,IAAI,KAAK,EAAE,QAAQ,CAAC,IAAI,EAAE,MAAM,GAAG;YACzG,qBACI;;kCACI,6LAAC,gIAAA,CAAA,UAAK;wBACF,WAAW,GAAG,6IAAA,CAAA,UAAM,CAAC,gBAAgB,CAAC,eAAe,CAAC;wBACtD,aAAY;wBACZ,OAAO;wBACP,QAAQ;wBACR,KAAK,EAAE,QAAQ,CAAC,OAAO;wBACvB,KAAI;;;;;;kCAER,6LAAC;wBAAK,OAAO,cAAc,CAAA,GAAA,sHAAA,CAAA,kBAAe,AAAD,EAAE,EAAE,IAAI,IAAI;kCAAY,EAAE,KAAK;;;;;;;;QAGpF;QACA,iBAAiB,MAAM,eAAe;QACtC,WAAW;QACX,UAAU;QACV,mBAAmB,MAAM,YAAY;QACrC,UAAU,MAAM,QAAQ;QACxB,SAAS;QACT,aAAa,MAAM,WAAW,IAAI;QAClC,UAAU;QACV,KAAK;QACL,UAAU,MAAM,QAAQ;OA5BnB;;;;;AA+BjB", "debugId": null}}, {"offset": {"line": 1546, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/ClientOnly/ClientOnly.tsx"], "sourcesContent": ["import { useEffect, useState } from 'react'\n\nexport default function ClientOnly({ children }) {\n    const [mounted, setMounted] = useState(false)\n\n    useEffect(() => {\n        setMounted(true)\n    }, [])\n\n    return mounted ? children : null\n}\n"], "names": [], "mappings": ";;;AAAA;;;AAEe,SAAS,WAAW,EAAE,QAAQ,EAAE;;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACN,WAAW;QACf;+BAAG,EAAE;IAEL,OAAO,UAAU,WAAW;AAChC;GARwB;KAAA", "debugId": null}}, {"offset": {"line": 1575, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/CopyButton/CopyButton.tsx"], "sourcesContent": ["'use client'\nimport { useMatomo } from '@jonkoops/matomo-tracker-react'\nimport React, { useEffect, useState, type JSX } from 'react'\nimport { Button } from 'react-bootstrap'\nimport { toast } from 'react-toastify'\nimport { isClientSideRendering } from '../../utils/SSRUtils'\nimport { canUseClipBoard, writeToClipboard } from '../../utils/ClipboardUtils'\n\ninterface Props {\n    onCopy?()\n    successMessage?: JSX.Element\n    copyValue?: string | null\n    buttonWrapperClass?: string\n    buttonClass?: string\n    buttonVariant?: string\n    forceIsCopied?: boolean\n    buttonStyle?: React.CSSProperties\n    buttonContent?: JSX.Element\n}\n\nexport function CopyButton(props: Props) {\n    let [isCopied, setIsCopied] = useState(false)\n    let { trackEvent } = useMatomo()\n    let [isSSR, setIsSSR] = useState(true)\n\n    useEffect(() => {\n        setIsSSR(false)\n    })\n\n    let copyIcon = (\n        <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" fill=\"currentColor\" className=\"bi bi-clipboard\" viewBox=\"0 0 16 16\">\n            <path d=\"M4 1.5H3a2 2 0 0 0-2 2V14a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V3.5a2 2 0 0 0-2-2h-1v1h1a1 1 0 0 1 1 1V14a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V3.5a1 1 0 0 1 1-1h1v-1z\" />\n            <path d=\"M9.5 1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5v-1a.5.5 0 0 1 .5-.5h3zm-3-1A1.5 1.5 0 0 0 5 1.5v1A1.5 1.5 0 0 0 6.5 4h3A1.5 1.5 0 0 0 11 2.5v-1A1.5 1.5 0 0 0 9.5 0h-3z\" />\n        </svg>\n    )\n\n    let copiedIcon = (\n        <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" fill=\"currentColor\" className=\"bi bi-clipboard-check\" viewBox=\"0 0 16 16\">\n            <path\n                fillRule=\"evenodd\"\n                d=\"M10.854 7.146a.5.5 0 0 1 0 .708l-3 3a.5.5 0 0 1-.708 0l-1.5-1.5a.5.5 0 1 1 .708-.708L7.5 9.793l2.646-2.647a.5.5 0 0 1 .708 0z\"\n            />\n            <path d=\"M4 1.5H3a2 2 0 0 0-2 2V14a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V3.5a2 2 0 0 0-2-2h-1v1h1a1 1 0 0 1 1 1V14a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V3.5a1 1 0 0 1 1-1h1v-1z\" />\n            <path d=\"M9.5 1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5v-1a.5.5 0 0 1 .5-.5h3zm-3-1A1.5 1.5 0 0 0 5 1.5v1A1.5 1.5 0 0 0 6.5 4h3A1.5 1.5 0 0 0 11 2.5v-1A1.5 1.5 0 0 0 9.5 0h-3z\" />\n        </svg>\n    )\n\n    function copyClick() {\n        setIsCopied(true)\n        if (props.copyValue) {\n            writeToClipboard(props.copyValue)\n            trackEvent({\n                category: 'copyButtonClick',\n                action: props.copyValue\n            })\n        }\n        if (props.onCopy) {\n            props.onCopy()\n        }\n        if (props.successMessage) {\n            toast.success(props.successMessage)\n        }\n    }\n\n    return (\n        <span>\n            {!isSSR && canUseClipBoard() ? (\n                <span className={props.buttonWrapperClass}>\n                    <Button\n                        style={props.buttonStyle}\n                        onMouseDown={copyClick}\n                        className={props.buttonClass}\n                        aria-label=\"copy to clipboard\"\n                        variant={props.buttonVariant || 'secondary'}\n                    >\n                        {isCopied || props.forceIsCopied ? copiedIcon : copyIcon}\n                        {props.buttonContent}\n                    </Button>\n                </span>\n            ) : (\n                ''\n            )}\n        </span>\n    )\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AAEA;;;AANA;;;;;;AAoBO,SAAS,WAAW,KAAY;;IACnC,IAAI,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,IAAI,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,sNAAA,CAAA,YAAS,AAAD;IAC7B,IAAI,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACN,SAAS;QACb;;IAEA,IAAI,yBACA,6LAAC;QAAI,OAAM;QAA6B,OAAM;QAAK,QAAO;QAAK,MAAK;QAAe,WAAU;QAAkB,SAAQ;;0BACnH,6LAAC;gBAAK,GAAE;;;;;;0BACR,6LAAC;gBAAK,GAAE;;;;;;;;;;;;IAIhB,IAAI,2BACA,6LAAC;QAAI,OAAM;QAA6B,OAAM;QAAK,QAAO;QAAK,MAAK;QAAe,WAAU;QAAwB,SAAQ;;0BACzH,6LAAC;gBACG,UAAS;gBACT,GAAE;;;;;;0BAEN,6LAAC;gBAAK,GAAE;;;;;;0BACR,6LAAC;gBAAK,GAAE;;;;;;;;;;;;IAIhB,SAAS;QACL,YAAY;QACZ,IAAI,MAAM,SAAS,EAAE;YACjB,CAAA,GAAA,2HAAA,CAAA,mBAAgB,AAAD,EAAE,MAAM,SAAS;YAChC,WAAW;gBACP,UAAU;gBACV,QAAQ,MAAM,SAAS;YAC3B;QACJ;QACA,IAAI,MAAM,MAAM,EAAE;YACd,MAAM,MAAM;QAChB;QACA,IAAI,MAAM,cAAc,EAAE;YACtB,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC,MAAM,cAAc;QACtC;IACJ;IAEA,qBACI,6LAAC;kBACI,CAAC,SAAS,CAAA,GAAA,2HAAA,CAAA,kBAAe,AAAD,oBACrB,6LAAC;YAAK,WAAW,MAAM,kBAAkB;sBACrC,cAAA,6LAAC,2LAAA,CAAA,SAAM;gBACH,OAAO,MAAM,WAAW;gBACxB,aAAa;gBACb,WAAW,MAAM,WAAW;gBAC5B,cAAW;gBACX,SAAS,MAAM,aAAa,IAAI;;oBAE/B,YAAY,MAAM,aAAa,GAAG,aAAa;oBAC/C,MAAM,aAAa;;;;;;;;;;;mBAI5B;;;;;;AAIhB;GAhEgB;;QAES,sNAAA,CAAA,YAAS;;;KAFlB", "debugId": null}}, {"offset": {"line": 1728, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/Number/Number.tsx"], "sourcesContent": ["'use client'\nimport React, { useEffect, useState } from 'react'\nimport { numberWithThousandsSeparators } from '../../utils/Formatter'\n\ninterface Props {\n    number: number | string\n}\n\nexport default function NumberElement(props: Props) {\n    let [isSSR, setIsSSR] = useState(true)\n\n    let value = Number(props.number)\n\n    useEffect(() => {\n        setIsSSR(false)\n    }, [])\n\n    // Use consistent formatting to prevent hydration mismatches\n    // Always use comma as thousand separator and period as decimal separator\n    return <span suppressHydrationWarning>{numberWithThousandsSeparators(value, ',', '.')}</span>\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;;;AAFA;;;AAQe,SAAS,cAAc,KAAY;;IAC9C,IAAI,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjC,IAAI,QAAQ,OAAO,MAAM,MAAM;IAE/B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACN,SAAS;QACb;kCAAG,EAAE;IAEL,4DAA4D;IAC5D,yEAAyE;IACzE,qBAAO,6LAAC;QAAK,wBAAwB;kBAAE,CAAA,GAAA,sHAAA,CAAA,gCAA6B,AAAD,EAAE,OAAO,KAAK;;;;;;AACrF;GAZwB;KAAA", "debugId": null}}, {"offset": {"line": 1771, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/components/GoogleSignIn/GoogleSignIn.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"googleButton\": \"GoogleSignIn-module__lTSYOa__googleButton\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA"}}, {"offset": {"line": 1780, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/GoogleSignIn/GoogleSignIn.tsx"], "sourcesContent": ["'use client'\nimport React, { useEffect, useState } from 'react'\nimport { toast } from 'react-toastify'\nimport api from '../../api/ApiHelper'\nimport { useMatomo } from '@jonkoops/matomo-tracker-react'\nimport { useForceUpdate } from '../../utils/Hooks'\nimport { isClientSideRendering } from '../../utils/SSRUtils'\nimport { CUSTOM_EVENTS } from '../../api/ApiTypes.d'\nimport { GoogleLogin } from '@react-oauth/google'\nimport styles from './GoogleSignIn.module.css'\nimport { GOOGLE_EMAIL, GOOGLE_NAME, GOOGLE_PROFILE_PICTURE_URL, setSetting } from '../../utils/SettingsUtils'\nimport { atobUnicode } from '../../utils/Base64Utils'\nimport { Modal } from 'react-bootstrap'\n\ninterface Props {\n    onAfterLogin?(): void\n    onLoginFail?(): void\n    onManualLoginClick?(): void\n    rerenderFlip?: number\n}\n\nfunction GoogleSignIn(props: Props) {\n    let [wasAlreadyLoggedInThisSession, setWasAlreadyLoggedInThisSession] = useState(\n        isClientSideRendering() ? isValidTokenAvailable(localStorage.getItem('googleId')) : false\n    )\n\n    let [isLoggedIn, setIsLoggedIn] = useState(false)\n    let [isSSR, setIsSSR] = useState(true)\n    let [isLoginNotShowing, setIsLoginNotShowing] = useState(false)\n    let [showButtonNotRenderingModal, setShowButtonNotRenderingModal] = useState(false)\n    let { trackEvent } = useMatomo()\n    let forceUpdate = useForceUpdate()\n\n    useEffect(() => {\n        setIsSSR(false)\n        if (wasAlreadyLoggedInThisSession) {\n            let token = localStorage.getItem('googleId')!\n            let userObject = JSON.parse(atobUnicode(token.split('.')[1]))\n            setSetting(GOOGLE_EMAIL, userObject.email)\n            onLoginSucces(token)\n        } else {\n            setTimeout(() => {\n                let isShown = false\n                document.querySelectorAll('iframe').forEach(e => {\n                    if (e.src && e.src.includes('accounts.google.com')) {\n                        isShown = true\n                    }\n                })\n                if (!isShown) {\n                    setIsLoggedIn(false)\n                    setIsLoginNotShowing(true)\n                    sessionStorage.removeItem('googleId')\n                    localStorage.removeItem('googleId')\n                }\n            }, 5000)\n        }\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [])\n\n    useEffect(() => {\n        if (wasAlreadyLoggedInThisSession) {\n            setIsLoggedIn(true)\n        }\n    }, [wasAlreadyLoggedInThisSession])\n\n    useEffect(() => {\n        forceUpdate()\n        setIsLoggedIn(sessionStorage.getItem('googleId') !== null)\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [props.rerenderFlip])\n\n    function onLoginSucces(token: string) {\n        setIsLoggedIn(true)\n        api.loginWithToken(token)\n            .then(token => {\n                localStorage.setItem('googleId', token)\n                sessionStorage.setItem('googleId', token)\n                let refId = (window as any).refId\n                if (refId) {\n                    api.setRef(refId)\n                }\n                document.dispatchEvent(new CustomEvent(CUSTOM_EVENTS.GOOGLE_LOGIN))\n                if (props.onAfterLogin) {\n                    props.onAfterLogin()\n                }\n            })\n            .catch(error => {\n                // dont show the error message for the invalid token error\n                // the google sign component sometimes sends an outdated token, causing this error\n                if (error.slug !== 'invalid_token') {\n                    toast.error(`An error occoured while trying to sign in with Google. ${error ? error.slug || JSON.stringify(error) : null}`)\n                } else {\n                    console.warn('setGoogle: Invalid token error', error)\n                    sessionStorage.removeItem('googleId')\n                    localStorage.removeItem('googleId')\n                }\n                setIsLoggedIn(false)\n                setWasAlreadyLoggedInThisSession(false)\n                sessionStorage.removeItem('googleId')\n                localStorage.removeItem('googleId')\n            })\n    }\n\n    function onLoginFail() {\n        toast.error('Something went wrong, please try again.', { autoClose: 20000 })\n    }\n\n    function onLoginClick() {\n        if (props.onManualLoginClick) {\n            props.onManualLoginClick()\n        }\n        trackEvent({\n            category: 'login',\n            action: 'click'\n        })\n    }\n\n    let style: React.CSSProperties = isLoggedIn\n        ? {\n              visibility: 'collapse',\n              height: 0\n          }\n        : {}\n\n    if (isSSR) {\n        return null\n    }\n\n    let buttonNotRenderingModal = (\n        <Modal\n            show={showButtonNotRenderingModal}\n            onHide={() => {\n                setShowButtonNotRenderingModal(false)\n            }}\n        >\n            <Modal.Header>\n                <Modal.Title>Google Login button not showing up?</Modal.Title>\n            </Modal.Header>\n            <Modal.Body>\n                <p>This is most likely caused by either an external software like an anti virus or your browser/extension blocking it.</p>\n                <hr />\n                <p>Known issues:</p>\n                <ul>\n                    <li>Kaspersky's \"Secure Browse\" feature seems to block the Google login.</li>\n                    <li>Opera GX seems to sometimes blocks the login button. The specific setting or reason on when it blocks it is unknown.</li>\n                </ul>\n            </Modal.Body>\n        </Modal>\n    )\n\n    return (\n        <div style={style} onClickCapture={onLoginClick}>\n            {!wasAlreadyLoggedInThisSession ? (\n                <>\n                    <div className={styles.googleButton}>\n                        {!isSSR ? (\n                            <GoogleLogin\n                                onSuccess={response => {\n                                    try {\n                                        let userObject = JSON.parse(atobUnicode(response.credential!.split('.')[1]))\n                                        setSetting(GOOGLE_PROFILE_PICTURE_URL, userObject.picture)\n                                        setSetting(GOOGLE_EMAIL, userObject.email)\n                                        setSetting(GOOGLE_NAME, userObject.name)\n                                    } catch {\n                                        toast.warn('Parsing issue with the google token. There might be issues when displaying details on the account page!')\n                                    }\n                                    onLoginSucces(response.credential!)\n                                }}\n                                onError={onLoginFail}\n                                theme={'filled_blue'}\n                                size={'large'}\n                                useOneTap\n                                auto_select\n                            />\n                        ) : null}\n                    </div>\n                    <p>\n                        I have read and agree to the <a href=\"https://coflnet.com/privacy\">Privacy Policy</a>\n                    </p>\n                    {isLoginNotShowing ? (\n                        <p>\n                            Login button not showing? Click{' '}\n                            <span\n                                style={{ color: '#007bff', cursor: 'pointer' }}\n                                onClick={() => {\n                                    setShowButtonNotRenderingModal(true)\n                                }}\n                            >\n                                here\n                            </span>\n                            .\n                        </p>\n                    ) : null}\n                </>\n            ) : null}\n            {buttonNotRenderingModal}\n        </div>\n    )\n}\n\nexport default GoogleSignIn\n\nexport function isValidTokenAvailable(token?: string | null) {\n    if (!token || token === 'null') {\n        return\n    }\n    try {\n        let details = JSON.parse(atobUnicode(token.split('.')[1]))\n        let expirationDate = new Date(parseInt(details.exp) * 1000)\n        return expirationDate.getTime() - 10000 > new Date().getTime()\n    } catch (e) {\n        toast.warn(\"Parsing issue with the google token. Can't automatically login!\")\n        return false\n    }\n}\n"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAZA;;;;;;;;;;;;;AAqBA,SAAS,aAAa,KAAY;;IAC9B,IAAI,CAAC,+BAA+B,iCAAiC,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAC3E,CAAA,GAAA,qHAAA,CAAA,wBAAqB,AAAD,MAAM,sBAAsB,aAAa,OAAO,CAAC,eAAe;IAGxF,IAAI,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,IAAI,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,IAAI,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,IAAI,CAAC,6BAA6B,+BAA+B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7E,IAAI,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,sNAAA,CAAA,YAAS,AAAD;IAC7B,IAAI,cAAc,CAAA,GAAA,kHAAA,CAAA,iBAAc,AAAD;IAE/B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACN,SAAS;YACT,IAAI,+BAA+B;gBAC/B,IAAI,QAAQ,aAAa,OAAO,CAAC;gBACjC,IAAI,aAAa,KAAK,KAAK,CAAC,CAAA,GAAA,wHAAA,CAAA,cAAW,AAAD,EAAE,MAAM,KAAK,CAAC,IAAI,CAAC,EAAE;gBAC3D,CAAA,GAAA,0HAAA,CAAA,aAAU,AAAD,EAAE,0HAAA,CAAA,eAAY,EAAE,WAAW,KAAK;gBACzC,cAAc;YAClB,OAAO;gBACH;8CAAW;wBACP,IAAI,UAAU;wBACd,SAAS,gBAAgB,CAAC,UAAU,OAAO;sDAAC,CAAA;gCACxC,IAAI,EAAE,GAAG,IAAI,EAAE,GAAG,CAAC,QAAQ,CAAC,wBAAwB;oCAChD,UAAU;gCACd;4BACJ;;wBACA,IAAI,CAAC,SAAS;4BACV,cAAc;4BACd,qBAAqB;4BACrB,eAAe,UAAU,CAAC;4BAC1B,aAAa,UAAU,CAAC;wBAC5B;oBACJ;6CAAG;YACP;QACA,uDAAuD;QAC3D;iCAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACN,IAAI,+BAA+B;gBAC/B,cAAc;YAClB;QACJ;iCAAG;QAAC;KAA8B;IAElC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACN;YACA,cAAc,eAAe,OAAO,CAAC,gBAAgB;QACrD,uDAAuD;QAC3D;iCAAG;QAAC,MAAM,YAAY;KAAC;IAEvB,SAAS,cAAc,KAAa;QAChC,cAAc;QACd,oHAAA,CAAA,UAAG,CAAC,cAAc,CAAC,OACd,IAAI,CAAC,CAAA;YACF,aAAa,OAAO,CAAC,YAAY;YACjC,eAAe,OAAO,CAAC,YAAY;YACnC,IAAI,QAAQ,AAAC,OAAe,KAAK;YACjC,IAAI,OAAO;gBACP,oHAAA,CAAA,UAAG,CAAC,MAAM,CAAC;YACf;YACA,SAAS,aAAa,CAAC,IAAI,YAAY,wHAAA,CAAA,gBAAa,CAAC,YAAY;YACjE,IAAI,MAAM,YAAY,EAAE;gBACpB,MAAM,YAAY;YACtB;QACJ,GACC,KAAK,CAAC,CAAA;YACH,0DAA0D;YAC1D,kFAAkF;YAClF,IAAI,MAAM,IAAI,KAAK,iBAAiB;gBAChC,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,uDAAuD,EAAE,QAAQ,MAAM,IAAI,IAAI,KAAK,SAAS,CAAC,SAAS,MAAM;YAC9H,OAAO;gBACH,QAAQ,IAAI,CAAC,kCAAkC;gBAC/C,eAAe,UAAU,CAAC;gBAC1B,aAAa,UAAU,CAAC;YAC5B;YACA,cAAc;YACd,iCAAiC;YACjC,eAAe,UAAU,CAAC;YAC1B,aAAa,UAAU,CAAC;QAC5B;IACR;IAEA,SAAS;QACL,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,2CAA2C;YAAE,WAAW;QAAM;IAC9E;IAEA,SAAS;QACL,IAAI,MAAM,kBAAkB,EAAE;YAC1B,MAAM,kBAAkB;QAC5B;QACA,WAAW;YACP,UAAU;YACV,QAAQ;QACZ;IACJ;IAEA,IAAI,QAA6B,aAC3B;QACI,YAAY;QACZ,QAAQ;IACZ,IACA,CAAC;IAEP,IAAI,OAAO;QACP,OAAO;IACX;IAEA,IAAI,wCACA,6LAAC,yLAAA,CAAA,QAAK;QACF,MAAM;QACN,QAAQ;YACJ,+BAA+B;QACnC;;0BAEA,6LAAC,yLAAA,CAAA,QAAK,CAAC,MAAM;0BACT,cAAA,6LAAC,yLAAA,CAAA,QAAK,CAAC,KAAK;8BAAC;;;;;;;;;;;0BAEjB,6LAAC,yLAAA,CAAA,QAAK,CAAC,IAAI;;kCACP,6LAAC;kCAAE;;;;;;kCACH,6LAAC;;;;;kCACD,6LAAC;kCAAE;;;;;;kCACH,6LAAC;;0CACG,6LAAC;0CAAG;;;;;;0CACJ,6LAAC;0CAAG;;;;;;;;;;;;;;;;;;;;;;;;IAMpB,qBACI,6LAAC;QAAI,OAAO;QAAO,gBAAgB;;YAC9B,CAAC,8CACE;;kCACI,6LAAC;wBAAI,WAAW,yJAAA,CAAA,UAAM,CAAC,YAAY;kCAC9B,CAAC,sBACE,6LAAC,qKAAA,CAAA,cAAW;4BACR,WAAW,CAAA;gCACP,IAAI;oCACA,IAAI,aAAa,KAAK,KAAK,CAAC,CAAA,GAAA,wHAAA,CAAA,cAAW,AAAD,EAAE,SAAS,UAAU,CAAE,KAAK,CAAC,IAAI,CAAC,EAAE;oCAC1E,CAAA,GAAA,0HAAA,CAAA,aAAU,AAAD,EAAE,0HAAA,CAAA,6BAA0B,EAAE,WAAW,OAAO;oCACzD,CAAA,GAAA,0HAAA,CAAA,aAAU,AAAD,EAAE,0HAAA,CAAA,eAAY,EAAE,WAAW,KAAK;oCACzC,CAAA,GAAA,0HAAA,CAAA,aAAU,AAAD,EAAE,0HAAA,CAAA,cAAW,EAAE,WAAW,IAAI;gCAC3C,EAAE,OAAM;oCACJ,sJAAA,CAAA,QAAK,CAAC,IAAI,CAAC;gCACf;gCACA,cAAc,SAAS,UAAU;4BACrC;4BACA,SAAS;4BACT,OAAO;4BACP,MAAM;4BACN,SAAS;4BACT,WAAW;;;;;mCAEf;;;;;;kCAER,6LAAC;;4BAAE;0CAC8B,6LAAC;gCAAE,MAAK;0CAA8B;;;;;;;;;;;;oBAEtE,kCACG,6LAAC;;4BAAE;4BACiC;0CAChC,6LAAC;gCACG,OAAO;oCAAE,OAAO;oCAAW,QAAQ;gCAAU;gCAC7C,SAAS;oCACL,+BAA+B;gCACnC;0CACH;;;;;;4BAEM;;;;;;+BAGX;;+BAER;YACH;;;;;;;AAGb;GAjLS;;QASgB,sNAAA,CAAA,YAAS;QACZ,kHAAA,CAAA,iBAAc;;;KAV3B;uCAmLM;AAER,SAAS,sBAAsB,KAAqB;IACvD,IAAI,CAAC,SAAS,UAAU,QAAQ;QAC5B;IACJ;IACA,IAAI;QACA,IAAI,UAAU,KAAK,KAAK,CAAC,CAAA,GAAA,wHAAA,CAAA,cAAW,AAAD,EAAE,MAAM,KAAK,CAAC,IAAI,CAAC,EAAE;QACxD,IAAI,iBAAiB,IAAI,KAAK,SAAS,QAAQ,GAAG,IAAI;QACtD,OAAO,eAAe,OAAO,KAAK,QAAQ,IAAI,OAAO,OAAO;IAChE,EAAE,OAAO,GAAG;QACR,sJAAA,CAAA,QAAK,CAAC,IAAI,CAAC;QACX,OAAO;IACX;AACJ", "debugId": null}}, {"offset": {"line": 2112, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/components/SubscribeButton/SubscribeButton.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"inputIata\": \"SubscribeButton-module__09S1Mq__inputIata\",\n  \"multiSearch\": \"SubscribeButton-module__09S1Mq__multiSearch\",\n  \"notifyButton\": \"SubscribeButton-module__09S1Mq__notifyButton\",\n  \"priceInput\": \"SubscribeButton-module__09S1Mq__priceInput\",\n  \"subscribe-dialog\": \"SubscribeButton-module__09S1Mq__subscribe-dialog\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 2124, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/components/SubscribeButton/SubscribeItemContent/SubscribeItemContent.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"checkBox\": \"SubscribeItemContent-module__LsBmca__checkBox\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA"}}, {"offset": {"line": 2133, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/SubscribeButton/SubscribeItemContent/SubscribeItemContent.tsx"], "sourcesContent": ["'use client'\nimport { useEffect, useState } from 'react'\nimport { FormControl, InputGroup } from 'react-bootstrap'\nimport { Form } from 'react-bootstrap'\nimport api from '../../../api/ApiHelper'\nimport { NotificationListener, SubscriptionType } from '../../../api/ApiTypes.d'\nimport ItemFilter from '../../ItemFilter/ItemFilter'\nimport styles from './SubscribeItemContent.module.css'\n\ninterface Props {\n    onPriceChange(value: string)\n    onIsPriceAboveChange(value: boolean)\n    onOnlyInstantBuyChange(value: boolean)\n    onFilterChange(filter: ItemFilter)\n    itemTag: string\n    prefill?: NotificationListener\n    onIsFilterValidChange?(newIsFilter: boolean)\n}\n\nfunction SubscribeItemContent(props: Props) {\n    let [filterOptions, setFilterOptions] = useState<FilterOptions[]>()\n\n    useEffect(() => {\n        api.getFilters(props.itemTag).then(options => {\n            setFilterOptions(options)\n        })\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [])\n\n    return (\n        <>\n            <div className=\"item-forms\">\n                <InputGroup className=\"price-input\">\n                    <InputGroup.Text id=\"inputGroup-sizing-sm\">Item price</InputGroup.Text>\n                    <FormControl\n                        aria-label=\"Small\"\n                        aria-describedby=\"inputGroup-sizing-sm\"\n                        type=\"number\"\n                        defaultValue={props.prefill?.price}\n                        onChange={e => props.onPriceChange(e.target.value)}\n                    />\n                </InputGroup>\n                <hr />\n                <h4 style={{ marginBottom: '20px' }}>Notify me...</h4>\n                <Form.Group>\n                    <Form.Label htmlFor=\"priceAboveCheckbox\">if the price is above the selected value</Form.Label>\n                    <Form.Check\n                        type=\"radio\"\n                        id=\"priceAboveCheckbox\"\n                        name=\"priceState\"\n                        defaultChecked={\n                            props.prefill && (props.prefill.types as unknown as string[]).includes(SubscriptionType[SubscriptionType.PRICE_HIGHER_THAN])\n                        }\n                        onChange={e => props.onIsPriceAboveChange(true)}\n                        className={styles.checkBox}\n                    />\n                </Form.Group>\n                <Form.Group>\n                    <Form.Label htmlFor=\"priceBelowCheckbox\">if the price is below the selected value</Form.Label>\n                    <Form.Check\n                        type=\"radio\"\n                        id=\"priceBelowCheckbox\"\n                        name=\"priceState\"\n                        defaultChecked={\n                            props.prefill && (props.prefill.types as unknown as string[]).includes(SubscriptionType[SubscriptionType.PRICE_LOWER_THAN])\n                        }\n                        onChange={e => props.onIsPriceAboveChange(false)}\n                        className={styles.checkBox}\n                    />\n                </Form.Group>\n                <Form.Group>\n                    <Form.Label htmlFor=\"onlyIstantBuy\">only for instant buy</Form.Label>\n                    <Form.Check\n                        className={styles.checkBox}\n                        type=\"checkbox\"\n                        defaultChecked={props.prefill && (props.prefill.types as unknown as string[]).includes(SubscriptionType[SubscriptionType.BIN])}\n                        id=\"onlyIstantBuy\"\n                        onClick={e => {\n                            props.onOnlyInstantBuyChange((e.target as HTMLInputElement).checked)\n                        }}\n                    />\n                </Form.Group>\n                <Form.Group>\n                    <ItemFilter\n                        defaultFilter={props.prefill?.filter}\n                        autoSelect={false}\n                        filters={filterOptions}\n                        forceOpen={true}\n                        ignoreURL={true}\n                        onFilterChange={props.onFilterChange}\n                        onIsValidChange={props.onIsFilterValidChange}\n                    />\n                </Form.Group>\n            </div>\n        </>\n    )\n}\n\nexport default SubscribeItemContent\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;;AAmBA,SAAS,qBAAqB,KAAY;;IACtC,IAAI,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;IAE/C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACN,oHAAA,CAAA,UAAG,CAAC,UAAU,CAAC,MAAM,OAAO,EAAE,IAAI;kDAAC,CAAA;oBAC/B,iBAAiB;gBACrB;;QACA,uDAAuD;QAC3D;yCAAG,EAAE;IAEL,qBACI;kBACI,cAAA,6LAAC;YAAI,WAAU;;8BACX,6LAAC,mMAAA,CAAA,aAAU;oBAAC,WAAU;;sCAClB,6LAAC,mMAAA,CAAA,aAAU,CAAC,IAAI;4BAAC,IAAG;sCAAuB;;;;;;sCAC3C,6LAAC,qMAAA,CAAA,cAAW;4BACR,cAAW;4BACX,oBAAiB;4BACjB,MAAK;4BACL,cAAc,MAAM,OAAO,EAAE;4BAC7B,UAAU,CAAA,IAAK,MAAM,aAAa,CAAC,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;8BAGzD,6LAAC;;;;;8BACD,6LAAC;oBAAG,OAAO;wBAAE,cAAc;oBAAO;8BAAG;;;;;;8BACrC,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;;sCACP,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;4BAAC,SAAQ;sCAAqB;;;;;;sCACzC,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;4BACP,MAAK;4BACL,IAAG;4BACH,MAAK;4BACL,gBACI,MAAM,OAAO,IAAI,AAAC,MAAM,OAAO,CAAC,KAAK,CAAyB,QAAQ,CAAC,wHAAA,CAAA,mBAAgB,CAAC,wHAAA,CAAA,mBAAgB,CAAC,iBAAiB,CAAC;4BAE/H,UAAU,CAAA,IAAK,MAAM,oBAAoB,CAAC;4BAC1C,WAAW,4LAAA,CAAA,UAAM,CAAC,QAAQ;;;;;;;;;;;;8BAGlC,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;;sCACP,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;4BAAC,SAAQ;sCAAqB;;;;;;sCACzC,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;4BACP,MAAK;4BACL,IAAG;4BACH,MAAK;4BACL,gBACI,MAAM,OAAO,IAAI,AAAC,MAAM,OAAO,CAAC,KAAK,CAAyB,QAAQ,CAAC,wHAAA,CAAA,mBAAgB,CAAC,wHAAA,CAAA,mBAAgB,CAAC,gBAAgB,CAAC;4BAE9H,UAAU,CAAA,IAAK,MAAM,oBAAoB,CAAC;4BAC1C,WAAW,4LAAA,CAAA,UAAM,CAAC,QAAQ;;;;;;;;;;;;8BAGlC,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;;sCACP,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;4BAAC,SAAQ;sCAAgB;;;;;;sCACpC,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;4BACP,WAAW,4LAAA,CAAA,UAAM,CAAC,QAAQ;4BAC1B,MAAK;4BACL,gBAAgB,MAAM,OAAO,IAAI,AAAC,MAAM,OAAO,CAAC,KAAK,CAAyB,QAAQ,CAAC,wHAAA,CAAA,mBAAgB,CAAC,wHAAA,CAAA,mBAAgB,CAAC,GAAG,CAAC;4BAC7H,IAAG;4BACH,SAAS,CAAA;gCACL,MAAM,sBAAsB,CAAC,AAAC,EAAE,MAAM,CAAsB,OAAO;4BACvE;;;;;;;;;;;;8BAGR,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;8BACP,cAAA,6LAAC,0IAAA,CAAA,UAAU;wBACP,eAAe,MAAM,OAAO,EAAE;wBAC9B,YAAY;wBACZ,SAAS;wBACT,WAAW;wBACX,WAAW;wBACX,gBAAgB,MAAM,cAAc;wBACpC,iBAAiB,MAAM,qBAAqB;;;;;;;;;;;;;;;;;;AAMpE;GA7ES;KAAA;uCA+EM", "debugId": null}}, {"offset": {"line": 2340, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/components/SubscribeButton/SubscribePlayerContent/SubscribePlayerContent.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"checkBox\": \"SubscribePlayerContent-module__gZHVOW__checkBox\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA"}}, {"offset": {"line": 2349, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/SubscribeButton/SubscribePlayerContent/SubscribePlayerContent.tsx"], "sourcesContent": ["import React from 'react'\nimport { NotificationListener, SubscriptionType } from '../../../api/ApiTypes.d'\nimport styles from './SubscribePlayerContent.module.css'\n\ninterface Props {\n    onGotOutbidChange(value: boolean)\n    onIsSoldChange(value: boolean)\n    onIsPlayerAuctionCreation(value: boolean)\n    onBoughtAnyAuctionChange(value: boolean)\n    prefill?: NotificationListener\n}\n\nfunction SubscribePlayerContent(props: Props) {\n    return (\n        <>\n            <div className=\"player-forms\">\n                <h4 style={{ marginBottom: '20px' }}>Notify me...</h4>\n                <div className=\"input-data\">\n                    <input\n                        type=\"checkbox\"\n                        defaultChecked={\n                            props.prefill && (props.prefill.types as unknown as string[]).includes(SubscriptionType[SubscriptionType.PLAYER_CREATES_AUCTION])\n                        }\n                        className={styles.checkBox}\n                        id=\"isPlayerAuctionCreation\"\n                        onChange={e => props.onIsPlayerAuctionCreation((e.target as HTMLInputElement).checked)}\n                    />\n                    <label htmlFor=\"isPlayerAuctionCreation\">if the player creates an auction</label>\n                </div>\n                <div className=\"input-data\">\n                    <input\n                        defaultChecked={props.prefill && (props.prefill.types as unknown as string[]).includes(SubscriptionType[SubscriptionType.OUTBID])}\n                        type=\"checkbox\"\n                        className={styles.checkBox}\n                        id=\"outbidCheckbox\"\n                        onChange={e => props.onGotOutbidChange((e.target as HTMLInputElement).checked)}\n                    />\n                    <label htmlFor=\"outbidCheckbox\">if the player gets outbid</label>\n                </div>\n                <div className=\"input-data\">\n                    <input\n                        defaultChecked={props.prefill && (props.prefill.types as unknown as string[]).includes(SubscriptionType[SubscriptionType.SOLD])}\n                        type=\"checkbox\"\n                        className={styles.checkBox}\n                        id=\"isSoldCheckbox\"\n                        onChange={e => props.onIsSoldChange((e.target as HTMLInputElement).checked)}\n                    />\n                    <label htmlFor=\"isSoldCheckbox\">if an auction of the player has ended</label>\n                </div>\n                <div className=\"input-data\">\n                    <input\n                        defaultChecked={\n                            props.prefill && (props.prefill.types as unknown as string[]).includes(SubscriptionType[SubscriptionType.BOUGHT_ANY_AUCTION])\n                        }\n                        type=\"checkbox\"\n                        className={styles.checkBox}\n                        id=\"hasBoughtAnyAuction\"\n                        onChange={e => props.onBoughtAnyAuctionChange((e.target as HTMLInputElement).checked)}\n                    />\n                    <label htmlFor=\"hasBoughtAnyAuction\">if the player bought any auction</label>\n                </div>\n            </div>\n        </>\n    )\n}\n\nexport default SubscribePlayerContent\n"], "names": [], "mappings": ";;;;AACA;AACA;;;;AAUA,SAAS,uBAAuB,KAAY;IACxC,qBACI;kBACI,cAAA,6LAAC;YAAI,WAAU;;8BACX,6LAAC;oBAAG,OAAO;wBAAE,cAAc;oBAAO;8BAAG;;;;;;8BACrC,6LAAC;oBAAI,WAAU;;sCACX,6LAAC;4BACG,MAAK;4BACL,gBACI,MAAM,OAAO,IAAI,AAAC,MAAM,OAAO,CAAC,KAAK,CAAyB,QAAQ,CAAC,wHAAA,CAAA,mBAAgB,CAAC,wHAAA,CAAA,mBAAgB,CAAC,sBAAsB,CAAC;4BAEpI,WAAW,gMAAA,CAAA,UAAM,CAAC,QAAQ;4BAC1B,IAAG;4BACH,UAAU,CAAA,IAAK,MAAM,yBAAyB,CAAC,AAAC,EAAE,MAAM,CAAsB,OAAO;;;;;;sCAEzF,6LAAC;4BAAM,SAAQ;sCAA0B;;;;;;;;;;;;8BAE7C,6LAAC;oBAAI,WAAU;;sCACX,6LAAC;4BACG,gBAAgB,MAAM,OAAO,IAAI,AAAC,MAAM,OAAO,CAAC,KAAK,CAAyB,QAAQ,CAAC,wHAAA,CAAA,mBAAgB,CAAC,wHAAA,CAAA,mBAAgB,CAAC,MAAM,CAAC;4BAChI,MAAK;4BACL,WAAW,gMAAA,CAAA,UAAM,CAAC,QAAQ;4BAC1B,IAAG;4BACH,UAAU,CAAA,IAAK,MAAM,iBAAiB,CAAC,AAAC,EAAE,MAAM,CAAsB,OAAO;;;;;;sCAEjF,6LAAC;4BAAM,SAAQ;sCAAiB;;;;;;;;;;;;8BAEpC,6LAAC;oBAAI,WAAU;;sCACX,6LAAC;4BACG,gBAAgB,MAAM,OAAO,IAAI,AAAC,MAAM,OAAO,CAAC,KAAK,CAAyB,QAAQ,CAAC,wHAAA,CAAA,mBAAgB,CAAC,wHAAA,CAAA,mBAAgB,CAAC,IAAI,CAAC;4BAC9H,MAAK;4BACL,WAAW,gMAAA,CAAA,UAAM,CAAC,QAAQ;4BAC1B,IAAG;4BACH,UAAU,CAAA,IAAK,MAAM,cAAc,CAAC,AAAC,EAAE,MAAM,CAAsB,OAAO;;;;;;sCAE9E,6LAAC;4BAAM,SAAQ;sCAAiB;;;;;;;;;;;;8BAEpC,6LAAC;oBAAI,WAAU;;sCACX,6LAAC;4BACG,gBACI,MAAM,OAAO,IAAI,AAAC,MAAM,OAAO,CAAC,KAAK,CAAyB,QAAQ,CAAC,wHAAA,CAAA,mBAAgB,CAAC,wHAAA,CAAA,mBAAgB,CAAC,kBAAkB,CAAC;4BAEhI,MAAK;4BACL,WAAW,gMAAA,CAAA,UAAM,CAAC,QAAQ;4BAC1B,IAAG;4BACH,UAAU,CAAA,IAAK,MAAM,wBAAwB,CAAC,AAAC,EAAE,MAAM,CAAsB,OAAO;;;;;;sCAExF,6LAAC;4BAAM,SAAQ;sCAAsB;;;;;;;;;;;;;;;;;;;AAKzD;KApDS;uCAsDM", "debugId": null}}, {"offset": {"line": 2506, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/SubscribeButton/SubscribeAuctionContent/SubscribeAuctionContent.tsx"], "sourcesContent": ["import React from 'react'\n\nfunction SubscribeAuctionContent() {\n    return (\n        <>\n            <h4 style={{ marginBottom: '20px' }}>Notify me if someone bids on or buys the auction.</h4>\n        </>\n    )\n}\n\nexport default SubscribeAuctionContent\n"], "names": [], "mappings": ";;;;;AAEA,SAAS;IACL,qBACI;kBACI,cAAA,6LAAC;YAAG,OAAO;gBAAE,cAAc;YAAO;sBAAG;;;;;;;AAGjD;KANS;uCAQM", "debugId": null}}, {"offset": {"line": 2537, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/components/SubscribeButton/SubscribeBazaarItemContent/SubscribeBazaarItemContent.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"checkBox\": \"SubscribeBazaarItemContent-module__FucSta__checkBox\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA"}}, {"offset": {"line": 2546, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/SubscribeButton/SubscribeBazaarItemContent/SubscribeBazaarItemContent.tsx"], "sourcesContent": ["'use client'\nimport { FormControl, InputGroup } from 'react-bootstrap'\nimport { Form } from 'react-bootstrap'\nimport { NotificationListener, SubscriptionType } from '../../../api/ApiTypes.d'\nimport styles from './SubscribeBazaarItemContent.module.css'\n\ninterface Props {\n    onPriceChange(value: string)\n    onIsPriceAboveChange(value: boolean)\n    onUseSellPriceChange(value: boolean)\n    itemTag: string\n    prefill?: NotificationListener\n}\n\nfunction SubscribeBazaarItemContent(props: Props) {\n    return (\n        <>\n            <div className=\"item-forms\">\n                <InputGroup className=\"price-input\">\n                    <InputGroup.Text id=\"inputGroup-sizing-sm\">Item price</InputGroup.Text>\n                    <FormControl\n                        aria-label=\"Small\"\n                        aria-describedby=\"inputGroup-sizing-sm\"\n                        type=\"number\"\n                        defaultValue={props.prefill?.price}\n                        onChange={e => props.onPriceChange(e.target.value)}\n                    />\n                </InputGroup>\n                <hr />\n                <h4 style={{ marginBottom: '20px' }}>Notify me...</h4>\n                <Form.Group>\n                    <Form.Label htmlFor=\"priceAboveCheckbox\">if the price is above the selected value</Form.Label>\n                    <Form.Check\n                        type=\"radio\"\n                        id=\"priceAboveCheckbox\"\n                        name=\"priceState\"\n                        defaultChecked={\n                            props.prefill && (props.prefill.types as unknown as string[]).includes(SubscriptionType[SubscriptionType.PRICE_HIGHER_THAN])\n                        }\n                        onChange={e => props.onIsPriceAboveChange(true)}\n                        className={styles.checkBox}\n                    />\n                </Form.Group>\n                <Form.Group>\n                    <Form.Label htmlFor=\"priceBelowCheckbox\">if the price is below the selected value</Form.Label>\n                    <Form.Check\n                        type=\"radio\"\n                        id=\"priceBelowCheckbox\"\n                        name=\"priceState\"\n                        defaultChecked={\n                            props.prefill && (props.prefill.types as unknown as string[]).includes(SubscriptionType[SubscriptionType.PRICE_LOWER_THAN])\n                        }\n                        onChange={e => props.onIsPriceAboveChange(false)}\n                        className={styles.checkBox}\n                    />\n                </Form.Group>\n                <Form.Group>\n                    <Form.Label htmlFor=\"useSellPriceCheckbox\">if the sell price should be used</Form.Label>\n                    <Form.Check\n                        type=\"checkbox\"\n                        id=\"useSellPriceCheckbox\"\n                        defaultChecked={\n                            props.prefill && (props.prefill.types as unknown as string[]).includes(SubscriptionType[SubscriptionType.USE_SELL_NOT_BUY])\n                        }\n                        onChange={e => props.onUseSellPriceChange(e.target.checked)}\n                        className={styles.checkBox}\n                    />\n                </Form.Group>\n            </div>\n        </>\n    )\n}\n\nexport default SubscribeBazaarItemContent\n"], "names": [], "mappings": ";;;;AACA;AAAA;AACA;AACA;AACA;AAJA;;;;;;AAcA,SAAS,2BAA2B,KAAY;IAC5C,qBACI;kBACI,cAAA,6LAAC;YAAI,WAAU;;8BACX,6LAAC,mMAAA,CAAA,aAAU;oBAAC,WAAU;;sCAClB,6LAAC,mMAAA,CAAA,aAAU,CAAC,IAAI;4BAAC,IAAG;sCAAuB;;;;;;sCAC3C,6LAAC,qMAAA,CAAA,cAAW;4BACR,cAAW;4BACX,oBAAiB;4BACjB,MAAK;4BACL,cAAc,MAAM,OAAO,EAAE;4BAC7B,UAAU,CAAA,IAAK,MAAM,aAAa,CAAC,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;8BAGzD,6LAAC;;;;;8BACD,6LAAC;oBAAG,OAAO;wBAAE,cAAc;oBAAO;8BAAG;;;;;;8BACrC,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;;sCACP,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;4BAAC,SAAQ;sCAAqB;;;;;;sCACzC,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;4BACP,MAAK;4BACL,IAAG;4BACH,MAAK;4BACL,gBACI,MAAM,OAAO,IAAI,AAAC,MAAM,OAAO,CAAC,KAAK,CAAyB,QAAQ,CAAC,wHAAA,CAAA,mBAAgB,CAAC,wHAAA,CAAA,mBAAgB,CAAC,iBAAiB,CAAC;4BAE/H,UAAU,CAAA,IAAK,MAAM,oBAAoB,CAAC;4BAC1C,WAAW,wMAAA,CAAA,UAAM,CAAC,QAAQ;;;;;;;;;;;;8BAGlC,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;;sCACP,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;4BAAC,SAAQ;sCAAqB;;;;;;sCACzC,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;4BACP,MAAK;4BACL,IAAG;4BACH,MAAK;4BACL,gBACI,MAAM,OAAO,IAAI,AAAC,MAAM,OAAO,CAAC,KAAK,CAAyB,QAAQ,CAAC,wHAAA,CAAA,mBAAgB,CAAC,wHAAA,CAAA,mBAAgB,CAAC,gBAAgB,CAAC;4BAE9H,UAAU,CAAA,IAAK,MAAM,oBAAoB,CAAC;4BAC1C,WAAW,wMAAA,CAAA,UAAM,CAAC,QAAQ;;;;;;;;;;;;8BAGlC,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;;sCACP,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;4BAAC,SAAQ;sCAAuB;;;;;;sCAC3C,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;4BACP,MAAK;4BACL,IAAG;4BACH,gBACI,MAAM,OAAO,IAAI,AAAC,MAAM,OAAO,CAAC,KAAK,CAAyB,QAAQ,CAAC,wHAAA,CAAA,mBAAgB,CAAC,wHAAA,CAAA,mBAAgB,CAAC,gBAAgB,CAAC;4BAE9H,UAAU,CAAA,IAAK,MAAM,oBAAoB,CAAC,EAAE,MAAM,CAAC,OAAO;4BAC1D,WAAW,wMAAA,CAAA,UAAM,CAAC,QAAQ;;;;;;;;;;;;;;;;;;;AAMlD;KAzDS;uCA2DM", "debugId": null}}, {"offset": {"line": 2713, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/SubscribeButton/SubscribeButton.tsx"], "sourcesContent": ["'use client'\nimport { useEffect, useState, type JSX } from 'react';\nimport { <PERSON><PERSON>, Modal } from 'react-bootstrap'\nimport { useMatomo } from '@jonkoops/matomo-tracker-react'\nimport api from '../../api/ApiHelper'\nimport { NotificationListener, SubscriptionType } from '../../api/ApiTypes.d'\nimport GoogleSignIn from '../GoogleSignIn/GoogleSignIn'\nimport { toast } from 'react-toastify'\nimport askForNotificationPermissons from '../../utils/NotificationPermisson'\nimport NotificationIcon from '@mui/icons-material/NotificationsOutlined'\nimport styles from './SubscribeButton.module.css'\nimport SubscribeItemContent from './SubscribeItemContent/SubscribeItemContent'\nimport { getLoadingElement } from '../../utils/LoadingUtils'\nimport SubscribePlayerContent from './SubscribePlayerContent/SubscribePlayerContent'\nimport SubscribeAuctionContent from './SubscribeAuctionContent/SubscribeAuctionContent'\nimport { useRouter } from 'next/navigation'\nimport { useWasAlreadyLoggedIn } from '../../utils/Hooks'\nimport EditIcon from '@mui/icons-material/Edit'\nimport { Typeahead } from 'react-bootstrap-typeahead'\nimport NotificationTargetForm from '../NotificationTargets/NotificationTargetForm'\nimport SubscribeBazaarItemContent from './SubscribeBazaarItemContent/SubscribeBazaarItemContent'\n\ninterface Props {\n    topic: string\n    type: 'player' | 'item' | 'auction' | 'bazaar'\n    buttonContent?: JSX.Element\n    isEditButton?: boolean\n    onAfterSubscribe?()\n    prefill?: {\n        listener: NotificationListener\n        targetNames: string[]\n    }\n    popupTitle?: string\n    popupButtonText?: string\n    successMessage?: string\n}\n\nconst MAX_FILTERS = 5\n\nfunction SubscribeButton(props: Props) {\n    let { trackEvent } = useMatomo()\n    let router = useRouter()\n    let [showDialog, setShowDialog] = useState(false)\n    let [price, setPrice] = useState(props.prefill?.listener?.price?.toString() || '0')\n    let [isPriceAbove, setIsPriceAbove] = useState(props.prefill?.listener?.types?.includes(SubscriptionType.PRICE_HIGHER_THAN) ?? false)\n    let [onlyInstantBuy, setOnlyInstantBuy] = useState(props.prefill?.listener?.types?.includes(SubscriptionType.BIN) ?? false)\n    let [gotOutbid, setGotOutbid] = useState(props.prefill?.listener?.types?.includes(SubscriptionType.OUTBID) ?? false)\n    let [isSold, setIsSold] = useState(props.prefill?.listener?.types?.includes(SubscriptionType.SOLD) ?? false)\n    let [isPlayerAuctionCreation, setIsPlayerAuctionCreation] = useState(\n        props.prefill?.listener?.types?.includes(SubscriptionType.PLAYER_CREATES_AUCTION) ?? false\n    )\n    let [hasPlayerBoughtAnyAuction, setHasPlayerBoughtAnyAuction] = useState(\n        props.prefill?.listener?.types?.includes(SubscriptionType.BOUGHT_ANY_AUCTION) ?? false\n    )\n    let [isUseBazaarSellNotBuy, setIsUseBazaarSellNotBuy] = useState(props.prefill?.listener?.types?.includes(SubscriptionType.USE_SELL_NOT_BUY) ?? false)\n    let [isLoggedIn, setIsLoggedIn] = useState(false)\n    let [itemFilter, setItemFilter] = useState<ItemFilter | undefined>(props.prefill?.listener?.filter || undefined)\n    let [isItemFilterValid, setIsItemFilterValid] = useState(true)\n    let wasAlreadyLoggedIn = useWasAlreadyLoggedIn()\n    let [notificationTargets, setNotificationTargets] = useState<NotificationTarget[]>([])\n    let [selectedNotificationTargets, setSelectedNotificationTargets] = useState<NotificationTarget[]>([])\n    let [isLoadingNotificationTargets, setIsLoadingNotificationTargets] = useState(false)\n    let [showCreateTargetDialog, setShowCreateTargetDialog] = useState(false)\n\n    async function onSubscribe() {\n        trackEvent({ action: 'subscribed', category: 'subscriptions' })\n        setShowDialog(false)\n        // Set price to 0 per default for item subscriptions\n        // This happens if a user only selects a filter and leaves the price field empty\n        if (props.type === 'item' && !price) {\n            price = '0'\n        }\n        if (props.type === 'bazaar' && !price) {\n            price = '0'\n        }\n        if (props.type === 'item' && !itemFilter) {\n            itemFilter = {}\n        }\n\n        api.subscribe(props.topic, getSubscriptionTypes(), selectedNotificationTargets, price ? parseInt(price) : undefined, itemFilter)\n            .then(() => {\n                toast.success(props.successMessage || 'Notifier successfully created!', {\n                    onClick: () => {\n                        router.push('/subscriptions')\n                    }\n                })\n                if (props.onAfterSubscribe) {\n                    props.onAfterSubscribe()\n                }\n            })\n            .catch(error => {\n                toast.error(error.message, {\n                    onClick: () => {\n                        router.push('/subscriptions')\n                    }\n                })\n            })\n    }\n\n    function getSubscriptionTypes(): SubscriptionType[] {\n        let types: SubscriptionType[] = []\n        if (props.type === 'item') {\n            if (isPriceAbove) {\n                types.push(SubscriptionType.PRICE_HIGHER_THAN)\n            }\n            if (!isPriceAbove) {\n                types.push(SubscriptionType.PRICE_LOWER_THAN)\n            }\n            if (onlyInstantBuy) {\n                types.push(SubscriptionType.BIN)\n            }\n        }\n        if (props.type === 'player') {\n            if (gotOutbid) {\n                types.push(SubscriptionType.OUTBID)\n            }\n            if (isSold) {\n                types.push(SubscriptionType.SOLD)\n            }\n            if (isPlayerAuctionCreation) {\n                types.push(SubscriptionType.PLAYER_CREATES_AUCTION)\n            }\n            if (hasPlayerBoughtAnyAuction) {\n                types.push(SubscriptionType.BOUGHT_ANY_AUCTION)\n            }\n        }\n        if (props.type === 'auction') {\n            types.push(SubscriptionType.AUCTION)\n        }\n        if (props.type === 'bazaar') {\n            if (isPriceAbove) {\n                types.push(SubscriptionType.PRICE_HIGHER_THAN)\n            }\n            if (!isPriceAbove) {\n                types.push(SubscriptionType.PRICE_LOWER_THAN)\n            }\n            if (isUseBazaarSellNotBuy) {\n                types.push(SubscriptionType.USE_SELL_NOT_BUY)\n            }\n        }\n        return types\n    }\n\n    function onLogin() {\n        setIsLoggedIn(true)\n        setIsLoadingNotificationTargets(true)\n        api.getNotificationTargets().then(targets => {\n            if (props.prefill?.targetNames) {\n                setSelectedNotificationTargets(targets.filter(target => (target.name ? props.prefill?.targetNames.includes(target.name) : false)))\n            }\n            setNotificationTargets(targets)\n            setIsLoadingNotificationTargets(false)\n        })\n    }\n\n    function isNotifyDisabled() {\n        if (itemFilter && Object.keys(itemFilter).length > MAX_FILTERS) {\n            return true\n        }\n        if (props.type === 'item') {\n            return itemFilter && Object.keys(itemFilter).length > 0 ? false : price === undefined || price === ''\n        }\n        if (props.type === 'player') {\n            return !gotOutbid && !isSold && !isPlayerAuctionCreation\n        }\n    }\n\n    function closeDialog() {\n        trackEvent({ action: 'subscription dialog closed', category: 'subscriptions' })\n        setShowDialog(false)\n    }\n\n    function openDialog() {\n        trackEvent({ action: 'subscription dialog opened', category: 'subscriptions' })\n        setShowDialog(true)\n    }\n\n    let dialog2 = (\n        <Modal\n            show={showCreateTargetDialog}\n            onHide={() => {\n                setShowCreateTargetDialog(false)\n            }}\n            className={styles.subscribeDialog}\n        >\n            <Modal.Header closeButton>\n                <Modal.Title>{props.popupTitle || 'Create a Notification Target'}</Modal.Title>\n            </Modal.Header>\n            <Modal.Body>\n                <NotificationTargetForm\n                    type=\"CREATE\"\n                    onSubmit={target => {\n                        setSelectedNotificationTargets([...selectedNotificationTargets, target])\n                    }}\n                />\n            </Modal.Body>\n        </Modal>\n    )\n\n    let dialog = (\n        <Modal show={showDialog} onHide={closeDialog} className={styles.subscribeDialog}>\n            <Modal.Header closeButton>\n                <Modal.Title>{props.popupTitle || 'Create a Notifier'}</Modal.Title>\n            </Modal.Header>\n            <Modal.Body>\n                {isLoggedIn ? (\n                    <div>\n                        {props.type === 'item' ? (\n                            <SubscribeItemContent\n                                itemTag={props.topic}\n                                onFilterChange={filter => {\n                                    setItemFilter({ ...filter })\n                                }}\n                                onIsPriceAboveChange={setIsPriceAbove}\n                                onOnlyInstantBuyChange={setOnlyInstantBuy}\n                                onPriceChange={setPrice}\n                                prefill={props.prefill?.listener}\n                                onIsFilterValidChange={setIsItemFilterValid}\n                            />\n                        ) : null}\n                        {props.type === 'bazaar' ? (\n                            <SubscribeBazaarItemContent\n                                itemTag={props.topic}\n                                onPriceChange={setPrice}\n                                onIsPriceAboveChange={setIsPriceAbove}\n                                onUseSellPriceChange={setIsUseBazaarSellNotBuy}\n                                prefill={props.prefill?.listener}\n                            />\n                        ) : null}\n                        {props.type === 'player' ? (\n                            <SubscribePlayerContent\n                                onGotOutbidChange={setGotOutbid}\n                                onIsSoldChange={setIsSold}\n                                onIsPlayerAuctionCreation={setIsPlayerAuctionCreation}\n                                onBoughtAnyAuctionChange={setHasPlayerBoughtAnyAuction}\n                                prefill={props.prefill?.listener}\n                            />\n                        ) : null}\n                        {props.type === 'auction' ? <SubscribeAuctionContent /> : null}\n                        <label htmlFor=\"notificationTargetsTypeahead\">Targets: </label>\n                        <div style={{ display: 'flex', gap: 10 }}>\n                            <Typeahead\n                                id=\"notificationTargetsTypeahead\"\n                                className={styles.multiSearch}\n                                isLoading={isLoadingNotificationTargets}\n                                labelKey=\"name\"\n                                style={{ flex: 1 }}\n                                options={notificationTargets}\n                                placeholder={'Select targets...'}\n                                selected={selectedNotificationTargets}\n                                onChange={selected => {\n                                    setSelectedNotificationTargets(selected as NotificationTarget[])\n                                }}\n                                multiple={true}\n                            />\n                            <Button\n                                onClick={() => {\n                                    setShowCreateTargetDialog(true)\n                                }}\n                            >\n                                Create new target\n                            </Button>\n                        </div>\n                        <Button onClick={onSubscribe} disabled={isNotifyDisabled() || !isItemFilterValid} className={styles.notifyButton}>\n                            {props.popupButtonText || 'Notify me'}\n                        </Button>\n                        {itemFilter && Object.keys(itemFilter).length > MAX_FILTERS ? (\n                            <p style={{ color: 'red' }}>You currently can't use more than 5 filters for Notifiers</p>\n                        ) : null}\n                    </div>\n                ) : (\n                    <p>To use notifiers, please login with Google: </p>\n                )}\n                <GoogleSignIn onAfterLogin={onLogin} />\n                {wasAlreadyLoggedIn && !isLoggedIn ? getLoadingElement() : ''}\n            </Modal.Body>\n        </Modal>\n    )\n\n    return (\n        <div className={styles.subscribeButton}>\n            {dialog}\n            {dialog2}\n            {props.isEditButton ? (\n                <div onClick={openDialog}>\n                    <EditIcon />\n                </div>\n            ) : (\n                <Button style={{ width: 'max-content' }} onClick={openDialog}>\n                    <NotificationIcon />\n                    {props.buttonContent || ' Notify'}\n                </Button>\n            )}\n        </div>\n    )\n}\n\nexport default SubscribeButton\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;;;AApBA;;;;;;;;;;;;;;;;;;;;AAqCA,MAAM,cAAc;AAEpB,SAAS,gBAAgB,KAAY;;IACjC,IAAI,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,sNAAA,CAAA,YAAS,AAAD;IAC7B,IAAI,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACrB,IAAI,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,IAAI,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,MAAM,OAAO,EAAE,UAAU,OAAO,cAAc;IAC/E,IAAI,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,MAAM,OAAO,EAAE,UAAU,OAAO,SAAS,wHAAA,CAAA,mBAAgB,CAAC,iBAAiB,KAAK;IAC/H,IAAI,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,MAAM,OAAO,EAAE,UAAU,OAAO,SAAS,wHAAA,CAAA,mBAAgB,CAAC,GAAG,KAAK;IACrH,IAAI,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,MAAM,OAAO,EAAE,UAAU,OAAO,SAAS,wHAAA,CAAA,mBAAgB,CAAC,MAAM,KAAK;IAC9G,IAAI,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,MAAM,OAAO,EAAE,UAAU,OAAO,SAAS,wHAAA,CAAA,mBAAgB,CAAC,IAAI,KAAK;IACtG,IAAI,CAAC,yBAAyB,2BAA2B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAC/D,MAAM,OAAO,EAAE,UAAU,OAAO,SAAS,wHAAA,CAAA,mBAAgB,CAAC,sBAAsB,KAAK;IAEzF,IAAI,CAAC,2BAA2B,6BAA6B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EACnE,MAAM,OAAO,EAAE,UAAU,OAAO,SAAS,wHAAA,CAAA,mBAAgB,CAAC,kBAAkB,KAAK;IAErF,IAAI,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,MAAM,OAAO,EAAE,UAAU,OAAO,SAAS,wHAAA,CAAA,mBAAgB,CAAC,gBAAgB,KAAK;IAChJ,IAAI,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,IAAI,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B,MAAM,OAAO,EAAE,UAAU,UAAU;IACtG,IAAI,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,IAAI,qBAAqB,CAAA,GAAA,kHAAA,CAAA,wBAAqB,AAAD;IAC7C,IAAI,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwB,EAAE;IACrF,IAAI,CAAC,6BAA6B,+BAA+B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwB,EAAE;IACrG,IAAI,CAAC,8BAA8B,gCAAgC,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/E,IAAI,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnE,eAAe;QACX,WAAW;YAAE,QAAQ;YAAc,UAAU;QAAgB;QAC7D,cAAc;QACd,oDAAoD;QACpD,gFAAgF;QAChF,IAAI,MAAM,IAAI,KAAK,UAAU,CAAC,OAAO;YACjC,QAAQ;QACZ;QACA,IAAI,MAAM,IAAI,KAAK,YAAY,CAAC,OAAO;YACnC,QAAQ;QACZ;QACA,IAAI,MAAM,IAAI,KAAK,UAAU,CAAC,YAAY;YACtC,aAAa,CAAC;QAClB;QAEA,oHAAA,CAAA,UAAG,CAAC,SAAS,CAAC,MAAM,KAAK,EAAE,wBAAwB,6BAA6B,QAAQ,SAAS,SAAS,WAAW,YAChH,IAAI,CAAC;YACF,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC,MAAM,cAAc,IAAI,kCAAkC;gBACpE,SAAS;oBACL,OAAO,IAAI,CAAC;gBAChB;YACJ;YACA,IAAI,MAAM,gBAAgB,EAAE;gBACxB,MAAM,gBAAgB;YAC1B;QACJ,GACC,KAAK,CAAC,CAAA;YACH,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,OAAO,EAAE;gBACvB,SAAS;oBACL,OAAO,IAAI,CAAC;gBAChB;YACJ;QACJ;IACR;IAEA,SAAS;QACL,IAAI,QAA4B,EAAE;QAClC,IAAI,MAAM,IAAI,KAAK,QAAQ;YACvB,IAAI,cAAc;gBACd,MAAM,IAAI,CAAC,wHAAA,CAAA,mBAAgB,CAAC,iBAAiB;YACjD;YACA,IAAI,CAAC,cAAc;gBACf,MAAM,IAAI,CAAC,wHAAA,CAAA,mBAAgB,CAAC,gBAAgB;YAChD;YACA,IAAI,gBAAgB;gBAChB,MAAM,IAAI,CAAC,wHAAA,CAAA,mBAAgB,CAAC,GAAG;YACnC;QACJ;QACA,IAAI,MAAM,IAAI,KAAK,UAAU;YACzB,IAAI,WAAW;gBACX,MAAM,IAAI,CAAC,wHAAA,CAAA,mBAAgB,CAAC,MAAM;YACtC;YACA,IAAI,QAAQ;gBACR,MAAM,IAAI,CAAC,wHAAA,CAAA,mBAAgB,CAAC,IAAI;YACpC;YACA,IAAI,yBAAyB;gBACzB,MAAM,IAAI,CAAC,wHAAA,CAAA,mBAAgB,CAAC,sBAAsB;YACtD;YACA,IAAI,2BAA2B;gBAC3B,MAAM,IAAI,CAAC,wHAAA,CAAA,mBAAgB,CAAC,kBAAkB;YAClD;QACJ;QACA,IAAI,MAAM,IAAI,KAAK,WAAW;YAC1B,MAAM,IAAI,CAAC,wHAAA,CAAA,mBAAgB,CAAC,OAAO;QACvC;QACA,IAAI,MAAM,IAAI,KAAK,UAAU;YACzB,IAAI,cAAc;gBACd,MAAM,IAAI,CAAC,wHAAA,CAAA,mBAAgB,CAAC,iBAAiB;YACjD;YACA,IAAI,CAAC,cAAc;gBACf,MAAM,IAAI,CAAC,wHAAA,CAAA,mBAAgB,CAAC,gBAAgB;YAChD;YACA,IAAI,uBAAuB;gBACvB,MAAM,IAAI,CAAC,wHAAA,CAAA,mBAAgB,CAAC,gBAAgB;YAChD;QACJ;QACA,OAAO;IACX;IAEA,SAAS;QACL,cAAc;QACd,gCAAgC;QAChC,oHAAA,CAAA,UAAG,CAAC,sBAAsB,GAAG,IAAI,CAAC,CAAA;YAC9B,IAAI,MAAM,OAAO,EAAE,aAAa;gBAC5B,+BAA+B,QAAQ,MAAM,CAAC,CAAA,SAAW,OAAO,IAAI,GAAG,MAAM,OAAO,EAAE,YAAY,SAAS,OAAO,IAAI,IAAI;YAC9H;YACA,uBAAuB;YACvB,gCAAgC;QACpC;IACJ;IAEA,SAAS;QACL,IAAI,cAAc,OAAO,IAAI,CAAC,YAAY,MAAM,GAAG,aAAa;YAC5D,OAAO;QACX;QACA,IAAI,MAAM,IAAI,KAAK,QAAQ;YACvB,OAAO,cAAc,OAAO,IAAI,CAAC,YAAY,MAAM,GAAG,IAAI,QAAQ,UAAU,aAAa,UAAU;QACvG;QACA,IAAI,MAAM,IAAI,KAAK,UAAU;YACzB,OAAO,CAAC,aAAa,CAAC,UAAU,CAAC;QACrC;IACJ;IAEA,SAAS;QACL,WAAW;YAAE,QAAQ;YAA8B,UAAU;QAAgB;QAC7E,cAAc;IAClB;IAEA,SAAS;QACL,WAAW;YAAE,QAAQ;YAA8B,UAAU;QAAgB;QAC7E,cAAc;IAClB;IAEA,IAAI,wBACA,6LAAC,yLAAA,CAAA,QAAK;QACF,MAAM;QACN,QAAQ;YACJ,0BAA0B;QAC9B;QACA,WAAW,+JAAA,CAAA,UAAM,CAAC,eAAe;;0BAEjC,6LAAC,yLAAA,CAAA,QAAK,CAAC,MAAM;gBAAC,WAAW;0BACrB,cAAA,6LAAC,yLAAA,CAAA,QAAK,CAAC,KAAK;8BAAE,MAAM,UAAU,IAAI;;;;;;;;;;;0BAEtC,6LAAC,yLAAA,CAAA,QAAK,CAAC,IAAI;0BACP,cAAA,6LAAC,+JAAA,CAAA,UAAsB;oBACnB,MAAK;oBACL,UAAU,CAAA;wBACN,+BAA+B;+BAAI;4BAA6B;yBAAO;oBAC3E;;;;;;;;;;;;;;;;;IAMhB,IAAI,uBACA,6LAAC,yLAAA,CAAA,QAAK;QAAC,MAAM;QAAY,QAAQ;QAAa,WAAW,+JAAA,CAAA,UAAM,CAAC,eAAe;;0BAC3E,6LAAC,yLAAA,CAAA,QAAK,CAAC,MAAM;gBAAC,WAAW;0BACrB,cAAA,6LAAC,yLAAA,CAAA,QAAK,CAAC,KAAK;8BAAE,MAAM,UAAU,IAAI;;;;;;;;;;;0BAEtC,6LAAC,yLAAA,CAAA,QAAK,CAAC,IAAI;;oBACN,2BACG,6LAAC;;4BACI,MAAM,IAAI,KAAK,uBACZ,6LAAC,iLAAA,CAAA,UAAoB;gCACjB,SAAS,MAAM,KAAK;gCACpB,gBAAgB,CAAA;oCACZ,cAAc;wCAAE,GAAG,MAAM;oCAAC;gCAC9B;gCACA,sBAAsB;gCACtB,wBAAwB;gCACxB,eAAe;gCACf,SAAS,MAAM,OAAO,EAAE;gCACxB,uBAAuB;;;;;uCAE3B;4BACH,MAAM,IAAI,KAAK,yBACZ,6LAAC,6LAAA,CAAA,UAA0B;gCACvB,SAAS,MAAM,KAAK;gCACpB,eAAe;gCACf,sBAAsB;gCACtB,sBAAsB;gCACtB,SAAS,MAAM,OAAO,EAAE;;;;;uCAE5B;4BACH,MAAM,IAAI,KAAK,yBACZ,6LAAC,qLAAA,CAAA,UAAsB;gCACnB,mBAAmB;gCACnB,gBAAgB;gCAChB,2BAA2B;gCAC3B,0BAA0B;gCAC1B,SAAS,MAAM,OAAO,EAAE;;;;;uCAE5B;4BACH,MAAM,IAAI,KAAK,0BAAY,6LAAC,uLAAA,CAAA,UAAuB;;;;uCAAM;0CAC1D,6LAAC;gCAAM,SAAQ;0CAA+B;;;;;;0CAC9C,6LAAC;gCAAI,OAAO;oCAAE,SAAS;oCAAQ,KAAK;gCAAG;;kDACnC,6LAAC,wOAAA,CAAA,YAAS;wCACN,IAAG;wCACH,WAAW,+JAAA,CAAA,UAAM,CAAC,WAAW;wCAC7B,WAAW;wCACX,UAAS;wCACT,OAAO;4CAAE,MAAM;wCAAE;wCACjB,SAAS;wCACT,aAAa;wCACb,UAAU;wCACV,UAAU,CAAA;4CACN,+BAA+B;wCACnC;wCACA,UAAU;;;;;;kDAEd,6LAAC,2LAAA,CAAA,SAAM;wCACH,SAAS;4CACL,0BAA0B;wCAC9B;kDACH;;;;;;;;;;;;0CAIL,6LAAC,2LAAA,CAAA,SAAM;gCAAC,SAAS;gCAAa,UAAU,sBAAsB,CAAC;gCAAmB,WAAW,+JAAA,CAAA,UAAM,CAAC,YAAY;0CAC3G,MAAM,eAAe,IAAI;;;;;;4BAE7B,cAAc,OAAO,IAAI,CAAC,YAAY,MAAM,GAAG,4BAC5C,6LAAC;gCAAE,OAAO;oCAAE,OAAO;gCAAM;0CAAG;;;;;uCAC5B;;;;;;6CAGR,6LAAC;kCAAE;;;;;;kCAEP,6LAAC,8IAAA,CAAA,UAAY;wBAAC,cAAc;;;;;;oBAC3B,sBAAsB,CAAC,aAAa,CAAA,GAAA,yHAAA,CAAA,oBAAiB,AAAD,MAAM;;;;;;;;;;;;;IAKvE,qBACI,6LAAC;QAAI,WAAW,+JAAA,CAAA,UAAM,CAAC,eAAe;;YACjC;YACA;YACA,MAAM,YAAY,iBACf,6LAAC;gBAAI,SAAS;0BACV,cAAA,6LAAC,4JAAA,CAAA,UAAQ;;;;;;;;;qCAGb,6LAAC,2LAAA,CAAA,SAAM;gBAAC,OAAO;oBAAE,OAAO;gBAAc;gBAAG,SAAS;;kCAC9C,6LAAC,6KAAA,CAAA,UAAgB;;;;;oBAChB,MAAM,aAAa,IAAI;;;;;;;;;;;;;AAK5C;GAhQS;;QACgB,sNAAA,CAAA,YAAS;QACjB,qIAAA,CAAA,YAAS;QAiBG,kHAAA,CAAA,wBAAqB;;;KAnBzC;uCAkQM", "debugId": null}}, {"offset": {"line": 3170, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/components/FilterElement/FilterElements/DateFilterElement.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"calendarIcon\": \"DateFilterElement-module__aG3NAG__calendarIcon\",\n  \"dateFilter\": \"DateFilterElement-module__aG3NAG__dateFilter\",\n  \"datePickerPopper\": \"DateFilterElement-module__aG3NAG__datePickerPopper\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 3181, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/FilterElement/FilterElements/DateFilterElement.tsx"], "sourcesContent": ["'use client'\nimport React from 'react'\nimport DatePicker from 'react-datepicker'\nimport styles from './DateFilterElement.module.css'\n\ninterface Props {\n    selected: Date\n    onChange(n: number)\n}\n\nexport function DateFilterElement(props: Props) {\n    function _onChange(date: Date) {\n        date = date || new Date()\n        props.onChange(Math.round(date.getTime() / 1000))\n    }\n\n    return (\n        <span>\n            <DatePicker\n                showIcon\n                calendarIconClassName={styles.calendarIcon}\n                showTimeSelect\n                className={`date-filter form-control ${styles.dateFilter}`}\n                selected={props.selected}\n                dateFormat={'yyyy/MM/dd HH:mm'}\n                onChange={_onChange}\n                popperClassName={styles.datePickerPopper}\n            />\n        </span>\n    )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAUO,SAAS,kBAAkB,KAAY;IAC1C,SAAS,UAAU,IAAU;QACzB,OAAO,QAAQ,IAAI;QACnB,MAAM,QAAQ,CAAC,KAAK,KAAK,CAAC,KAAK,OAAO,KAAK;IAC/C;IAEA,qBACI,6LAAC;kBACG,cAAA,6LAAC,6JAAA,CAAA,UAAU;YACP,QAAQ;YACR,uBAAuB,iLAAA,CAAA,UAAM,CAAC,YAAY;YAC1C,cAAc;YACd,WAAW,CAAC,yBAAyB,EAAE,iLAAA,CAAA,UAAM,CAAC,UAAU,EAAE;YAC1D,UAAU,MAAM,QAAQ;YACxB,YAAY;YACZ,UAAU;YACV,iBAAiB,iLAAA,CAAA,UAAM,CAAC,gBAAgB;;;;;;;;;;;AAIxD;KApBgB", "debugId": null}}, {"offset": {"line": 3229, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/FilterElement/FilterElements/RangeFilterElement.tsx"], "sourcesContent": ["'use client'\nimport { ChangeEvent } from 'react'\nimport { Form } from 'react-bootstrap'\n\ninterface Props {\n    onChange(n: string)\n    isValid: boolean\n    defaultValue: any\n}\n\nexport function RangeFilterElement(props: Props) {\n    function _onChange(event: ChangeEvent<HTMLInputElement>) {\n        props.onChange(event.target.value)\n    }\n\n    return <Form.Control isInvalid={!props.isValid} defaultValue={props.defaultValue} onChange={_onChange}></Form.Control>\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAUO,SAAS,mBAAmB,KAAY;IAC3C,SAAS,UAAU,KAAoC;QACnD,MAAM,QAAQ,CAAC,MAAM,MAAM,CAAC,KAAK;IACrC;IAEA,qBAAO,6LAAC,uLAAA,CAAA,OAAI,CAAC,OAAO;QAAC,WAAW,CAAC,MAAM,OAAO;QAAE,cAAc,MAAM,YAAY;QAAE,UAAU;;;;;;AAChG;KANgB", "debugId": null}}, {"offset": {"line": 3263, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/FilterElement/FilterElements/PlayerFilterElement.tsx"], "sourcesContent": ["'use client'\nimport { forwardRef, Ref, useState } from 'react'\nimport { AsyncTypeahead } from 'react-bootstrap-typeahead'\nimport api from '../../../api/ApiHelper'\nimport { v4 as generateUUID } from 'uuid'\nimport Typeahead from 'react-bootstrap-typeahead/types/core/Typeahead'\ninterface Props {\n    onChange(n: string | Player)\n    disabled?: boolean\n    returnType: 'name' | 'uuid' | 'player'\n    defaultValue: string\n    ref?(ref)\n    placeholder?: string\n    isValid?: boolean\n}\n\nexport let PlayerFilterElement = forwardRef((props: Props, ref: Ref<Typeahead>) => {\n    // for player search\n    let [players, setPlayers] = useState<Player[]>([])\n    let [isLoading, setIsLoading] = useState(false)\n\n    function _onChange(selected) {\n        props.onChange(selected[0] || '')\n    }\n\n    function handlePlayerSearch(query) {\n        setIsLoading(true)\n\n        api.playerSearch(query).then(players => {\n            setPlayers(players)\n            setIsLoading(false)\n        })\n    }\n\n    return (\n        <AsyncTypeahead\n            id={generateUUID()}\n            disabled={props.disabled}\n            filterBy={() => true}\n            isLoading={isLoading}\n            labelKey=\"name\"\n            minLength={1}\n            isInvalid={!props.isValid}\n            onSearch={handlePlayerSearch}\n            defaultInputValue={props.defaultValue}\n            options={players}\n            placeholder={props.placeholder || 'Search users...'}\n            onChange={selected =>\n                _onChange(\n                    selected.map(s => {\n                        if (props.returnType === 'player') {\n                            return s\n                        }\n                        return s[props.returnType]\n                    })\n                )\n            }\n            ref={ref}\n        />\n    )\n})\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AACA;AACA;;;AAJA;;;;;AAgBO,IAAI,oCAAsB,GAAA,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,UAAE,CAAC,OAAc;;IACvD,oBAAoB;IACpB,IAAI,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACjD,IAAI,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,SAAS,UAAU,QAAQ;QACvB,MAAM,QAAQ,CAAC,QAAQ,CAAC,EAAE,IAAI;IAClC;IAEA,SAAS,mBAAmB,KAAK;QAC7B,aAAa;QAEb,oHAAA,CAAA,UAAG,CAAC,YAAY,CAAC,OAAO,IAAI,CAAC,CAAA;YACzB,WAAW;YACX,aAAa;QACjB;IACJ;IAEA,qBACI,6LAAC,uPAAA,CAAA,iBAAc;QACX,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAY,AAAD;QACf,UAAU,MAAM,QAAQ;QACxB,UAAU,IAAM;QAChB,WAAW;QACX,UAAS;QACT,WAAW;QACX,WAAW,CAAC,MAAM,OAAO;QACzB,UAAU;QACV,mBAAmB,MAAM,YAAY;QACrC,SAAS;QACT,aAAa,MAAM,WAAW,IAAI;QAClC,UAAU,CAAA,WACN,UACI,SAAS,GAAG,CAAC,CAAA;gBACT,IAAI,MAAM,UAAU,KAAK,UAAU;oBAC/B,OAAO;gBACX;gBACA,OAAO,CAAC,CAAC,MAAM,UAAU,CAAC;YAC9B;QAGR,KAAK;;;;;;AAGjB", "debugId": null}}, {"offset": {"line": 3332, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/FilterElement/FilterElements/SimpleEqualFilterElement.tsx"], "sourcesContent": ["'use client'\nimport { ChangeEvent } from 'react'\nimport { Form } from 'react-bootstrap'\nimport { convertTagToName } from '../../../utils/Formatter'\n\ninterface Props {\n    onChange(n: string)\n    options: string[]\n    defaultValue: any\n    isValid: boolean\n}\n\nexport function SimpleEqualFilterElement(props: Props) {\n    function _onChange(event: ChangeEvent<HTMLSelectElement>) {\n        let selectedIndex = event.target.options.selectedIndex\n        let value = event.target.options[selectedIndex].getAttribute('data-id')!\n        props.onChange(value)\n    }\n\n    function getSelectOptions() {\n        return props.options.map(option => (\n            <option data-id={option} key={option} value={option}>\n                {convertTagToName(option)}\n            </option>\n        ))\n    }\n\n    return (\n        <Form.Select isInvalid={!props.isValid} defaultValue={props.defaultValue} onChange={_onChange}>\n            {getSelectOptions()}\n        </Form.Select>\n    )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAYO,SAAS,yBAAyB,KAAY;IACjD,SAAS,UAAU,KAAqC;QACpD,IAAI,gBAAgB,MAAM,MAAM,CAAC,OAAO,CAAC,aAAa;QACtD,IAAI,QAAQ,MAAM,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,YAAY,CAAC;QAC7D,MAAM,QAAQ,CAAC;IACnB;IAEA,SAAS;QACL,OAAO,MAAM,OAAO,CAAC,GAAG,CAAC,CAAA,uBACrB,6LAAC;gBAAO,WAAS;gBAAqB,OAAO;0BACxC,CAAA,GAAA,sHAAA,CAAA,mBAAgB,AAAD,EAAE;eADQ;;;;;IAItC;IAEA,qBACI,6LAAC,uLAAA,CAAA,OAAI,CAAC,MAAM;QAAC,WAAW,CAAC,MAAM,OAAO;QAAE,cAAc,MAAM,YAAY;QAAE,UAAU;kBAC/E;;;;;;AAGb;KApBgB", "debugId": null}}, {"offset": {"line": 3382, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/FilterElement/FilterElements/EqualFilterElement.tsx"], "sourcesContent": ["'use client'\nimport React from 'react'\nimport { convertTagToName } from '../../../utils/Formatter'\nimport { Menu, MenuItem, Typeahead, useItem } from 'react-bootstrap-typeahead'\nimport { Option } from 'react-bootstrap-typeahead/types/types'\nimport api from '../../../api/ApiHelper'\n\nconst Item = props => <MenuItem {...props} {...useItem(props)} />\n\ninterface Props {\n    onChange(n: string)\n    options: FilterOptions\n    defaultValue?: any\n    isValid?: boolean\n    showIcon?: boolean\n}\n\nexport function EqualFilterElement(props: Props) {\n    function _onChange(selected) {\n        props.onChange(selected[0] || '')\n    }\n\n    return (\n        <Typeahead\n            id={props.options.name}\n            style={{ display: 'block' }}\n            defaultSelected={props.defaultValue ? [props.defaultValue] : undefined}\n            onChange={_onChange}\n            options={props.options?.options}\n            labelKey={option => {\n                return convertTagToName(option as string)\n            }}\n            isInvalid={!props.isValid}\n            selectHint={(shouldSelect, event) => {\n                return event.key === 'Enter' || shouldSelect\n            }}\n            renderMenu={(results, menuProps) => {\n                return <Menu id={menuProps.id} style={menuProps.style} innerRef={menuProps.innerRef} >\n                    {results.map((result, index) => {\n                        if (result['paginationOption']) {\n                            return (\n                                <MenuItem option={result} position={index} key={index}>\n                                    More results...\n                                </MenuItem>\n                            )\n                        }\n                        return (\n                            <Item option={result} position={index} key={index}>\n                                {typeof result === 'string' ? convertTagToName(result as string) : (result as Option)['label']}\n                                {props.showIcon && result !== 'None' && result !== 'Any' && (\n                                    <div style={{ float: 'right' }}>\n                                        <img src={api.getItemImageUrl({ tag: result as string })} style={{ width: '24px', height: '24px' }}></img>\n                                    </div>\n                                )}\n                            </Item>\n                        )\n                    })}\n                </Menu>\n            }}\n        ></Typeahead>\n    )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAEA;;;AALA;;;;AAOA,MAAM,OAAO,CAAA;;IAAS,OAAA,6LAAC,qOAAA,CAAA,WAAQ;QAAE,GAAG,KAAK;QAAG,GAAG,CAAA,GAAA,6KAAA,CAAA,UAAO,AAAD,EAAE,MAAM;;;;;;AAAG;GAA1D;;QAAyC,6KAAA,CAAA,UAAO;;;KAAhD;AAUC,SAAS,mBAAmB,KAAY;IAC3C,SAAS,UAAU,QAAQ;QACvB,MAAM,QAAQ,CAAC,QAAQ,CAAC,EAAE,IAAI;IAClC;IAEA,qBACI,6LAAC,wOAAA,CAAA,YAAS;QACN,IAAI,MAAM,OAAO,CAAC,IAAI;QACtB,OAAO;YAAE,SAAS;QAAQ;QAC1B,iBAAiB,MAAM,YAAY,GAAG;YAAC,MAAM,YAAY;SAAC,GAAG;QAC7D,UAAU;QACV,SAAS,MAAM,OAAO,EAAE;QACxB,UAAU,CAAA;YACN,OAAO,CAAA,GAAA,sHAAA,CAAA,mBAAgB,AAAD,EAAE;QAC5B;QACA,WAAW,CAAC,MAAM,OAAO;QACzB,YAAY,CAAC,cAAc;YACvB,OAAO,MAAM,GAAG,KAAK,WAAW;QACpC;QACA,YAAY,CAAC,SAAS;YAClB,qBAAO,6LAAC,yNAAA,CAAA,OAAI;gBAAC,IAAI,UAAU,EAAE;gBAAE,OAAO,UAAU,KAAK;gBAAE,UAAU,UAAU,QAAQ;0BAC9E,QAAQ,GAAG,CAAC,CAAC,QAAQ;oBAClB,IAAI,MAAM,CAAC,mBAAmB,EAAE;wBAC5B,qBACI,6LAAC,qOAAA,CAAA,WAAQ;4BAAC,QAAQ;4BAAQ,UAAU;sCAAmB;2BAAP;;;;;oBAIxD;oBACA,qBACI,6LAAC;wBAAK,QAAQ;wBAAQ,UAAU;;4BAC3B,OAAO,WAAW,WAAW,CAAA,GAAA,sHAAA,CAAA,mBAAgB,AAAD,EAAE,UAAoB,AAAC,MAAiB,CAAC,QAAQ;4BAC7F,MAAM,QAAQ,IAAI,WAAW,UAAU,WAAW,uBAC/C,6LAAC;gCAAI,OAAO;oCAAE,OAAO;gCAAQ;0CACzB,cAAA,6LAAC;oCAAI,KAAK,oHAAA,CAAA,UAAG,CAAC,eAAe,CAAC;wCAAE,KAAK;oCAAiB;oCAAI,OAAO;wCAAE,OAAO;wCAAQ,QAAQ;oCAAO;;;;;;;;;;;;uBAJjE;;;;;gBASpD;;;;;;QAER;;;;;;AAGZ;MA5CgB", "debugId": null}}, {"offset": {"line": 3513, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/FilterElement/FilterElements/PlayerWithRankFilterElement.tsx"], "sourcesContent": ["'use client'\nimport { useEffect, useRef, useState } from 'react'\nimport { Form } from 'react-bootstrap'\nimport { PlayerFilterElement } from '../FilterElements/PlayerFilterElement'\n\ninterface Props {\n    onChange(n: string)\n    defaultValue: any\n}\n\nconst RANKS = [\n    {\n        color: '#AAAAAA',\n        tag: '[NO_PLAYER]',\n        value: ' '\n    },\n    {\n        color: '#AAAAAA',\n        tag: '[NO_RANK]',\n        value: ' '\n    },\n    {\n        color: '#FF5555',\n        tag: '[OWNER]'\n    },\n    {\n        color: '#FF5555',\n        tag: '[ADMIN]'\n    },\n    {\n        color: '#00AAAA',\n        tag: '[BUILD TEAM]'\n    },\n    {\n        color: '#00AA00',\n        tag: '[MOD]'\n    },\n    {\n        color: '#00AA00',\n        tag: '[GM]'\n    },\n    {\n        color: '#5555FF',\n        tag: '[HELPER]'\n    },\n    {\n        color: '#FF5555',\n        tag: '[YOUTUBE]'\n    },\n    {\n        color: '#FFAA00',\n        tag: '[MVP++]'\n    },\n    {\n        color: '#55FFFF',\n        tag: '[MVP+]'\n    },\n    {\n        color: '#55FFFF',\n        tag: '[MVP]'\n    },\n    {\n        color: '#55FF55',\n        tag: '[VIP+]'\n    },\n    {\n        color: '#55FF55',\n        tag: '[VIP]'\n    },\n    {\n        color: '#FF55FF',\n        tag: '[PIG+++]'\n    },\n    {\n        color: '#FF5555',\n        tag: '[MINISTER]'\n    },\n    {\n        color: '#FF55FF',\n        tag: '[MAYOR]'\n    }\n]\n\nexport function PlayerWithRankFilterElement(props: Props) {\n    let [rank, setRank] = useState<string>(getDefaultValues()[0])\n    let [player, setPlayer] = useState<string>(getDefaultValues()[1])\n\n    let playerRef = useRef(null)\n\n    function getDefaultValues() {\n        let splits: string[] = []\n        if (props.defaultValue) {\n            let s = props.defaultValue.trim().split(']')\n            if (s.length === 1) {\n                if (props.defaultValue.indexOf(']') !== -1) {\n                    splits = [props.defaultValue, '']\n                } else {\n                    splits = [RANKS[1].tag, props.defaultValue.trim()]\n                }\n            } else {\n                splits = [s[0] + ']', s[1]]\n            }\n        } else {\n            splits = [RANKS[0].tag, '']\n        }\n        return splits\n    }\n\n    useEffect(() => {\n        // If there is no default value, call _onChange, so the default is set from here\n        // timeout, so the useEffect of the FilterElement-Component is executed first\n        if (!props.defaultValue) {\n            setTimeout(() => {\n                _onChange()\n            })\n        }\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [])\n\n    function _onChange() {\n        let rankObject = RANKS.find(r => r.tag === rank)!\n        let rankValue = rankObject.value !== undefined ? rankObject.value : rankObject.tag\n        props.onChange(rankValue + ' ' + player)\n    }\n\n    function _onPlayerChange(uuid) {\n        player = uuid\n        setPlayer(uuid)\n        _onChange()\n    }\n\n    function _onRankChange(event) {\n        let selectedIndex = event.target.options.selectedIndex\n        let _rank = event.target.options[selectedIndex].value\n        rank = _rank\n\n        if (RANKS[0].tag === rank) {\n            setPlayer('')\n            player = ''\n            if (playerRef && playerRef.current) {\n                ;(playerRef.current as any).clear()\n            }\n        }\n\n        setRank(_rank)\n        _onChange()\n    }\n\n    return (\n        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>\n            <Form.Select style={{ width: '50%', color: RANKS.find(r => r.tag === rank)?.color }} defaultValue={rank} onChange={_onRankChange}>\n                {RANKS.map(rank => {\n                    return (\n                        <option key={rank.tag} value={rank.tag} style={{ color: rank.color }}>\n                            {rank.tag}\n                        </option>\n                    )\n                })}\n            </Form.Select>\n            <div style={{ width: '50%' }}>\n                <PlayerFilterElement ref={playerRef} returnType=\"name\" defaultValue={player} onChange={_onPlayerChange} disabled={rank === RANKS[0].tag} />\n            </div>\n        </div>\n    )\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;AAHA;;;;AAUA,MAAM,QAAQ;IACV;QACI,OAAO;QACP,KAAK;QACL,OAAO;IACX;IACA;QACI,OAAO;QACP,KAAK;QACL,OAAO;IACX;IACA;QACI,OAAO;QACP,KAAK;IACT;IACA;QACI,OAAO;QACP,KAAK;IACT;IACA;QACI,OAAO;QACP,KAAK;IACT;IACA;QACI,OAAO;QACP,KAAK;IACT;IACA;QACI,OAAO;QACP,KAAK;IACT;IACA;QACI,OAAO;QACP,KAAK;IACT;IACA;QACI,OAAO;QACP,KAAK;IACT;IACA;QACI,OAAO;QACP,KAAK;IACT;IACA;QACI,OAAO;QACP,KAAK;IACT;IACA;QACI,OAAO;QACP,KAAK;IACT;IACA;QACI,OAAO;QACP,KAAK;IACT;IACA;QACI,OAAO;QACP,KAAK;IACT;IACA;QACI,OAAO;QACP,KAAK;IACT;IACA;QACI,OAAO;QACP,KAAK;IACT;IACA;QACI,OAAO;QACP,KAAK;IACT;CACH;AAEM,SAAS,4BAA4B,KAAY;;IACpD,IAAI,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,kBAAkB,CAAC,EAAE;IAC5D,IAAI,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,kBAAkB,CAAC,EAAE;IAEhE,IAAI,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAEvB,SAAS;QACL,IAAI,SAAmB,EAAE;QACzB,IAAI,MAAM,YAAY,EAAE;YACpB,IAAI,IAAI,MAAM,YAAY,CAAC,IAAI,GAAG,KAAK,CAAC;YACxC,IAAI,EAAE,MAAM,KAAK,GAAG;gBAChB,IAAI,MAAM,YAAY,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG;oBACxC,SAAS;wBAAC,MAAM,YAAY;wBAAE;qBAAG;gBACrC,OAAO;oBACH,SAAS;wBAAC,KAAK,CAAC,EAAE,CAAC,GAAG;wBAAE,MAAM,YAAY,CAAC,IAAI;qBAAG;gBACtD;YACJ,OAAO;gBACH,SAAS;oBAAC,CAAC,CAAC,EAAE,GAAG;oBAAK,CAAC,CAAC,EAAE;iBAAC;YAC/B;QACJ,OAAO;YACH,SAAS;gBAAC,KAAK,CAAC,EAAE,CAAC,GAAG;gBAAE;aAAG;QAC/B;QACA,OAAO;IACX;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iDAAE;YACN,gFAAgF;YAChF,6EAA6E;YAC7E,IAAI,CAAC,MAAM,YAAY,EAAE;gBACrB;6DAAW;wBACP;oBACJ;;YACJ;QACA,uDAAuD;QAC3D;gDAAG,EAAE;IAEL,SAAS;QACL,IAAI,aAAa,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK;QAC3C,IAAI,YAAY,WAAW,KAAK,KAAK,YAAY,WAAW,KAAK,GAAG,WAAW,GAAG;QAClF,MAAM,QAAQ,CAAC,YAAY,MAAM;IACrC;IAEA,SAAS,gBAAgB,IAAI;QACzB,SAAS;QACT,UAAU;QACV;IACJ;IAEA,SAAS,cAAc,KAAK;QACxB,IAAI,gBAAgB,MAAM,MAAM,CAAC,OAAO,CAAC,aAAa;QACtD,IAAI,QAAQ,MAAM,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,KAAK;QACrD,OAAO;QAEP,IAAI,KAAK,CAAC,EAAE,CAAC,GAAG,KAAK,MAAM;YACvB,UAAU;YACV,SAAS;YACT,IAAI,aAAa,UAAU,OAAO,EAAE;;gBAC9B,UAAU,OAAO,CAAS,KAAK;YACrC;QACJ;QAEA,QAAQ;QACR;IACJ;IAEA,qBACI,6LAAC;QAAI,OAAO;YAAE,SAAS;YAAQ,gBAAgB;YAAU,YAAY;QAAS;;0BAC1E,6LAAC,uLAAA,CAAA,OAAI,CAAC,MAAM;gBAAC,OAAO;oBAAE,OAAO;oBAAO,OAAO,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK,OAAO;gBAAM;gBAAG,cAAc;gBAAM,UAAU;0BAC9G,MAAM,GAAG,CAAC,CAAA;oBACP,qBACI,6LAAC;wBAAsB,OAAO,KAAK,GAAG;wBAAE,OAAO;4BAAE,OAAO,KAAK,KAAK;wBAAC;kCAC9D,KAAK,GAAG;uBADA,KAAK,GAAG;;;;;gBAI7B;;;;;;0BAEJ,6LAAC;gBAAI,OAAO;oBAAE,OAAO;gBAAM;0BACvB,cAAA,6LAAC,wKAAA,CAAA,sBAAmB;oBAAC,KAAK;oBAAW,YAAW;oBAAO,cAAc;oBAAQ,UAAU;oBAAiB,UAAU,SAAS,KAAK,CAAC,EAAE,CAAC,GAAG;;;;;;;;;;;;;;;;;AAIvJ;GAjFgB;KAAA", "debugId": null}}, {"offset": {"line": 3744, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/FilterElement/FilterElements/ColorFilterElement.tsx"], "sourcesContent": ["'use client'\nimport { useState } from 'react'\nimport { HexColorInput } from 'react-colorful'\n\ninterface Props {\n    onChange(n: string)\n    defaultValue: any\n}\n\nexport function ColorFilterElement(props: Props) {\n    const [color, setColor] = useState(props.defaultValue)\n\n    function _onChange(color) {\n        color = color.replace('#', '')\n        setColor(color)\n        props.onChange(color)\n    }\n\n    return (\n        <div>\n            <HexColorInput className=\"form-control\" style={{ textTransform: 'uppercase' }} color={color} onChange={_onChange} prefixed />\n        </div>\n    )\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;;;AAFA;;;AASO,SAAS,mBAAmB,KAAY;;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,MAAM,YAAY;IAErD,SAAS,UAAU,KAAK;QACpB,QAAQ,MAAM,OAAO,CAAC,KAAK;QAC3B,SAAS;QACT,MAAM,QAAQ,CAAC;IACnB;IAEA,qBACI,6LAAC;kBACG,cAAA,6LAAC,sJAAA,CAAA,gBAAa;YAAC,WAAU;YAAe,OAAO;gBAAE,eAAe;YAAY;YAAG,OAAO;YAAO,UAAU;YAAW,QAAQ;;;;;;;;;;;AAGtI;GAdgB;KAAA", "debugId": null}}, {"offset": {"line": 3796, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/FilterElement/FilterElements/BooleanFilterElement.tsx"], "sourcesContent": ["'use client'\nimport { ChangeEvent } from 'react'\nimport { Form } from 'react-bootstrap'\n\ninterface Props {\n    defaultValue: string\n    onChange(n: boolean)\n}\n\nexport function BooleanFilterElement(props: Props) {\n    function _onChange(e: ChangeEvent<HTMLSelectElement>) {\n        props.onChange((e.target as any).value)\n    }\n\n    return (\n        <span>\n            <Form.Select defaultValue={props.defaultValue || 'true'} onChange={_onChange}>\n                <option key={'true'} value={'true'}>\n                    true\n                </option>\n                <option key={'false'} value={'false'}>\n                    false\n                </option>\n            </Form.Select>\n        </span>\n    )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AASO,SAAS,qBAAqB,KAAY;IAC7C,SAAS,UAAU,CAAiC;QAChD,MAAM,QAAQ,CAAC,AAAC,EAAE,MAAM,CAAS,KAAK;IAC1C;IAEA,qBACI,6LAAC;kBACG,cAAA,6LAAC,uLAAA,CAAA,OAAI,CAAC,MAAM;YAAC,cAAc,MAAM,YAAY,IAAI;YAAQ,UAAU;;8BAC/D,6LAAC;oBAAoB,OAAO;8BAAQ;mBAAvB;;;;;8BAGb,6LAAC;oBAAqB,OAAO;8BAAS;mBAAzB;;;;;;;;;;;;;;;;AAM7B;KAjBgB", "debugId": null}}, {"offset": {"line": 3852, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/components/FilterElement/FilterElement.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"genericFilter\": \"FilterElement-module__NQNxTa__genericFilter\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA"}}, {"offset": {"line": 3861, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/FilterElement/FilterElements/NumericalFilterElement.tsx"], "sourcesContent": ["'use client'\nimport { Form } from 'react-bootstrap'\nimport { NumberFormatValues, NumericFormat } from 'react-number-format'\nimport { getDecimalSeparator, getThousandSeparator } from '../../../utils/Formatter'\n\ninterface Props {\n    onChange(n: string)\n    options: FilterOptions\n    defaultValue: any\n}\n\nexport function NumericalFilterElement(props: Props) {\n    function _onChange(values: NumberFormatValues) {\n        props.onChange(values.value || '')\n    }\n\n\n    return (\n        <NumericFormat\n            onValueChange={_onChange}\n            customInput={Form.Control}\n            defaultValue={props.defaultValue}\n            allowNegative={false}\n            isAllowed={value => {\n                if (!value.floatValue) {\n                    return true\n                }\n                let options = props.options?.options\n                if (options.length === 2 && !isNaN(+options[0]) && !isNaN(+options[1])) {\n                    return value.floatValue >= +options[0] && value.floatValue <= +options[1]\n                }\n                return true\n            }}\n            decimalScale={2}\n        />\n    )\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AAFA;;;;AAWO,SAAS,uBAAuB,KAAY;IAC/C,SAAS,UAAU,MAA0B;QACzC,MAAM,QAAQ,CAAC,OAAO,KAAK,IAAI;IACnC;IAGA,qBACI,6LAAC,uLAAA,CAAA,gBAAa;QACV,eAAe;QACf,aAAa,uLAAA,CAAA,OAAI,CAAC,OAAO;QACzB,cAAc,MAAM,YAAY;QAChC,eAAe;QACf,WAAW,CAAA;YACP,IAAI,CAAC,MAAM,UAAU,EAAE;gBACnB,OAAO;YACX;YACA,IAAI,UAAU,MAAM,OAAO,EAAE;YAC7B,IAAI,QAAQ,MAAM,KAAK,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,GAAG;gBACpE,OAAO,MAAM,UAAU,IAAI,CAAC,OAAO,CAAC,EAAE,IAAI,MAAM,UAAU,IAAI,CAAC,OAAO,CAAC,EAAE;YAC7E;YACA,OAAO;QACX;QACA,cAAc;;;;;;AAG1B;KAzBgB", "debugId": null}}, {"offset": {"line": 3908, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/components/FilterElement/FilterElements/NumberRangeFilterElement.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"container\": \"NumberRangeFilterElement-module__oxFKZG__container\",\n  \"slider\": \"NumberRangeFilterElement-module__oxFKZG__slider\",\n  \"textField\": \"NumberRangeFilterElement-module__oxFKZG__textField\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 3919, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/FilterElement/FilterElements/NumberRangeFilterElement.tsx"], "sourcesContent": ["'use client'\nimport { ChangeEvent, useMemo, useState } from 'react'\nimport Slider from 'rc-slider'\nimport styles from './NumberRangeFilterElement.module.css'\nimport 'rc-slider/assets/index.css'\nimport { Form } from 'react-bootstrap'\n\ninterface Props {\n    onChange(n: string)\n    min?: number\n    max?: number\n    defaultValue: any\n}\n\nexport function NumberRangeFilterElement(props: Props) {\n    let defaultValue = useMemo(() => {\n        return parseValue(props.defaultValue)\n    }, [props.defaultValue])\n\n    let [value, setValue] = useState(defaultValue)\n    let [textValue, setTextValue] = useState(props.defaultValue)\n\n    function parseValue(value: string | number | string[] | number[]): number[] {\n        if (!value) {\n            return [5, 5]\n        }\n\n        if (Array.isArray(value)) {\n            return value.map(v => parseInt(v.toString()))\n        }\n\n        let checks = [\n            {\n                regexp: new RegExp(/^\\d+-\\d+$/),\n                handler: value => value.split('-').map(v => parseInt(v))\n            },\n            {\n                regexp: new RegExp(/^\\d+$/),\n                handler: value => [parseInt(value), parseInt(value)]\n            },\n            {\n                regexp: new RegExp(/^<\\d+$/),\n                handler: value => [props.min || 0, parseInt(value.split('<')[1]) - 1]\n            },\n            {\n                regexp: new RegExp(/^>\\d+$/),\n                handler: value => [parseInt(value.split('>')[1]) + 1, props.max]\n            }\n        ]\n\n        let result\n        checks.forEach(check => {\n            if (value.toString().match(check.regexp)) {\n                result = check.handler(value)\n            }\n        })\n        return result\n    }\n\n    function _onTextChange(e: ChangeEvent<HTMLInputElement>) {\n        setTextValue(e.target.value)\n        if (!e.target.value) {\n            return\n        }\n        let parsed = parseValue(e.target.value)\n        if (!parsed) {\n            return\n        }\n        setValue(parsed)\n        props.onChange(`${parsed[0]}-${parsed[1]}`)\n    }\n\n    function _onRangeChange(values: number[]) {\n        setTextValue(`${values[0]}-${values[1]}`)\n        setValue(values)\n        props.onChange(`${values[0]}-${values[1]}`)\n    }\n\n    function getMarks() {\n        if (props.max === undefined || props.max === null) {\n            return undefined\n        }\n        let marks = {}\n        for (let i = props.min || 0; i <= props.max; i++) {\n            marks[i] = i === 0 ? 'None' : i.toString()\n        }\n        return marks\n    }\n\n    return (\n        <div className={styles.container}>\n            <Form.Control value={textValue} onChange={_onTextChange} className={styles.textField} />\n            <Slider\n                className={styles.slider}\n                range\n                marks={getMarks()}\n                allowCross={false}\n                onChange={_onRangeChange}\n                min={props.min || 0}\n                max={props.max}\n                value={value}\n            />\n        </div>\n    )\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAEA;;;AALA;;;;;;AAcO,SAAS,yBAAyB,KAAY;;IACjD,IAAI,eAAe,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;0DAAE;YACvB,OAAO,WAAW,MAAM,YAAY;QACxC;yDAAG;QAAC,MAAM,YAAY;KAAC;IAEvB,IAAI,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,IAAI,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,MAAM,YAAY;IAE3D,SAAS,WAAW,KAA4C;QAC5D,IAAI,CAAC,OAAO;YACR,OAAO;gBAAC;gBAAG;aAAE;QACjB;QAEA,IAAI,MAAM,OAAO,CAAC,QAAQ;YACtB,OAAO,MAAM,GAAG,CAAC,CAAA,IAAK,SAAS,EAAE,QAAQ;QAC7C;QAEA,IAAI,SAAS;YACT;gBACI,QAAQ,IAAI,OAAO;gBACnB,SAAS,CAAA,QAAS,MAAM,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,SAAS;YACzD;YACA;gBACI,QAAQ,IAAI,OAAO;gBACnB,SAAS,CAAA,QAAS;wBAAC,SAAS;wBAAQ,SAAS;qBAAO;YACxD;YACA;gBACI,QAAQ,IAAI,OAAO;gBACnB,SAAS,CAAA,QAAS;wBAAC,MAAM,GAAG,IAAI;wBAAG,SAAS,MAAM,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI;qBAAE;YACzE;YACA;gBACI,QAAQ,IAAI,OAAO;gBACnB,SAAS,CAAA,QAAS;wBAAC,SAAS,MAAM,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI;wBAAG,MAAM,GAAG;qBAAC;YACpE;SACH;QAED,IAAI;QACJ,OAAO,OAAO,CAAC,CAAA;YACX,IAAI,MAAM,QAAQ,GAAG,KAAK,CAAC,MAAM,MAAM,GAAG;gBACtC,SAAS,MAAM,OAAO,CAAC;YAC3B;QACJ;QACA,OAAO;IACX;IAEA,SAAS,cAAc,CAAgC;QACnD,aAAa,EAAE,MAAM,CAAC,KAAK;QAC3B,IAAI,CAAC,EAAE,MAAM,CAAC,KAAK,EAAE;YACjB;QACJ;QACA,IAAI,SAAS,WAAW,EAAE,MAAM,CAAC,KAAK;QACtC,IAAI,CAAC,QAAQ;YACT;QACJ;QACA,SAAS;QACT,MAAM,QAAQ,CAAC,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC,EAAE,EAAE;IAC9C;IAEA,SAAS,eAAe,MAAgB;QACpC,aAAa,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC,EAAE,EAAE;QACxC,SAAS;QACT,MAAM,QAAQ,CAAC,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC,EAAE,EAAE;IAC9C;IAEA,SAAS;QACL,IAAI,MAAM,GAAG,KAAK,aAAa,MAAM,GAAG,KAAK,MAAM;YAC/C,OAAO;QACX;QACA,IAAI,QAAQ,CAAC;QACb,IAAK,IAAI,IAAI,MAAM,GAAG,IAAI,GAAG,KAAK,MAAM,GAAG,EAAE,IAAK;YAC9C,KAAK,CAAC,EAAE,GAAG,MAAM,IAAI,SAAS,EAAE,QAAQ;QAC5C;QACA,OAAO;IACX;IAEA,qBACI,6LAAC;QAAI,WAAW,wLAAA,CAAA,UAAM,CAAC,SAAS;;0BAC5B,6LAAC,uLAAA,CAAA,OAAI,CAAC,OAAO;gBAAC,OAAO;gBAAW,UAAU;gBAAe,WAAW,wLAAA,CAAA,UAAM,CAAC,SAAS;;;;;;0BACpF,6LAAC,8IAAA,CAAA,UAAM;gBACH,WAAW,wLAAA,CAAA,UAAM,CAAC,MAAM;gBACxB,KAAK;gBACL,OAAO;gBACP,YAAY;gBACZ,UAAU;gBACV,KAAK,MAAM,GAAG,IAAI;gBAClB,KAAK,MAAM,GAAG;gBACd,OAAO;;;;;;;;;;;;AAIvB;GA1FgB;KAAA", "debugId": null}}, {"offset": {"line": 4064, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/FilterElement/FilterElement.tsx"], "sourcesContent": ["'use client'\n/* eslint-disable react-hooks/exhaustive-deps */\n/* eslint-disable jsx-a11y/anchor-is-valid */\nimport { useEffect, useState, type JSX } from 'react';\nimport { Form, Spinner } from 'react-bootstrap'\nimport { camelCaseToSentenceCase, convertTagToName } from '../../utils/Formatter'\nimport { FilterType, hasFlag } from './FilterType'\nimport { DateFilterElement } from './FilterElements/DateFilterElement'\nimport { RangeFilterElement } from './FilterElements/RangeFilterElement'\nimport { PlayerFilterElement } from './FilterElements/PlayerFilterElement'\nimport { SimpleEqualFilterElement } from './FilterElements/SimpleEqualFilterElement'\nimport { EqualFilterElement } from './FilterElements/EqualFilterElement'\nimport { PlayerWithRankFilterElement } from './FilterElements/PlayerWithRankFilterElement'\nimport { ColorFilterElement } from './FilterElements/ColorFilterElement'\nimport { BooleanFilterElement } from './FilterElements/BooleanFilterElement'\nimport styles from './FilterElement.module.css'\nimport { NumericalFilterElement } from './FilterElements/NumericalFilterElement'\nimport { NumberRangeFilterElement } from './FilterElements/NumberRangeFilterElement'\nimport { validateFilterNumber, validateFilterRange } from '../../utils/NumberValidationUtils'\nimport Tooltip from '../Tooltip/Tooltip'\nimport HelpIcon from '@mui/icons-material/Help'\n\ninterface Props {\n    onFilterChange?(filter?: ItemFilter): void\n    options?: FilterOptions\n    defaultValue: any\n    onIsValidChange?(newIsValid: boolean)\n}\n\nfunction FilterElement(props: Props) {\n    let [value, _setValue] = useState<any>()\n    let [isValid, _setIsValid] = useState(true)\n    let [errorText, setErrorText] = useState('')\n\n    useEffect(() => {\n        if (value) {\n            return\n        }\n        let parsedDefaultValue = parseValue(props.defaultValue)\n        updateValue(parsedDefaultValue)\n        setValue(parsedDefaultValue)\n    }, [])\n\n    function parseValue(newValue?: any) {\n        if (props.options && hasFlag(props.options.type, FilterType.DATE)) {\n            if (!newValue) {\n                return Math.round(new Date().getTime() / 1000)\n            }\n            if (!isNaN(newValue)) {\n                return newValue\n            }\n            let date = Math.round(Date.parse(newValue) / 1000)\n            if (!isNaN(date)) {\n                return date\n            }\n            return newValue\n        } else {\n            if (!newValue && newValue !== 0) {\n                return ''\n            }\n            return newValue\n        }\n    }\n\n    function onFilterElementChange(value?: any) {\n        if (!value) {\n            setValue('')\n            updateValue('')\n        } else {\n            setValue(value)\n            updateValue(value.toString())\n        }\n    }\n\n    function updateValue(value: string) {\n        if (!validate(value)) {\n            return\n        }\n\n        let newFilter = {}\n        newFilter[props.options!.name] = value.toString()\n\n        props.onFilterChange!(newFilter)\n    }\n\n    function setValue(value?: any) {\n        _setValue(parseValue(value))\n    }\n\n    function setIsValid(newValue: boolean) {\n        if (props.onIsValidChange) {\n            props.onIsValidChange(newValue)\n        }\n        _setIsValid(newValue)\n    }\n\n    function validate(value?: any) {\n        if (!value && value !== 0) {\n            setErrorText('Please fill the filter or remove it')\n            setIsValid(false)\n            return false\n        }\n        if (props.options && hasFlag(props.options.type, FilterType.NUMERICAL) && hasFlag(props.options.type, FilterType.RANGE)) {\n            let validationResult = validateFilterRange(value.toString(), props.options)\n            setIsValid(validationResult[0])\n            if (!validationResult[0]) {\n                setErrorText(validationResult[1] || '')\n                return false\n            }\n            return true\n        }\n        if (props.options && hasFlag(props.options.type, FilterType.DATE)) {\n            let date = new Date(value * 1000)\n            if (date < new Date(props.options.options[0])) {\n                setErrorText(`Date needs to be after ${props.options.options[0]}`)\n                setIsValid(false)\n                return false\n            }\n            if (date > new Date(props.options.options[1])) {\n                setErrorText(`Date needs to be before ${props.options.options[1]}`)\n                setIsValid(false)\n                return false\n            }\n            setIsValid(true)\n            return true\n        }\n        if (props.options && hasFlag(props.options.type, FilterType.RANGE)) {\n            if (props.options?.options.length === 2 && props.options.options[0] === '000000000000' && props.options.options[1] === 'ffffffffffff') {\n                let result = new RegExp(/^[0-9A-Fa-f]{12}$/).test(value)\n                setIsValid(result)\n                if (!isValid) {\n                    setErrorText('This field needs to be 12 characters long and must only include hex characters.')\n                }\n                return result\n            }\n        }\n        if (props.options && hasFlag(props.options.type, FilterType.NUMERICAL)) {\n            let validationResult = validateFilterNumber(value.toString(), props.options)\n            setIsValid(validationResult[0])\n            if (!validationResult[0]) {\n                setErrorText(validationResult[1] || '')\n                return false\n            }\n            return true\n        }\n        setIsValid(true)\n        return true\n    }\n\n    function getFilterElement(type: FilterType, options: FilterOptions): JSX.Element {\n        // Special case for the color filter, as there is no FilterType on the backend for that\n        if (options.name === 'Color') {\n            return <ColorFilterElement key={options.name} defaultValue={props.defaultValue} onChange={onFilterElementChange} />\n        }\n        if (\n            hasFlag(type, FilterType.NUMERICAL) &&\n            hasFlag(type, FilterType.RANGE) &&\n            options.options.length === 2 &&\n            !isNaN(parseInt(options.options[0])) &&\n            !isNaN(parseInt(options.options[1])) &&\n            parseInt(options.options[1]) <= 10\n        ) {\n            return (\n                <NumberRangeFilterElement\n                    key={options.name}\n                    defaultValue={props.defaultValue}\n                    min={props.options ? parseInt(props.options.options[0]) : undefined}\n                    max={props.options ? parseInt(props.options.options[1]) : undefined}\n                    onChange={onFilterElementChange}\n                />\n            )\n        }\n        if (hasFlag(type, FilterType.DATE)) {\n            return <DateFilterElement key={options.name} selected={value ? new Date(value * 1000) : new Date()} onChange={onFilterElementChange} />\n        }\n        if (hasFlag(type, FilterType.RANGE)) {\n            return <RangeFilterElement isValid={isValid} key={options.name} defaultValue={props.defaultValue} onChange={onFilterElementChange} />\n        }\n        if (hasFlag(type, FilterType.PLAYER_WITH_RANK)) {\n            return <PlayerWithRankFilterElement key={options.name} defaultValue={props.defaultValue} onChange={onFilterElementChange} />\n        }\n        if (hasFlag(type, FilterType.PLAYER)) {\n            return (\n                <PlayerFilterElement\n                    key={options.name}\n                    defaultValue={props.defaultValue}\n                    isValid={isValid}\n                    returnType=\"uuid\"\n                    onChange={onFilterElementChange}\n                />\n            )\n        }\n        if (hasFlag(type, FilterType.BOOLEAN)) {\n            return <BooleanFilterElement key={options.name} defaultValue={props.defaultValue} onChange={onFilterElementChange} />\n        }\n        if (hasFlag(type, FilterType.EQUAL)) {\n            if (hasFlag(options.type, FilterType.SIMPLE)) {\n                return (\n                    <SimpleEqualFilterElement\n                        key={options.name}\n                        options={options.options}\n                        defaultValue={props.defaultValue}\n                        isValid={isValid}\n                        onChange={onFilterElementChange}\n                    />\n                )\n            } else {\n                return (\n                    <EqualFilterElement\n                        key={options.name}\n                        isValid={isValid}\n                        options={options}\n                        defaultValue={props.defaultValue}\n                        onChange={onFilterElementChange}\n                        showIcon={hasFlag(options.type, FilterType.SHOW_ICON)}\n                    />\n                )\n            }\n        }\n        if (hasFlag(type, FilterType.NUMERICAL)) {\n            return <NumericalFilterElement key={options.name} defaultValue={props.defaultValue} options={options} onChange={onFilterElementChange} />\n        }\n        return <div />\n    }\n\n    return (\n        <div className={styles.genericFilter}>\n            {!props.options ? (\n                <Spinner animation=\"border\" role=\"status\" variant=\"primary\" />\n            ) : (\n                <div style={{ display: 'grid' }}>\n                    <Form.Label style={{ float: 'left', display: 'flex', alignContent: 'center' }}>\n                        <b style={{ marginRight: 5 }}>\n                            {props.options.name[0].toLowerCase() === props.options.name[0]\n                                ? convertTagToName(props.options.name)\n                                : camelCaseToSentenceCase(props.options.name)}\n                        </b>\n                        {props.options.description ? (\n                            <Tooltip\n                                type=\"hover\"\n                                content={<HelpIcon style={{ color: '#007bff', cursor: 'pointer' }} />}\n                                tooltipContent={<span>{props.options.description}</span>}\n                            />\n                        ) : null}\n                    </Form.Label>\n                    {getFilterElement(props.options.type, props.options)}\n                    {!isValid ? (\n                        <div>\n                            <span style={{ color: 'red' }}>{errorText}</span>\n                        </div>\n                    ) : null}\n                </div>\n            )}\n        </div>\n    )\n}\n\nexport default FilterElement\n"], "names": [], "mappings": ";;;;AACA,8CAA8C,GAC9C,2CAA2C,GAC3C;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AApBA;;;;;;;;;;;;;;;;;;;AA6BA,SAAS,cAAc,KAAY;;IAC/B,IAAI,CAAC,OAAO,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;IAChC,IAAI,CAAC,SAAS,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACtC,IAAI,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACN,IAAI,OAAO;gBACP;YACJ;YACA,IAAI,qBAAqB,WAAW,MAAM,YAAY;YACtD,YAAY;YACZ,SAAS;QACb;kCAAG,EAAE;IAEL,SAAS,WAAW,QAAc;QAC9B,IAAI,MAAM,OAAO,IAAI,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EAAE,MAAM,OAAO,CAAC,IAAI,EAAE,6IAAA,CAAA,aAAU,CAAC,IAAI,GAAG;YAC/D,IAAI,CAAC,UAAU;gBACX,OAAO,KAAK,KAAK,CAAC,IAAI,OAAO,OAAO,KAAK;YAC7C;YACA,IAAI,CAAC,MAAM,WAAW;gBAClB,OAAO;YACX;YACA,IAAI,OAAO,KAAK,KAAK,CAAC,KAAK,KAAK,CAAC,YAAY;YAC7C,IAAI,CAAC,MAAM,OAAO;gBACd,OAAO;YACX;YACA,OAAO;QACX,OAAO;YACH,IAAI,CAAC,YAAY,aAAa,GAAG;gBAC7B,OAAO;YACX;YACA,OAAO;QACX;IACJ;IAEA,SAAS,sBAAsB,KAAW;QACtC,IAAI,CAAC,OAAO;YACR,SAAS;YACT,YAAY;QAChB,OAAO;YACH,SAAS;YACT,YAAY,MAAM,QAAQ;QAC9B;IACJ;IAEA,SAAS,YAAY,KAAa;QAC9B,IAAI,CAAC,SAAS,QAAQ;YAClB;QACJ;QAEA,IAAI,YAAY,CAAC;QACjB,SAAS,CAAC,MAAM,OAAO,CAAE,IAAI,CAAC,GAAG,MAAM,QAAQ;QAE/C,MAAM,cAAc,CAAE;IAC1B;IAEA,SAAS,SAAS,KAAW;QACzB,UAAU,WAAW;IACzB;IAEA,SAAS,WAAW,QAAiB;QACjC,IAAI,MAAM,eAAe,EAAE;YACvB,MAAM,eAAe,CAAC;QAC1B;QACA,YAAY;IAChB;IAEA,SAAS,SAAS,KAAW;QACzB,IAAI,CAAC,SAAS,UAAU,GAAG;YACvB,aAAa;YACb,WAAW;YACX,OAAO;QACX;QACA,IAAI,MAAM,OAAO,IAAI,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EAAE,MAAM,OAAO,CAAC,IAAI,EAAE,6IAAA,CAAA,aAAU,CAAC,SAAS,KAAK,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EAAE,MAAM,OAAO,CAAC,IAAI,EAAE,6IAAA,CAAA,aAAU,CAAC,KAAK,GAAG;YACrH,IAAI,mBAAmB,CAAA,GAAA,kIAAA,CAAA,sBAAmB,AAAD,EAAE,MAAM,QAAQ,IAAI,MAAM,OAAO;YAC1E,WAAW,gBAAgB,CAAC,EAAE;YAC9B,IAAI,CAAC,gBAAgB,CAAC,EAAE,EAAE;gBACtB,aAAa,gBAAgB,CAAC,EAAE,IAAI;gBACpC,OAAO;YACX;YACA,OAAO;QACX;QACA,IAAI,MAAM,OAAO,IAAI,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EAAE,MAAM,OAAO,CAAC,IAAI,EAAE,6IAAA,CAAA,aAAU,CAAC,IAAI,GAAG;YAC/D,IAAI,OAAO,IAAI,KAAK,QAAQ;YAC5B,IAAI,OAAO,IAAI,KAAK,MAAM,OAAO,CAAC,OAAO,CAAC,EAAE,GAAG;gBAC3C,aAAa,CAAC,uBAAuB,EAAE,MAAM,OAAO,CAAC,OAAO,CAAC,EAAE,EAAE;gBACjE,WAAW;gBACX,OAAO;YACX;YACA,IAAI,OAAO,IAAI,KAAK,MAAM,OAAO,CAAC,OAAO,CAAC,EAAE,GAAG;gBAC3C,aAAa,CAAC,wBAAwB,EAAE,MAAM,OAAO,CAAC,OAAO,CAAC,EAAE,EAAE;gBAClE,WAAW;gBACX,OAAO;YACX;YACA,WAAW;YACX,OAAO;QACX;QACA,IAAI,MAAM,OAAO,IAAI,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EAAE,MAAM,OAAO,CAAC,IAAI,EAAE,6IAAA,CAAA,aAAU,CAAC,KAAK,GAAG;YAChE,IAAI,MAAM,OAAO,EAAE,QAAQ,WAAW,KAAK,MAAM,OAAO,CAAC,OAAO,CAAC,EAAE,KAAK,kBAAkB,MAAM,OAAO,CAAC,OAAO,CAAC,EAAE,KAAK,gBAAgB;gBACnI,IAAI,SAAS,IAAI,OAAO,qBAAqB,IAAI,CAAC;gBAClD,WAAW;gBACX,IAAI,CAAC,SAAS;oBACV,aAAa;gBACjB;gBACA,OAAO;YACX;QACJ;QACA,IAAI,MAAM,OAAO,IAAI,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EAAE,MAAM,OAAO,CAAC,IAAI,EAAE,6IAAA,CAAA,aAAU,CAAC,SAAS,GAAG;YACpE,IAAI,mBAAmB,CAAA,GAAA,kIAAA,CAAA,uBAAoB,AAAD,EAAE,MAAM,QAAQ,IAAI,MAAM,OAAO;YAC3E,WAAW,gBAAgB,CAAC,EAAE;YAC9B,IAAI,CAAC,gBAAgB,CAAC,EAAE,EAAE;gBACtB,aAAa,gBAAgB,CAAC,EAAE,IAAI;gBACpC,OAAO;YACX;YACA,OAAO;QACX;QACA,WAAW;QACX,OAAO;IACX;IAEA,SAAS,iBAAiB,IAAgB,EAAE,OAAsB;QAC9D,uFAAuF;QACvF,IAAI,QAAQ,IAAI,KAAK,SAAS;YAC1B,qBAAO,6LAAC,uKAAA,CAAA,qBAAkB;gBAAoB,cAAc,MAAM,YAAY;gBAAE,UAAU;eAA1D,QAAQ,IAAI;;;;;QAChD;QACA,IACI,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EAAE,MAAM,6IAAA,CAAA,aAAU,CAAC,SAAS,KAClC,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EAAE,MAAM,6IAAA,CAAA,aAAU,CAAC,KAAK,KAC9B,QAAQ,OAAO,CAAC,MAAM,KAAK,KAC3B,CAAC,MAAM,SAAS,QAAQ,OAAO,CAAC,EAAE,MAClC,CAAC,MAAM,SAAS,QAAQ,OAAO,CAAC,EAAE,MAClC,SAAS,QAAQ,OAAO,CAAC,EAAE,KAAK,IAClC;YACE,qBACI,6LAAC,6KAAA,CAAA,2BAAwB;gBAErB,cAAc,MAAM,YAAY;gBAChC,KAAK,MAAM,OAAO,GAAG,SAAS,MAAM,OAAO,CAAC,OAAO,CAAC,EAAE,IAAI;gBAC1D,KAAK,MAAM,OAAO,GAAG,SAAS,MAAM,OAAO,CAAC,OAAO,CAAC,EAAE,IAAI;gBAC1D,UAAU;eAJL,QAAQ,IAAI;;;;;QAO7B;QACA,IAAI,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EAAE,MAAM,6IAAA,CAAA,aAAU,CAAC,IAAI,GAAG;YAChC,qBAAO,6LAAC,sKAAA,CAAA,oBAAiB;gBAAoB,UAAU,QAAQ,IAAI,KAAK,QAAQ,QAAQ,IAAI;gBAAQ,UAAU;eAA/E,QAAQ,IAAI;;;;;QAC/C;QACA,IAAI,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EAAE,MAAM,6IAAA,CAAA,aAAU,CAAC,KAAK,GAAG;YACjC,qBAAO,6LAAC,uKAAA,CAAA,qBAAkB;gBAAC,SAAS;gBAA4B,cAAc,MAAM,YAAY;gBAAE,UAAU;eAA1D,QAAQ,IAAI;;;;;QAClE;QACA,IAAI,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EAAE,MAAM,6IAAA,CAAA,aAAU,CAAC,gBAAgB,GAAG;YAC5C,qBAAO,6LAAC,gLAAA,CAAA,8BAA2B;gBAAoB,cAAc,MAAM,YAAY;gBAAE,UAAU;eAA1D,QAAQ,IAAI;;;;;QACzD;QACA,IAAI,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EAAE,MAAM,6IAAA,CAAA,aAAU,CAAC,MAAM,GAAG;YAClC,qBACI,6LAAC,wKAAA,CAAA,sBAAmB;gBAEhB,cAAc,MAAM,YAAY;gBAChC,SAAS;gBACT,YAAW;gBACX,UAAU;eAJL,QAAQ,IAAI;;;;;QAO7B;QACA,IAAI,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EAAE,MAAM,6IAAA,CAAA,aAAU,CAAC,OAAO,GAAG;YACnC,qBAAO,6LAAC,yKAAA,CAAA,uBAAoB;gBAAoB,cAAc,MAAM,YAAY;gBAAE,UAAU;eAA1D,QAAQ,IAAI;;;;;QAClD;QACA,IAAI,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EAAE,MAAM,6IAAA,CAAA,aAAU,CAAC,KAAK,GAAG;YACjC,IAAI,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EAAE,QAAQ,IAAI,EAAE,6IAAA,CAAA,aAAU,CAAC,MAAM,GAAG;gBAC1C,qBACI,6LAAC,6KAAA,CAAA,2BAAwB;oBAErB,SAAS,QAAQ,OAAO;oBACxB,cAAc,MAAM,YAAY;oBAChC,SAAS;oBACT,UAAU;mBAJL,QAAQ,IAAI;;;;;YAO7B,OAAO;gBACH,qBACI,6LAAC,uKAAA,CAAA,qBAAkB;oBAEf,SAAS;oBACT,SAAS;oBACT,cAAc,MAAM,YAAY;oBAChC,UAAU;oBACV,UAAU,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EAAE,QAAQ,IAAI,EAAE,6IAAA,CAAA,aAAU,CAAC,SAAS;mBAL/C,QAAQ,IAAI;;;;;YAQ7B;QACJ;QACA,IAAI,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EAAE,MAAM,6IAAA,CAAA,aAAU,CAAC,SAAS,GAAG;YACrC,qBAAO,6LAAC,2KAAA,CAAA,yBAAsB;gBAAoB,cAAc,MAAM,YAAY;gBAAE,SAAS;gBAAS,UAAU;eAA5E,QAAQ,IAAI;;;;;QACpD;QACA,qBAAO,6LAAC;;;;;IACZ;IAEA,qBACI,6LAAC;QAAI,WAAW,2JAAA,CAAA,UAAM,CAAC,aAAa;kBAC/B,CAAC,MAAM,OAAO,iBACX,6LAAC,6LAAA,CAAA,UAAO;YAAC,WAAU;YAAS,MAAK;YAAS,SAAQ;;;;;iCAElD,6LAAC;YAAI,OAAO;gBAAE,SAAS;YAAO;;8BAC1B,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;oBAAC,OAAO;wBAAE,OAAO;wBAAQ,SAAS;wBAAQ,cAAc;oBAAS;;sCACxE,6LAAC;4BAAE,OAAO;gCAAE,aAAa;4BAAE;sCACtB,MAAM,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,WAAW,OAAO,MAAM,OAAO,CAAC,IAAI,CAAC,EAAE,GACxD,CAAA,GAAA,sHAAA,CAAA,mBAAgB,AAAD,EAAE,MAAM,OAAO,CAAC,IAAI,IACnC,CAAA,GAAA,sHAAA,CAAA,0BAAuB,AAAD,EAAE,MAAM,OAAO,CAAC,IAAI;;;;;;wBAEnD,MAAM,OAAO,CAAC,WAAW,iBACtB,6LAAC,oIAAA,CAAA,UAAO;4BACJ,MAAK;4BACL,uBAAS,6LAAC,4JAAA,CAAA,UAAQ;gCAAC,OAAO;oCAAE,OAAO;oCAAW,QAAQ;gCAAU;;;;;;4BAChE,8BAAgB,6LAAC;0CAAM,MAAM,OAAO,CAAC,WAAW;;;;;;;;;;mCAEpD;;;;;;;gBAEP,iBAAiB,MAAM,OAAO,CAAC,IAAI,EAAE,MAAM,OAAO;gBAClD,CAAC,wBACE,6LAAC;8BACG,cAAA,6LAAC;wBAAK,OAAO;4BAAE,OAAO;wBAAM;kCAAI;;;;;;;;;;2BAEpC;;;;;;;;;;;;AAKxB;GAlOS;KAAA;uCAoOM", "debugId": null}}, {"offset": {"line": 4445, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/components/ItemFilter/ItemFilter.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"addFilterSelect\": \"ItemFilter-module__EG8HRq__addFilterSelect\",\n  \"filterContainer\": \"ItemFilter-module__EG8HRq__filterContainer\",\n  \"filterElement\": \"ItemFilter-module__EG8HRq__filterElement\",\n  \"itemFilter\": \"ItemFilter-module__EG8HRq__itemFilter\",\n  \"removeFilter\": \"ItemFilter-module__EG8HRq__removeFilter\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 4458, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/ItemFilter/ModAdvert.tsx"], "sourcesContent": ["'use client'\nimport Link from 'next/link'\nimport { Card } from 'react-bootstrap'\nimport { CancelOutlined as CancelIcon } from '@mui/icons-material'\nimport { useEffect, useState } from 'react'\n\nexport default function ModAdvert() {\n    let [show, setShow] = useState(false)\n\n    useEffect(() => {\n        setShow(localStorage.getItem('hideModAdvertInFilter') !== 'true')\n    }, [])\n\n    function onClose() {\n        setShow(false)\n        localStorage.setItem('hideModAdvertInFilter', 'true')\n    }\n\n    if (!show) {\n        return null\n    }\n\n    return (\n        <>\n            <Card style={{ marginBottom: '15px' }}>\n                <Card.Header style={{ borderRadius: '5px' }}>\n                    <Card.Text>\n                        Checking item prices? We provide a mod showing market data like volume, lowest bin and median prices in game. <br />\n                        <Link\n                            href=\"/mod\"\n                            style={{\n                                color: '#007bff',\n                                cursor: 'pointer'\n                            }}\n                        >\n                            Check out the mod\n                        </Link>\n                        <div style={{ position: 'absolute', top: 1, right: 5, color: 'red', cursor: 'pointer' }} onClick={onClose}>\n                            <CancelIcon />\n                        </div>\n                    </Card.Text>\n                </Card.Header>\n            </Card>\n            <hr />\n        </>\n    )\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;;;AAJA;;;;;AAMe,SAAS;;IACpB,IAAI,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACN,QAAQ,aAAa,OAAO,CAAC,6BAA6B;QAC9D;8BAAG,EAAE;IAEL,SAAS;QACL,QAAQ;QACR,aAAa,OAAO,CAAC,yBAAyB;IAClD;IAEA,IAAI,CAAC,MAAM;QACP,OAAO;IACX;IAEA,qBACI;;0BACI,6LAAC,uLAAA,CAAA,OAAI;gBAAC,OAAO;oBAAE,cAAc;gBAAO;0BAChC,cAAA,6LAAC,uLAAA,CAAA,OAAI,CAAC,MAAM;oBAAC,OAAO;wBAAE,cAAc;oBAAM;8BACtC,cAAA,6LAAC,uLAAA,CAAA,OAAI,CAAC,IAAI;;4BAAC;0CACuG,6LAAC;;;;;0CAC/G,6LAAC,+JAAA,CAAA,UAAI;gCACD,MAAK;gCACL,OAAO;oCACH,OAAO;oCACP,QAAQ;gCACZ;0CACH;;;;;;0CAGD,6LAAC;gCAAI,OAAO;oCAAE,UAAU;oCAAY,KAAK;oCAAG,OAAO;oCAAG,OAAO;oCAAO,QAAQ;gCAAU;gCAAG,SAAS;0CAC9F,cAAA,6LAAC,sKAAA,CAAA,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;0BAK3B,6LAAC;;;;;;;AAGb;GAxCwB;KAAA", "debugId": null}}, {"offset": {"line": 4574, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/ItemFilter/ItemFilter.tsx"], "sourcesContent": ["'use client'\n/* eslint-disable react-hooks/exhaustive-deps */\n/* eslint-disable jsx-a11y/anchor-is-valid */\nimport { useEffect, useMemo, useRef, useState } from 'react'\nimport { <PERSON>ge, <PERSON>ton, Card, Form, Modal, Spinner } from 'react-bootstrap'\nimport { getItemFilterFromUrl, setFilterIntoUrlParams } from '../../utils/Parser/URLParser'\nimport FilterElement from '../FilterElement/FilterElement'\nimport DeleteIcon from '@mui/icons-material/Delete'\nimport HelpIcon from '@mui/icons-material/Help'\nimport AddIcon from '@mui/icons-material/AddCircleOutline'\nimport { camelCaseToSentenceCase, convertTagToName } from '../../utils/Formatter'\nimport { FilterType, hasFlag } from '../FilterElement/FilterType'\nimport { Typeahead, TypeaheadRef } from 'react-bootstrap-typeahead'\nimport styles from './ItemFilter.module.css'\nimport { btoaUnicode } from '../../utils/Base64Utils'\nimport { ITEM_FILTER_USE_COUNT, LAST_USED_FILTER, getSettingsObject, setSetting } from '../../utils/SettingsUtils'\nimport ModAdvert from './ModAdvert'\nimport { isClientSideRendering } from '../../utils/SSRUtils'\nimport { usePathname, useRouter } from 'next/navigation'\nimport option from '../PriceGraph/AuctionHousePriceGraph/PriceGraphConfig'\n\ninterface Props {\n    onFilterChange?(filter?: ItemFilter): void\n    filters?: FilterOptions[]\n    forceOpen?: boolean\n    ignoreURL?: boolean\n    autoSelect?: boolean\n    defaultFilter?: ItemFilter\n    disableLastUsedFilter?: boolean\n    showModAdvert?: boolean\n    showFilterInfoElement?: boolean\n    onIsValidChange?(newIsValid: boolean)\n    emptyLabel?: string\n}\n\nconst groupedFilter = [\n    ['Enchantment', 'EnchantLvl'],\n    ['SecondEnchantment', 'SecondEnchantLvl']\n]\n\nfunction ItemFilter(props: Props) {\n    let router = useRouter()\n    let pathname = usePathname()\n    let [itemFilter, _setItemFilter] = useState<ItemFilter>({})\n    let [expanded, setExpanded] = useState(props.forceOpen || false)\n    let [selectedFilters, setSelectedFilters] = useState<string[]>([])\n    let [showInfoDialog, setShowInfoDialog] = useState(false)\n    let [invalidFilters, _setInvalidFilters] = useState(new Set<string>())\n\n    let typeaheadRef = useRef<TypeaheadRef>(null)\n\n    useEffect(() => {\n        if (props.filters && props.filters.length > 0) {\n            initFilter()\n        }\n    }, [JSON.stringify(props.filters)])\n\n    let sortedFilterOptions = useMemo(() => {\n        if (!props.filters) {\n            return undefined\n        }\n        let sorting = getSettingsObject<{ [key: string]: number }>(ITEM_FILTER_USE_COUNT, {})\n\n        let sorted = props.filters.sort((a, b) => {\n            let aCount = sorting[a.name] || 0\n            let bCount = sorting[b.name] || 0\n            return bCount - aCount\n        })\n\n        return sorted\n    }, [props.filters])\n\n    function initFilter() {\n        if (props.ignoreURL && !props.defaultFilter) {\n            return\n        }\n        itemFilter = props.defaultFilter\n            ? JSON.parse(JSON.stringify(props.defaultFilter))\n            : getPrefillFilter(props.filters, props.ignoreURL, props.disableLastUsedFilter)\n        if (Object.keys(itemFilter).length > 0) {\n            setExpanded(true)\n            Object.keys(itemFilter).forEach(name => {\n                if (!props.filters?.find(f => f.name === name)) {\n                    delete itemFilter[name]\n                    return\n                }\n                enableFilter(name)\n                getGroupedFilter(name).forEach(filter => enableFilter(filter))\n            })\n            setItemFilter(itemFilter)\n            onFilterChange(itemFilter)\n        }\n    }\n\n    function getGroupedFilter(filterName: string): string[] {\n        let result: string[] = []\n\n        let index = groupedFilter.findIndex(group => {\n            let groupIndex = group.findIndex(element => {\n                return filterName === element\n            })\n            return groupIndex !== -1\n        })\n\n        if (index !== -1) {\n            let groupToEnable = groupedFilter[index]\n            groupToEnable.forEach(filter => {\n                if (filter !== filterName) {\n                    result.push(filter)\n                }\n            })\n        }\n\n        return result\n    }\n\n    let enableFilter = (filterName: string, filterValue?: string) => {\n        if (selectedFilters.some(n => n === filterName)) {\n            return\n        }\n\n        selectedFilters = [...selectedFilters, filterName]\n        setSelectedFilters(selectedFilters)\n\n        if (itemFilter[filterName] === undefined && !filterValue) {\n            itemFilter[filterName] = getDefaultValue(filterName)\n        }\n        if (itemFilter[filterName] === undefined && filterValue) {\n            itemFilter[filterName] = filterValue\n        }\n\n        updateURLQuery(itemFilter)\n        setItemFilter(itemFilter)\n    }\n\n    let addFilter = ([selectedFilter]: FilterOptions[]) => {\n        if (!selectedFilter) {\n            return\n        }\n\n        let sortingByUsedMost = getSettingsObject<{ [key: string]: number }>(ITEM_FILTER_USE_COUNT, {})\n        if (sortingByUsedMost[selectedFilter.name] !== undefined) {\n            sortingByUsedMost[selectedFilter.name] = sortingByUsedMost[selectedFilter.name] + 1\n        } else {\n            sortingByUsedMost[selectedFilter.name] = 1\n        }\n        setSetting(ITEM_FILTER_USE_COUNT, JSON.stringify(sortingByUsedMost))\n\n        let currentText = typeaheadRef?.current?.state.text\n        let match = currentText?.match(/\\((\\w+)\\)$/)\n        let prefillValue: string | undefined = undefined\n        if (match) {\n            prefillValue = selectedFilter.options.find(option => option.toLowerCase() === match![1].toLowerCase())\n        }\n\n        typeaheadRef?.current?.clear()\n        typeaheadRef?.current?.blur()\n\n        enableFilter(selectedFilter.name, prefillValue)\n        getGroupedFilter(selectedFilter.name).forEach(filter => enableFilter(filter))\n    }\n\n    let onFilterClose = () => {\n        setSelectedFilters([])\n        setExpanded(false)\n        setItemFilter({})\n        onFilterChange({})\n    }\n\n    function onFilterRemoveClick(filterName: string) {\n        if (invalidFilters.has(filterName)) {\n            let newInvalidFilters = new Set(invalidFilters)\n            newInvalidFilters.delete(filterName)\n            setInvalidFilters(newInvalidFilters)\n        }\n        removeFilter(filterName)\n        getGroupedFilter(filterName).forEach(filter => removeFilter(filter))\n    }\n\n    function removeFilter(filterName: string) {\n        if (itemFilter) {\n            delete itemFilter[filterName]\n            setItemFilter(itemFilter)\n            updateURLQuery(itemFilter)\n            onFilterChange(itemFilter)\n        }\n        let newSelectedFilters = selectedFilters.filter(f => f !== filterName)\n        selectedFilters = newSelectedFilters\n        setSelectedFilters(newSelectedFilters)\n    }\n\n    let onEnable = () => {\n        setExpanded(true)\n        if (!itemFilter) {\n            itemFilter = {}\n            setItemFilter(itemFilter)\n        }\n        updateURLQuery(itemFilter)\n    }\n\n    let setItemFilter = (itemFilter: ItemFilter) => {\n        _setItemFilter({ ...itemFilter })\n        updateURLQuery(itemFilter)\n    }\n\n    let updateURLQuery = (filter?: ItemFilter) => {\n        if (props.ignoreURL) {\n            return\n        }\n\n        setFilterIntoUrlParams(router, pathname, filter || {})\n    }\n\n    function onFilterChange(filter: ItemFilter) {\n        let filterCopy = { ...filter }\n\n        let valid = true\n        Object.keys(filterCopy).forEach(key => {\n            if (!checkForValidGroupedFilter(key, filterCopy)) {\n                valid = false\n                return\n            }\n        })\n\n        if (!valid) {\n            return\n        }\n\n        setItemFilter(filterCopy!)\n        if (!props.disableLastUsedFilter) {\n            localStorage.setItem(LAST_USED_FILTER, JSON.stringify(filterCopy))\n        }\n        if (props.onFilterChange) {\n            Object.keys(filterCopy).forEach(key => {\n                if (filterCopy[key] === '' || filterCopy[key] === null) {\n                    delete filterCopy[key]\n                }\n            })\n            props.onFilterChange(filterCopy)\n        }\n    }\n\n    function checkForValidGroupedFilter(filterName: string, filter: ItemFilter): boolean {\n        let groupFilters = getGroupedFilter(filterName)\n\n        let invalid = false\n        groupFilters.forEach(name => {\n            if (filter[name] === undefined || filter[name] === null) {\n                invalid = true\n            }\n        })\n\n        return !invalid\n    }\n\n    function onFilterElementChange(filter?: ItemFilter) {\n        let newFilter = itemFilter\n        var keys = Object.keys(filter as object)\n        if (keys.length > 0) {\n            var key = keys[0]\n            newFilter![key] = filter![key]\n        }\n\n        if ((newFilter.EnchantLvl || newFilter.Enchantment) && !(newFilter.EnchantLvl && newFilter.Enchantment)) {\n            return\n        }\n\n        onFilterChange(newFilter)\n    }\n\n    function onIsValidChange(filterName: string, newIsValid: boolean) {\n        let newInvalidFilters = new Set(invalidFilters)\n        if (newIsValid) {\n            newInvalidFilters.delete(filterName)\n        } else {\n            newInvalidFilters.add(filterName)\n        }\n        setInvalidFilters(newInvalidFilters)\n    }\n\n    function setInvalidFilters(newInvalidFilters: Set<string>) {\n        if (props.onIsValidChange) {\n            props.onIsValidChange(newInvalidFilters.size === 0)\n        }\n        _setInvalidFilters(newInvalidFilters)\n    }\n\n    function getDefaultValue(filterName: string): string {\n        let options = props.filters?.find(f => f.name === filterName)\n        let defaultValue: any = ''\n        if (options && options.options[0] !== null && options.options[0] !== undefined) {\n            // dont set the first option for search-selects\n            if ((hasFlag(options.type, FilterType.EQUAL) && hasFlag(options.type, FilterType.SIMPLE)) || hasFlag(options.type, FilterType.BOOLEAN)) {\n                defaultValue = options.options[0]\n                if (options.name === 'Everything') {\n                    defaultValue = 'true'\n                }\n            }\n        }\n        if (filterName === 'Color') {\n            defaultValue = '#000000'\n        }\n        return defaultValue\n    }\n\n    let filterList = selectedFilters.map(filterName => {\n        let options = props.filters?.find(f => f.name === filterName)\n        if (!options) {\n            return null\n        }\n\n        let defaultValue = getDefaultValue(filterName)\n        if (itemFilter[filterName]) {\n            defaultValue = itemFilter[filterName]\n        }\n        return (\n            <div key={filterName} className={styles.filterElement}>\n                <FilterElement\n                    onFilterChange={onFilterElementChange}\n                    options={options}\n                    defaultValue={defaultValue}\n                    onIsValidChange={newValue => onIsValidChange(filterName, newValue)}\n                />\n                <div className={styles.removeFilter} onClick={() => onFilterRemoveClick(filterName)}>\n                    <DeleteIcon color=\"error\" />\n                </div>\n            </div>\n        )\n    })\n\n    let infoIconElement = (\n        <div>\n            <span\n                style={{ cursor: 'pointer', position: 'absolute', top: '10px', right: '10px', color: '#007bff' }}\n                onClick={() => {\n                    setShowInfoDialog(true)\n                }}\n            >\n                <HelpIcon />\n            </span>\n            {showInfoDialog ? (\n                <Modal\n                    show={showInfoDialog}\n                    onHide={() => {\n                        setShowInfoDialog(false)\n                    }}\n                >\n                    <Modal.Header closeButton>\n                        <h4>Item Filter Information</h4>\n                    </Modal.Header>\n                    <Modal.Body>\n                        <p>\n                            You can add various filters depending on the item type. The graph and recent/active auctions will be updated to only include items\n                            with the selected properties.\n                        </p>\n                        <hr />\n                        <h4>\n                            <Badge bg=\"danger\">Caution</Badge>\n                        </h4>\n                        <p>\n                            Some filter requests take quite some time to process. That's because we have to search through millions of auctions that potentially\n                            match your filter. This can lead to no auctions being displayed at all because your browser thinks that our server is unavailable.\n                            If that happens please let us know. We may implement scheduled filters where you will get an email or push notification when we\n                            computed a result for your filter.\n                        </p>\n                        <p>\n                            If you are missing a filter please ask for it on our{' '}\n                            <a target=\"_blank\" rel=\"noreferrer\" href=\"https://discord.gg/wvKXfTgCfb\">\n                                Discord\n                            </a>\n                            .\n                        </p>\n                    </Modal.Body>\n                </Modal>\n            ) : (\n                ''\n            )}\n        </div>\n    )\n\n    return (\n        <div className={styles.itemFilter}>\n            {!expanded ? (\n                <div>\n                    <a href=\"#\" onClick={() => onEnable()}>\n                        <AddIcon />\n                        <span> Add Filter</span>\n                    </a>\n                </div>\n            ) : (\n                <Card>\n                    <Card.Title style={{ margin: '10px' }}>\n                        Filter\n                        {props.showFilterInfoElement ? infoIconElement : null}\n                    </Card.Title>\n                    <Card.Body>\n                        {props.showModAdvert ? <ModAdvert /> : null}\n                        <Form style={{ marginBottom: '5px' }}>\n                            <Form.Group>\n                                {props?.filters && props.filters?.length > 0 ? (\n                                    <Typeahead\n                                        id=\"add-filter-typeahead\"\n                                        autoFocus={\n                                            props.autoSelect === undefined\n                                                ? Object.keys(getPrefillFilter(props.filters, props.ignoreURL, props.disableLastUsedFilter)).length === 0\n                                                : props.autoSelect\n                                        }\n                                        defaultOpen={\n                                            props.autoSelect === undefined\n                                                ? Object.keys(getPrefillFilter(props.filters, props.ignoreURL, props.disableLastUsedFilter)).length === 0\n                                                : props.autoSelect\n                                        }\n                                        ref={typeaheadRef}\n                                        placeholder=\"Add filter\"\n                                        className={styles.addFilterSelect}\n                                        onChange={addFilter}\n                                        options={sortedFilterOptions || []}\n                                        labelKey={(options: FilterOptions) => {\n                                            let text = typeaheadRef.current?.state.text\n                                            let optionsString = ''\n                                            let name = options.name\n                                            let searchString = name?.replace(/\\s/g, '').toLowerCase()\n                                            let description = options.description ? options.description.replace(/\\s/g, '').toLowerCase() : ''\n\n                                            // If the restult was found because of the options, show the options at the end of the string\n                                            if (text && !searchString?.includes(text) && !description.includes(text)) {\n                                                let searchString = text.replace(/\\s/g, '').toLowerCase()\n                                                let matchingOptions = options.options.filter(option => option.toLowerCase().includes(searchString))\n\n                                                if (matchingOptions.length > 0) {\n                                                    optionsString = matchingOptions\n                                                        .map(option => {\n                                                            if (option.toLowerCase() === option) {\n                                                                return convertTagToName(option).trim()\n                                                            }\n                                                            if (option.toUpperCase() === option) {\n                                                                return convertTagToName(option).trim()\n                                                            }\n                                                            return camelCaseToSentenceCase(option).trim()\n                                                        })\n                                                        .join(', ')\n                                                }\n                                            }\n\n                                            if (name[0].toLowerCase() === name[0]) {\n                                                return `${convertTagToName(name)} ${optionsString ? `(${optionsString})` : ''}`\n                                            }\n\n                                            return `${camelCaseToSentenceCase(name)} ${optionsString ? `(${optionsString})` : ''}`\n                                        }}\n                                        filterBy={(options: FilterOptions, props) => {\n                                            let searchString = props.text.replace(/\\s/g, '').toLowerCase()\n                                            let name = (props.labelKey as Function)(options).toLowerCase()\n                                            let initials = name.match(/\\b\\w/g).join('')\n                                            let description = options.description ? options.description.replace(/\\s/g, '').toLowerCase() : ''\n\n                                            let matchingOptions = options.options.filter(option => option.toLowerCase().includes(searchString))\n\n                                            return (\n                                                name.replace(/\\s/g, '').includes(searchString) ||\n                                                initials.includes(searchString) ||\n                                                description.includes(searchString) ||\n                                                matchingOptions.length > 0\n                                            )\n                                        }}\n                                        emptyLabel={props.emptyLabel || 'No matches found. Filters which would not show any results are hidden'}\n                                    ></Typeahead>\n                                ) : (\n                                    <Spinner animation=\"border\" role=\"status\" variant=\"primary\" />\n                                )}\n                            </Form.Group>\n                            <div className={styles.filterContainer}>{filterList}</div>\n                        </Form>\n                        {props.forceOpen ? null : (\n                            <div style={{ display: 'flex', justifyContent: 'end', marginTop: '10px' }}>\n                                <Button variant=\"danger\" onClick={() => onFilterClose()}>\n                                    Close\n                                </Button>\n                            </div>\n                        )}\n                    </Card.Body>\n                </Card>\n            )}\n        </div>\n    )\n}\nexport default ItemFilter\n\nexport function getPrefillFilter(filterOptions: FilterOptions[] = [], ignoreURL: boolean = false, disableLastUsedFilter: boolean = false) {\n    let itemFilter = !ignoreURL ? getItemFilterFromUrl() : {}\n    if (Object.keys(itemFilter).length === 0 && !disableLastUsedFilter) {\n        itemFilter = getFilterFromLocalStorage(filterOptions) || {}\n    }\n    return itemFilter\n}\n\n/**\n * Gets the last used filter from the local storage and removes all properties not available in the allowed filters\n * @returns the filter or null if no last used filter is found\n */\nfunction getFilterFromLocalStorage(filterOptions: FilterOptions[] = []): ItemFilter | null {\n    let localStorageLastFilter = localStorage.getItem(LAST_USED_FILTER)\n    if (localStorageLastFilter === null) {\n        return null\n    }\n    let filter: ItemFilter = JSON.parse(localStorageLastFilter)\n    Object.keys(filter).forEach(key => {\n        if (filterOptions.findIndex(f => f.name === key) === -1) {\n            delete filter[key]\n        }\n    })\n    return filter\n}\n"], "names": [], "mappings": ";;;;;AACA,8CAA8C,GAC9C,2CAA2C,GAC3C;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAEA;AACA;AAEA;;;AAlBA;;;;;;;;;;;;;;;AAmCA,MAAM,gBAAgB;IAClB;QAAC;QAAe;KAAa;IAC7B;QAAC;QAAqB;KAAmB;CAC5C;AAED,SAAS,WAAW,KAAY;;IAC5B,IAAI,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACrB,IAAI,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IACzB,IAAI,CAAC,YAAY,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,CAAC;IACzD,IAAI,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,MAAM,SAAS,IAAI;IAC1D,IAAI,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACjE,IAAI,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,IAAI,CAAC,gBAAgB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,IAAI;IAExD,IAAI,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAgB;IAExC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACN,IAAI,MAAM,OAAO,IAAI,MAAM,OAAO,CAAC,MAAM,GAAG,GAAG;gBAC3C;YACJ;QACJ;+BAAG;QAAC,KAAK,SAAS,CAAC,MAAM,OAAO;KAAE;IAElC,IAAI,sBAAsB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;mDAAE;YAC9B,IAAI,CAAC,MAAM,OAAO,EAAE;gBAChB,OAAO;YACX;YACA,IAAI,UAAU,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD,EAA6B,0HAAA,CAAA,wBAAqB,EAAE,CAAC;YAEnF,IAAI,SAAS,MAAM,OAAO,CAAC,IAAI;kEAAC,CAAC,GAAG;oBAChC,IAAI,SAAS,OAAO,CAAC,EAAE,IAAI,CAAC,IAAI;oBAChC,IAAI,SAAS,OAAO,CAAC,EAAE,IAAI,CAAC,IAAI;oBAChC,OAAO,SAAS;gBACpB;;YAEA,OAAO;QACX;kDAAG;QAAC,MAAM,OAAO;KAAC;IAElB,SAAS;QACL,IAAI,MAAM,SAAS,IAAI,CAAC,MAAM,aAAa,EAAE;YACzC;QACJ;QACA,aAAa,MAAM,aAAa,GAC1B,KAAK,KAAK,CAAC,KAAK,SAAS,CAAC,MAAM,aAAa,KAC7C,iBAAiB,MAAM,OAAO,EAAE,MAAM,SAAS,EAAE,MAAM,qBAAqB;QAClF,IAAI,OAAO,IAAI,CAAC,YAAY,MAAM,GAAG,GAAG;YACpC,YAAY;YACZ,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,CAAA;gBAC5B,IAAI,CAAC,MAAM,OAAO,EAAE,KAAK,CAAA,IAAK,EAAE,IAAI,KAAK,OAAO;oBAC5C,OAAO,UAAU,CAAC,KAAK;oBACvB;gBACJ;gBACA,aAAa;gBACb,iBAAiB,MAAM,OAAO,CAAC,CAAA,SAAU,aAAa;YAC1D;YACA,cAAc;YACd,eAAe;QACnB;IACJ;IAEA,SAAS,iBAAiB,UAAkB;QACxC,IAAI,SAAmB,EAAE;QAEzB,IAAI,QAAQ,cAAc,SAAS,CAAC,CAAA;YAChC,IAAI,aAAa,MAAM,SAAS,CAAC,CAAA;gBAC7B,OAAO,eAAe;YAC1B;YACA,OAAO,eAAe,CAAC;QAC3B;QAEA,IAAI,UAAU,CAAC,GAAG;YACd,IAAI,gBAAgB,aAAa,CAAC,MAAM;YACxC,cAAc,OAAO,CAAC,CAAA;gBAClB,IAAI,WAAW,YAAY;oBACvB,OAAO,IAAI,CAAC;gBAChB;YACJ;QACJ;QAEA,OAAO;IACX;IAEA,IAAI,eAAe,CAAC,YAAoB;QACpC,IAAI,gBAAgB,IAAI,CAAC,CAAA,IAAK,MAAM,aAAa;YAC7C;QACJ;QAEA,kBAAkB;eAAI;YAAiB;SAAW;QAClD,mBAAmB;QAEnB,IAAI,UAAU,CAAC,WAAW,KAAK,aAAa,CAAC,aAAa;YACtD,UAAU,CAAC,WAAW,GAAG,gBAAgB;QAC7C;QACA,IAAI,UAAU,CAAC,WAAW,KAAK,aAAa,aAAa;YACrD,UAAU,CAAC,WAAW,GAAG;QAC7B;QAEA,eAAe;QACf,cAAc;IAClB;IAEA,IAAI,YAAY,CAAC,CAAC,eAAgC;QAC9C,IAAI,CAAC,gBAAgB;YACjB;QACJ;QAEA,IAAI,oBAAoB,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD,EAA6B,0HAAA,CAAA,wBAAqB,EAAE,CAAC;QAC7F,IAAI,iBAAiB,CAAC,eAAe,IAAI,CAAC,KAAK,WAAW;YACtD,iBAAiB,CAAC,eAAe,IAAI,CAAC,GAAG,iBAAiB,CAAC,eAAe,IAAI,CAAC,GAAG;QACtF,OAAO;YACH,iBAAiB,CAAC,eAAe,IAAI,CAAC,GAAG;QAC7C;QACA,CAAA,GAAA,0HAAA,CAAA,aAAU,AAAD,EAAE,0HAAA,CAAA,wBAAqB,EAAE,KAAK,SAAS,CAAC;QAEjD,IAAI,cAAc,cAAc,SAAS,MAAM;QAC/C,IAAI,QAAQ,aAAa,MAAM;QAC/B,IAAI,eAAmC;QACvC,IAAI,OAAO;YACP,eAAe,eAAe,OAAO,CAAC,IAAI,CAAC,CAAA,SAAU,OAAO,WAAW,OAAO,KAAM,CAAC,EAAE,CAAC,WAAW;QACvG;QAEA,cAAc,SAAS;QACvB,cAAc,SAAS;QAEvB,aAAa,eAAe,IAAI,EAAE;QAClC,iBAAiB,eAAe,IAAI,EAAE,OAAO,CAAC,CAAA,SAAU,aAAa;IACzE;IAEA,IAAI,gBAAgB;QAChB,mBAAmB,EAAE;QACrB,YAAY;QACZ,cAAc,CAAC;QACf,eAAe,CAAC;IACpB;IAEA,SAAS,oBAAoB,UAAkB;QAC3C,IAAI,eAAe,GAAG,CAAC,aAAa;YAChC,IAAI,oBAAoB,IAAI,IAAI;YAChC,kBAAkB,MAAM,CAAC;YACzB,kBAAkB;QACtB;QACA,aAAa;QACb,iBAAiB,YAAY,OAAO,CAAC,CAAA,SAAU,aAAa;IAChE;IAEA,SAAS,aAAa,UAAkB;QACpC,IAAI,YAAY;YACZ,OAAO,UAAU,CAAC,WAAW;YAC7B,cAAc;YACd,eAAe;YACf,eAAe;QACnB;QACA,IAAI,qBAAqB,gBAAgB,MAAM,CAAC,CAAA,IAAK,MAAM;QAC3D,kBAAkB;QAClB,mBAAmB;IACvB;IAEA,IAAI,WAAW;QACX,YAAY;QACZ,IAAI,CAAC,YAAY;YACb,aAAa,CAAC;YACd,cAAc;QAClB;QACA,eAAe;IACnB;IAEA,IAAI,gBAAgB,CAAC;QACjB,eAAe;YAAE,GAAG,UAAU;QAAC;QAC/B,eAAe;IACnB;IAEA,IAAI,iBAAiB,CAAC;QAClB,IAAI,MAAM,SAAS,EAAE;YACjB;QACJ;QAEA,CAAA,GAAA,gIAAA,CAAA,yBAAsB,AAAD,EAAE,QAAQ,UAAU,UAAU,CAAC;IACxD;IAEA,SAAS,eAAe,MAAkB;QACtC,IAAI,aAAa;YAAE,GAAG,MAAM;QAAC;QAE7B,IAAI,QAAQ;QACZ,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,CAAA;YAC5B,IAAI,CAAC,2BAA2B,KAAK,aAAa;gBAC9C,QAAQ;gBACR;YACJ;QACJ;QAEA,IAAI,CAAC,OAAO;YACR;QACJ;QAEA,cAAc;QACd,IAAI,CAAC,MAAM,qBAAqB,EAAE;YAC9B,aAAa,OAAO,CAAC,0HAAA,CAAA,mBAAgB,EAAE,KAAK,SAAS,CAAC;QAC1D;QACA,IAAI,MAAM,cAAc,EAAE;YACtB,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,CAAA;gBAC5B,IAAI,UAAU,CAAC,IAAI,KAAK,MAAM,UAAU,CAAC,IAAI,KAAK,MAAM;oBACpD,OAAO,UAAU,CAAC,IAAI;gBAC1B;YACJ;YACA,MAAM,cAAc,CAAC;QACzB;IACJ;IAEA,SAAS,2BAA2B,UAAkB,EAAE,MAAkB;QACtE,IAAI,eAAe,iBAAiB;QAEpC,IAAI,UAAU;QACd,aAAa,OAAO,CAAC,CAAA;YACjB,IAAI,MAAM,CAAC,KAAK,KAAK,aAAa,MAAM,CAAC,KAAK,KAAK,MAAM;gBACrD,UAAU;YACd;QACJ;QAEA,OAAO,CAAC;IACZ;IAEA,SAAS,sBAAsB,MAAmB;QAC9C,IAAI,YAAY;QAChB,IAAI,OAAO,OAAO,IAAI,CAAC;QACvB,IAAI,KAAK,MAAM,GAAG,GAAG;YACjB,IAAI,MAAM,IAAI,CAAC,EAAE;YACjB,SAAU,CAAC,IAAI,GAAG,MAAO,CAAC,IAAI;QAClC;QAEA,IAAI,CAAC,UAAU,UAAU,IAAI,UAAU,WAAW,KAAK,CAAC,CAAC,UAAU,UAAU,IAAI,UAAU,WAAW,GAAG;YACrG;QACJ;QAEA,eAAe;IACnB;IAEA,SAAS,gBAAgB,UAAkB,EAAE,UAAmB;QAC5D,IAAI,oBAAoB,IAAI,IAAI;QAChC,IAAI,YAAY;YACZ,kBAAkB,MAAM,CAAC;QAC7B,OAAO;YACH,kBAAkB,GAAG,CAAC;QAC1B;QACA,kBAAkB;IACtB;IAEA,SAAS,kBAAkB,iBAA8B;QACrD,IAAI,MAAM,eAAe,EAAE;YACvB,MAAM,eAAe,CAAC,kBAAkB,IAAI,KAAK;QACrD;QACA,mBAAmB;IACvB;IAEA,SAAS,gBAAgB,UAAkB;QACvC,IAAI,UAAU,MAAM,OAAO,EAAE,KAAK,CAAA,IAAK,EAAE,IAAI,KAAK;QAClD,IAAI,eAAoB;QACxB,IAAI,WAAW,QAAQ,OAAO,CAAC,EAAE,KAAK,QAAQ,QAAQ,OAAO,CAAC,EAAE,KAAK,WAAW;YAC5E,+CAA+C;YAC/C,IAAI,AAAC,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EAAE,QAAQ,IAAI,EAAE,6IAAA,CAAA,aAAU,CAAC,KAAK,KAAK,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EAAE,QAAQ,IAAI,EAAE,6IAAA,CAAA,aAAU,CAAC,MAAM,KAAM,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EAAE,QAAQ,IAAI,EAAE,6IAAA,CAAA,aAAU,CAAC,OAAO,GAAG;gBACpI,eAAe,QAAQ,OAAO,CAAC,EAAE;gBACjC,IAAI,QAAQ,IAAI,KAAK,cAAc;oBAC/B,eAAe;gBACnB;YACJ;QACJ;QACA,IAAI,eAAe,SAAS;YACxB,eAAe;QACnB;QACA,OAAO;IACX;IAEA,IAAI,aAAa,gBAAgB,GAAG,CAAC,CAAA;QACjC,IAAI,UAAU,MAAM,OAAO,EAAE,KAAK,CAAA,IAAK,EAAE,IAAI,KAAK;QAClD,IAAI,CAAC,SAAS;YACV,OAAO;QACX;QAEA,IAAI,eAAe,gBAAgB;QACnC,IAAI,UAAU,CAAC,WAAW,EAAE;YACxB,eAAe,UAAU,CAAC,WAAW;QACzC;QACA,qBACI,6LAAC;YAAqB,WAAW,qJAAA,CAAA,UAAM,CAAC,aAAa;;8BACjD,6LAAC,gJAAA,CAAA,UAAa;oBACV,gBAAgB;oBAChB,SAAS;oBACT,cAAc;oBACd,iBAAiB,CAAA,WAAY,gBAAgB,YAAY;;;;;;8BAE7D,6LAAC;oBAAI,WAAW,qJAAA,CAAA,UAAM,CAAC,YAAY;oBAAE,SAAS,IAAM,oBAAoB;8BACpE,cAAA,6LAAC,8JAAA,CAAA,UAAU;wBAAC,OAAM;;;;;;;;;;;;WARhB;;;;;IAYlB;IAEA,IAAI,gCACA,6LAAC;;0BACG,6LAAC;gBACG,OAAO;oBAAE,QAAQ;oBAAW,UAAU;oBAAY,KAAK;oBAAQ,OAAO;oBAAQ,OAAO;gBAAU;gBAC/F,SAAS;oBACL,kBAAkB;gBACtB;0BAEA,cAAA,6LAAC,4JAAA,CAAA,UAAQ;;;;;;;;;;YAEZ,+BACG,6LAAC,yLAAA,CAAA,QAAK;gBACF,MAAM;gBACN,QAAQ;oBACJ,kBAAkB;gBACtB;;kCAEA,6LAAC,yLAAA,CAAA,QAAK,CAAC,MAAM;wBAAC,WAAW;kCACrB,cAAA,6LAAC;sCAAG;;;;;;;;;;;kCAER,6LAAC,yLAAA,CAAA,QAAK,CAAC,IAAI;;0CACP,6LAAC;0CAAE;;;;;;0CAIH,6LAAC;;;;;0CACD,6LAAC;0CACG,cAAA,6LAAC,yLAAA,CAAA,QAAK;oCAAC,IAAG;8CAAS;;;;;;;;;;;0CAEvB,6LAAC;0CAAE;;;;;;0CAMH,6LAAC;;oCAAE;oCACsD;kDACrD,6LAAC;wCAAE,QAAO;wCAAS,KAAI;wCAAa,MAAK;kDAAgC;;;;;;oCAErE;;;;;;;;;;;;;;;;;;uBAMhB;;;;;;;IAKZ,qBACI,6LAAC;QAAI,WAAW,qJAAA,CAAA,UAAM,CAAC,UAAU;kBAC5B,CAAC,yBACE,6LAAC;sBACG,cAAA,6LAAC;gBAAE,MAAK;gBAAI,SAAS,IAAM;;kCACvB,6LAAC,wKAAA,CAAA,UAAO;;;;;kCACR,6LAAC;kCAAK;;;;;;;;;;;;;;;;iCAId,6LAAC,uLAAA,CAAA,OAAI;;8BACD,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;oBAAC,OAAO;wBAAE,QAAQ;oBAAO;;wBAAG;wBAElC,MAAM,qBAAqB,GAAG,kBAAkB;;;;;;;8BAErD,6LAAC,uLAAA,CAAA,OAAI,CAAC,IAAI;;wBACL,MAAM,aAAa,iBAAG,6LAAC,yIAAA,CAAA,UAAS;;;;mCAAM;sCACvC,6LAAC,uLAAA,CAAA,OAAI;4BAAC,OAAO;gCAAE,cAAc;4BAAM;;8CAC/B,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;8CACN,OAAO,WAAW,MAAM,OAAO,EAAE,SAAS,kBACvC,6LAAC,wOAAA,CAAA,YAAS;wCACN,IAAG;wCACH,WACI,MAAM,UAAU,KAAK,YACf,OAAO,IAAI,CAAC,iBAAiB,MAAM,OAAO,EAAE,MAAM,SAAS,EAAE,MAAM,qBAAqB,GAAG,MAAM,KAAK,IACtG,MAAM,UAAU;wCAE1B,aACI,MAAM,UAAU,KAAK,YACf,OAAO,IAAI,CAAC,iBAAiB,MAAM,OAAO,EAAE,MAAM,SAAS,EAAE,MAAM,qBAAqB,GAAG,MAAM,KAAK,IACtG,MAAM,UAAU;wCAE1B,KAAK;wCACL,aAAY;wCACZ,WAAW,qJAAA,CAAA,UAAM,CAAC,eAAe;wCACjC,UAAU;wCACV,SAAS,uBAAuB,EAAE;wCAClC,UAAU,CAAC;4CACP,IAAI,OAAO,aAAa,OAAO,EAAE,MAAM;4CACvC,IAAI,gBAAgB;4CACpB,IAAI,OAAO,QAAQ,IAAI;4CACvB,IAAI,eAAe,MAAM,QAAQ,OAAO,IAAI;4CAC5C,IAAI,cAAc,QAAQ,WAAW,GAAG,QAAQ,WAAW,CAAC,OAAO,CAAC,OAAO,IAAI,WAAW,KAAK;4CAE/F,6FAA6F;4CAC7F,IAAI,QAAQ,CAAC,cAAc,SAAS,SAAS,CAAC,YAAY,QAAQ,CAAC,OAAO;gDACtE,IAAI,eAAe,KAAK,OAAO,CAAC,OAAO,IAAI,WAAW;gDACtD,IAAI,kBAAkB,QAAQ,OAAO,CAAC,MAAM,CAAC,CAAA,SAAU,OAAO,WAAW,GAAG,QAAQ,CAAC;gDAErF,IAAI,gBAAgB,MAAM,GAAG,GAAG;oDAC5B,gBAAgB,gBACX,GAAG,CAAC,CAAA;wDACD,IAAI,OAAO,WAAW,OAAO,QAAQ;4DACjC,OAAO,CAAA,GAAA,sHAAA,CAAA,mBAAgB,AAAD,EAAE,QAAQ,IAAI;wDACxC;wDACA,IAAI,OAAO,WAAW,OAAO,QAAQ;4DACjC,OAAO,CAAA,GAAA,sHAAA,CAAA,mBAAgB,AAAD,EAAE,QAAQ,IAAI;wDACxC;wDACA,OAAO,CAAA,GAAA,sHAAA,CAAA,0BAAuB,AAAD,EAAE,QAAQ,IAAI;oDAC/C,GACC,IAAI,CAAC;gDACd;4CACJ;4CAEA,IAAI,IAAI,CAAC,EAAE,CAAC,WAAW,OAAO,IAAI,CAAC,EAAE,EAAE;gDACnC,OAAO,GAAG,CAAA,GAAA,sHAAA,CAAA,mBAAgB,AAAD,EAAE,MAAM,CAAC,EAAE,gBAAgB,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC,GAAG,IAAI;4CACnF;4CAEA,OAAO,GAAG,CAAA,GAAA,sHAAA,CAAA,0BAAuB,AAAD,EAAE,MAAM,CAAC,EAAE,gBAAgB,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC,GAAG,IAAI;wCAC1F;wCACA,UAAU,CAAC,SAAwB;4CAC/B,IAAI,eAAe,MAAM,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,WAAW;4CAC5D,IAAI,OAAO,AAAC,MAAM,QAAQ,CAAc,SAAS,WAAW;4CAC5D,IAAI,WAAW,KAAK,KAAK,CAAC,SAAS,IAAI,CAAC;4CACxC,IAAI,cAAc,QAAQ,WAAW,GAAG,QAAQ,WAAW,CAAC,OAAO,CAAC,OAAO,IAAI,WAAW,KAAK;4CAE/F,IAAI,kBAAkB,QAAQ,OAAO,CAAC,MAAM,CAAC,CAAA,SAAU,OAAO,WAAW,GAAG,QAAQ,CAAC;4CAErF,OACI,KAAK,OAAO,CAAC,OAAO,IAAI,QAAQ,CAAC,iBACjC,SAAS,QAAQ,CAAC,iBAClB,YAAY,QAAQ,CAAC,iBACrB,gBAAgB,MAAM,GAAG;wCAEjC;wCACA,YAAY,MAAM,UAAU,IAAI;;;;;6DAGpC,6LAAC,6LAAA,CAAA,UAAO;wCAAC,WAAU;wCAAS,MAAK;wCAAS,SAAQ;;;;;;;;;;;8CAG1D,6LAAC;oCAAI,WAAW,qJAAA,CAAA,UAAM,CAAC,eAAe;8CAAG;;;;;;;;;;;;wBAE5C,MAAM,SAAS,GAAG,qBACf,6LAAC;4BAAI,OAAO;gCAAE,SAAS;gCAAQ,gBAAgB;gCAAO,WAAW;4BAAO;sCACpE,cAAA,6LAAC,2LAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAS,SAAS,IAAM;0CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUzF;GA7bS;;QACQ,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;;;KAFrB;uCA8bM;AAER,SAAS,iBAAiB,gBAAiC,EAAE,EAAE,YAAqB,KAAK,EAAE,wBAAiC,KAAK;IACpI,IAAI,aAAa,CAAC,YAAY,CAAA,GAAA,gIAAA,CAAA,uBAAoB,AAAD,MAAM,CAAC;IACxD,IAAI,OAAO,IAAI,CAAC,YAAY,MAAM,KAAK,KAAK,CAAC,uBAAuB;QAChE,aAAa,0BAA0B,kBAAkB,CAAC;IAC9D;IACA,OAAO;AACX;AAEA;;;CAGC,GACD,SAAS,0BAA0B,gBAAiC,EAAE;IAClE,IAAI,yBAAyB,aAAa,OAAO,CAAC,0HAAA,CAAA,mBAAgB;IAClE,IAAI,2BAA2B,MAAM;QACjC,OAAO;IACX;IACA,IAAI,SAAqB,KAAK,KAAK,CAAC;IACpC,OAAO,IAAI,CAAC,QAAQ,OAAO,CAAC,CAAA;QACxB,IAAI,cAAc,SAAS,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,SAAS,CAAC,GAAG;YACrD,OAAO,MAAM,CAAC,IAAI;QACtB;IACJ;IACA,OAAO;AACX", "debugId": null}}, {"offset": {"line": 5242, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/ItemFilter/ItemFilterPropertiesDisplay.tsx"], "sourcesContent": ["'use client'\n/* eslint-disable react-hooks/exhaustive-deps */\n/* eslint-disable jsx-a11y/anchor-is-valid */\nimport RemoveIcon from '@mui/icons-material/Remove'\nimport { useEffect, useState } from 'react'\nimport api from '../../api/ApiHelper'\nimport { camelCaseToSentenceCase, convertTagToName, numberWithThousandsSeparators } from '../../utils/Formatter'\nimport { useForceUpdate } from '../../utils/Hooks'\nimport Tooltip from '../Tooltip/Tooltip'\nimport HelpIcon from '@mui/icons-material/Help'\n\ninterface Props {\n    filter?: ItemFilter\n    onAfterEdit?(filter: ItemFilter)\n    isEditable?: boolean\n}\n\nconst DATE_FORMAT_FILTER = ['EndBefore', 'EndAfter', 'ItemCreatedBefore', 'ItemCreatedAfter']\nconst SELLER_FORMAT_FILTER = 'Seller'\nconst SKIN_FILTER = 'Skin'\nconst PET_SKIN_FILTER = 'PetSkin'\n\nfunction ItemFilterPropertiesDisplay(props: Props) {\n    let [localFilter, setLocalFilter] = useState(props.filter || {})\n\n    let forceUpdate = useForceUpdate()\n\n    useEffect(() => {\n        updateLocalFilter()\n    }, [JSON.stringify(props.filter)])\n\n    function updateLocalFilter() {\n        if (!props.filter) {\n            return\n        }\n        let localFilter = JSON.parse(JSON.stringify(props.filter))\n        setLocalFilter(localFilter)\n        checkForSellerName(localFilter)\n    }\n\n    function checkForSellerName(filter: ItemFilter) {\n        if (filter) {\n            Object.keys(filter).forEach(key => {\n                if (key === SELLER_FORMAT_FILTER) {\n                    filter!._hide = true\n                    api.getPlayerName(filter![key]).then(name => {\n                        filter!._hide = false\n                        filter!._label = name || '-'\n                        setLocalFilter(filter)\n                        forceUpdate()\n                    })\n                }\n            })\n        }\n    }\n\n    function onRemoveClick(key) {\n        let newLocalFilter = { ...localFilter }\n        delete newLocalFilter[key]\n        setLocalFilter(newLocalFilter)\n        if (props.onAfterEdit) {\n            props.onAfterEdit(newLocalFilter)\n        }\n    }\n\n    return (\n        <div>\n            {!localFilter ? (\n                <></>\n            ) : (\n                Object.keys(localFilter).map(key => {\n                    if (!localFilter || localFilter._hide) {\n                        return ''\n                    }\n\n                    let display = convertTagToName(localFilter[key])\n                    let info: string | null = null;\n\n                    if (key === 'ItemNameContains') {\n                        display = localFilter[key]\n                    }\n\n                    if (key.startsWith('_')) {\n                        return ''\n                    }\n\n                    // finds \">\",\"<\",\"=\"\" and combinations at the beginning\n                    let beginningSymbolRegexp = new RegExp(/^[<>=]+/)\n                    if (!isNaN(Number(display.replace(beginningSymbolRegexp, '')))) {\n                        let symbols = display.match(beginningSymbolRegexp)\n                        let number = display.replace(beginningSymbolRegexp, '')\n                        display = numberWithThousandsSeparators(Number(number))\n                        display = symbols ? symbols[0] + display : display\n                    }\n\n                    // finds number ranges (e.g. \"10000-999999\")\n                    let numberRangeRegex = new RegExp(/^\\d+-\\d+$/)\n                    if (display.match(numberRangeRegex)) {\n                        let numbers = display.split('-').map(numberString => numberWithThousandsSeparators(Number(numberString)))\n\n                        if (numbers[0] === numbers[1]) {\n                            display = numbers[0].toString()\n                        } else {\n                            display = numbers.join('-')\n                        }\n                    }\n\n                    // Special case -> display as date\n                    if (localFilter[key] && DATE_FORMAT_FILTER.findIndex(f => f === key) !== -1) {\n                        display = new Date(Number(localFilter[key]) * 1000).toLocaleDateString()\n                    }\n\n                    // Special case if the restriction has a special label\n                    if (localFilter._sellerName && key === SELLER_FORMAT_FILTER) {\n                        display = localFilter._sellerName\n                    }\n\n                    // Special case for skin filter\n                    if (key === SKIN_FILTER || key === PET_SKIN_FILTER) {\n                        info = 'This filter only works on applied skins on pets/armor. For the items there is the \"ItemCategory\" filter.';\n                    }\n\n                    if (!localFilter[key] && !display) {\n                        display = '-'\n                    }\n\n                    return (\n                        <span key={key} >\n                            <div className=\"ellipse mb-2\" title={display}>\n                                {camelCaseToSentenceCase(key)}: {display}\n                                {info ? <Tooltip\n                                    type=\"hover\"\n                                    content={<HelpIcon style={{ color: '#007bff', cursor: 'pointer', marginLeft: 5 }} />}\n                                    tooltipContent={<span>{info}</span>}\n                                /> : null}\n                            </div>\n\n                            {props.isEditable ? (\n                                <span\n                                    style={{ color: 'red', cursor: 'pointer' }}\n                                    onClick={() => {\n                                        onRemoveClick(key)\n                                    }}\n                                >\n                                    <RemoveIcon />\n                                </span>\n                            ) : null}\n                        </span>\n                    )\n                })\n            )}\n        </div>\n    )\n}\n\nexport default ItemFilterPropertiesDisplay\n"], "names": [], "mappings": ";;;;AACA,8CAA8C,GAC9C,2CAA2C,GAC3C;AACA;AACA;AACA;AACA;AACA;AACA;;;AATA;;;;;;;;AAiBA,MAAM,qBAAqB;IAAC;IAAa;IAAY;IAAqB;CAAmB;AAC7F,MAAM,uBAAuB;AAC7B,MAAM,cAAc;AACpB,MAAM,kBAAkB;AAExB,SAAS,4BAA4B,KAAY;;IAC7C,IAAI,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,MAAM,MAAM,IAAI,CAAC;IAE9D,IAAI,cAAc,CAAA,GAAA,kHAAA,CAAA,iBAAc,AAAD;IAE/B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iDAAE;YACN;QACJ;gDAAG;QAAC,KAAK,SAAS,CAAC,MAAM,MAAM;KAAE;IAEjC,SAAS;QACL,IAAI,CAAC,MAAM,MAAM,EAAE;YACf;QACJ;QACA,IAAI,cAAc,KAAK,KAAK,CAAC,KAAK,SAAS,CAAC,MAAM,MAAM;QACxD,eAAe;QACf,mBAAmB;IACvB;IAEA,SAAS,mBAAmB,MAAkB;QAC1C,IAAI,QAAQ;YACR,OAAO,IAAI,CAAC,QAAQ,OAAO,CAAC,CAAA;gBACxB,IAAI,QAAQ,sBAAsB;oBAC9B,OAAQ,KAAK,GAAG;oBAChB,oHAAA,CAAA,UAAG,CAAC,aAAa,CAAC,MAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;wBACjC,OAAQ,KAAK,GAAG;wBAChB,OAAQ,MAAM,GAAG,QAAQ;wBACzB,eAAe;wBACf;oBACJ;gBACJ;YACJ;QACJ;IACJ;IAEA,SAAS,cAAc,GAAG;QACtB,IAAI,iBAAiB;YAAE,GAAG,WAAW;QAAC;QACtC,OAAO,cAAc,CAAC,IAAI;QAC1B,eAAe;QACf,IAAI,MAAM,WAAW,EAAE;YACnB,MAAM,WAAW,CAAC;QACtB;IACJ;IAEA,qBACI,6LAAC;kBACI,CAAC,4BACE,6YAEA,OAAO,IAAI,CAAC,aAAa,GAAG,CAAC,CAAA;YACzB,IAAI,CAAC,eAAe,YAAY,KAAK,EAAE;gBACnC,OAAO;YACX;YAEA,IAAI,UAAU,CAAA,GAAA,sHAAA,CAAA,mBAAgB,AAAD,EAAE,WAAW,CAAC,IAAI;YAC/C,IAAI,OAAsB;YAE1B,IAAI,QAAQ,oBAAoB;gBAC5B,UAAU,WAAW,CAAC,IAAI;YAC9B;YAEA,IAAI,IAAI,UAAU,CAAC,MAAM;gBACrB,OAAO;YACX;YAEA,uDAAuD;YACvD,IAAI,wBAAwB,IAAI,OAAO;YACvC,IAAI,CAAC,MAAM,OAAO,QAAQ,OAAO,CAAC,uBAAuB,OAAO;gBAC5D,IAAI,UAAU,QAAQ,KAAK,CAAC;gBAC5B,IAAI,SAAS,QAAQ,OAAO,CAAC,uBAAuB;gBACpD,UAAU,CAAA,GAAA,sHAAA,CAAA,gCAA6B,AAAD,EAAE,OAAO;gBAC/C,UAAU,UAAU,OAAO,CAAC,EAAE,GAAG,UAAU;YAC/C;YAEA,4CAA4C;YAC5C,IAAI,mBAAmB,IAAI,OAAO;YAClC,IAAI,QAAQ,KAAK,CAAC,mBAAmB;gBACjC,IAAI,UAAU,QAAQ,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,eAAgB,CAAA,GAAA,sHAAA,CAAA,gCAA6B,AAAD,EAAE,OAAO;gBAE1F,IAAI,OAAO,CAAC,EAAE,KAAK,OAAO,CAAC,EAAE,EAAE;oBAC3B,UAAU,OAAO,CAAC,EAAE,CAAC,QAAQ;gBACjC,OAAO;oBACH,UAAU,QAAQ,IAAI,CAAC;gBAC3B;YACJ;YAEA,kCAAkC;YAClC,IAAI,WAAW,CAAC,IAAI,IAAI,mBAAmB,SAAS,CAAC,CAAA,IAAK,MAAM,SAAS,CAAC,GAAG;gBACzE,UAAU,IAAI,KAAK,OAAO,WAAW,CAAC,IAAI,IAAI,MAAM,kBAAkB;YAC1E;YAEA,sDAAsD;YACtD,IAAI,YAAY,WAAW,IAAI,QAAQ,sBAAsB;gBACzD,UAAU,YAAY,WAAW;YACrC;YAEA,+BAA+B;YAC/B,IAAI,QAAQ,eAAe,QAAQ,iBAAiB;gBAChD,OAAO;YACX;YAEA,IAAI,CAAC,WAAW,CAAC,IAAI,IAAI,CAAC,SAAS;gBAC/B,UAAU;YACd;YAEA,qBACI,6LAAC;;kCACG,6LAAC;wBAAI,WAAU;wBAAe,OAAO;;4BAChC,CAAA,GAAA,sHAAA,CAAA,0BAAuB,AAAD,EAAE;4BAAK;4BAAG;4BAChC,qBAAO,6LAAC,oIAAA,CAAA,UAAO;gCACZ,MAAK;gCACL,uBAAS,6LAAC,4JAAA,CAAA,UAAQ;oCAAC,OAAO;wCAAE,OAAO;wCAAW,QAAQ;wCAAW,YAAY;oCAAE;;;;;;gCAC/E,8BAAgB,6LAAC;8CAAM;;;;;;;;;;uCACtB;;;;;;;oBAGR,MAAM,UAAU,iBACb,6LAAC;wBACG,OAAO;4BAAE,OAAO;4BAAO,QAAQ;wBAAU;wBACzC,SAAS;4BACL,cAAc;wBAClB;kCAEA,cAAA,6LAAC,8JAAA,CAAA,UAAU;;;;;;;;;+BAEf;;eAnBG;;;;;QAsBnB;;;;;;AAIhB;GAnIS;;QAGa,kHAAA,CAAA,iBAAc;;;KAH3B;uCAqIM", "debugId": null}}, {"offset": {"line": 5451, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/NotificationTargets/NotificationTargetForm.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Button, Form } from 'react-bootstrap'\nimport api from '../../api/ApiHelper'\nimport askForNotificationPermissons from '../../utils/NotificationPermisson'\nimport { getNotficationWhenEnumAsString, getNotificationTypeAsString } from '../../utils/NotificationUtils'\n\nconst TYPE_OPTIONS: NotificationType[] = ['DiscordWebhook', 'FIREBASE', 'InGame']\nconst WHEN_OPITIONS: NotificationWhen[] = ['NEVER', 'AfterFail', 'ALWAYS']\n\ninterface Props {\n    type: 'CREATE' | 'UPDATE'\n    defaultNotificationTarget?: NotificationTarget | null\n    onSubmit(target: NotificationTarget): void\n}\n\nfunction NotificationTargetForm(props: Props) {\n    let [name, setName] = useState<string>(props.defaultNotificationTarget?.name || '')\n    let [type, setType] = useState<NotificationType | number>(props.defaultNotificationTarget?.type || 'FIREBASE')\n    let [target, setTarget] = useState<string | null>(props.defaultNotificationTarget?.target || null)\n    let [when, setWhen] = useState<NotificationWhen | number>(props.defaultNotificationTarget?.when || 'ALWAYS')\n    let [disabled, setDisabled] = useState(false)\n\n    async function addNotificationTarget() {\n        let targetToSend = target\n\n        if (type === 'FIREBASE') {\n            if (localStorage.getItem('fcmToken') === null) {\n                let token = await askForNotificationPermissons()\n                await api.setToken(token)\n                localStorage.setItem('fcmToken', token)\n                targetToSend = token\n            } else {\n                targetToSend = localStorage.getItem('fcmToken') as string\n            }\n        }\n\n        setDisabled(true)\n        let notificationTarget: NotificationTarget = {\n            id: props.defaultNotificationTarget?.id,\n            name: name,\n            type: type,\n            target: targetToSend,\n            when: when,\n            useCount: 0\n        }\n\n        let updateOrCreateFunction = props.type === 'CREATE' ? api.addNotificationTarget : api.updateNotificationTarget\n        updateOrCreateFunction(notificationTarget)\n            .then(newTarget => {\n                props.onSubmit(newTarget)\n            })\n            .finally(() => {\n                setDisabled(false)\n            })\n    }\n\n    return (\n        <>\n            <Form>\n                <Form.Group className=\"mb-3\">\n                    <Form.Label>Name</Form.Label>\n                    <Form.Control defaultValue={name} type=\"text\" onChange={e => setName(e.target.value)} placeholder=\"Enter name\" />\n                </Form.Group>\n                <Form.Group className=\"mb-3\">\n                    <Form.Label>Type</Form.Label>\n                    <Form.Select defaultValue={type} onChange={e => setType(e.target.value as any)}>\n                        {TYPE_OPTIONS.map(type => (\n                            <option value={type} key={type}>\n                                {getNotificationTypeAsString(type)}\n                            </option>\n                        ))}\n                    </Form.Select>\n                </Form.Group>\n                {type === 'WEBHOOK' || type === 'DiscordWebhook' || type === 3 || type === 1 ? (\n                    <Form.Group className=\"mb-3\">\n                        <Form.Label>Target</Form.Label>\n                        <Form.Control\n                            defaultValue={target || undefined}\n                            type=\"text\"\n                            onChange={e => setTarget(e.target.value)}\n                            placeholder={type === 'DiscordWebhook' ? 'Discord Webhook Url (https://discord.com/api/...)' : 'Webhook Url'}\n                            isInvalid={(type === 'DiscordWebhook' && target && !target?.startsWith('https://discord.com/api/')) === true}\n                        />\n                        {type === 'DiscordWebhook' && target && !target?.startsWith('https://discord.com/api/') ? (\n                            <div>\n                                <span style={{ color: 'red' }}>The Discord Webhook URL has to start with \"https://discord.com/api/...\"</span>\n                            </div>\n                        ) : null}\n                    </Form.Group>\n                ) : null}\n                <Form.Group className=\"mb-3\">\n                    <Form.Label>When</Form.Label>\n                    <Form.Select defaultValue={when} onChange={e => setWhen(e.target.value as any)}>\n                        {WHEN_OPITIONS.map(when => (\n                            <option value={when} key={when}>\n                                {getNotficationWhenEnumAsString(when)}\n                            </option>\n                        ))}\n                    </Form.Select>\n                </Form.Group>\n                <Button variant=\"primary\" onClick={addNotificationTarget} disabled={disabled}>\n                    {props.type === 'CREATE' ? 'Add' : 'Update'}\n                </Button>\n            </Form>\n        </>\n    )\n}\n\nexport default NotificationTargetForm\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AACA;;;AANA;;;;;;AAQA,MAAM,eAAmC;IAAC;IAAkB;IAAY;CAAS;AACjF,MAAM,gBAAoC;IAAC;IAAS;IAAa;CAAS;AAQ1E,SAAS,uBAAuB,KAAY;;IACxC,IAAI,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,MAAM,yBAAyB,EAAE,QAAQ;IAChF,IAAI,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA6B,MAAM,yBAAyB,EAAE,QAAQ;IACnG,IAAI,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,MAAM,yBAAyB,EAAE,UAAU;IAC7F,IAAI,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA6B,MAAM,yBAAyB,EAAE,QAAQ;IACnG,IAAI,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,eAAe;QACX,IAAI,eAAe;QAEnB,IAAI,SAAS,YAAY;YACrB,IAAI,aAAa,OAAO,CAAC,gBAAgB,MAAM;gBAC3C,IAAI,QAAQ,MAAM,CAAA,GAAA,kIAAA,CAAA,UAA4B,AAAD;gBAC7C,MAAM,oHAAA,CAAA,UAAG,CAAC,QAAQ,CAAC;gBACnB,aAAa,OAAO,CAAC,YAAY;gBACjC,eAAe;YACnB,OAAO;gBACH,eAAe,aAAa,OAAO,CAAC;YACxC;QACJ;QAEA,YAAY;QACZ,IAAI,qBAAyC;YACzC,IAAI,MAAM,yBAAyB,EAAE;YACrC,MAAM;YACN,MAAM;YACN,QAAQ;YACR,MAAM;YACN,UAAU;QACd;QAEA,IAAI,yBAAyB,MAAM,IAAI,KAAK,WAAW,oHAAA,CAAA,UAAG,CAAC,qBAAqB,GAAG,oHAAA,CAAA,UAAG,CAAC,wBAAwB;QAC/G,uBAAuB,oBAClB,IAAI,CAAC,CAAA;YACF,MAAM,QAAQ,CAAC;QACnB,GACC,OAAO,CAAC;YACL,YAAY;QAChB;IACR;IAEA,qBACI;kBACI,cAAA,6LAAC,uLAAA,CAAA,OAAI;;8BACD,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;oBAAC,WAAU;;sCAClB,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;sCAAC;;;;;;sCACZ,6LAAC,uLAAA,CAAA,OAAI,CAAC,OAAO;4BAAC,cAAc;4BAAM,MAAK;4BAAO,UAAU,CAAA,IAAK,QAAQ,EAAE,MAAM,CAAC,KAAK;4BAAG,aAAY;;;;;;;;;;;;8BAEtG,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;oBAAC,WAAU;;sCAClB,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;sCAAC;;;;;;sCACZ,6LAAC,uLAAA,CAAA,OAAI,CAAC,MAAM;4BAAC,cAAc;4BAAM,UAAU,CAAA,IAAK,QAAQ,EAAE,MAAM,CAAC,KAAK;sCACjE,aAAa,GAAG,CAAC,CAAA,qBACd,6LAAC;oCAAO,OAAO;8CACV,CAAA,GAAA,8HAAA,CAAA,8BAA2B,AAAD,EAAE;mCADP;;;;;;;;;;;;;;;;gBAMrC,SAAS,aAAa,SAAS,oBAAoB,SAAS,KAAK,SAAS,kBACvE,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;oBAAC,WAAU;;sCAClB,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;sCAAC;;;;;;sCACZ,6LAAC,uLAAA,CAAA,OAAI,CAAC,OAAO;4BACT,cAAc,UAAU;4BACxB,MAAK;4BACL,UAAU,CAAA,IAAK,UAAU,EAAE,MAAM,CAAC,KAAK;4BACvC,aAAa,SAAS,mBAAmB,sDAAsD;4BAC/F,WAAW,CAAC,SAAS,oBAAoB,UAAU,CAAC,QAAQ,WAAW,2BAA2B,MAAM;;;;;;wBAE3G,SAAS,oBAAoB,UAAU,CAAC,QAAQ,WAAW,4CACxD,6LAAC;sCACG,cAAA,6LAAC;gCAAK,OAAO;oCAAE,OAAO;gCAAM;0CAAG;;;;;;;;;;mCAEnC;;;;;;2BAER;8BACJ,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;oBAAC,WAAU;;sCAClB,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;sCAAC;;;;;;sCACZ,6LAAC,uLAAA,CAAA,OAAI,CAAC,MAAM;4BAAC,cAAc;4BAAM,UAAU,CAAA,IAAK,QAAQ,EAAE,MAAM,CAAC,KAAK;sCACjE,cAAc,GAAG,CAAC,CAAA,qBACf,6LAAC;oCAAO,OAAO;8CACV,CAAA,GAAA,8HAAA,CAAA,iCAA8B,AAAD,EAAE;mCADV;;;;;;;;;;;;;;;;8BAMtC,6LAAC,2LAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAU,SAAS;oBAAuB,UAAU;8BAC/D,MAAM,IAAI,KAAK,WAAW,QAAQ;;;;;;;;;;;;;AAKvD;GA3FS;KAAA;uCA6FM", "debugId": null}}, {"offset": {"line": 5681, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/components/AuctionDetails/AuctionDetails.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"auctionCard\": \"AuctionDetails-module__Zyg3LG__auctionCard\",\n  \"auctionCardHeader\": \"AuctionDetails-module__Zyg3LG__auctionCardHeader\",\n  \"auctionDetails\": \"AuctionDetails-module__Zyg3LG__auctionDetails\",\n  \"backButton\": \"AuctionDetails-module__Zyg3LG__backButton\",\n  \"cardHeadSubtext\": \"AuctionDetails-module__Zyg3LG__cardHeadSubtext\",\n  \"center\": \"AuctionDetails-module__Zyg3LG__center\",\n  \"centerChild\": \"AuctionDetails-module__Zyg3LG__centerChild\",\n  \"compareButtonLabel\": \"AuctionDetails-module__Zyg3LG__compareButtonLabel\",\n  \"detailContainer\": \"AuctionDetails-module__Zyg3LG__detailContainer\",\n  \"detailRow\": \"AuctionDetails-module__Zyg3LG__detailRow\",\n  \"firstCard\": \"AuctionDetails-module__Zyg3LG__firstCard\",\n  \"fixedBottom\": \"AuctionDetails-module__Zyg3LG__fixedBottom\",\n  \"itemIcon\": \"AuctionDetails-module__Zyg3LG__itemIcon\",\n  \"label\": \"AuctionDetails-module__Zyg3LG__label\",\n  \"labelForList\": \"AuctionDetails-module__Zyg3LG__labelForList\",\n  \"list\": \"AuctionDetails-module__Zyg3LG__list\",\n  \"topRowButtonContent\": \"AuctionDetails-module__Zyg3LG__topRowButtonContent\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 5706, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/AuctionDetails/AuctionDetails.tsx"], "sourcesContent": ["'use client'\nimport moment from 'moment'\nimport Link from 'next/link'\nimport { useEffect, useState, type JSX } from 'react';\nimport { <PERSON>ge, Button, Card, ListGroup, Modal, OverlayTrigger, Tooltip as TooltipBootstrap } from 'react-bootstrap'\nimport Countdown from 'react-countdown'\nimport { toast } from 'react-toastify'\nimport { v4 as generateUUID } from 'uuid'\nimport api from '../../api/ApiHelper'\nimport {\n    convertTagToName,\n    formatDungeonStarsInString as getDungeonStarFormattedItemName,\n    getMinecraftColorCodedElement,\n    getStyleForTier\n} from '../../utils/Formatter'\nimport { useForceUpdate } from '../../utils/Hooks'\nimport { getLoadingElement } from '../../utils/LoadingUtils'\nimport { isClientSideRendering } from '../../utils/SSRUtils'\nimport { CopyButton } from '../CopyButton/CopyButton'\nimport FlipBased from '../Flipper/FlipBased/FlipBased'\nimport SubscribeButton from '../SubscribeButton/SubscribeButton'\nimport Tooltip from '../Tooltip/Tooltip'\nimport styles from './AuctionDetails.module.css'\nimport { Help as HelpIcon, ArrowDropDown as ArrowDownIcon, ArrowRight as ArrowRightIcon } from '@mui/icons-material'\nimport { FilterChecker } from '../FilterChecker/FilterChecker'\nimport Image from 'next/image'\nimport Number from '../Number/Number'\nimport { parseAuctionDetails } from '../../utils/Parser/APIResponseParser'\nimport ItemHistory from '../OwnerHistory/OwnerHistory'\n\ninterface Props {\n    auctionUUID: string\n    auctionDetails?: any\n    retryCounter?: number\n    unparsedAuctionDetails?: any\n    copyButtonValue: 'ingame' | 'web'\n}\n\nfunction AuctionDetails(props: Props) {\n    let [isNoAuctionFound, setIsNoAuctionFound] = useState(false)\n    let [auctionDetails, setAuctionDetails] = useState<AuctionDetails | undefined>(props.auctionDetails ? parseAuctionDetails(props.auctionDetails) : undefined)\n    let [unparsedAuctionDetails, setUnparsedAuctionDetails] = useState(props.unparsedAuctionDetails)\n    let [isLoading, setIsLoading] = useState(false)\n    let [showBasedOnDialog, setShowBasedOnDialog] = useState(false)\n    let [showFilterChecker, setShowFilterChecker] = useState(false)\n    let [showItemHistoryDialog, setShowItemHistoryDialog] = useState(false)\n    let forceUpdate = useForceUpdate()\n\n    useEffect(() => {\n        // Dont load auction details if\n        // - either the auctionUUID is not present (then it cant be loaded here and needs props.auctionDetails)\n        // - or props.auctionDetails is already filled\n        if (!props.auctionUUID || props.auctionDetails) {\n            return\n        }\n        loadAuctionDetails(props.auctionUUID!)\n    }, [props.auctionUUID])\n\n    let tryNumber = 1\n    function loadAuctionDetails(auctionUUID: string) {\n        // if auction details are already available, don't show loading animation to prevent flickering\n        if (!auctionDetails) {\n            setIsLoading(true)\n        }\n        api.getAuctionDetails(auctionUUID)\n            .then(result => {\n                setUnparsedAuctionDetails(result.original)\n                let auctionDetails = result.parsed\n                auctionDetails.bids.sort((a, b) => b.amount - a.amount)\n                auctionDetails.auction.item.iconUrl = api.getItemImageUrl(auctionDetails.auction.item)\n                setAuctionDetails(auctionDetails)\n                api.getItemDetails(auctionDetails.auction.item.tag).then(item => {\n                    if (!auctionDetails.auction.item.name) {\n                        auctionDetails.auction.item.name = item.name\n                    }\n                    setAuctionDetails(auctionDetails)\n                    forceUpdate()\n                })\n\n                let namePromises: Promise<void>[] = []\n                auctionDetails.bids.forEach(bid => {\n                    let promise = api.getPlayerName(bid.bidder.uuid).then(name => {\n                        bid.bidder.name = name\n                    })\n                    namePromises.push(promise)\n                })\n                namePromises.push(\n                    api.getPlayerName(auctionDetails.auctioneer.uuid).then(name => {\n                        auctionDetails.auctioneer.name = name\n                    })\n                )\n                Promise.all(namePromises).then(() => {\n                    forceUpdate()\n                    setIsLoading(false)\n                })\n            })\n            .catch(error => {\n                setIsLoading(false)\n                if (tryNumber < (props.retryCounter || 5)) {\n                    tryNumber++\n                    setTimeout(() => {\n                        loadAuctionDetails(auctionUUID)\n                    }, 2000)\n                } else {\n                    setIsNoAuctionFound(true)\n                    if (error) {\n                        toast.error(error.message)\n                    }\n                }\n            })\n    }\n\n    let isRunning = (auctionDetails: AuctionDetails) => {\n        return auctionDetails.auction.end.getTime() >= Date.now() && !(auctionDetails.auction.bin && auctionDetails.bids.length > 0)\n    }\n\n    let getTimeToolTipString = () => {\n        if (!auctionDetails) {\n            return ''\n        }\n\n        if (auctionDetails?.auction.bin && auctionDetails.auction.highestBid > 0 && auctionDetails.bids.length > 0) {\n            return moment(auctionDetails.bids[0].timestamp).format('MMMM Do YYYY, h:mm:ss a')\n        }\n        return moment(auctionDetails.auction.end).format('MMMM Do YYYY, h:mm:ss a')\n    }\n\n    function getNBTElement(): JSX.Element | null {\n        if (!auctionDetails?.nbtData) {\n            return null\n        }\n        return (\n            <div className={styles.detailContainer}>\n                {Object.keys(auctionDetails?.nbtData).map(key => {\n                    let currentNBT = auctionDetails?.nbtData[key]\n                    return (\n                        <div className={styles.detailRow} key={key}>\n                            <span className={styles.label}>\n                                <Badge bg={labelBadgeVariant}>{formatNBTKey(key)}:</Badge>\n                            </span>\n                            <span className=\"ellipse\">{formatNBTValue(key, currentNBT, auctionDetails)}</span>\n                        </div>\n                    )\n                })}\n            </div>\n        )\n    }\n\n    function getCopyButtonValue(auctionDetails: AuctionDetails) {\n        if (!isClientSideRendering()) {\n            return ''\n        }\n        if (props.copyButtonValue === 'web') {\n            return `${location.origin}/auction/${auctionDetails.auction.uuid}`\n        }\n        return isRunning(auctionDetails) ? '/viewauction ' + auctionDetails.auction.uuid : `${location.origin}/auction/${auctionDetails.auction.uuid}`\n    }\n\n    function formatNBTKey(key: string) {\n        if (key === 'mending') {\n            return 'Vitality'\n        }\n        return convertTagToName(key)\n    }\n\n    function formatNBTValue(key: string, value: any, auctionDetails?: AuctionDetails) {\n        let tagNbt = [\n            'heldItem',\n            'personal_compact_0',\n            'personal_compact_1',\n            'personal_compact_2',\n            'personal_compact_3',\n            'personal_compact_4',\n            'personal_compact_5',\n            'personal_compact_6',\n            'personal_compact_7',\n            'personal_compact_8',\n            'personal_compact_9',\n            'personal_compact_10',\n            'personal_compact_11',\n            'personal_compactor_0',\n            'personal_compactor_1',\n            'personal_compactor_2',\n            'personal_compactor_3',\n            'personal_compactor_4',\n            'personal_compactor_5',\n            'personal_compactor_6',\n            'personal_deletor_0',\n            'personal_deletor_1',\n            'personal_deletor_2',\n            'personal_deletor_3',\n            'personal_deletor_4',\n            'personal_deletor_5',\n            'personal_deletor_6',\n            'personal_deletor_7',\n            'personal_deletor_8',\n            'personal_deletor_9',\n            'last_potion_ingredient',\n            'power_ability_scroll',\n            'skin'\n        ]\n\n        if (key === 'rarity_upgrades') {\n            if (value === '0') {\n                return 'false'\n            }\n            if (value === '1') {\n                return 'true'\n            }\n            return value\n        }\n\n        if (key === 'color') {\n            let decSplits = value ? value.split(':') : []\n            let hexSplits: string[] = []\n            decSplits.forEach(split => {\n                hexSplits.push(parseInt(split).toString(16).padStart(2, '0'))\n            })\n            return (\n                <Tooltip\n                    type=\"hover\"\n                    content={\n                        <span>\n                            <span>{hexSplits.join('')}</span>{' '}\n                            <span\n                                style={{\n                                    textAlign: 'center',\n                                    display: 'inline-block',\n                                    width: '16px',\n                                    height: '16px',\n                                    backgroundColor: `#${hexSplits.join('')}`,\n                                    borderRadius: '50%',\n                                    borderColor: 'black',\n                                    border: 'solid black 1px',\n                                    marginLeft: '4px'\n                                }}\n                            ></span>\n                        </span>\n                    }\n                    tooltipContent={value}\n                />\n            )\n        }\n\n        if (key === 'date') {\n            let date = new Date(parseInt(value))\n            return date.toLocaleDateString() + ' ' + date.toLocaleTimeString()\n        }\n\n        // Don't use the number formating if it includes a 'e' as it is then treated as an exponential number resulting in displaying \"∞\"\n        if (!isNaN(value) && !value.toString().includes('e')) {\n            return <Number number={value} />\n        }\n\n        let index = tagNbt.findIndex(tag => tag === key)\n        if (index !== -1) {\n            if (key === 'skin' && auctionDetails?.auction?.item?.tag?.startsWith('PET_')) {\n                return <Link href={'/item/PET_SKIN_' + value}>{convertTagToName(value)}</Link>\n            }\n            return <Link href={'/item/' + value}>{convertTagToName(value)}</Link>\n        }\n\n        if (value?.toString().includes('§')) {\n            return getMinecraftColorCodedElement(value, false)\n        }\n\n        return value.toString()\n    }\n\n    const labelBadgeVariant = 'primary'\n    const binBadgeVariant = 'success'\n    const countBadgeVariant = 'dark'\n\n    let basedOnDialog = showBasedOnDialog ? (\n        <Modal\n            size={'xl'}\n            show={showBasedOnDialog}\n            onHide={() => {\n                setShowBasedOnDialog(false)\n            }}\n        >\n            <Modal.Header closeButton>\n                <Modal.Title>Similar auctions from the past</Modal.Title>\n            </Modal.Header>\n            <Modal.Body>{auctionDetails ? <FlipBased auctionUUID={auctionDetails.auction.uuid} item={auctionDetails.auction.item} /> : null}</Modal.Body>\n        </Modal>\n    ) : null\n\n    let previousOwnersDialog = showItemHistoryDialog ? (\n        <Modal\n            size={'xl'}\n            show={showItemHistoryDialog}\n            onHide={() => {\n                setShowItemHistoryDialog(false)\n            }}\n        >\n            <Modal.Header closeButton>\n                <Modal.Title>Previous Owners</Modal.Title>\n            </Modal.Header>\n            <Modal.Body>\n                <ItemHistory uid={auctionDetails?.nbtData.uid} />\n            </Modal.Body>\n        </Modal>\n    ) : null\n\n    let auctionCardContent = !auctionDetails ? (\n        getLoadingElement()\n    ) : (\n        <div>\n            <Card.Header className={styles.auctionCardHeader}>\n                <Link href={'/item/' + auctionDetails.auction.item.tag} className=\"disableLinkStyle\">\n                    <h1>\n                        <span className={styles.itemIcon}>\n                            <Image\n                                crossOrigin=\"anonymous\"\n                                src={api.getItemImageUrl(auctionDetails.auction.item) || ''}\n                                height={48}\n                                width={48}\n                                alt=\"item icon\"\n                                loading=\"lazy\"\n                            />\n                        </span>\n                        <span style={{ paddingLeft: '10px', display: 'flex', justifyContent: 'center' }}>\n                            <span style={{ marginRight: '10px' }}>\n                                {auctionDetails?.auction.item.name?.includes('§')\n                                    ? getMinecraftColorCodedElement(auctionDetails?.auction.item.name)\n                                    : getDungeonStarFormattedItemName(\n                                          auctionDetails.auction.item.name,\n                                          getStyleForTier(auctionDetails.auction.item.tier),\n                                          auctionDetails?.nbtData['dungeon_item_level']\n                                      )}\n                            </span>\n                            <Badge bg={countBadgeVariant} style={{ marginLeft: '5px' }}>\n                                x{auctionDetails?.count}\n                            </Badge>\n                            {auctionDetails.auction.bin ? (\n                                <Badge bg={binBadgeVariant} style={{ marginLeft: '5px' }}>\n                                    BIN\n                                </Badge>\n                            ) : (\n                                ''\n                            )}\n                        </span>\n                    </h1>\n                </Link>\n                <div className={styles.cardHeadSubtext}>\n                    <OverlayTrigger\n                        overlay={<TooltipBootstrap id={generateUUID()}>{getTimeToolTipString()}</TooltipBootstrap>}\n                        children={\n                            <div>\n                                {isRunning(auctionDetails) ? (\n                                    <span>\n                                        End: {auctionDetails?.auction.end ? <Countdown date={auctionDetails.auction.end} onComplete={forceUpdate} /> : '-'}\n                                    </span>\n                                ) : (\n                                    <span>\n                                        Auction ended{' '}\n                                        {auctionDetails.auction.bin && auctionDetails.bids.length > 0\n                                            ? moment(auctionDetails.bids[0].timestamp).fromNow()\n                                            : moment(auctionDetails.auction.end).fromNow()}\n                                    </span>\n                                )}\n                            </div>\n                        }\n                    ></OverlayTrigger>\n                    {isRunning(auctionDetails) ? (\n                        <div>\n                            <SubscribeButton\n                                type=\"auction\"\n                                topic={auctionDetails.auction.uuid}\n                                buttonContent={<span className={styles.topRowButtonContent}>Notify</span>}\n                            />\n                        </div>\n                    ) : (\n                        ''\n                    )}\n                    <CopyButton\n                        buttonVariant=\"primary\"\n                        copyValue={getCopyButtonValue(auctionDetails)}\n                        successMessage={\n                            getCopyButtonValue(auctionDetails)?.startsWith('/viewauction') ? (\n                                <p>\n                                    Copied ingame link <br />\n                                    <i>/viewauction {auctionDetails.auction.uuid}</i>\n                                </p>\n                            ) : (\n                                <p>Copied link to clipboard</p>\n                            )\n                        }\n                        buttonContent={<span className={styles.topRowButtonContent}>Copy</span>}\n                    />\n                    <Button\n                        onClick={() => {\n                            setShowBasedOnDialog(true)\n                        }}\n                    >\n                        <HelpIcon />\n                        <span className={styles.topRowButtonContent}>Compare to ended auctions</span>\n                    </Button>\n                    {auctionDetails?.nbtData.uid ? (\n                        <Button\n                            onClick={() => {\n                                setShowItemHistoryDialog(true)\n                            }}\n                        >\n                            <HelpIcon />\n                            <span className={styles.topRowButtonContent}>Show previous owners</span>\n                        </Button>\n                    ) : null}\n                </div>\n            </Card.Header>\n            <Card.Body>\n                <div className={styles.detailContainer}>\n                    <div className={styles.detailRow}>\n                        <span className={styles.label}>\n                            <Badge bg={labelBadgeVariant}>Tier:</Badge>\n                        </span>\n                        <span className=\"ellipse\" style={getStyleForTier(auctionDetails.auction.item.tier)}>\n                            {auctionDetails?.auction.item.tier}\n                        </span>\n                    </div>\n                    <div className={styles.detailRow}>\n                        <span className={styles.label}>\n                            <Badge bg={labelBadgeVariant}>Category:</Badge>\n                        </span>\n                        <span className=\"ellipse\">{convertTagToName(auctionDetails?.auction.item.category)}</span>\n                    </div>\n                    <div className={styles.detailRow}>\n                        <span className={styles.label}>\n                            <Badge bg={labelBadgeVariant}>Reforge:</Badge>\n                        </span>\n                        <span className=\"ellipse\">{auctionDetails?.reforge}</span>\n                    </div>\n                    <div className={styles.detailRow}>\n                        <span className={styles.label}>\n                            <Badge bg={labelBadgeVariant}>Auctioneer:</Badge>\n                        </span>\n                        <Link href={`/player/${auctionDetails.auctioneer.uuid}`} className=\"ellipse\">\n                            {auctionDetails?.auctioneer.name}\n                            <Image\n                                crossOrigin=\"anonymous\"\n                                className=\"playerHeadIcon\"\n                                src={auctionDetails?.auctioneer.iconUrl || ''}\n                                alt=\"auctioneer icon\"\n                                height=\"16\"\n                                width=\"16\"\n                                style={{ marginLeft: '5px' }}\n                                loading=\"lazy\"\n                            />\n                        </Link>\n                    </div>\n                    <div className={styles.detailRow}>\n                        <span className={styles.label}>\n                            <Badge bg={labelBadgeVariant}>Auction Created:</Badge>\n                        </span>\n                        <span className=\"ellipse\">{auctionDetails?.start.toLocaleDateString() + ' ' + auctionDetails.start.toLocaleTimeString()}</span>\n                    </div>\n                    {auctionDetails?.itemCreatedAt?.getTime() > 0 ? (\n                        <div className={styles.detailRow}>\n                            <span className={styles.label}>\n                                <Badge bg={labelBadgeVariant}>Item Created:</Badge>\n                            </span>\n                            <span className=\"ellipse\">\n                                {auctionDetails?.itemCreatedAt.toLocaleDateString() + ' ' + auctionDetails.itemCreatedAt.toLocaleTimeString()}\n                            </span>\n                        </div>\n                    ) : null}\n                </div>\n                <div style={{ overflow: 'auto', marginTop: '15px' }}>\n                    <span className={auctionDetails && auctionDetails!.enchantments.length > 0 ? styles.labelForList : styles.label}>\n                        <Badge bg={labelBadgeVariant}>Enchantments:</Badge>\n                    </span>\n                    {auctionDetails && auctionDetails!.enchantments.length > 0 ? (\n                        <ul className={styles.list}>\n                            {auctionDetails?.enchantments.map(enchantment => {\n                                if (enchantment.name === 'Ultimate Reiterate') {\n                                    enchantment.name = 'Ultimate Duplex'\n                                }\n                                let enchantmentString = <span>{enchantment.name}</span>\n\n                                if (enchantment.color) {\n                                    enchantmentString = getMinecraftColorCodedElement(enchantment.color + enchantment.name + ' ' + enchantment.level)\n                                }\n                                return enchantment.name ? <li key={enchantment.name}>{enchantmentString}</li> : ''\n                            })}\n                        </ul>\n                    ) : (\n                        <span>None</span>\n                    )}\n                </div>\n                <div style={{ marginTop: '15px' }}>{getNBTElement()}</div>\n            </Card.Body>\n        </div>\n    )\n\n    let bidList =\n        auctionDetails?.bids.length === 0 ? (\n            <p>No bids</p>\n        ) : (\n            auctionDetails?.bids.map((bid, i) => {\n                let headingStyle = i === 0 ? { color: 'green' } : { color: 'red' }\n                return (\n                    <Link href={`/player/${bid.bidder.uuid}`} key={'bid-' + i} className=\"disableLinkStyle\">\n                        <ListGroup.Item key={bid.amount} action>\n                            <Image\n                                crossOrigin=\"anonymous\"\n                                className=\"playerHeadIcon\"\n                                src={bid.bidder.iconUrl || ''}\n                                height=\"64\"\n                                width=\"64\"\n                                alt=\"bidder minecraft icon\"\n                                style={{ marginRight: '15px', float: 'left' }}\n                                loading=\"lazy\"\n                            />\n                            <h6 style={headingStyle}>\n                                <Number number={bid.amount} /> Coins\n                            </h6>\n                            <span>{bid.bidder.name}</span>\n                            <br />\n                            <span>{moment(bid.timestamp).fromNow()}</span>\n                        </ListGroup.Item>\n                    </Link>\n                )\n            })\n        )\n\n    return (\n        <div className={styles.auctionDetails}>\n            {isLoading ? (\n                getLoadingElement()\n            ) : isNoAuctionFound ? (\n                <div>\n                    <p>The auction you tried to see doesn't seem to exist. Please go back.</p>\n                    <br />\n                    <Link href=\"/\" className=\"disableLinkStyle\">\n                        <Button>Get back</Button>\n                    </Link>\n                </div>\n            ) : (\n                <div>\n                    <div>\n                        <Card className={`${styles.auctionCard} ${styles.firstCard}`}>{auctionCardContent}</Card>\n                        <Card className={styles.auctionCard}>\n                            <Card.Header>\n                                <h2>Bids</h2>\n                                {auctionDetails ? (\n                                    <h6>\n                                        Starting bid: <Number number={auctionDetails?.auction.startingBid} /> Coins\n                                    </h6>\n                                ) : (\n                                    ''\n                                )}\n                            </Card.Header>\n                            <Card.Body>\n                                <ListGroup>{bidList || getLoadingElement()}</ListGroup>\n                            </Card.Body>\n                        </Card>\n                    </div>\n                </div>\n            )}\n            <div\n                style={{ cursor: 'pointer' }}\n                onClick={() => {\n                    setShowFilterChecker(!showFilterChecker)\n                }}\n            >\n                Show filter checker {showFilterChecker ? <ArrowDownIcon /> : <ArrowRightIcon />}\n            </div>\n            {showFilterChecker && unparsedAuctionDetails ? (\n                <div style={{ minHeight: 400 }}>\n                    <FilterChecker auctionToCheck={unparsedAuctionDetails} />\n                </div>\n            ) : null}\n            {basedOnDialog}\n            {previousOwnersDialog}\n        </div>\n    )\n}\n\nexport default AuctionDetails\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;;;AA5BA;;;;;;;;;;;;;;;;;;;;;;;;;;AAsCA,SAAS,eAAe,KAAY;;IAChC,IAAI,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,IAAI,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA8B,MAAM,cAAc,GAAG,CAAA,GAAA,wIAAA,CAAA,sBAAmB,AAAD,EAAE,MAAM,cAAc,IAAI;IAClJ,IAAI,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,MAAM,sBAAsB;IAC/F,IAAI,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,IAAI,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,IAAI,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,IAAI,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,IAAI,cAAc,CAAA,GAAA,kHAAA,CAAA,iBAAc,AAAD;IAE/B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACN,+BAA+B;YAC/B,uGAAuG;YACvG,8CAA8C;YAC9C,IAAI,CAAC,MAAM,WAAW,IAAI,MAAM,cAAc,EAAE;gBAC5C;YACJ;YACA,mBAAmB,MAAM,WAAW;QACxC;mCAAG;QAAC,MAAM,WAAW;KAAC;IAEtB,IAAI,YAAY;IAChB,SAAS,mBAAmB,WAAmB;QAC3C,+FAA+F;QAC/F,IAAI,CAAC,gBAAgB;YACjB,aAAa;QACjB;QACA,oHAAA,CAAA,UAAG,CAAC,iBAAiB,CAAC,aACjB,IAAI,CAAC,CAAA;YACF,0BAA0B,OAAO,QAAQ;YACzC,IAAI,iBAAiB,OAAO,MAAM;YAClC,eAAe,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,MAAM,GAAG,EAAE,MAAM;YACtD,eAAe,OAAO,CAAC,IAAI,CAAC,OAAO,GAAG,oHAAA,CAAA,UAAG,CAAC,eAAe,CAAC,eAAe,OAAO,CAAC,IAAI;YACrF,kBAAkB;YAClB,oHAAA,CAAA,UAAG,CAAC,cAAc,CAAC,eAAe,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAA;gBACrD,IAAI,CAAC,eAAe,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE;oBACnC,eAAe,OAAO,CAAC,IAAI,CAAC,IAAI,GAAG,KAAK,IAAI;gBAChD;gBACA,kBAAkB;gBAClB;YACJ;YAEA,IAAI,eAAgC,EAAE;YACtC,eAAe,IAAI,CAAC,OAAO,CAAC,CAAA;gBACxB,IAAI,UAAU,oHAAA,CAAA,UAAG,CAAC,aAAa,CAAC,IAAI,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;oBAClD,IAAI,MAAM,CAAC,IAAI,GAAG;gBACtB;gBACA,aAAa,IAAI,CAAC;YACtB;YACA,aAAa,IAAI,CACb,oHAAA,CAAA,UAAG,CAAC,aAAa,CAAC,eAAe,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;gBACnD,eAAe,UAAU,CAAC,IAAI,GAAG;YACrC;YAEJ,QAAQ,GAAG,CAAC,cAAc,IAAI,CAAC;gBAC3B;gBACA,aAAa;YACjB;QACJ,GACC,KAAK,CAAC,CAAA;YACH,aAAa;YACb,IAAI,YAAY,CAAC,MAAM,YAAY,IAAI,CAAC,GAAG;gBACvC;gBACA,WAAW;oBACP,mBAAmB;gBACvB,GAAG;YACP,OAAO;gBACH,oBAAoB;gBACpB,IAAI,OAAO;oBACP,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,OAAO;gBAC7B;YACJ;QACJ;IACR;IAEA,IAAI,YAAY,CAAC;QACb,OAAO,eAAe,OAAO,CAAC,GAAG,CAAC,OAAO,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,eAAe,OAAO,CAAC,GAAG,IAAI,eAAe,IAAI,CAAC,MAAM,GAAG,CAAC;IAC/H;IAEA,IAAI,uBAAuB;QACvB,IAAI,CAAC,gBAAgB;YACjB,OAAO;QACX;QAEA,IAAI,gBAAgB,QAAQ,OAAO,eAAe,OAAO,CAAC,UAAU,GAAG,KAAK,eAAe,IAAI,CAAC,MAAM,GAAG,GAAG;YACxG,OAAO,CAAA,GAAA,mIAAA,CAAA,UAAM,AAAD,EAAE,eAAe,IAAI,CAAC,EAAE,CAAC,SAAS,EAAE,MAAM,CAAC;QAC3D;QACA,OAAO,CAAA,GAAA,mIAAA,CAAA,UAAM,AAAD,EAAE,eAAe,OAAO,CAAC,GAAG,EAAE,MAAM,CAAC;IACrD;IAEA,SAAS;QACL,IAAI,CAAC,gBAAgB,SAAS;YAC1B,OAAO;QACX;QACA,qBACI,6LAAC;YAAI,WAAW,6JAAA,CAAA,UAAM,CAAC,eAAe;sBACjC,OAAO,IAAI,CAAC,gBAAgB,SAAS,GAAG,CAAC,CAAA;gBACtC,IAAI,aAAa,gBAAgB,OAAO,CAAC,IAAI;gBAC7C,qBACI,6LAAC;oBAAI,WAAW,6JAAA,CAAA,UAAM,CAAC,SAAS;;sCAC5B,6LAAC;4BAAK,WAAW,6JAAA,CAAA,UAAM,CAAC,KAAK;sCACzB,cAAA,6LAAC,yLAAA,CAAA,QAAK;gCAAC,IAAI;;oCAAoB,aAAa;oCAAK;;;;;;;;;;;;sCAErD,6LAAC;4BAAK,WAAU;sCAAW,eAAe,KAAK,YAAY;;;;;;;mBAJxB;;;;;YAO/C;;;;;;IAGZ;IAEA,SAAS,mBAAmB,cAA8B;QACtD,IAAI,CAAC,CAAA,GAAA,qHAAA,CAAA,wBAAqB,AAAD,KAAK;YAC1B,OAAO;QACX;QACA,IAAI,MAAM,eAAe,KAAK,OAAO;YACjC,OAAO,GAAG,SAAS,MAAM,CAAC,SAAS,EAAE,eAAe,OAAO,CAAC,IAAI,EAAE;QACtE;QACA,OAAO,UAAU,kBAAkB,kBAAkB,eAAe,OAAO,CAAC,IAAI,GAAG,GAAG,SAAS,MAAM,CAAC,SAAS,EAAE,eAAe,OAAO,CAAC,IAAI,EAAE;IAClJ;IAEA,SAAS,aAAa,GAAW;QAC7B,IAAI,QAAQ,WAAW;YACnB,OAAO;QACX;QACA,OAAO,CAAA,GAAA,sHAAA,CAAA,mBAAgB,AAAD,EAAE;IAC5B;IAEA,SAAS,eAAe,GAAW,EAAE,KAAU,EAAE,cAA+B;QAC5E,IAAI,SAAS;YACT;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACH;QAED,IAAI,QAAQ,mBAAmB;YAC3B,IAAI,UAAU,KAAK;gBACf,OAAO;YACX;YACA,IAAI,UAAU,KAAK;gBACf,OAAO;YACX;YACA,OAAO;QACX;QAEA,IAAI,QAAQ,SAAS;YACjB,IAAI,YAAY,QAAQ,MAAM,KAAK,CAAC,OAAO,EAAE;YAC7C,IAAI,YAAsB,EAAE;YAC5B,UAAU,OAAO,CAAC,CAAA;gBACd,UAAU,IAAI,CAAC,SAAS,OAAO,QAAQ,CAAC,IAAI,QAAQ,CAAC,GAAG;YAC5D;YACA,qBACI,6LAAC,oIAAA,CAAA,UAAO;gBACJ,MAAK;gBACL,uBACI,6LAAC;;sCACG,6LAAC;sCAAM,UAAU,IAAI,CAAC;;;;;;wBAAY;sCAClC,6LAAC;4BACG,OAAO;gCACH,WAAW;gCACX,SAAS;gCACT,OAAO;gCACP,QAAQ;gCACR,iBAAiB,CAAC,CAAC,EAAE,UAAU,IAAI,CAAC,KAAK;gCACzC,cAAc;gCACd,aAAa;gCACb,QAAQ;gCACR,YAAY;4BAChB;;;;;;;;;;;;gBAIZ,gBAAgB;;;;;;QAG5B;QAEA,IAAI,QAAQ,QAAQ;YAChB,IAAI,OAAO,IAAI,KAAK,SAAS;YAC7B,OAAO,KAAK,kBAAkB,KAAK,MAAM,KAAK,kBAAkB;QACpE;QAEA,iIAAiI;QACjI,IAAI,CAAC,MAAM,UAAU,CAAC,MAAM,QAAQ,GAAG,QAAQ,CAAC,MAAM;YAClD,qBAAO,6LAAC,kIAAA,CAAA,UAAM;gBAAC,QAAQ;;;;;;QAC3B;QAEA,IAAI,QAAQ,OAAO,SAAS,CAAC,CAAA,MAAO,QAAQ;QAC5C,IAAI,UAAU,CAAC,GAAG;YACd,IAAI,QAAQ,UAAU,gBAAgB,SAAS,MAAM,KAAK,WAAW,SAAS;gBAC1E,qBAAO,6LAAC,+JAAA,CAAA,UAAI;oBAAC,MAAM,oBAAoB;8BAAQ,CAAA,GAAA,sHAAA,CAAA,mBAAgB,AAAD,EAAE;;;;;;YACpE;YACA,qBAAO,6LAAC,+JAAA,CAAA,UAAI;gBAAC,MAAM,WAAW;0BAAQ,CAAA,GAAA,sHAAA,CAAA,mBAAgB,AAAD,EAAE;;;;;;QAC3D;QAEA,IAAI,OAAO,WAAW,SAAS,MAAM;YACjC,OAAO,CAAA,GAAA,sHAAA,CAAA,gCAA6B,AAAD,EAAE,OAAO;QAChD;QAEA,OAAO,MAAM,QAAQ;IACzB;IAEA,MAAM,oBAAoB;IAC1B,MAAM,kBAAkB;IACxB,MAAM,oBAAoB;IAE1B,IAAI,gBAAgB,kCAChB,6LAAC,yLAAA,CAAA,QAAK;QACF,MAAM;QACN,MAAM;QACN,QAAQ;YACJ,qBAAqB;QACzB;;0BAEA,6LAAC,yLAAA,CAAA,QAAK,CAAC,MAAM;gBAAC,WAAW;0BACrB,cAAA,6LAAC,yLAAA,CAAA,QAAK,CAAC,KAAK;8BAAC;;;;;;;;;;;0BAEjB,6LAAC,yLAAA,CAAA,QAAK,CAAC,IAAI;0BAAE,+BAAiB,6LAAC,mJAAA,CAAA,UAAS;oBAAC,aAAa,eAAe,OAAO,CAAC,IAAI;oBAAE,MAAM,eAAe,OAAO,CAAC,IAAI;;;;;2BAAO;;;;;;;;;;;eAE/H;IAEJ,IAAI,uBAAuB,sCACvB,6LAAC,yLAAA,CAAA,QAAK;QACF,MAAM;QACN,MAAM;QACN,QAAQ;YACJ,yBAAyB;QAC7B;;0BAEA,6LAAC,yLAAA,CAAA,QAAK,CAAC,MAAM;gBAAC,WAAW;0BACrB,cAAA,6LAAC,yLAAA,CAAA,QAAK,CAAC,KAAK;8BAAC;;;;;;;;;;;0BAEjB,6LAAC,yLAAA,CAAA,QAAK,CAAC,IAAI;0BACP,cAAA,6LAAC,8IAAA,CAAA,UAAW;oBAAC,KAAK,gBAAgB,QAAQ;;;;;;;;;;;;;;;;eAGlD;IAEJ,IAAI,qBAAqB,CAAC,iBACtB,CAAA,GAAA,yHAAA,CAAA,oBAAiB,AAAD,oBAEhB,6LAAC;;0BACG,6LAAC,uLAAA,CAAA,OAAI,CAAC,MAAM;gBAAC,WAAW,6JAAA,CAAA,UAAM,CAAC,iBAAiB;;kCAC5C,6LAAC,+JAAA,CAAA,UAAI;wBAAC,MAAM,WAAW,eAAe,OAAO,CAAC,IAAI,CAAC,GAAG;wBAAE,WAAU;kCAC9D,cAAA,6LAAC;;8CACG,6LAAC;oCAAK,WAAW,6JAAA,CAAA,UAAM,CAAC,QAAQ;8CAC5B,cAAA,6LAAC,gIAAA,CAAA,UAAK;wCACF,aAAY;wCACZ,KAAK,oHAAA,CAAA,UAAG,CAAC,eAAe,CAAC,eAAe,OAAO,CAAC,IAAI,KAAK;wCACzD,QAAQ;wCACR,OAAO;wCACP,KAAI;wCACJ,SAAQ;;;;;;;;;;;8CAGhB,6LAAC;oCAAK,OAAO;wCAAE,aAAa;wCAAQ,SAAS;wCAAQ,gBAAgB;oCAAS;;sDAC1E,6LAAC;4CAAK,OAAO;gDAAE,aAAa;4CAAO;sDAC9B,gBAAgB,QAAQ,KAAK,MAAM,SAAS,OACvC,CAAA,GAAA,sHAAA,CAAA,gCAA6B,AAAD,EAAE,gBAAgB,QAAQ,KAAK,QAC3D,CAAA,GAAA,sHAAA,CAAA,6BAA+B,AAAD,EAC1B,eAAe,OAAO,CAAC,IAAI,CAAC,IAAI,EAChC,CAAA,GAAA,sHAAA,CAAA,kBAAe,AAAD,EAAE,eAAe,OAAO,CAAC,IAAI,CAAC,IAAI,GAChD,gBAAgB,OAAO,CAAC,qBAAqB;;;;;;sDAG3D,6LAAC,yLAAA,CAAA,QAAK;4CAAC,IAAI;4CAAmB,OAAO;gDAAE,YAAY;4CAAM;;gDAAG;gDACtD,gBAAgB;;;;;;;wCAErB,eAAe,OAAO,CAAC,GAAG,iBACvB,6LAAC,yLAAA,CAAA,QAAK;4CAAC,IAAI;4CAAiB,OAAO;gDAAE,YAAY;4CAAM;sDAAG;;;;;mDAI1D;;;;;;;;;;;;;;;;;;kCAKhB,6LAAC;wBAAI,WAAW,6JAAA,CAAA,UAAM,CAAC,eAAe;;0CAClC,6LAAC,2MAAA,CAAA,iBAAc;gCACX,uBAAS,6LAAC,6LAAA,CAAA,UAAgB;oCAAC,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAY,AAAD;8CAAM;;;;;;gCAChD,wBACI,6LAAC;8CACI,UAAU,gCACP,6LAAC;;4CAAK;4CACI,gBAAgB,QAAQ,oBAAM,6LAAC,4JAAA,CAAA,UAAS;gDAAC,MAAM,eAAe,OAAO,CAAC,GAAG;gDAAE,YAAY;;;;;yDAAkB;;;;;;+DAGnH,6LAAC;;4CAAK;4CACY;4CACb,eAAe,OAAO,CAAC,GAAG,IAAI,eAAe,IAAI,CAAC,MAAM,GAAG,IACtD,CAAA,GAAA,mIAAA,CAAA,UAAM,AAAD,EAAE,eAAe,IAAI,CAAC,EAAE,CAAC,SAAS,EAAE,OAAO,KAChD,CAAA,GAAA,mIAAA,CAAA,UAAM,AAAD,EAAE,eAAe,OAAO,CAAC,GAAG,EAAE,OAAO;;;;;;;;;;;;;;;;;4BAMnE,UAAU,gCACP,6LAAC;0CACG,cAAA,6LAAC,oJAAA,CAAA,UAAe;oCACZ,MAAK;oCACL,OAAO,eAAe,OAAO,CAAC,IAAI;oCAClC,6BAAe,6LAAC;wCAAK,WAAW,6JAAA,CAAA,UAAM,CAAC,mBAAmB;kDAAE;;;;;;;;;;;;;;;uCAIpE;0CAEJ,6LAAC,0IAAA,CAAA,aAAU;gCACP,eAAc;gCACd,WAAW,mBAAmB;gCAC9B,gBACI,mBAAmB,iBAAiB,WAAW,gCAC3C,6LAAC;;wCAAE;sDACoB,6LAAC;;;;;sDACpB,6LAAC;;gDAAE;gDAAc,eAAe,OAAO,CAAC,IAAI;;;;;;;;;;;;2DAGhD,6LAAC;8CAAE;;;;;;gCAGX,6BAAe,6LAAC;oCAAK,WAAW,6JAAA,CAAA,UAAM,CAAC,mBAAmB;8CAAE;;;;;;;;;;;0CAEhE,6LAAC,2LAAA,CAAA,SAAM;gCACH,SAAS;oCACL,qBAAqB;gCACzB;;kDAEA,6LAAC,4JAAA,CAAA,UAAQ;;;;;kDACT,6LAAC;wCAAK,WAAW,6JAAA,CAAA,UAAM,CAAC,mBAAmB;kDAAE;;;;;;;;;;;;4BAEhD,gBAAgB,QAAQ,oBACrB,6LAAC,2LAAA,CAAA,SAAM;gCACH,SAAS;oCACL,yBAAyB;gCAC7B;;kDAEA,6LAAC,4JAAA,CAAA,UAAQ;;;;;kDACT,6LAAC;wCAAK,WAAW,6JAAA,CAAA,UAAM,CAAC,mBAAmB;kDAAE;;;;;;;;;;;uCAEjD;;;;;;;;;;;;;0BAGZ,6LAAC,uLAAA,CAAA,OAAI,CAAC,IAAI;;kCACN,6LAAC;wBAAI,WAAW,6JAAA,CAAA,UAAM,CAAC,eAAe;;0CAClC,6LAAC;gCAAI,WAAW,6JAAA,CAAA,UAAM,CAAC,SAAS;;kDAC5B,6LAAC;wCAAK,WAAW,6JAAA,CAAA,UAAM,CAAC,KAAK;kDACzB,cAAA,6LAAC,yLAAA,CAAA,QAAK;4CAAC,IAAI;sDAAmB;;;;;;;;;;;kDAElC,6LAAC;wCAAK,WAAU;wCAAU,OAAO,CAAA,GAAA,sHAAA,CAAA,kBAAe,AAAD,EAAE,eAAe,OAAO,CAAC,IAAI,CAAC,IAAI;kDAC5E,gBAAgB,QAAQ,KAAK;;;;;;;;;;;;0CAGtC,6LAAC;gCAAI,WAAW,6JAAA,CAAA,UAAM,CAAC,SAAS;;kDAC5B,6LAAC;wCAAK,WAAW,6JAAA,CAAA,UAAM,CAAC,KAAK;kDACzB,cAAA,6LAAC,yLAAA,CAAA,QAAK;4CAAC,IAAI;sDAAmB;;;;;;;;;;;kDAElC,6LAAC;wCAAK,WAAU;kDAAW,CAAA,GAAA,sHAAA,CAAA,mBAAgB,AAAD,EAAE,gBAAgB,QAAQ,KAAK;;;;;;;;;;;;0CAE7E,6LAAC;gCAAI,WAAW,6JAAA,CAAA,UAAM,CAAC,SAAS;;kDAC5B,6LAAC;wCAAK,WAAW,6JAAA,CAAA,UAAM,CAAC,KAAK;kDACzB,cAAA,6LAAC,yLAAA,CAAA,QAAK;4CAAC,IAAI;sDAAmB;;;;;;;;;;;kDAElC,6LAAC;wCAAK,WAAU;kDAAW,gBAAgB;;;;;;;;;;;;0CAE/C,6LAAC;gCAAI,WAAW,6JAAA,CAAA,UAAM,CAAC,SAAS;;kDAC5B,6LAAC;wCAAK,WAAW,6JAAA,CAAA,UAAM,CAAC,KAAK;kDACzB,cAAA,6LAAC,yLAAA,CAAA,QAAK;4CAAC,IAAI;sDAAmB;;;;;;;;;;;kDAElC,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAM,CAAC,QAAQ,EAAE,eAAe,UAAU,CAAC,IAAI,EAAE;wCAAE,WAAU;;4CAC9D,gBAAgB,WAAW;0DAC5B,6LAAC,gIAAA,CAAA,UAAK;gDACF,aAAY;gDACZ,WAAU;gDACV,KAAK,gBAAgB,WAAW,WAAW;gDAC3C,KAAI;gDACJ,QAAO;gDACP,OAAM;gDACN,OAAO;oDAAE,YAAY;gDAAM;gDAC3B,SAAQ;;;;;;;;;;;;;;;;;;0CAIpB,6LAAC;gCAAI,WAAW,6JAAA,CAAA,UAAM,CAAC,SAAS;;kDAC5B,6LAAC;wCAAK,WAAW,6JAAA,CAAA,UAAM,CAAC,KAAK;kDACzB,cAAA,6LAAC,yLAAA,CAAA,QAAK;4CAAC,IAAI;sDAAmB;;;;;;;;;;;kDAElC,6LAAC;wCAAK,WAAU;kDAAW,gBAAgB,MAAM,uBAAuB,MAAM,eAAe,KAAK,CAAC,kBAAkB;;;;;;;;;;;;4BAExH,gBAAgB,eAAe,YAAY,kBACxC,6LAAC;gCAAI,WAAW,6JAAA,CAAA,UAAM,CAAC,SAAS;;kDAC5B,6LAAC;wCAAK,WAAW,6JAAA,CAAA,UAAM,CAAC,KAAK;kDACzB,cAAA,6LAAC,yLAAA,CAAA,QAAK;4CAAC,IAAI;sDAAmB;;;;;;;;;;;kDAElC,6LAAC;wCAAK,WAAU;kDACX,gBAAgB,cAAc,uBAAuB,MAAM,eAAe,aAAa,CAAC,kBAAkB;;;;;;;;;;;uCAGnH;;;;;;;kCAER,6LAAC;wBAAI,OAAO;4BAAE,UAAU;4BAAQ,WAAW;wBAAO;;0CAC9C,6LAAC;gCAAK,WAAW,kBAAkB,eAAgB,YAAY,CAAC,MAAM,GAAG,IAAI,6JAAA,CAAA,UAAM,CAAC,YAAY,GAAG,6JAAA,CAAA,UAAM,CAAC,KAAK;0CAC3G,cAAA,6LAAC,yLAAA,CAAA,QAAK;oCAAC,IAAI;8CAAmB;;;;;;;;;;;4BAEjC,kBAAkB,eAAgB,YAAY,CAAC,MAAM,GAAG,kBACrD,6LAAC;gCAAG,WAAW,6JAAA,CAAA,UAAM,CAAC,IAAI;0CACrB,gBAAgB,aAAa,IAAI,CAAA;oCAC9B,IAAI,YAAY,IAAI,KAAK,sBAAsB;wCAC3C,YAAY,IAAI,GAAG;oCACvB;oCACA,IAAI,kCAAoB,6LAAC;kDAAM,YAAY,IAAI;;;;;;oCAE/C,IAAI,YAAY,KAAK,EAAE;wCACnB,oBAAoB,CAAA,GAAA,sHAAA,CAAA,gCAA6B,AAAD,EAAE,YAAY,KAAK,GAAG,YAAY,IAAI,GAAG,MAAM,YAAY,KAAK;oCACpH;oCACA,OAAO,YAAY,IAAI,iBAAG,6LAAC;kDAA2B;uCAAnB,YAAY,IAAI;;;;+CAA6B;gCACpF;;;;;qDAGJ,6LAAC;0CAAK;;;;;;;;;;;;kCAGd,6LAAC;wBAAI,OAAO;4BAAE,WAAW;wBAAO;kCAAI;;;;;;;;;;;;;;;;;;IAKhD,IAAI,UACA,gBAAgB,KAAK,WAAW,kBAC5B,6LAAC;kBAAE;;;;;eAEH,gBAAgB,KAAK,IAAI,CAAC,KAAK;QAC3B,IAAI,eAAe,MAAM,IAAI;YAAE,OAAO;QAAQ,IAAI;YAAE,OAAO;QAAM;QACjE,qBACI,6LAAC,+JAAA,CAAA,UAAI;YAAC,MAAM,CAAC,QAAQ,EAAE,IAAI,MAAM,CAAC,IAAI,EAAE;YAAmB,WAAU;sBACjE,cAAA,6LAAC,iMAAA,CAAA,YAAS,CAAC,IAAI;gBAAkB,MAAM;;kCACnC,6LAAC,gIAAA,CAAA,UAAK;wBACF,aAAY;wBACZ,WAAU;wBACV,KAAK,IAAI,MAAM,CAAC,OAAO,IAAI;wBAC3B,QAAO;wBACP,OAAM;wBACN,KAAI;wBACJ,OAAO;4BAAE,aAAa;4BAAQ,OAAO;wBAAO;wBAC5C,SAAQ;;;;;;kCAEZ,6LAAC;wBAAG,OAAO;;0CACP,6LAAC,kIAAA,CAAA,UAAM;gCAAC,QAAQ,IAAI,MAAM;;;;;;4BAAI;;;;;;;kCAElC,6LAAC;kCAAM,IAAI,MAAM,CAAC,IAAI;;;;;;kCACtB,6LAAC;;;;;kCACD,6LAAC;kCAAM,CAAA,GAAA,mIAAA,CAAA,UAAM,AAAD,EAAE,IAAI,SAAS,EAAE,OAAO;;;;;;;eAhBnB,IAAI,MAAM;;;;;WADY,SAAS;;;;;IAqBhE;IAGR,qBACI,6LAAC;QAAI,WAAW,6JAAA,CAAA,UAAM,CAAC,cAAc;;YAChC,YACG,CAAA,GAAA,yHAAA,CAAA,oBAAiB,AAAD,MAChB,iCACA,6LAAC;;kCACG,6LAAC;kCAAE;;;;;;kCACH,6LAAC;;;;;kCACD,6LAAC,+JAAA,CAAA,UAAI;wBAAC,MAAK;wBAAI,WAAU;kCACrB,cAAA,6LAAC,2LAAA,CAAA,SAAM;sCAAC;;;;;;;;;;;;;;;;qCAIhB,6LAAC;0BACG,cAAA,6LAAC;;sCACG,6LAAC,uLAAA,CAAA,OAAI;4BAAC,WAAW,GAAG,6JAAA,CAAA,UAAM,CAAC,WAAW,CAAC,CAAC,EAAE,6JAAA,CAAA,UAAM,CAAC,SAAS,EAAE;sCAAG;;;;;;sCAC/D,6LAAC,uLAAA,CAAA,OAAI;4BAAC,WAAW,6JAAA,CAAA,UAAM,CAAC,WAAW;;8CAC/B,6LAAC,uLAAA,CAAA,OAAI,CAAC,MAAM;;sDACR,6LAAC;sDAAG;;;;;;wCACH,+BACG,6LAAC;;gDAAG;8DACc,6LAAC,kIAAA,CAAA,UAAM;oDAAC,QAAQ,gBAAgB,QAAQ;;;;;;gDAAe;;;;;;mDAGzE;;;;;;;8CAGR,6LAAC,uLAAA,CAAA,OAAI,CAAC,IAAI;8CACN,cAAA,6LAAC,iMAAA,CAAA,YAAS;kDAAE,WAAW,CAAA,GAAA,yHAAA,CAAA,oBAAiB,AAAD;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAM3D,6LAAC;gBACG,OAAO;oBAAE,QAAQ;gBAAU;gBAC3B,SAAS;oBACL,qBAAqB,CAAC;gBAC1B;;oBACH;oBACwB,kCAAoB,6LAAC,qKAAA,CAAA,UAAa;;;;6CAAM,6LAAC,kKAAA,CAAA,UAAc;;;;;;;;;;;YAE/E,qBAAqB,uCAClB,6LAAC;gBAAI,OAAO;oBAAE,WAAW;gBAAI;0BACzB,cAAA,6LAAC,gJAAA,CAAA,gBAAa;oBAAC,gBAAgB;;;;;;;;;;uBAEnC;YACH;YACA;;;;;;;AAGb;GA3hBS;;QAQa,kHAAA,CAAA,iBAAc;;;KAR3B;uCA6hBM", "debugId": null}}, {"offset": {"line": 6970, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/FilterChecker/FilterChecker.tsx"], "sourcesContent": ["'use client'\nimport React, { useEffect, useState } from 'react'\nimport { Button } from 'react-bootstrap'\nimport api from '../../api/ApiHelper'\nimport ItemFilter from '../ItemFilter/ItemFilter'\nimport { ArrowRightAlt } from '@mui/icons-material'\n\ninterface Props {\n    auctionToCheck: any\n}\n\nexport function FilterChecker(props: Props) {\n    let [filter, setFilter] = useState<ItemFilter>({})\n    let [filterOptions, setFilterOptions] = useState<FilterOptions[]>([])\n    let [hasFilterApplied, setHasFilterApplied] = useState<boolean | null>(null)\n    let [disabled, setDisabled] = useState(true)\n    let [isLoading, setIsLoading] = useState(false)\n    let [isFilterValid, setIsFilterValid] = useState(true)\n\n    useEffect(() => {\n        api.getFilters(props.auctionToCheck.tag).then(setFilterOptions)\n    }, [])\n\n    function onFilterChange(newFilter: ItemFilter) {\n        let toBeDisabled = !newFilter || Object.keys(newFilter).length === 0\n        if (toBeDisabled) {\n            setHasFilterApplied(null)\n        }\n        setDisabled(toBeDisabled)\n        setFilter(newFilter)\n    }\n\n    function onCheck() {\n        setDisabled(true)\n        setIsLoading(true)\n        api.checkFilter({ ...props.auctionToCheck, bids: [] }, filter)\n            .then(result => {\n                setHasFilterApplied(result)\n            })\n            .finally(() => {\n                setIsLoading(false)\n                setDisabled(false)\n            })\n    }\n\n    return (\n        <>\n            <ItemFilter\n                ignoreURL={true}\n                forceOpen={true}\n                autoSelect={false}\n                onFilterChange={onFilterChange}\n                filters={filterOptions}\n                onIsValidChange={setIsFilterValid}\n            />\n            <div>\n                <Button onClick={onCheck} disabled={disabled || !isFilterValid} style={{ width: '100%' }}>\n                    {!isLoading ? 'Check filter' : 'Loading...'}\n                </Button>\n                {hasFilterApplied != null ? (\n                    <p style={{ color: hasFilterApplied ? 'lime' : 'red', fontSize: 'large', fontWeight: 'bold' }}>\n                        <ArrowRightAlt />\n                        {hasFilterApplied ? 'Filter applies to this auction' : 'Filter does not apply to this auction'}\n                    </p>\n                ) : null}\n            </div>\n        </>\n    )\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;;;AALA;;;;;;AAWO,SAAS,cAAc,KAAY;;IACtC,IAAI,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,CAAC;IAChD,IAAI,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IACpE,IAAI,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IACvE,IAAI,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,IAAI,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,IAAI,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACN,oHAAA,CAAA,UAAG,CAAC,UAAU,CAAC,MAAM,cAAc,CAAC,GAAG,EAAE,IAAI,CAAC;QAClD;kCAAG,EAAE;IAEL,SAAS,eAAe,SAAqB;QACzC,IAAI,eAAe,CAAC,aAAa,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;QACnE,IAAI,cAAc;YACd,oBAAoB;QACxB;QACA,YAAY;QACZ,UAAU;IACd;IAEA,SAAS;QACL,YAAY;QACZ,aAAa;QACb,oHAAA,CAAA,UAAG,CAAC,WAAW,CAAC;YAAE,GAAG,MAAM,cAAc;YAAE,MAAM,EAAE;QAAC,GAAG,QAClD,IAAI,CAAC,CAAA;YACF,oBAAoB;QACxB,GACC,OAAO,CAAC;YACL,aAAa;YACb,YAAY;QAChB;IACR;IAEA,qBACI;;0BACI,6LAAC,0IAAA,CAAA,UAAU;gBACP,WAAW;gBACX,WAAW;gBACX,YAAY;gBACZ,gBAAgB;gBAChB,SAAS;gBACT,iBAAiB;;;;;;0BAErB,6LAAC;;kCACG,6LAAC,2LAAA,CAAA,SAAM;wBAAC,SAAS;wBAAS,UAAU,YAAY,CAAC;wBAAe,OAAO;4BAAE,OAAO;wBAAO;kCAClF,CAAC,YAAY,iBAAiB;;;;;;oBAElC,oBAAoB,qBACjB,6LAAC;wBAAE,OAAO;4BAAE,OAAO,mBAAmB,SAAS;4BAAO,UAAU;4BAAS,YAAY;wBAAO;;0CACxF,6LAAC,qKAAA,CAAA,UAAa;;;;;4BACb,mBAAmB,mCAAmC;;;;;;+BAE3D;;;;;;;;;AAIpB;GAzDgB;KAAA", "debugId": null}}, {"offset": {"line": 7089, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/components/OwnerHistory/OwnerHistory.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"list\": \"OwnerHistory-module__VV4xVG__list\",\n  \"listGroupItem\": \"OwnerHistory-module__VV4xVG__listGroupItem\",\n  \"playerField\": \"OwnerHistory-module__VV4xVG__playerField\",\n  \"playerFieldHeader\": \"OwnerHistory-module__VV4xVG__playerFieldHeader\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 7101, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/OwnerHistory/OwnerHistory.tsx"], "sourcesContent": ["'use client'\nimport React, { useEffect, useState } from 'react'\nimport { Card, ListGroup } from 'react-bootstrap'\nimport api from '../../api/ApiHelper'\nimport { getLoadingElement } from '../../utils/LoadingUtils'\nimport styles from './OwnerHistory.module.css'\nimport ArrowRightIcon from '@mui/icons-material/ArrowRightAlt'\nimport moment from 'moment'\nimport OpenInNewIcon from '@mui/icons-material/OpenInNew'\nimport Number from '../Number/Number'\n\ninterface Props {\n    uid: string\n}\n\nfunction ItemHistory(props: Props) {\n    let [historyEntries, setHistoryEntries] = useState<OwnerHistory[]>([])\n    let [isLoading, setIsLoading] = useState(true)\n\n    useEffect(() => {\n        loadOwnerHistory()\n    }, [])\n\n    async function loadOwnerHistory() {\n        setIsLoading(true)\n\n        let ownerHistory = await api.getOwnerHistory(props.uid)\n\n        let prevOwnerObjects: OwnerHistory[] = []\n\n        let namesToFetch: string[] = []\n        ownerHistory.forEach(history => {\n            namesToFetch.push(history.buyer.uuid)\n            namesToFetch.push(history.seller.uuid)\n        })\n        let names = await api.getPlayerNames(namesToFetch)\n\n        ownerHistory.forEach(history => {\n            history.seller.name = names[history.seller.uuid]\n            history.buyer.name = names[history.buyer.uuid]\n\n            prevOwnerObjects.push(history)\n        })\n\n        let sorted = prevOwnerObjects.sort((a, b) => b.timestamp!.getTime() - a.timestamp!.getTime())\n        for (let i = 0; i < sorted.length; i++) {\n            let entry = sorted[i]\n            if (i === 0) {\n                continue\n            }\n            if (entry.buyer.uuid !== sorted[i - 1].seller.uuid) {\n                sorted.splice(i, 0, {\n                    highestBid: -1,\n                    itemTag: sorted[i - 1].itemTag,\n                    timestamp: null,\n                    buyer: sorted[i - 1].seller,\n                    seller: entry.buyer,\n                    uuid: entry.uuid\n                })\n                i++\n            }\n        }\n        setHistoryEntries(prevOwnerObjects)\n        setIsLoading(false)\n    }\n\n    function getPlayerElement(player: Player) {\n        return (\n            <div className={styles.playerElement}>\n                <img\n                    crossOrigin=\"anonymous\"\n                    className=\"playerHeadIcon\"\n                    src={player.iconUrl}\n                    alt=\"player icon\"\n                    height=\"36\"\n                    style={{ marginRight: '10px' }}\n                    loading=\"lazy\"\n                />\n                {player.name}\n            </div>\n        )\n    }\n\n    return (\n        <>\n            {isLoading ? (\n                getLoadingElement()\n            ) : (\n                <ListGroup className={styles.list}>\n                    {historyEntries.map((historyEntry, i) => {\n                        return (\n                            <ListGroup.Item className={styles.listGroupItem}>\n                                <h1 style={{ fontSize: 'large' }}>\n                                    <img\n                                        crossOrigin=\"anonymous\"\n                                        className=\"playerHeadIcon\"\n                                        src={api.getItemImageUrl({\n                                            tag: historyEntry.itemTag\n                                        })}\n                                        alt=\"player icon\"\n                                        height=\"36\"\n                                        style={{ marginRight: '10px' }}\n                                        loading=\"lazy\"\n                                    />\n                                    {historyEntry.timestamp ? moment(historyEntry.timestamp).format('MMMM Do YYYY, h:mm:ss a') : 'Outside of AH'}\n                                    {historyEntry.uuid ? (\n                                        <a style={{ float: 'right', cursor: 'pointer' }} href={`/auction/${historyEntry.uuid}`} className=\"disableLinkStyle\">\n                                            Open auction\n                                            <OpenInNewIcon />\n                                        </a>\n                                    ) : null}\n                                </h1>\n                                <hr />\n                                <div style={{ display: 'flex', justifyContent: 'space-around', alignItems: 'center' }}>\n                                    <Card className={styles.playerField}>\n                                        <a href={`/player/${historyEntry.seller.uuid}`} target={'_blank'} className=\"disableLinkStyle\">\n                                            <Card.Header className={styles.playerFieldHeader}>\n                                                <Card.Title style={{ margin: 0 }}>{getPlayerElement(historyEntry.seller)}</Card.Title>\n                                            </Card.Header>\n                                        </a>\n                                    </Card>\n                                    <div style={{ display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center' }}>\n                                        <ArrowRightIcon style={{ fontSize: '50px' }} />\n                                        {historyEntry.highestBid === -1 ? null : <Number number={historyEntry.highestBid} />}\n                                    </div>\n                                    <Card className={styles.playerField}>\n                                        <a href={`/player/${historyEntry.buyer.uuid}`} target={'_blank'} className=\"disableLinkStyle\">\n                                            <Card.Header className={styles.playerFieldHeader}>\n                                                <Card.Title style={{ margin: 0 }}>{getPlayerElement(historyEntry.buyer)}</Card.Title>\n                                            </Card.Header>\n                                        </a>\n                                    </Card>\n                                </div>\n                            </ListGroup.Item>\n                        )\n                    })}\n                </ListGroup>\n            )}\n        </>\n    )\n}\n\nexport default ItemHistory\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AATA;;;;;;;;;;AAeA,SAAS,YAAY,KAAY;;IAC7B,IAAI,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACrE,IAAI,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACN;QACJ;gCAAG,EAAE;IAEL,eAAe;QACX,aAAa;QAEb,IAAI,eAAe,MAAM,oHAAA,CAAA,UAAG,CAAC,eAAe,CAAC,MAAM,GAAG;QAEtD,IAAI,mBAAmC,EAAE;QAEzC,IAAI,eAAyB,EAAE;QAC/B,aAAa,OAAO,CAAC,CAAA;YACjB,aAAa,IAAI,CAAC,QAAQ,KAAK,CAAC,IAAI;YACpC,aAAa,IAAI,CAAC,QAAQ,MAAM,CAAC,IAAI;QACzC;QACA,IAAI,QAAQ,MAAM,oHAAA,CAAA,UAAG,CAAC,cAAc,CAAC;QAErC,aAAa,OAAO,CAAC,CAAA;YACjB,QAAQ,MAAM,CAAC,IAAI,GAAG,KAAK,CAAC,QAAQ,MAAM,CAAC,IAAI,CAAC;YAChD,QAAQ,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,QAAQ,KAAK,CAAC,IAAI,CAAC;YAE9C,iBAAiB,IAAI,CAAC;QAC1B;QAEA,IAAI,SAAS,iBAAiB,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,SAAS,CAAE,OAAO,KAAK,EAAE,SAAS,CAAE,OAAO;QAC1F,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;YACpC,IAAI,QAAQ,MAAM,CAAC,EAAE;YACrB,IAAI,MAAM,GAAG;gBACT;YACJ;YACA,IAAI,MAAM,KAAK,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,EAAE,CAAC,MAAM,CAAC,IAAI,EAAE;gBAChD,OAAO,MAAM,CAAC,GAAG,GAAG;oBAChB,YAAY,CAAC;oBACb,SAAS,MAAM,CAAC,IAAI,EAAE,CAAC,OAAO;oBAC9B,WAAW;oBACX,OAAO,MAAM,CAAC,IAAI,EAAE,CAAC,MAAM;oBAC3B,QAAQ,MAAM,KAAK;oBACnB,MAAM,MAAM,IAAI;gBACpB;gBACA;YACJ;QACJ;QACA,kBAAkB;QAClB,aAAa;IACjB;IAEA,SAAS,iBAAiB,MAAc;QACpC,qBACI,6LAAC;YAAI,WAAW,yJAAA,CAAA,UAAM,CAAC,aAAa;;8BAChC,6LAAC;oBACG,aAAY;oBACZ,WAAU;oBACV,KAAK,OAAO,OAAO;oBACnB,KAAI;oBACJ,QAAO;oBACP,OAAO;wBAAE,aAAa;oBAAO;oBAC7B,SAAQ;;;;;;gBAEX,OAAO,IAAI;;;;;;;IAGxB;IAEA,qBACI;kBACK,YACG,CAAA,GAAA,yHAAA,CAAA,oBAAiB,AAAD,oBAEhB,6LAAC,iMAAA,CAAA,YAAS;YAAC,WAAW,yJAAA,CAAA,UAAM,CAAC,IAAI;sBAC5B,eAAe,GAAG,CAAC,CAAC,cAAc;gBAC/B,qBACI,6LAAC,iMAAA,CAAA,YAAS,CAAC,IAAI;oBAAC,WAAW,yJAAA,CAAA,UAAM,CAAC,aAAa;;sCAC3C,6LAAC;4BAAG,OAAO;gCAAE,UAAU;4BAAQ;;8CAC3B,6LAAC;oCACG,aAAY;oCACZ,WAAU;oCACV,KAAK,oHAAA,CAAA,UAAG,CAAC,eAAe,CAAC;wCACrB,KAAK,aAAa,OAAO;oCAC7B;oCACA,KAAI;oCACJ,QAAO;oCACP,OAAO;wCAAE,aAAa;oCAAO;oCAC7B,SAAQ;;;;;;gCAEX,aAAa,SAAS,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAM,AAAD,EAAE,aAAa,SAAS,EAAE,MAAM,CAAC,6BAA6B;gCAC5F,aAAa,IAAI,iBACd,6LAAC;oCAAE,OAAO;wCAAE,OAAO;wCAAS,QAAQ;oCAAU;oCAAG,MAAM,CAAC,SAAS,EAAE,aAAa,IAAI,EAAE;oCAAE,WAAU;;wCAAmB;sDAEjH,6LAAC,iKAAA,CAAA,UAAa;;;;;;;;;;2CAElB;;;;;;;sCAER,6LAAC;;;;;sCACD,6LAAC;4BAAI,OAAO;gCAAE,SAAS;gCAAQ,gBAAgB;gCAAgB,YAAY;4BAAS;;8CAChF,6LAAC,uLAAA,CAAA,OAAI;oCAAC,WAAW,yJAAA,CAAA,UAAM,CAAC,WAAW;8CAC/B,cAAA,6LAAC;wCAAE,MAAM,CAAC,QAAQ,EAAE,aAAa,MAAM,CAAC,IAAI,EAAE;wCAAE,QAAQ;wCAAU,WAAU;kDACxE,cAAA,6LAAC,uLAAA,CAAA,OAAI,CAAC,MAAM;4CAAC,WAAW,yJAAA,CAAA,UAAM,CAAC,iBAAiB;sDAC5C,cAAA,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;gDAAC,OAAO;oDAAE,QAAQ;gDAAE;0DAAI,iBAAiB,aAAa,MAAM;;;;;;;;;;;;;;;;;;;;;8CAInF,6LAAC;oCAAI,OAAO;wCAAE,SAAS;wCAAQ,eAAe;wCAAU,gBAAgB;wCAAU,YAAY;oCAAS;;sDACnG,6LAAC,qKAAA,CAAA,UAAc;4CAAC,OAAO;gDAAE,UAAU;4CAAO;;;;;;wCACzC,aAAa,UAAU,KAAK,CAAC,IAAI,qBAAO,6LAAC,kIAAA,CAAA,UAAM;4CAAC,QAAQ,aAAa,UAAU;;;;;;;;;;;;8CAEpF,6LAAC,uLAAA,CAAA,OAAI;oCAAC,WAAW,yJAAA,CAAA,UAAM,CAAC,WAAW;8CAC/B,cAAA,6LAAC;wCAAE,MAAM,CAAC,QAAQ,EAAE,aAAa,KAAK,CAAC,IAAI,EAAE;wCAAE,QAAQ;wCAAU,WAAU;kDACvE,cAAA,6LAAC,uLAAA,CAAA,OAAI,CAAC,MAAM;4CAAC,WAAW,yJAAA,CAAA,UAAM,CAAC,iBAAiB;sDAC5C,cAAA,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;gDAAC,OAAO;oDAAE,QAAQ;gDAAE;0DAAI,iBAAiB,aAAa,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAOlG;;;;;;;AAKpB;GA7HS;KAAA;uCA+HM", "debugId": null}}]}