(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["static/chunks/_32c59b6a._.js", {

"[project]/utils/Hooks.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "useCoflCoins": (()=>useCoflCoins),
    "useDebounce": (()=>useDebounce),
    "useForceUpdate": (()=>useForceUpdate),
    "useIsMobile": (()=>useIsMobile),
    "useQueryParamState": (()=>useQueryParamState),
    "useStateWithRef": (()=>useStateWithRef),
    "useSwipe": (()=>useSwipe),
    "useWasAlreadyLoggedIn": (()=>useWasAlreadyLoggedIn)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$CoflCoinsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/CoflCoinsUtils.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SSRUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/SSRUtils.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Parser$2f$URLParser$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/Parser/URLParser.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature(), _s3 = __turbopack_context__.k.signature(), _s4 = __turbopack_context__.k.signature(), _s5 = __turbopack_context__.k.signature(), _s6 = __turbopack_context__.k.signature();
;
;
;
;
;
function useForceUpdate() {
    _s();
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const [update, setUpdate] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(0);
    return ()=>setUpdate((update)=>update + 1);
}
_s(useForceUpdate, "U5iovX6txcuyzahsHyptxdF1Nws=");
function useSwipe(onSwipeUp, onSwipeRight, onSwipeDown, onSwipeLeft) {
    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SSRUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isClientSideRendering"])()) {
        return;
    }
    document.addEventListener('touchstart', handleTouchStart, false);
    document.addEventListener('touchmove', handleTouchMove, false);
    var xDown = null;
    var yDown = null;
    function getTouches(evt) {
        return evt.touches || // browser API
        evt.originalEvent.touches; // jQuery
    }
    function handleTouchStart(evt) {
        const firstTouch = getTouches(evt)[0];
        xDown = firstTouch.clientX;
        yDown = firstTouch.clientY;
    }
    function handleTouchMove(evt) {
        if (xDown === null || yDown === null) {
            return;
        }
        var xUp = evt.touches[0].clientX;
        var yUp = evt.touches[0].clientY;
        var xDiff = xDown - xUp;
        var yDiff = yDown - yUp;
        if (Math.abs(xDiff) > Math.abs(yDiff)) {
            /*most significant*/ if (xDiff > 0) {
                if (onSwipeLeft) {
                    onSwipeLeft();
                }
            } else {
                if (onSwipeRight) {
                    onSwipeRight();
                }
            }
        } else {
            if (yDiff > 0) {
                if (onSwipeUp) {
                    onSwipeUp();
                }
            } else {
                if (onSwipeDown) {
                    onSwipeDown();
                }
            }
        }
        /* reset values */ xDown = null;
        yDown = null;
    }
    return ()=>{
        document.removeEventListener('touchstart', handleTouchStart, false);
        document.removeEventListener('touchmove', handleTouchMove, false);
    };
}
function useCoflCoins() {
    _s1();
    const [coflCoins, setCoflCoins] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$CoflCoinsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getCurrentCoflCoins"])());
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useCoflCoins.useEffect": ()=>{
            let unsubscribe = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$CoflCoinsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["subscribeToCoflcoinChange"])(setCoflCoins);
            return ({
                "useCoflCoins.useEffect": ()=>{
                    unsubscribe();
                }
            })["useCoflCoins.useEffect"];
        }
    }["useCoflCoins.useEffect"], []);
    return coflCoins;
}
_s1(useCoflCoins, "C+enwg4fK/g34CESb/vw33ukaqE=");
function useWasAlreadyLoggedIn() {
    _s2();
    const [wasAlreadyLoggedIn, setWasAlreadyLoggedIn] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useWasAlreadyLoggedIn.useEffect": ()=>{
            setWasAlreadyLoggedIn(localStorage.getItem('googleId') !== null);
        }
    }["useWasAlreadyLoggedIn.useEffect"], []);
    return wasAlreadyLoggedIn;
}
_s2(useWasAlreadyLoggedIn, "1f04SSxs2Qtmf783W7lV2PTlbiw=");
function useDebounce(value, delay) {
    _s3();
    const [debouncedValue, setDebouncedValue] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(value);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useDebounce.useEffect": ()=>{
            const handler = setTimeout({
                "useDebounce.useEffect.handler": ()=>{
                    setDebouncedValue(value);
                }
            }["useDebounce.useEffect.handler"], delay);
            return ({
                "useDebounce.useEffect": ()=>{
                    clearTimeout(handler);
                }
            })["useDebounce.useEffect"];
        }
    }["useDebounce.useEffect"], [
        value,
        delay
    ]);
    return debouncedValue;
}
_s3(useDebounce, "KDuPAtDOgxm8PU6legVJOb3oOmA=");
function useStateWithRef(defaultValue) {
    _s4();
    const [state, _setState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(defaultValue);
    let stateRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(state);
    const setState = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useStateWithRef.useCallback[setState]": (newState)=>{
            stateRef.current = newState;
            _setState(newState);
        }
    }["useStateWithRef.useCallback[setState]"], []);
    return [
        state,
        setState,
        stateRef
    ];
}
_s4(useStateWithRef, "2zUIfANzaBXnh0dIWyeU0rgp5no=");
function useQueryParamState(key, defaultValue) {
    _s5();
    const [state, setState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(getDefaultValue() || defaultValue);
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    function getDefaultValue() {
        let param = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Parser$2f$URLParser$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getURLSearchParam"])(key);
        if (!param) {
            return undefined;
        }
        return JSON.parse(decodeURIComponent(param));
    }
    function _setState(newState) {
        setState(newState);
        let urlparams = new URLSearchParams(window.location.search);
        if (!newState) {
            urlparams.delete(key);
        } else {
            urlparams.set(key, encodeURIComponent(JSON.stringify(newState)));
        }
        router.replace(`${window.location.pathname}?${urlparams.toString()}`);
    }
    return [
        state,
        _setState
    ];
}
_s5(useQueryParamState, "gFWI+omlRaxP5wHoiPXmgkzW71U=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"]
    ];
});
function useIsMobile() {
    _s6();
    let [isMobile, setIsMobile] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useIsMobile.useEffect": ()=>{
            setIsMobile(isMobileCheck());
        }
    }["useIsMobile.useEffect"], []);
    function isMobileCheck() {
        let check = false;
        (function(a) {
            if (/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i.test(a) || /1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i.test(a.substr(0, 4))) check = true;
        })(navigator.userAgent || navigator.vendor || window.opera);
        return check;
    }
    return isMobile;
}
_s6(useIsMobile, "0VTTNJATKABQPGLm9RVT0tKGUgU=");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/GoogleSignIn/GoogleSignIn.module.css [app-client] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "googleButton": "GoogleSignIn-module__lTSYOa__googleButton",
});
}}),
"[project]/components/GoogleSignIn/GoogleSignIn.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__),
    "isValidTokenAvailable": (()=>isValidTokenAvailable)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-toastify/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/api/ApiHelper.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$jonkoops$2f$matomo$2d$tracker$2d$react$2f$es$2f$useMatomo$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useMatomo$3e$__ = __turbopack_context__.i("[project]/node_modules/@jonkoops/matomo-tracker-react/es/useMatomo.js [app-client] (ecmascript) <export default as useMatomo>");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Hooks$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/Hooks.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SSRUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/SSRUtils.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/api/ApiTypes.d.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$oauth$2f$google$2f$dist$2f$index$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@react-oauth/google/dist/index.esm.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$GoogleSignIn$2f$GoogleSignIn$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/components/GoogleSignIn/GoogleSignIn.module.css [app-client] (css module)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SettingsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/SettingsUtils.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Base64Utils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/Base64Utils.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Modal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Modal$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/Modal.js [app-client] (ecmascript) <export default as Modal>");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
;
;
;
;
;
;
function GoogleSignIn(props) {
    _s();
    let [wasAlreadyLoggedInThisSession, setWasAlreadyLoggedInThisSession] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SSRUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isClientSideRendering"])() ? isValidTokenAvailable(localStorage.getItem('googleId')) : false);
    let [isLoggedIn, setIsLoggedIn] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    let [isSSR, setIsSSR] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    let [isLoginNotShowing, setIsLoginNotShowing] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    let [showButtonNotRenderingModal, setShowButtonNotRenderingModal] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    let { trackEvent } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$jonkoops$2f$matomo$2d$tracker$2d$react$2f$es$2f$useMatomo$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useMatomo$3e$__["useMatomo"])();
    let forceUpdate = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Hooks$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForceUpdate"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "GoogleSignIn.useEffect": ()=>{
            setIsSSR(false);
            if (wasAlreadyLoggedInThisSession) {
                let token = localStorage.getItem('googleId');
                let userObject = JSON.parse((0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Base64Utils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["atobUnicode"])(token.split('.')[1]));
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SettingsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setSetting"])(__TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SettingsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["GOOGLE_EMAIL"], userObject.email);
                onLoginSucces(token);
            } else {
                setTimeout({
                    "GoogleSignIn.useEffect": ()=>{
                        let isShown = false;
                        document.querySelectorAll('iframe').forEach({
                            "GoogleSignIn.useEffect": (e)=>{
                                if (e.src && e.src.includes('accounts.google.com')) {
                                    isShown = true;
                                }
                            }
                        }["GoogleSignIn.useEffect"]);
                        if (!isShown) {
                            setIsLoggedIn(false);
                            setIsLoginNotShowing(true);
                            sessionStorage.removeItem('googleId');
                            localStorage.removeItem('googleId');
                        }
                    }
                }["GoogleSignIn.useEffect"], 5000);
            }
        // eslint-disable-next-line react-hooks/exhaustive-deps
        }
    }["GoogleSignIn.useEffect"], []);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "GoogleSignIn.useEffect": ()=>{
            if (wasAlreadyLoggedInThisSession) {
                setIsLoggedIn(true);
            }
        }
    }["GoogleSignIn.useEffect"], [
        wasAlreadyLoggedInThisSession
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "GoogleSignIn.useEffect": ()=>{
            forceUpdate();
            setIsLoggedIn(sessionStorage.getItem('googleId') !== null);
        // eslint-disable-next-line react-hooks/exhaustive-deps
        }
    }["GoogleSignIn.useEffect"], [
        props.rerenderFlip
    ]);
    function onLoginSucces(token) {
        setIsLoggedIn(true);
        __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].loginWithToken(token).then((token)=>{
            localStorage.setItem('googleId', token);
            sessionStorage.setItem('googleId', token);
            let refId = window.refId;
            if (refId) {
                __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].setRef(refId);
            }
            document.dispatchEvent(new CustomEvent(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CUSTOM_EVENTS"].GOOGLE_LOGIN));
            if (props.onAfterLogin) {
                props.onAfterLogin();
            }
        }).catch((error)=>{
            // dont show the error message for the invalid token error
            // the google sign component sometimes sends an outdated token, causing this error
            if (error.slug !== 'invalid_token') {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(`An error occoured while trying to sign in with Google. ${error ? error.slug || JSON.stringify(error) : null}`);
            } else {
                console.warn('setGoogle: Invalid token error', error);
                sessionStorage.removeItem('googleId');
                localStorage.removeItem('googleId');
            }
            setIsLoggedIn(false);
            setWasAlreadyLoggedInThisSession(false);
            sessionStorage.removeItem('googleId');
            localStorage.removeItem('googleId');
        });
    }
    function onLoginFail() {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('Something went wrong, please try again.', {
            autoClose: 20000
        });
    }
    function onLoginClick() {
        if (props.onManualLoginClick) {
            props.onManualLoginClick();
        }
        trackEvent({
            category: 'login',
            action: 'click'
        });
    }
    let style = isLoggedIn ? {
        visibility: 'collapse',
        height: 0
    } : {};
    if (isSSR) {
        return null;
    }
    let buttonNotRenderingModal = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Modal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Modal$3e$__["Modal"], {
        show: showButtonNotRenderingModal,
        onHide: ()=>{
            setShowButtonNotRenderingModal(false);
        },
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Modal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Modal$3e$__["Modal"].Header, {
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Modal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Modal$3e$__["Modal"].Title, {
                    children: "Google Login button not showing up?"
                }, void 0, false, {
                    fileName: "[project]/components/GoogleSignIn/GoogleSignIn.tsx",
                    lineNumber: 137,
                    columnNumber: 17
                }, this)
            }, void 0, false, {
                fileName: "[project]/components/GoogleSignIn/GoogleSignIn.tsx",
                lineNumber: 136,
                columnNumber: 13
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Modal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Modal$3e$__["Modal"].Body, {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: "This is most likely caused by either an external software like an anti virus or your browser/extension blocking it."
                    }, void 0, false, {
                        fileName: "[project]/components/GoogleSignIn/GoogleSignIn.tsx",
                        lineNumber: 140,
                        columnNumber: 17
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("hr", {}, void 0, false, {
                        fileName: "[project]/components/GoogleSignIn/GoogleSignIn.tsx",
                        lineNumber: 141,
                        columnNumber: 17
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: "Known issues:"
                    }, void 0, false, {
                        fileName: "[project]/components/GoogleSignIn/GoogleSignIn.tsx",
                        lineNumber: 142,
                        columnNumber: 17
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                children: 'Kaspersky\'s "Secure Browse" feature seems to block the Google login.'
                            }, void 0, false, {
                                fileName: "[project]/components/GoogleSignIn/GoogleSignIn.tsx",
                                lineNumber: 144,
                                columnNumber: 21
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                children: "Opera GX seems to sometimes blocks the login button. The specific setting or reason on when it blocks it is unknown."
                            }, void 0, false, {
                                fileName: "[project]/components/GoogleSignIn/GoogleSignIn.tsx",
                                lineNumber: 145,
                                columnNumber: 21
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/GoogleSignIn/GoogleSignIn.tsx",
                        lineNumber: 143,
                        columnNumber: 17
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/components/GoogleSignIn/GoogleSignIn.tsx",
                lineNumber: 139,
                columnNumber: 13
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/components/GoogleSignIn/GoogleSignIn.tsx",
        lineNumber: 130,
        columnNumber: 9
    }, this);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        style: style,
        onClickCapture: onLoginClick,
        children: [
            !wasAlreadyLoggedInThisSession ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$GoogleSignIn$2f$GoogleSignIn$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].googleButton,
                        children: !isSSR ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$oauth$2f$google$2f$dist$2f$index$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["GoogleLogin"], {
                            onSuccess: (response)=>{
                                try {
                                    let userObject = JSON.parse((0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Base64Utils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["atobUnicode"])(response.credential.split('.')[1]));
                                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SettingsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setSetting"])(__TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SettingsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["GOOGLE_PROFILE_PICTURE_URL"], userObject.picture);
                                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SettingsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setSetting"])(__TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SettingsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["GOOGLE_EMAIL"], userObject.email);
                                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SettingsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setSetting"])(__TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SettingsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["GOOGLE_NAME"], userObject.name);
                                } catch  {
                                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].warn('Parsing issue with the google token. There might be issues when displaying details on the account page!');
                                }
                                onLoginSucces(response.credential);
                            },
                            onError: onLoginFail,
                            theme: 'filled_blue',
                            size: 'large',
                            useOneTap: true,
                            auto_select: true
                        }, void 0, false, {
                            fileName: "[project]/components/GoogleSignIn/GoogleSignIn.tsx",
                            lineNumber: 157,
                            columnNumber: 29
                        }, this) : null
                    }, void 0, false, {
                        fileName: "[project]/components/GoogleSignIn/GoogleSignIn.tsx",
                        lineNumber: 155,
                        columnNumber: 21
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: [
                            "I have read and agree to the ",
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                                href: "https://coflnet.com/privacy",
                                children: "Privacy Policy"
                            }, void 0, false, {
                                fileName: "[project]/components/GoogleSignIn/GoogleSignIn.tsx",
                                lineNumber: 178,
                                columnNumber: 54
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/GoogleSignIn/GoogleSignIn.tsx",
                        lineNumber: 177,
                        columnNumber: 21
                    }, this),
                    isLoginNotShowing ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: [
                            "Login button not showing? Click",
                            ' ',
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                style: {
                                    color: '#007bff',
                                    cursor: 'pointer'
                                },
                                onClick: ()=>{
                                    setShowButtonNotRenderingModal(true);
                                },
                                children: "here"
                            }, void 0, false, {
                                fileName: "[project]/components/GoogleSignIn/GoogleSignIn.tsx",
                                lineNumber: 183,
                                columnNumber: 29
                            }, this),
                            "."
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/GoogleSignIn/GoogleSignIn.tsx",
                        lineNumber: 181,
                        columnNumber: 25
                    }, this) : null
                ]
            }, void 0, true) : null,
            buttonNotRenderingModal
        ]
    }, void 0, true, {
        fileName: "[project]/components/GoogleSignIn/GoogleSignIn.tsx",
        lineNumber: 152,
        columnNumber: 9
    }, this);
}
_s(GoogleSignIn, "yI8N0aYJ97NxxDhTACnH53SYTnc=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$jonkoops$2f$matomo$2d$tracker$2d$react$2f$es$2f$useMatomo$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useMatomo$3e$__["useMatomo"],
        __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Hooks$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForceUpdate"]
    ];
});
_c = GoogleSignIn;
const __TURBOPACK__default__export__ = GoogleSignIn;
function isValidTokenAvailable(token) {
    if (!token || token === 'null') {
        return;
    }
    try {
        let details = JSON.parse((0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Base64Utils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["atobUnicode"])(token.split('.')[1]));
        let expirationDate = new Date(parseInt(details.exp) * 1000);
        return expirationDate.getTime() - 10000 > new Date().getTime();
    } catch (e) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].warn("Parsing issue with the google token. Can't automatically login!");
        return false;
    }
}
var _c;
__turbopack_context__.k.register(_c, "GoogleSignIn");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/utils/LoadingUtils.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "getInitialLoadingElement": (()=>getInitialLoadingElement),
    "getLoadingElement": (()=>getLoadingElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Spinner$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Spinner$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/Spinner.js [app-client] (ecmascript) <export default as Spinner>");
;
;
;
function getLoadingElement(text) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        style: {
            textAlign: 'center'
        },
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Spinner$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Spinner$3e$__["Spinner"], {
                        animation: "grow",
                        variant: "primary"
                    }, void 0, false, {
                        fileName: "[project]/utils/LoadingUtils.tsx",
                        lineNumber: 9,
                        columnNumber: 17
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Spinner$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Spinner$3e$__["Spinner"], {
                        animation: "grow",
                        variant: "primary"
                    }, void 0, false, {
                        fileName: "[project]/utils/LoadingUtils.tsx",
                        lineNumber: 10,
                        columnNumber: 17
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Spinner$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Spinner$3e$__["Spinner"], {
                        animation: "grow",
                        variant: "primary"
                    }, void 0, false, {
                        fileName: "[project]/utils/LoadingUtils.tsx",
                        lineNumber: 11,
                        columnNumber: 17
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/utils/LoadingUtils.tsx",
                lineNumber: 8,
                columnNumber: 13
            }, this),
            text ? text : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                children: "Loading Data..."
            }, void 0, false, {
                fileName: "[project]/utils/LoadingUtils.tsx",
                lineNumber: 13,
                columnNumber: 28
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/utils/LoadingUtils.tsx",
        lineNumber: 7,
        columnNumber: 9
    }, this);
}
function getInitialLoadingElement() {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "main-loading",
        style: {
            height: '500px'
        },
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    src: "/logo192.png",
                    height: "192",
                    width: "192",
                    alt: "auction house logo"
                }, void 0, false, {
                    fileName: "[project]/utils/LoadingUtils.tsx",
                    lineNumber: 22,
                    columnNumber: 17
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "main-loading",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        children: "Loading App..."
                    }, void 0, false, {
                        fileName: "[project]/utils/LoadingUtils.tsx",
                        lineNumber: 24,
                        columnNumber: 21
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/utils/LoadingUtils.tsx",
                    lineNumber: 23,
                    columnNumber: 17
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/utils/LoadingUtils.tsx",
            lineNumber: 21,
            columnNumber: 13
        }, this)
    }, void 0, false, {
        fileName: "[project]/utils/LoadingUtils.tsx",
        lineNumber: 20,
        columnNumber: 9
    }, this);
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/NavBar/NavBar.module.css [app-client] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "hamburgerIcon": "NavBar-module__yBvhsG__hamburgerIcon",
  "logo": "NavBar-module__yBvhsG__logo",
  "menuItem": "NavBar-module__yBvhsG__menuItem",
  "navBar": "NavBar-module__yBvhsG__navBar",
  "navClosing": "NavBar-module__yBvhsG__navClosing",
  "navOpen": "NavBar-module__yBvhsG__navOpen",
});
}}),
"[project]/components/NavBar/NavBar.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$AccountBalance$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/icons-material/esm/AccountBalance.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$AccountCircle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/icons-material/esm/AccountCircle.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Build$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/icons-material/esm/Build.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Chat$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/icons-material/esm/Chat.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Download$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/icons-material/esm/Download.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Home$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/icons-material/esm/Home.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Menu$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/icons-material/esm/Menu.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$NotificationsOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/icons-material/esm/NotificationsOutlined.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$PetsOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/icons-material/esm/PetsOutlined.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Policy$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/icons-material/esm/Policy.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$ShareOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/icons-material/esm/ShareOutlined.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Storefront$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/icons-material/esm/Storefront.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$CurrencyExchange$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/icons-material/esm/CurrencyExchange.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pro$2d$sidebar$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-pro-sidebar/dist/index.es.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Hooks$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/Hooks.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$NavBar$2f$NavBar$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/components/NavBar/NavBar.module.css [app-client] (css module)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
let resizePromise = null;
function NavBar(props) {
    _s();
    let [isWideOpen, setIsWideOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    let [isHovering, setIsHovering] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    let [isSmall, setIsSmall] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    let [collapsed, setCollapsed] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    let forceUpdate = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Hooks$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForceUpdate"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "NavBar.useEffect": ()=>{
            setIsSmall(document.body.clientWidth < 1500);
            window.addEventListener('resize', resizeHandler);
            return ({
                "NavBar.useEffect": ()=>{
                    window.removeEventListener('resize', resizeHandler);
                }
            })["NavBar.useEffect"];
        }
    }["NavBar.useEffect"], []);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "NavBar.useEffect": ()=>{
            if (isWideOpen) {
                document.addEventListener('click', outsideClickHandler, true);
            } else {
                document.removeEventListener('click', outsideClickHandler, true);
            }
            return ({
                "NavBar.useEffect": ()=>{
                    document.removeEventListener('click', outsideClickHandler, true);
                }
            })["NavBar.useEffect"];
        // eslint-disable-next-line react-hooks/exhaustive-deps
        }
    }["NavBar.useEffect"], [
        isWideOpen
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "NavBar.useEffect": ()=>{
            setCollapsed(isCollapsed());
        }
    }["NavBar.useEffect"], [
        isSmall,
        isWideOpen,
        isHovering
    ]);
    function isCollapsed() {
        if (isSmall) {
            return false;
        }
        return !isWideOpen && !isHovering;
    }
    function outsideClickHandler(evt) {
        const flyoutEl = document.getElementById('navBar');
        const hamburgerEl = document.getElementById('hamburgerIcon');
        let targetEl = evt.target;
        do {
            if (targetEl === flyoutEl || targetEl === hamburgerEl) {
                return;
            }
            targetEl = targetEl.parentNode;
        }while (targetEl)
        if (isWideOpen) {
            if (isSmall) {
                let el = document.getElementById('pro-sidebar');
                el?.classList.add(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$NavBar$2f$NavBar$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].navClosing);
                el?.classList.remove(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$NavBar$2f$NavBar$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].navOpen);
                setTimeout(()=>{
                    setIsWideOpen(false);
                    el?.classList.remove(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$NavBar$2f$NavBar$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].navClosing);
                }, 500);
            } else {
                setIsWideOpen(false);
            }
        }
    }
    function onMouseMove() {
        setIsHovering(true);
    }
    function onMouseOut() {
        setIsHovering(false);
    }
    function resizeHandler() {
        if (resizePromise) {
            return;
        }
        resizePromise = setTimeout(()=>{
            setIsWideOpen(false);
            setIsSmall(document.body.clientWidth < 1500);
            forceUpdate();
            resizePromise = null;
            let el = document.getElementById('pro-sidebar');
            if (el) {
                el.style.left = '0px';
            }
        }, 500);
    }
    function onHamburgerClick() {
        if (isSmall && !isWideOpen) {
            let el = document.getElementById('pro-sidebar');
            if (el) {
                el.hidden = false;
                el.style.left = '-270px';
                setTimeout(()=>{
                    if (el) {
                        el.classList.add(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$NavBar$2f$NavBar$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].navOpen);
                    }
                });
                setTimeout(()=>{
                    setIsWideOpen(true);
                }, 500);
            }
        } else {
            setIsWideOpen(!isWideOpen);
        }
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("aside", {
                className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$NavBar$2f$NavBar$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].navBar,
                id: "navBar",
                onMouseEnter: onMouseMove,
                onMouseLeave: onMouseOut,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pro$2d$sidebar$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Sidebar"], {
                    id: "pro-sidebar",
                    hidden: isSmall && !isWideOpen,
                    backgroundColor: "#1d1d1d",
                    collapsed: collapsed,
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        style: {
                            height: '100%',
                            display: 'flex',
                            flexDirection: 'column'
                        },
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$NavBar$2f$NavBar$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].logo,
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            src: "/logo512.png",
                                            alt: "Logo",
                                            width: 40,
                                            height: 40,
                                            style: {
                                                translate: '-5px'
                                            }
                                        }, void 0, false, {
                                            fileName: "[project]/components/NavBar/NavBar.tsx",
                                            lineNumber: 147,
                                            columnNumber: 33
                                        }, this),
                                        " ",
                                        !isCollapsed() ? 'Coflnet' : ''
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/components/NavBar/NavBar.tsx",
                                    lineNumber: 146,
                                    columnNumber: 29
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/components/NavBar/NavBar.tsx",
                                lineNumber: 145,
                                columnNumber: 25
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("hr", {}, void 0, false, {
                                fileName: "[project]/components/NavBar/NavBar.tsx",
                                lineNumber: 150,
                                columnNumber: 25
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pro$2d$sidebar$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Menu"], {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pro$2d$sidebar$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MenuItem"], {
                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$NavBar$2f$NavBar$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].menuItem,
                                        component: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            href: '/'
                                        }, void 0, false, {
                                            fileName: "[project]/components/NavBar/NavBar.tsx",
                                            lineNumber: 152,
                                            columnNumber: 78
                                        }, void 0),
                                        icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Home$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                            fileName: "[project]/components/NavBar/NavBar.tsx",
                                            lineNumber: 152,
                                            columnNumber: 105
                                        }, void 0),
                                        children: "Home"
                                    }, void 0, false, {
                                        fileName: "[project]/components/NavBar/NavBar.tsx",
                                        lineNumber: 152,
                                        columnNumber: 29
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pro$2d$sidebar$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MenuItem"], {
                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$NavBar$2f$NavBar$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].menuItem,
                                        component: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            href: '/flipper'
                                        }, void 0, false, {
                                            fileName: "[project]/components/NavBar/NavBar.tsx",
                                            lineNumber: 155,
                                            columnNumber: 78
                                        }, void 0),
                                        icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Storefront$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                            fileName: "[project]/components/NavBar/NavBar.tsx",
                                            lineNumber: 155,
                                            columnNumber: 112
                                        }, void 0),
                                        children: "Item Flipper"
                                    }, void 0, false, {
                                        fileName: "[project]/components/NavBar/NavBar.tsx",
                                        lineNumber: 155,
                                        columnNumber: 29
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pro$2d$sidebar$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MenuItem"], {
                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$NavBar$2f$NavBar$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].menuItem,
                                        component: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            href: '/account'
                                        }, void 0, false, {
                                            fileName: "[project]/components/NavBar/NavBar.tsx",
                                            lineNumber: 158,
                                            columnNumber: 78
                                        }, void 0),
                                        icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$AccountCircle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                            fileName: "[project]/components/NavBar/NavBar.tsx",
                                            lineNumber: 158,
                                            columnNumber: 112
                                        }, void 0),
                                        children: "Account"
                                    }, void 0, false, {
                                        fileName: "[project]/components/NavBar/NavBar.tsx",
                                        lineNumber: 158,
                                        columnNumber: 29
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pro$2d$sidebar$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MenuItem"], {
                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$NavBar$2f$NavBar$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].menuItem,
                                        component: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            href: '/subscriptions'
                                        }, void 0, false, {
                                            fileName: "[project]/components/NavBar/NavBar.tsx",
                                            lineNumber: 161,
                                            columnNumber: 78
                                        }, void 0),
                                        icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$NotificationsOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                            fileName: "[project]/components/NavBar/NavBar.tsx",
                                            lineNumber: 161,
                                            columnNumber: 118
                                        }, void 0),
                                        children: "Notifier"
                                    }, void 0, false, {
                                        fileName: "[project]/components/NavBar/NavBar.tsx",
                                        lineNumber: 161,
                                        columnNumber: 29
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pro$2d$sidebar$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MenuItem"], {
                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$NavBar$2f$NavBar$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].menuItem,
                                        component: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            href: '/crafts'
                                        }, void 0, false, {
                                            fileName: "[project]/components/NavBar/NavBar.tsx",
                                            lineNumber: 164,
                                            columnNumber: 78
                                        }, void 0),
                                        icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Build$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                            fileName: "[project]/components/NavBar/NavBar.tsx",
                                            lineNumber: 164,
                                            columnNumber: 111
                                        }, void 0),
                                        children: "Profitable Crafts"
                                    }, void 0, false, {
                                        fileName: "[project]/components/NavBar/NavBar.tsx",
                                        lineNumber: 164,
                                        columnNumber: 29
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pro$2d$sidebar$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MenuItem"], {
                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$NavBar$2f$NavBar$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].menuItem,
                                        component: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            href: '/premium'
                                        }, void 0, false, {
                                            fileName: "[project]/components/NavBar/NavBar.tsx",
                                            lineNumber: 167,
                                            columnNumber: 78
                                        }, void 0),
                                        icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$AccountBalance$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                            fileName: "[project]/components/NavBar/NavBar.tsx",
                                            lineNumber: 167,
                                            columnNumber: 112
                                        }, void 0),
                                        children: "Premium / Shop"
                                    }, void 0, false, {
                                        fileName: "[project]/components/NavBar/NavBar.tsx",
                                        lineNumber: 167,
                                        columnNumber: 29
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pro$2d$sidebar$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MenuItem"], {
                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$NavBar$2f$NavBar$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].menuItem,
                                        component: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            href: '/trade'
                                        }, void 0, false, {
                                            fileName: "[project]/components/NavBar/NavBar.tsx",
                                            lineNumber: 170,
                                            columnNumber: 78
                                        }, void 0),
                                        icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$CurrencyExchange$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                            fileName: "[project]/components/NavBar/NavBar.tsx",
                                            lineNumber: 170,
                                            columnNumber: 110
                                        }, void 0),
                                        children: "Trading"
                                    }, void 0, false, {
                                        fileName: "[project]/components/NavBar/NavBar.tsx",
                                        lineNumber: 170,
                                        columnNumber: 29
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pro$2d$sidebar$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MenuItem"], {
                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$NavBar$2f$NavBar$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].menuItem,
                                        component: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            href: '/kat'
                                        }, void 0, false, {
                                            fileName: "[project]/components/NavBar/NavBar.tsx",
                                            lineNumber: 173,
                                            columnNumber: 78
                                        }, void 0),
                                        icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$PetsOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                            fileName: "[project]/components/NavBar/NavBar.tsx",
                                            lineNumber: 173,
                                            columnNumber: 108
                                        }, void 0),
                                        children: "Kat Flips"
                                    }, void 0, false, {
                                        fileName: "[project]/components/NavBar/NavBar.tsx",
                                        lineNumber: 173,
                                        columnNumber: 29
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pro$2d$sidebar$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MenuItem"], {
                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$NavBar$2f$NavBar$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].menuItem,
                                        component: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            href: '/mod'
                                        }, void 0, false, {
                                            fileName: "[project]/components/NavBar/NavBar.tsx",
                                            lineNumber: 176,
                                            columnNumber: 78
                                        }, void 0),
                                        icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Download$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                            fileName: "[project]/components/NavBar/NavBar.tsx",
                                            lineNumber: 176,
                                            columnNumber: 108
                                        }, void 0),
                                        children: "Mod"
                                    }, void 0, false, {
                                        fileName: "[project]/components/NavBar/NavBar.tsx",
                                        lineNumber: 176,
                                        columnNumber: 29
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pro$2d$sidebar$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MenuItem"], {
                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$NavBar$2f$NavBar$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].menuItem,
                                        component: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            href: '/ref'
                                        }, void 0, false, {
                                            fileName: "[project]/components/NavBar/NavBar.tsx",
                                            lineNumber: 179,
                                            columnNumber: 78
                                        }, void 0),
                                        icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$ShareOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                            fileName: "[project]/components/NavBar/NavBar.tsx",
                                            lineNumber: 179,
                                            columnNumber: 108
                                        }, void 0),
                                        children: "Referral"
                                    }, void 0, false, {
                                        fileName: "[project]/components/NavBar/NavBar.tsx",
                                        lineNumber: 179,
                                        columnNumber: 29
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pro$2d$sidebar$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MenuItem"], {
                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$NavBar$2f$NavBar$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].menuItem,
                                        component: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            href: '/about'
                                        }, void 0, false, {
                                            fileName: "[project]/components/NavBar/NavBar.tsx",
                                            lineNumber: 182,
                                            columnNumber: 78
                                        }, void 0),
                                        icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Policy$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                            fileName: "[project]/components/NavBar/NavBar.tsx",
                                            lineNumber: 182,
                                            columnNumber: 110
                                        }, void 0),
                                        children: "Links / Legal"
                                    }, void 0, false, {
                                        fileName: "[project]/components/NavBar/NavBar.tsx",
                                        lineNumber: 182,
                                        columnNumber: 29
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pro$2d$sidebar$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MenuItem"], {
                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$NavBar$2f$NavBar$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].menuItem,
                                        component: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            href: '/feedback'
                                        }, void 0, false, {
                                            fileName: "[project]/components/NavBar/NavBar.tsx",
                                            lineNumber: 185,
                                            columnNumber: 78
                                        }, void 0),
                                        icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Chat$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                            fileName: "[project]/components/NavBar/NavBar.tsx",
                                            lineNumber: 185,
                                            columnNumber: 113
                                        }, void 0),
                                        children: "Feedback"
                                    }, void 0, false, {
                                        fileName: "[project]/components/NavBar/NavBar.tsx",
                                        lineNumber: 185,
                                        columnNumber: 29
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pro$2d$sidebar$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MenuItem"], {
                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$NavBar$2f$NavBar$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].menuItem,
                                        component: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            href: 'https://discord.gg/wvKXfTgCfb',
                                            target: "_blank"
                                        }, void 0, false, {
                                            fileName: "[project]/components/NavBar/NavBar.tsx",
                                            lineNumber: 190,
                                            columnNumber: 44
                                        }, void 0),
                                        rel: "noreferrer",
                                        icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            src: "/discord_icon.svg",
                                            alt: "Discord icon",
                                            height: 24,
                                            width: 32
                                        }, void 0, false, {
                                            fileName: "[project]/components/NavBar/NavBar.tsx",
                                            lineNumber: 192,
                                            columnNumber: 39
                                        }, void 0),
                                        children: "Discord"
                                    }, void 0, false, {
                                        fileName: "[project]/components/NavBar/NavBar.tsx",
                                        lineNumber: 188,
                                        columnNumber: 29
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/components/NavBar/NavBar.tsx",
                                lineNumber: 151,
                                columnNumber: 25
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/NavBar/NavBar.tsx",
                        lineNumber: 144,
                        columnNumber: 21
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/components/NavBar/NavBar.tsx",
                    lineNumber: 143,
                    columnNumber: 17
                }, this)
            }, void 0, false, {
                fileName: "[project]/components/NavBar/NavBar.tsx",
                lineNumber: 142,
                columnNumber: 13
            }, this),
            isSmall ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                onClick: onHamburgerClick,
                className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$NavBar$2f$NavBar$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].hamburgerIcon,
                id: "hamburgerIcon",
                style: props.hamburgerIconStyle,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Menu$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    fontSize: "large"
                }, void 0, false, {
                    fileName: "[project]/components/NavBar/NavBar.tsx",
                    lineNumber: 202,
                    columnNumber: 21
                }, this)
            }, void 0, false, {
                fileName: "[project]/components/NavBar/NavBar.tsx",
                lineNumber: 201,
                columnNumber: 17
            }, this) : ''
        ]
    }, void 0, true, {
        fileName: "[project]/components/NavBar/NavBar.tsx",
        lineNumber: 141,
        columnNumber: 9
    }, this);
}
_s(NavBar, "ejfVnX45tW8OIsOm1ZQEJxnncxQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Hooks$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForceUpdate"]
    ];
});
_c = NavBar;
const __TURBOPACK__default__export__ = NavBar;
var _c;
__turbopack_context__.k.register(_c, "NavBar");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/Number/Number.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>NumberElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Formatter$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/Formatter.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
function NumberElement(props) {
    _s();
    let [isSSR, setIsSSR] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    let value = Number(props.number);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "NumberElement.useEffect": ()=>{
            setIsSSR(false);
        }
    }["NumberElement.useEffect"], []);
    // Use consistent formatting to prevent hydration mismatches
    // Always use comma as thousand separator and period as decimal separator
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
        suppressHydrationWarning: true,
        children: (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Formatter$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["numberWithThousandsSeparators"])(value, ',', '.')
    }, void 0, false, {
        fileName: "[project]/components/Number/Number.tsx",
        lineNumber: 20,
        columnNumber: 12
    }, this);
}
_s(NumberElement, "DdT/RTUMRscU32S7lal5R4JlHrU=");
_c = NumberElement;
var _c;
__turbopack_context__.k.register(_c, "NumberElement");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/Premium/PremiumFeatures/PremiumFeatures.module.css [app-client] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "featureCard": "PremiumFeatures-module__Tm37xq__featureCard",
  "featureColumn": "PremiumFeatures-module__Tm37xq__featureColumn",
  "featureColumnHeading": "PremiumFeatures-module__Tm37xq__featureColumnHeading",
  "ingamePriceHoverImage": "PremiumFeatures-module__Tm37xq__ingamePriceHoverImage",
  "premiumFeatures": "PremiumFeatures-module__Tm37xq__premiumFeatures",
  "premiumProductColumn": "PremiumFeatures-module__Tm37xq__premiumProductColumn",
  "premiumProductHeading": "PremiumFeatures-module__Tm37xq__premiumProductHeading",
  "tooltipHoverId": "PremiumFeatures-module__Tm37xq__tooltipHoverId",
});
}}),
"[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Help$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/icons-material/esm/Help.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Table$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Table$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/Table.js [app-client] (ecmascript) <export default as Table>");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Number$2f$Number$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/Number/Number.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Tooltip$2f$Tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/Tooltip/Tooltip.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$PremiumFeatures$2f$PremiumFeatures$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/components/Premium/PremiumFeatures/PremiumFeatures.module.css [app-client] (css module)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-client] (ecmascript)");
'use client';
;
;
;
;
;
;
;
;
function PremiumFeatures() {
    let checkIconSvg = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        xmlns: "http://www.w3.org/2000/svg",
        width: "16",
        height: "16",
        fill: "lime",
        className: "bi bi-check-circle-fill",
        viewBox: "0 0 16 16",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
            d: "M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zm-3.97-3.03a.75.75 0 0 0-1.08.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-.01-1.05z"
        }, void 0, false, {
            fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
            lineNumber: 13,
            columnNumber: 13
        }, this)
    }, void 0, false, {
        fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
        lineNumber: 12,
        columnNumber: 9
    }, this);
    let checkIconElement = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
        className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$PremiumFeatures$2f$PremiumFeatures$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].premiumProductColumn,
        children: checkIconSvg
    }, void 0, false, {
        fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
        lineNumber: 17,
        columnNumber: 9
    }, this);
    let xIconElement = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
        className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$PremiumFeatures$2f$PremiumFeatures$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].premiumProductColumn,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
            xmlns: "http://www.w3.org/2000/svg",
            width: "16",
            height: "16",
            fill: "red",
            className: "bi bi-x-circle-fill",
            viewBox: "0 0 16 16",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zM5.354 4.646a.5.5 0 1 0-.708.708L7.293 8l-2.647 2.646a.5.5 0 0 0 .708.708L8 8.707l2.646 2.647a.5.5 0 0 0 .708-.708L8.707 8l2.647-2.646a.5.5 0 0 0-.708-.708L8 7.293 5.354 4.646z"
            }, void 0, false, {
                fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                lineNumber: 25,
                columnNumber: 17
            }, this)
        }, void 0, false, {
            fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
            lineNumber: 24,
            columnNumber: 13
        }, this)
    }, void 0, false, {
        fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
        lineNumber: 23,
        columnNumber: 9
    }, this);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$PremiumFeatures$2f$PremiumFeatures$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].premiumFeatures,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Table$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Table$3e$__["Table"], {
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("thead", {
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$PremiumFeatures$2f$PremiumFeatures$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].featureColumnHeading,
                                children: "Feature"
                            }, void 0, false, {
                                fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                                lineNumber: 35,
                                columnNumber: 25
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$PremiumFeatures$2f$PremiumFeatures$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].premiumProductHeading,
                                children: "Free"
                            }, void 0, false, {
                                fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                                lineNumber: 36,
                                columnNumber: 25
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$PremiumFeatures$2f$PremiumFeatures$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].premiumProductHeading,
                                children: "Starter"
                            }, void 0, false, {
                                fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                                lineNumber: 37,
                                columnNumber: 25
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$PremiumFeatures$2f$PremiumFeatures$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].premiumProductHeading,
                                children: "Premium"
                            }, void 0, false, {
                                fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                                lineNumber: 38,
                                columnNumber: 25
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$PremiumFeatures$2f$PremiumFeatures$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].premiumProductHeading,
                                children: "Premium+"
                            }, void 0, false, {
                                fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                                lineNumber: 39,
                                columnNumber: 25
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                        lineNumber: 34,
                        columnNumber: 21
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                    lineNumber: 33,
                    columnNumber: 17
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tbody", {
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$PremiumFeatures$2f$PremiumFeatures$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].featureColumn,
                                    children: "Price History"
                                }, void 0, false, {
                                    fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                                    lineNumber: 44,
                                    columnNumber: 25
                                }, this),
                                checkIconElement,
                                checkIconElement,
                                checkIconElement,
                                checkIconElement
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                            lineNumber: 43,
                            columnNumber: 21
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$PremiumFeatures$2f$PremiumFeatures$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].featureColumn,
                                    children: "Bazaar Data History"
                                }, void 0, false, {
                                    fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                                    lineNumber: 51,
                                    columnNumber: 25
                                }, this),
                                checkIconElement,
                                checkIconElement,
                                checkIconElement,
                                checkIconElement
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                            lineNumber: 50,
                            columnNumber: 21
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$PremiumFeatures$2f$PremiumFeatures$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].featureColumn,
                                    children: "Auction Explorer"
                                }, void 0, false, {
                                    fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                                    lineNumber: 58,
                                    columnNumber: 25
                                }, this),
                                checkIconElement,
                                checkIconElement,
                                checkIconElement,
                                checkIconElement
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                            lineNumber: 57,
                            columnNumber: 21
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$PremiumFeatures$2f$PremiumFeatures$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].featureColumn,
                                    children: "Player Auction History"
                                }, void 0, false, {
                                    fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                                    lineNumber: 65,
                                    columnNumber: 25
                                }, this),
                                checkIconElement,
                                checkIconElement,
                                checkIconElement,
                                checkIconElement
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                            lineNumber: 64,
                            columnNumber: 21
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$PremiumFeatures$2f$PremiumFeatures$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].featureColumn,
                                    children: "Display Active Auctions"
                                }, void 0, false, {
                                    fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                                    lineNumber: 72,
                                    columnNumber: 25
                                }, this),
                                checkIconElement,
                                checkIconElement,
                                checkIconElement,
                                checkIconElement
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                            lineNumber: 71,
                            columnNumber: 21
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$PremiumFeatures$2f$PremiumFeatures$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].featureColumn,
                                    children: [
                                        "Price estimations in game",
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Tooltip$2f$Tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            id: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$PremiumFeatures$2f$PremiumFeatures$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].tooltipHoverId,
                                            content: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                style: {
                                                    marginLeft: '5px'
                                                },
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Help$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                                    fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                                                    lineNumber: 85,
                                                    columnNumber: 41
                                                }, void 0)
                                            }, void 0, false, {
                                                fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                                                lineNumber: 84,
                                                columnNumber: 37
                                            }, void 0),
                                            type: "hover",
                                            tooltipContent: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$PremiumFeatures$2f$PremiumFeatures$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].ingamePriceHoverImage,
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                    fill: true,
                                                    src: "/price-estimation-ingame.png",
                                                    alt: "Price Estimation Tooltip"
                                                }, void 0, false, {
                                                    fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                                                    lineNumber: 91,
                                                    columnNumber: 41
                                                }, void 0)
                                            }, void 0, false, {
                                                fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                                                lineNumber: 90,
                                                columnNumber: 37
                                            }, void 0)
                                        }, void 0, false, {
                                            fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                                            lineNumber: 81,
                                            columnNumber: 29
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                                    lineNumber: 79,
                                    columnNumber: 25
                                }, this),
                                checkIconElement,
                                checkIconElement,
                                checkIconElement,
                                checkIconElement
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                            lineNumber: 78,
                            columnNumber: 21
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$PremiumFeatures$2f$PremiumFeatures$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].featureColumn,
                                    children: "Top 3 Kat Flips"
                                }, void 0, false, {
                                    fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                                    lineNumber: 102,
                                    columnNumber: 25
                                }, this),
                                xIconElement,
                                checkIconElement,
                                checkIconElement,
                                checkIconElement
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                            lineNumber: 101,
                            columnNumber: 21
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$PremiumFeatures$2f$PremiumFeatures$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].featureColumn,
                                    children: "Top 3 Craft Flips"
                                }, void 0, false, {
                                    fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                                    lineNumber: 109,
                                    columnNumber: 25
                                }, this),
                                xIconElement,
                                checkIconElement,
                                checkIconElement,
                                checkIconElement
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                            lineNumber: 108,
                            columnNumber: 21
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$PremiumFeatures$2f$PremiumFeatures$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].featureColumn,
                                    children: "Full Access to Flipper Filters"
                                }, void 0, false, {
                                    fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                                    lineNumber: 116,
                                    columnNumber: 25
                                }, this),
                                xIconElement,
                                checkIconElement,
                                checkIconElement,
                                checkIconElement
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                            lineNumber: 115,
                            columnNumber: 21
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$PremiumFeatures$2f$PremiumFeatures$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].featureColumn,
                                    children: "Filter player auctions"
                                }, void 0, false, {
                                    fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                                    lineNumber: 123,
                                    columnNumber: 25
                                }, this),
                                xIconElement,
                                checkIconElement,
                                checkIconElement,
                                checkIconElement
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                            lineNumber: 122,
                            columnNumber: 21
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$PremiumFeatures$2f$PremiumFeatures$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].featureColumn,
                                    children: [
                                        "Look at `buyspeed` and `most profit` leaderboard",
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Tooltip$2f$Tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            content: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                style: {
                                                    marginLeft: '5px'
                                                },
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Help$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                                    fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                                                    lineNumber: 135,
                                                    columnNumber: 41
                                                }, void 0)
                                            }, void 0, false, {
                                                fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                                                lineNumber: 134,
                                                columnNumber: 37
                                            }, void 0),
                                            type: "hover",
                                            tooltipContent: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                children: "This is currently available in our mod via the command /cl leaderbaord and /cl buyspeedboard"
                                            }, void 0, false, {
                                                fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                                                lineNumber: 139,
                                                columnNumber: 49
                                            }, void 0)
                                        }, void 0, false, {
                                            fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                                            lineNumber: 132,
                                            columnNumber: 29
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                                    lineNumber: 130,
                                    columnNumber: 25
                                }, this),
                                xIconElement,
                                xIconElement,
                                xIconElement,
                                checkIconElement
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                            lineNumber: 129,
                            columnNumber: 21
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$PremiumFeatures$2f$PremiumFeatures$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].featureColumn,
                                    children: [
                                        "Replay flips",
                                        ' ',
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Tooltip$2f$Tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            content: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                style: {
                                                    marginLeft: '5px'
                                                },
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Help$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                                    fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                                                    lineNumber: 153,
                                                    columnNumber: 41
                                                }, void 0)
                                            }, void 0, false, {
                                                fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                                                lineNumber: 152,
                                                columnNumber: 37
                                            }, void 0),
                                            type: "hover",
                                            tooltipContent: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                children: "Replay all active auctions against your flip filter to find flips that were created while you were offline"
                                            }, void 0, false, {
                                                fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                                                lineNumber: 157,
                                                columnNumber: 49
                                            }, void 0)
                                        }, void 0, false, {
                                            fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                                            lineNumber: 150,
                                            columnNumber: 29
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                                    lineNumber: 148,
                                    columnNumber: 25
                                }, this),
                                xIconElement,
                                xIconElement,
                                xIconElement,
                                checkIconElement
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                            lineNumber: 147,
                            columnNumber: 21
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$PremiumFeatures$2f$PremiumFeatures$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].featureColumn,
                                    children: "Support the Project"
                                }, void 0, false, {
                                    fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                                    lineNumber: 166,
                                    columnNumber: 25
                                }, this),
                                xIconElement,
                                checkIconElement,
                                checkIconElement,
                                checkIconElement
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                            lineNumber: 165,
                            columnNumber: 21
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$PremiumFeatures$2f$PremiumFeatures$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].featureColumn,
                                    children: [
                                        "List of low supply items (",
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            href: "/lowSupply",
                                            children: "here"
                                        }, void 0, false, {
                                            fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                                            lineNumber: 174,
                                            columnNumber: 55
                                        }, this),
                                        ")"
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                                    lineNumber: 173,
                                    columnNumber: 25
                                }, this),
                                xIconElement,
                                xIconElement,
                                checkIconElement,
                                checkIconElement
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                            lineNumber: 172,
                            columnNumber: 21
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$PremiumFeatures$2f$PremiumFeatures$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].featureColumn,
                                    children: "Priority Feature Request"
                                }, void 0, false, {
                                    fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                                    lineNumber: 182,
                                    columnNumber: 25
                                }, this),
                                xIconElement,
                                xIconElement,
                                checkIconElement,
                                checkIconElement
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                            lineNumber: 181,
                            columnNumber: 21
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$PremiumFeatures$2f$PremiumFeatures$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].featureColumn,
                                    children: "Discord Role"
                                }, void 0, false, {
                                    fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                                    lineNumber: 189,
                                    columnNumber: 25
                                }, this),
                                xIconElement,
                                xIconElement,
                                checkIconElement,
                                checkIconElement
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                            lineNumber: 188,
                            columnNumber: 21
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$PremiumFeatures$2f$PremiumFeatures$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].featureColumn,
                                    children: "Longer flip-tracking history"
                                }, void 0, false, {
                                    fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                                    lineNumber: 196,
                                    columnNumber: 25
                                }, this),
                                xIconElement,
                                xIconElement,
                                checkIconElement,
                                checkIconElement
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                            lineNumber: 195,
                            columnNumber: 21
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$PremiumFeatures$2f$PremiumFeatures$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].featureColumn,
                                    children: "Max. Recent/Active Auctions"
                                }, void 0, false, {
                                    fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                                    lineNumber: 203,
                                    columnNumber: 25
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$PremiumFeatures$2f$PremiumFeatures$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].premiumProductColumn,
                                    children: "12"
                                }, void 0, false, {
                                    fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                                    lineNumber: 204,
                                    columnNumber: 25
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$PremiumFeatures$2f$PremiumFeatures$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].premiumProductColumn,
                                    children: "60"
                                }, void 0, false, {
                                    fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                                    lineNumber: 205,
                                    columnNumber: 25
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$PremiumFeatures$2f$PremiumFeatures$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].premiumProductColumn,
                                    children: "120"
                                }, void 0, false, {
                                    fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                                    lineNumber: 206,
                                    columnNumber: 25
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$PremiumFeatures$2f$PremiumFeatures$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].premiumProductColumn,
                                    children: "120"
                                }, void 0, false, {
                                    fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                                    lineNumber: 207,
                                    columnNumber: 25
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                            lineNumber: 202,
                            columnNumber: 21
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$PremiumFeatures$2f$PremiumFeatures$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].featureColumn,
                                    children: "Max. Notifications"
                                }, void 0, false, {
                                    fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                                    lineNumber: 210,
                                    columnNumber: 25
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$PremiumFeatures$2f$PremiumFeatures$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].premiumProductColumn,
                                    children: "3"
                                }, void 0, false, {
                                    fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                                    lineNumber: 211,
                                    columnNumber: 25
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$PremiumFeatures$2f$PremiumFeatures$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].premiumProductColumn,
                                    children: "10"
                                }, void 0, false, {
                                    fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                                    lineNumber: 212,
                                    columnNumber: 25
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$PremiumFeatures$2f$PremiumFeatures$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].premiumProductColumn,
                                    children: "100"
                                }, void 0, false, {
                                    fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                                    lineNumber: 213,
                                    columnNumber: 25
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$PremiumFeatures$2f$PremiumFeatures$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].premiumProductColumn,
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Number$2f$Number$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                        number: 1000
                                    }, void 0, false, {
                                        fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                                        lineNumber: 215,
                                        columnNumber: 29
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                                    lineNumber: 214,
                                    columnNumber: 25
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                            lineNumber: 209,
                            columnNumber: 21
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$PremiumFeatures$2f$PremiumFeatures$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].featureColumn,
                                    children: "Refresh time of /cofl cheapmuseum "
                                }, void 0, false, {
                                    fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                                    lineNumber: 219,
                                    columnNumber: 25
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$PremiumFeatures$2f$PremiumFeatures$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].premiumProductColumn,
                                    children: "2 h"
                                }, void 0, false, {
                                    fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                                    lineNumber: 220,
                                    columnNumber: 25
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$PremiumFeatures$2f$PremiumFeatures$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].premiumProductColumn,
                                    children: "5 min"
                                }, void 0, false, {
                                    fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                                    lineNumber: 221,
                                    columnNumber: 25
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$PremiumFeatures$2f$PremiumFeatures$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].premiumProductColumn,
                                    children: "2 min"
                                }, void 0, false, {
                                    fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                                    lineNumber: 222,
                                    columnNumber: 25
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$PremiumFeatures$2f$PremiumFeatures$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].premiumProductColumn,
                                    children: "2 min"
                                }, void 0, false, {
                                    fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                                    lineNumber: 223,
                                    columnNumber: 25
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                            lineNumber: 218,
                            columnNumber: 21
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$PremiumFeatures$2f$PremiumFeatures$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].featureColumn,
                                    children: "Use /cofl forge in game"
                                }, void 0, false, {
                                    fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                                    lineNumber: 226,
                                    columnNumber: 25
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$PremiumFeatures$2f$PremiumFeatures$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].premiumProductColumn,
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Tooltip$2f$Tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                        content: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            style: {
                                                marginLeft: '5px'
                                            },
                                            children: [
                                                checkIconSvg,
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Help$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                                    fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                                                    lineNumber: 230,
                                                    columnNumber: 51
                                                }, void 0)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                                            lineNumber: 229,
                                            columnNumber: 33
                                        }, void 0),
                                        type: "hover",
                                        tooltipContent: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            children: "The top 3 options require a paid plan"
                                        }, void 0, false, {
                                            fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                                            lineNumber: 234,
                                            columnNumber: 45
                                        }, void 0)
                                    }, void 0, false, {
                                        fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                                        lineNumber: 227,
                                        columnNumber: 69
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                                    lineNumber: 227,
                                    columnNumber: 25
                                }, this),
                                checkIconElement,
                                checkIconElement,
                                checkIconElement
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                            lineNumber: 225,
                            columnNumber: 21
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$PremiumFeatures$2f$PremiumFeatures$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].featureColumn,
                                    children: "Chat Colors"
                                }, void 0, false, {
                                    fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                                    lineNumber: 241,
                                    columnNumber: 25
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$PremiumFeatures$2f$PremiumFeatures$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].premiumProductColumn,
                                    style: {
                                        color: 'gray',
                                        fontWeight: 'bold'
                                    },
                                    children: "Gray"
                                }, void 0, false, {
                                    fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                                    lineNumber: 242,
                                    columnNumber: 25
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$PremiumFeatures$2f$PremiumFeatures$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].premiumProductColumn,
                                    style: {
                                        color: 'white',
                                        fontWeight: 'bold'
                                    },
                                    children: "White"
                                }, void 0, false, {
                                    fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                                    lineNumber: 245,
                                    columnNumber: 25
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$PremiumFeatures$2f$PremiumFeatures$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].premiumProductColumn,
                                    style: {
                                        color: '#32de84',
                                        fontWeight: 'bold'
                                    },
                                    children: "Green"
                                }, void 0, false, {
                                    fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                                    lineNumber: 248,
                                    columnNumber: 25
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$PremiumFeatures$2f$PremiumFeatures$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].premiumProductColumn,
                                    style: {
                                        color: '#ffaa00',
                                        fontWeight: 'bold'
                                    },
                                    children: "Gold"
                                }, void 0, false, {
                                    fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                                    lineNumber: 251,
                                    columnNumber: 25
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                            lineNumber: 240,
                            columnNumber: 21
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$PremiumFeatures$2f$PremiumFeatures$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].featureColumn,
                                    children: [
                                        "Average flip receive time",
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Tooltip$2f$Tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            content: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                style: {
                                                    marginLeft: '5px'
                                                },
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Help$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                                    fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                                                    lineNumber: 261,
                                                    columnNumber: 41
                                                }, void 0)
                                            }, void 0, false, {
                                                fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                                                lineNumber: 260,
                                                columnNumber: 37
                                            }, void 0),
                                            type: "hover",
                                            tooltipContent: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                children: "The Hypixel Auctions API updates once every 60 seconds. After we were able to load new auctions, this is how long it will take until they are shown to you. (Parsing auctions, finding references, comparing to determine profit, distributing and filtering and sending to you)"
                                            }, void 0, false, {
                                                fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                                                lineNumber: 266,
                                                columnNumber: 37
                                            }, void 0)
                                        }, void 0, false, {
                                            fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                                            lineNumber: 258,
                                            columnNumber: 29
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                                    lineNumber: 256,
                                    columnNumber: 25
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$PremiumFeatures$2f$PremiumFeatures$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].premiumProductColumn,
                                    children: "40 sec"
                                }, void 0, false, {
                                    fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                                    lineNumber: 274,
                                    columnNumber: 25
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$PremiumFeatures$2f$PremiumFeatures$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].premiumProductColumn,
                                    children: "10-20 sec"
                                }, void 0, false, {
                                    fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                                    lineNumber: 275,
                                    columnNumber: 25
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$PremiumFeatures$2f$PremiumFeatures$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].premiumProductColumn,
                                    children: "~1 sec"
                                }, void 0, false, {
                                    fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                                    lineNumber: 276,
                                    columnNumber: 25
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$PremiumFeatures$2f$PremiumFeatures$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].premiumProductColumn,
                                    children: "< 1 sec"
                                }, void 0, false, {
                                    fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                                    lineNumber: 277,
                                    columnNumber: 25
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                            lineNumber: 255,
                            columnNumber: 21
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
                    lineNumber: 42,
                    columnNumber: 17
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
            lineNumber: 32,
            columnNumber: 13
        }, this)
    }, void 0, false, {
        fileName: "[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx",
        lineNumber: 31,
        columnNumber: 9
    }, this);
}
_c = PremiumFeatures;
const __TURBOPACK__default__export__ = PremiumFeatures;
var _c;
__turbopack_context__.k.register(_c, "PremiumFeatures");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/Premium/Premium.module.css [app-client] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "cancellationRightCheckbox": "Premium-module__qLYXbW__cancellationRightCheckbox",
  "label": "Premium-module__qLYXbW__label",
  "premiumPayment": "Premium-module__qLYXbW__premiumPayment",
  "premiumPrice": "Premium-module__qLYXbW__premiumPrice",
  "premiumProduct": "Premium-module__qLYXbW__premiumProduct",
  "premiumProductLabel": "Premium-module__qLYXbW__premiumProductLabel",
  "premiumProducts": "Premium-module__qLYXbW__premiumProducts",
  "purchaseCard": "Premium-module__qLYXbW__purchaseCard",
  "sendCoflCoinsButton": "Premium-module__qLYXbW__sendCoflCoinsButton",
});
}}),
"[project]/components/CoflCoins/CoflCoinsPurchase.module.css [app-client] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "discount": "CoflCoinsPurchase-module__25Dhdq__discount",
  "manualRedirectLink": "CoflCoinsPurchase-module__25Dhdq__manualRedirectLink",
  "paymentButton": "CoflCoinsPurchase-module__25Dhdq__paymentButton",
  "paymentButtonWrapper": "CoflCoinsPurchase-module__25Dhdq__paymentButtonWrapper",
  "paymentLabel": "CoflCoinsPurchase-module__25Dhdq__paymentLabel",
  "paymentOption": "CoflCoinsPurchase-module__25Dhdq__paymentOption",
  "premiumPlanCard": "CoflCoinsPurchase-module__25Dhdq__premiumPlanCard",
  "premiumPrice": "CoflCoinsPurchase-module__25Dhdq__premiumPrice",
  "productGrid": "CoflCoinsPurchase-module__25Dhdq__productGrid",
});
}}),
"[project]/components/CoflCoins/GenericProviderPurchaseCard.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>GenericProviderPurchaseCard)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Button$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/Button.js [app-client] (ecmascript) <export default as Button>");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Tooltip$2f$Tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/Tooltip/Tooltip.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$CoflCoins$2f$CoflCoinsPurchase$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/components/CoflCoins/CoflCoinsPurchase.module.css [app-client] (css module)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Help$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/icons-material/esm/Help.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Number$2f$Number$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/Number/Number.tsx [app-client] (ecmascript)");
;
;
;
;
;
;
function GenericProviderPurchaseCard(props) {
    function getRoundedPrice(price) {
        return Math.round(price * 100) / 100;
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$CoflCoins$2f$CoflCoinsPurchase$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].paymentOption,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$CoflCoins$2f$CoflCoinsPurchase$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].paymentLabel,
                children: [
                    props.type === 'PayPal' ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Tooltip$2f$Tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                        content: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                            children: [
                                "Buy with ",
                                props.type,
                                ' ',
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    style: {
                                        marginLeft: '5px'
                                    },
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Help$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                        fileName: "[project]/components/CoflCoins/GenericProviderPurchaseCard.tsx",
                                        lineNumber: 32,
                                        columnNumber: 37
                                    }, void 0)
                                }, void 0, false, {
                                    fileName: "[project]/components/CoflCoins/GenericProviderPurchaseCard.tsx",
                                    lineNumber: 31,
                                    columnNumber: 33
                                }, void 0)
                            ]
                        }, void 0, true),
                        type: "hover",
                        tooltipContent: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            children: "Higher price than with credit card due to higher fees"
                        }, void 0, false, {
                            fileName: "[project]/components/CoflCoins/GenericProviderPurchaseCard.tsx",
                            lineNumber: 37,
                            columnNumber: 41
                        }, void 0)
                    }, void 0, false, {
                        fileName: "[project]/components/CoflCoins/GenericProviderPurchaseCard.tsx",
                        lineNumber: 27,
                        columnNumber: 21
                    }, this) : null,
                    props.type === 'Stripe' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        children: "Buy with other payment methods"
                    }, void 0, false, {
                        fileName: "[project]/components/CoflCoins/GenericProviderPurchaseCard.tsx",
                        lineNumber: 40,
                        columnNumber: 45
                    }, this),
                    props.type === 'LemonSqueezy' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        children: "Continue to payment"
                    }, void 0, false, {
                        fileName: "[project]/components/CoflCoins/GenericProviderPurchaseCard.tsx",
                        lineNumber: 41,
                        columnNumber: 51
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/components/CoflCoins/GenericProviderPurchaseCard.tsx",
                lineNumber: 25,
                columnNumber: 13
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Tooltip$2f$Tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                type: "hover",
                tooltipContent: props.disabledTooltip,
                content: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$CoflCoins$2f$CoflCoinsPurchase$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].paymentButtonWrapper,
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Button$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__["Button"], {
                        variant: "success",
                        onClick: ()=>{
                            props.onPay();
                        },
                        className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$CoflCoins$2f$CoflCoinsPurchase$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].paymentButton,
                        disabled: props.isDisabled,
                        children: props.isRedirecting ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$CoflCoins$2f$CoflCoinsPurchase$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].manualRedirectLink,
                            children: props.redirectLink ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                children: [
                                    "Redirecting to PayPal...",
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("br", {}, void 0, false, {
                                        fileName: "[project]/components/CoflCoins/GenericProviderPurchaseCard.tsx",
                                        lineNumber: 61,
                                        columnNumber: 45
                                    }, void 0),
                                    " Not working?",
                                    ' ',
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                                        href: props.redirectLink,
                                        onClick: (e)=>{
                                            e.stopPropagation();
                                        },
                                        target: "_blank",
                                        children: "Click here"
                                    }, void 0, false, {
                                        fileName: "[project]/components/CoflCoins/GenericProviderPurchaseCard.tsx",
                                        lineNumber: 62,
                                        columnNumber: 45
                                    }, void 0)
                                ]
                            }, void 0, true) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: "Contacting payment provider..."
                            }, void 0, false, {
                                fileName: "[project]/components/CoflCoins/GenericProviderPurchaseCard.tsx",
                                lineNumber: 73,
                                columnNumber: 41
                            }, void 0)
                        }, void 0, false, {
                            fileName: "[project]/components/CoflCoins/GenericProviderPurchaseCard.tsx",
                            lineNumber: 57,
                            columnNumber: 33
                        }, void 0) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Number$2f$Number$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    number: getRoundedPrice(props.discount ? props.price * props.discount : props.price)
                                }, void 0, false, {
                                    fileName: "[project]/components/CoflCoins/GenericProviderPurchaseCard.tsx",
                                    lineNumber: 78,
                                    columnNumber: 37
                                }, void 0),
                                " Euro",
                                props.discount ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    style: {
                                        color: 'red',
                                        fontWeight: 'bold',
                                        paddingLeft: '20px'
                                    },
                                    children: [
                                        Math.round((1 - props.discount) * 100),
                                        "% OFF"
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/components/CoflCoins/GenericProviderPurchaseCard.tsx",
                                    lineNumber: 80,
                                    columnNumber: 41
                                }, void 0) : null,
                                props.discount ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    style: {
                                        fontSize: 'x-small',
                                        margin: 0,
                                        padding: 0
                                    },
                                    children: [
                                        "Original price: ",
                                        getRoundedPrice(props.price)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/components/CoflCoins/GenericProviderPurchaseCard.tsx",
                                    lineNumber: 85,
                                    columnNumber: 41
                                }, void 0) : null
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/CoflCoins/GenericProviderPurchaseCard.tsx",
                            lineNumber: 77,
                            columnNumber: 33
                        }, void 0)
                    }, void 0, false, {
                        fileName: "[project]/components/CoflCoins/GenericProviderPurchaseCard.tsx",
                        lineNumber: 48,
                        columnNumber: 25
                    }, void 0)
                }, void 0, false, {
                    fileName: "[project]/components/CoflCoins/GenericProviderPurchaseCard.tsx",
                    lineNumber: 47,
                    columnNumber: 21
                }, void 0)
            }, void 0, false, {
                fileName: "[project]/components/CoflCoins/GenericProviderPurchaseCard.tsx",
                lineNumber: 43,
                columnNumber: 13
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/components/CoflCoins/GenericProviderPurchaseCard.tsx",
        lineNumber: 24,
        columnNumber: 9
    }, this);
}
_c = GenericProviderPurchaseCard;
var _c;
__turbopack_context__.k.register(_c, "GenericProviderPurchaseCard");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/CoflCoins/PurchaseElement.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>PurchaseElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Card$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/Card.js [app-client] (ecmascript) <export default as Card>");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$CoflCoins$2f$CoflCoinsPurchase$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/components/CoflCoins/CoflCoinsPurchase.module.css [app-client] (css module)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Number$2f$Number$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/Number/Number.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$CoflCoins$2f$GenericProviderPurchaseCard$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/CoflCoins/GenericProviderPurchaseCard.tsx [app-client] (ecmascript)");
'use client';
;
;
;
;
;
// prettier-ignore
const EU_Countries = [
    "AT",
    "BE",
    "BG",
    "HR",
    "CY",
    "CZ",
    "DK",
    "EE",
    "FI",
    "FR",
    "DE",
    "GR",
    "HU",
    "IE",
    "IT",
    "LV",
    "LT",
    "LU",
    "MT",
    "NL",
    "PL",
    "PT",
    "RO",
    "SK",
    "SI",
    "ES",
    "SE"
];
let PAYPAL_STRIPE_ALLOWED = [
    ...EU_Countries,
    'GB',
    'US'
];
function PurchaseElement(props) {
    let isDisabled = props.isDisabled || !props.countryCode;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Card$3e$__["Card"], {
        className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$CoflCoins$2f$CoflCoinsPurchase$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].premiumPlanCard,
        style: props.isSpecial1800CoinsMultiplier ? {
            width: '100%'
        } : {},
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Card$3e$__["Card"].Header, {
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Card$3e$__["Card"].Title, {
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Number$2f$Number$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                            number: props.coflCoinsToBuy
                        }, void 0, false, {
                            fileName: "[project]/components/CoflCoins/PurchaseElement.tsx",
                            lineNumber: 40,
                            columnNumber: 21
                        }, this),
                        " CoflCoins"
                    ]
                }, void 0, true, {
                    fileName: "[project]/components/CoflCoins/PurchaseElement.tsx",
                    lineNumber: 39,
                    columnNumber: 17
                }, this)
            }, void 0, false, {
                fileName: "[project]/components/CoflCoins/PurchaseElement.tsx",
                lineNumber: 38,
                columnNumber: 13
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Card$3e$__["Card"].Body, {
                children: [
                    props.isSpecial1800CoinsMultiplier ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                children: [
                                    "We noticed that your CoflCoins are not a multiple of ",
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Number$2f$Number$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                        number: 1800
                                    }, void 0, false, {
                                        fileName: "[project]/components/CoflCoins/PurchaseElement.tsx",
                                        lineNumber: 47,
                                        columnNumber: 82
                                    }, this),
                                    " and therefore you would not be able to use all of them to buy premium. Here you can purchase ",
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Number$2f$Number$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                        number: props.coflCoinsToBuy
                                    }, void 0, false, {
                                        fileName: "[project]/components/CoflCoins/PurchaseElement.tsx",
                                        lineNumber: 48,
                                        columnNumber: 67
                                    }, this),
                                    " CoflCoins to again be able to do that."
                                ]
                            }, void 0, true, {
                                fileName: "[project]/components/CoflCoins/PurchaseElement.tsx",
                                lineNumber: 46,
                                columnNumber: 25
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                children: [
                                    "Due to the fees we have to pay to our payment providers we sadly can't provide purchases of less than ",
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Number$2f$Number$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                        number: 1800
                                    }, void 0, false, {
                                        fileName: "[project]/components/CoflCoins/PurchaseElement.tsx",
                                        lineNumber: 51,
                                        columnNumber: 131
                                    }, this),
                                    ' ',
                                    "CoflCoins at once."
                                ]
                            }, void 0, true, {
                                fileName: "[project]/components/CoflCoins/PurchaseElement.tsx",
                                lineNumber: 50,
                                columnNumber: 25
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("hr", {}, void 0, false, {
                                fileName: "[project]/components/CoflCoins/PurchaseElement.tsx",
                                lineNumber: 54,
                                columnNumber: 25
                            }, this)
                        ]
                    }, void 0, true) : null,
                    props.countryCode && PAYPAL_STRIPE_ALLOWED.includes(props.countryCode) ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$CoflCoins$2f$GenericProviderPurchaseCard$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                type: "PayPal",
                                isDisabled: isDisabled,
                                onPay: ()=>{
                                    props.onPayPalPay(props.paypalProductId, props.isSpecial1800CoinsMultiplier ? props.coflCoinsToBuy : undefined);
                                },
                                price: props.paypalPrice,
                                redirectLink: props.redirectLink,
                                discount: props.discount,
                                isRedirecting: !props.isSpecial1800CoinsMultiplier ? props.paypalProductId === props.loadingProductId : `${props.paypalProductId}_${props.coflCoinsToBuy}` === props.loadingProductId,
                                disabledTooltip: props.disabledTooltip
                            }, void 0, false, {
                                fileName: "[project]/components/CoflCoins/PurchaseElement.tsx",
                                lineNumber: 59,
                                columnNumber: 25
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$CoflCoins$2f$GenericProviderPurchaseCard$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                type: "Stripe",
                                isDisabled: isDisabled,
                                onPay: ()=>{
                                    props.onStripePay(props.stripeProductId, props.isSpecial1800CoinsMultiplier ? props.coflCoinsToBuy : undefined);
                                },
                                price: props.stripePrice,
                                redirectLink: props.redirectLink,
                                discount: props.discount,
                                isRedirecting: !props.isSpecial1800CoinsMultiplier ? props.stripeProductId === props.loadingProductId : `${props.stripeProductId}_${props.coflCoinsToBuy}` === props.loadingProductId,
                                disabledTooltip: props.disabledTooltip
                            }, void 0, false, {
                                fileName: "[project]/components/CoflCoins/PurchaseElement.tsx",
                                lineNumber: 75,
                                columnNumber: 25
                            }, this)
                        ]
                    }, void 0, true) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$CoflCoins$2f$GenericProviderPurchaseCard$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                        type: "LemonSqueezy",
                        isDisabled: isDisabled,
                        onPay: ()=>{
                            props.onLemonSqeezyPay(props.lemonsqueezyProductId, props.isSpecial1800CoinsMultiplier ? props.coflCoinsToBuy : undefined);
                        },
                        price: props.lemonsqueezyPrice,
                        redirectLink: props.redirectLink,
                        discount: props.discount,
                        isRedirecting: !props.isSpecial1800CoinsMultiplier ? props.lemonsqueezyProductId === props.loadingProductId : `${props.lemonsqueezyProductId}_${props.coflCoinsToBuy}` === props.loadingProductId,
                        disabledTooltip: props.disabledTooltip
                    }, void 0, false, {
                        fileName: "[project]/components/CoflCoins/PurchaseElement.tsx",
                        lineNumber: 93,
                        columnNumber: 21
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/components/CoflCoins/PurchaseElement.tsx",
                lineNumber: 43,
                columnNumber: 13
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/components/CoflCoins/PurchaseElement.tsx",
        lineNumber: 37,
        columnNumber: 9
    }, this);
}
_c = PurchaseElement;
var _c;
__turbopack_context__.k.register(_c, "PurchaseElement");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/utils/CountryUtils.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "getCountries": (()=>getCountries),
    "getCountry": (()=>getCountry),
    "getCountryFromUserLanguage": (()=>getCountryFromUserLanguage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$select$2d$country$2d$list$2f$country$2d$list$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-select-country-list/country-list.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SSRUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/SSRUtils.tsx [app-client] (ecmascript)");
;
;
let countries;
function getCountries() {
    if (countries) {
        return countries;
    }
    let result = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$select$2d$country$2d$list$2f$country$2d$list$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])().getData();
    countries = result;
    return result;
}
function getCountry(countryCode) {
    return getCountries().find((country)=>country.value === countryCode);
}
function getCountryFromUserLanguage() {
    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SSRUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isClientSideRendering"])()) {
        return undefined;
    }
    let language = navigator.language;
    if (!language) {
        language = 'en-US';
    }
    if (language.includes('-')) {
        language = language.split('-')[1];
    }
    return getCountries().find((country)=>country.value.toLowerCase() === language.toLowerCase());
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/CountrySelect/CountrySelect.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>CountrySelect)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2d$typeahead$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap-typeahead/es/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2d$typeahead$2f$es$2f$components$2f$Menu$2f$Menu$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Menu$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap-typeahead/es/components/Menu/Menu.js [app-client] (ecmascript) <export default as Menu>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2d$typeahead$2f$es$2f$components$2f$MenuItem$2f$MenuItem$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__MenuItem$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap-typeahead/es/components/MenuItem/MenuItem.js [app-client] (ecmascript) <export default as MenuItem>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2d$typeahead$2f$es$2f$components$2f$Typeahead$2f$Typeahead$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Typeahead$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap-typeahead/es/components/Typeahead/Typeahead.js [app-client] (ecmascript) <export default as Typeahead>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/Form.js [app-client] (ecmascript) <export default as Form>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$InputGroup$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__InputGroup$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/InputGroup.js [app-client] (ecmascript) <export default as InputGroup>");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$CountryUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/CountryUtils.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
function CountrySelect(props) {
    _s();
    const countryOptions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$CountryUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getCountries"])();
    let [selectedCountry, setSelectedCountryCode] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(props.defaultCountry);
    let ref = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    function getCountryImage(countryCode) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
            src: `https://flagcdn.com/16x12/${countryCode.toLowerCase()}.png`,
            srcSet: `https://flagcdn.com/32x24/${countryCode.toLowerCase()}.png 2x, https://flagcdn.com/48x36/${countryCode.toLowerCase()}.png 3x`,
            width: "16",
            height: "12",
            alt: countryCode,
            style: {
                marginRight: '5px'
            }
        }, void 0, false, {
            fileName: "[project]/components/CountrySelect/CountrySelect.tsx",
            lineNumber: 21,
            columnNumber: 13
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        style: {
            display: 'flex',
            alignItems: 'center',
            gap: 15,
            paddingBottom: 15
        },
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                htmlFor: "countryTypeahead",
                children: "Your Country: "
            }, void 0, false, {
                fileName: "[project]/components/CountrySelect/CountrySelect.tsx",
                lineNumber: 33,
                columnNumber: 13
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2d$typeahead$2f$es$2f$components$2f$Typeahead$2f$Typeahead$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Typeahead$3e$__["Typeahead"], {
                id: "countryTypeahead",
                style: {
                    width: 'auto'
                },
                disabled: props.isLoading,
                placeholder: props.isLoading ? 'Loading...' : 'Select your country',
                ref: ref,
                defaultSelected: selectedCountry ? [
                    selectedCountry
                ] : [],
                isLoading: props.isLoading,
                onChange: (e)=>{
                    if (e[0]) {
                        setSelectedCountryCode(e[0]);
                        if (props.onCountryChange) {
                            props.onCountryChange(e[0]);
                        }
                    }
                },
                labelKey: (option)=>{
                    return option ? option.label : '';
                },
                onFocus: (e)=>{
                    if (ref.current) {
                        ref.current.clear();
                    }
                },
                options: countryOptions,
                selectHint: (shouldSelect, event)=>{
                    return event.key === 'Enter' || shouldSelect;
                },
                renderInput: ({ inputRef, referenceElementRef, ...inputProps })=>{
                    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$InputGroup$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__InputGroup$3e$__["InputGroup"], {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$InputGroup$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__InputGroup$3e$__["InputGroup"].Text, {
                                children: selectedCountry ? getCountryImage(selectedCountry.value) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    style: {
                                        minWidth: 16
                                    }
                                }, void 0, false, {
                                    fileName: "[project]/components/CountrySelect/CountrySelect.tsx",
                                    lineNumber: 65,
                                    columnNumber: 106
                                }, void 0)
                            }, void 0, false, {
                                fileName: "[project]/components/CountrySelect/CountrySelect.tsx",
                                lineNumber: 65,
                                columnNumber: 29
                            }, void 0),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__["Form"].Control, {
                                ...inputProps,
                                ref: (input)=>{
                                    inputRef(input);
                                    referenceElementRef(input);
                                }
                            }, void 0, false, {
                                fileName: "[project]/components/CountrySelect/CountrySelect.tsx",
                                lineNumber: 66,
                                columnNumber: 29
                            }, void 0)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/CountrySelect/CountrySelect.tsx",
                        lineNumber: 64,
                        columnNumber: 25
                    }, void 0);
                },
                renderMenu: (results, menuProps)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2d$typeahead$2f$es$2f$components$2f$Menu$2f$Menu$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Menu$3e$__["Menu"], {
                        ...menuProps,
                        children: results.map((result, index)=>{
                            let element = result;
                            if (element.paginationOption) {
                                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2d$typeahead$2f$es$2f$components$2f$MenuItem$2f$MenuItem$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__MenuItem$3e$__["MenuItem"], {
                                    option: element,
                                    position: index,
                                    children: "More results..."
                                }, void 0, false, {
                                    fileName: "[project]/components/CountrySelect/CountrySelect.tsx",
                                    lineNumber: 82,
                                    columnNumber: 37
                                }, void 0);
                            }
                            if (!element || !element.label || !element.value) {
                                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2d$typeahead$2f$es$2f$components$2f$MenuItem$2f$MenuItem$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__MenuItem$3e$__["MenuItem"], {
                                    option: element,
                                    position: index
                                }, void 0, false, {
                                    fileName: "[project]/components/CountrySelect/CountrySelect.tsx",
                                    lineNumber: 88,
                                    columnNumber: 40
                                }, void 0);
                            }
                            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2d$typeahead$2f$es$2f$components$2f$MenuItem$2f$MenuItem$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__MenuItem$3e$__["MenuItem"], {
                                option: element,
                                position: index,
                                children: [
                                    getCountryImage(element.value),
                                    element.label
                                ]
                            }, element.value, true, {
                                fileName: "[project]/components/CountrySelect/CountrySelect.tsx",
                                lineNumber: 91,
                                columnNumber: 33
                            }, void 0);
                        })
                    }, void 0, false, {
                        fileName: "[project]/components/CountrySelect/CountrySelect.tsx",
                        lineNumber: 77,
                        columnNumber: 21
                    }, void 0)
            }, void 0, false, {
                fileName: "[project]/components/CountrySelect/CountrySelect.tsx",
                lineNumber: 34,
                columnNumber: 13
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/components/CountrySelect/CountrySelect.tsx",
        lineNumber: 32,
        columnNumber: 9
    }, this);
}
_s(CountrySelect, "03AwYBYr/P2AXjZxFLHBcnqOJRo=");
_c = CountrySelect;
var _c;
__turbopack_context__.k.register(_c, "CountrySelect");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/CoflCoins/CoflCoinsPurchase.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Button$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/Button.js [app-client] (ecmascript) <export default as Button>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-toastify/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/api/ApiHelper.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Hooks$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/Hooks.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$CoflCoins$2f$CoflCoinsPurchase$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/components/CoflCoins/CoflCoinsPurchase.module.css [app-client] (css module)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$CoflCoins$2f$PurchaseElement$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/CoflCoins/PurchaseElement.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$CountryUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/CountryUtils.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$CountrySelect$2f$CountrySelect$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/CountrySelect/CountrySelect.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SettingsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/SettingsUtils.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
;
;
;
;
function Payment(props) {
    _s();
    let [loadingId, setLoadingId] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    let [currentRedirectLink, setCurrentRedirectLink] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    let [showAll, setShowAll] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    let [defaultCountry, setDefaultCountry] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])();
    let [selectedCountry, setSelectedCountry] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])();
    let coflCoins = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Hooks$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCoflCoins"])();
    let isDisabled = !props.cancellationRightLossConfirmed || !selectedCountry;
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "Payment.useEffect": ()=>{
            loadDefaultCountry();
        }
    }["Payment.useEffect"], []);
    async function loadDefaultCountry() {
        let cachedCountryCode = localStorage.getItem(__TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SettingsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["USER_COUNTRY_CODE"]);
        if (cachedCountryCode) {
            setDefaultCountry((0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$CountryUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getCountry"])(cachedCountryCode));
            setSelectedCountry((0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$CountryUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getCountry"])(cachedCountryCode));
            return;
        }
        let response = null;
        try {
            response = await fetch('https://api.country.is');
        } catch  {
            console.error('Failed to fetch country from api.country.is');
        }
        if (response && response.ok) {
            let result = await response.json();
            let country = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$CountryUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getCountry"])(result.country) || (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$CountryUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getCountryFromUserLanguage"])();
            setDefaultCountry(country);
            setSelectedCountry(country);
            localStorage.setItem(__TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SettingsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["USER_COUNTRY_CODE"], result.country);
        } else {
            let country = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$CountryUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getCountryFromUserLanguage"])();
            setDefaultCountry(country);
            setSelectedCountry(country);
        }
    }
    function onPayPaypal(productId, coflCoins) {
        setLoadingId(coflCoins ? `${productId}_${coflCoins}` : productId);
        setCurrentRedirectLink('');
        __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].paypalPurchase(productId, coflCoins).then((data)=>{
            setCurrentRedirectLink(data.directLink);
            window.open(data.directLink, '_self');
        }).catch(onPaymentRedirectFail);
    }
    function onPayStripe(productId, coflCoins) {
        setLoadingId(coflCoins ? `${productId}_${coflCoins}` : productId);
        setCurrentRedirectLink('');
        __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].stripePurchase(productId, coflCoins).then((data)=>{
            setCurrentRedirectLink(data.directLink);
            window.open(data.directLink, '_self');
        }).catch(onPaymentRedirectFail);
    }
    function onPayLemonSqueezy(productId, coflCoins) {
        setLoadingId(coflCoins ? `${productId}_${coflCoins}` : productId);
        setCurrentRedirectLink('');
        __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].lemonsqueezyPurchase(productId, coflCoins).then((data)=>{
            setCurrentRedirectLink(data.directLink);
            window.open(data.directLink, '_self');
        }).catch(onPaymentRedirectFail);
    }
    function onPaymentRedirectFail() {
        setCurrentRedirectLink('');
        setLoadingId('');
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('Something went wrong. Please try again.');
    }
    function getDisabledPaymentTooltip() {
        if (!props.cancellationRightLossConfirmed) {
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                children: "Please note the information regarding your cancellation right above."
            }, void 0, false, {
                fileName: "[project]/components/CoflCoins/CoflCoinsPurchase.tsx",
                lineNumber: 100,
                columnNumber: 20
            }, this);
        }
        if (!selectedCountry) {
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                children: "Please select your country. This information is necessary for tax purposes."
            }, void 0, false, {
                fileName: "[project]/components/CoflCoins/CoflCoinsPurchase.tsx",
                lineNumber: 103,
                columnNumber: 20
            }, this);
        }
        return undefined;
    }
    let disabledTooltip = getDisabledPaymentTooltip();
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            children: [
                defaultCountry ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$CountrySelect$2f$CountrySelect$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    isLoading: !defaultCountry,
                    defaultCountry: defaultCountry,
                    onCountryChange: setSelectedCountry
                }, "country-select", false, {
                    fileName: "[project]/components/CoflCoins/CoflCoinsPurchase.tsx",
                    lineNumber: 113,
                    columnNumber: 21
                }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$CountrySelect$2f$CountrySelect$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    isLoading: true
                }, "loading-country-select", false, {
                    fileName: "[project]/components/CoflCoins/CoflCoinsPurchase.tsx",
                    lineNumber: 115,
                    columnNumber: 21
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$CoflCoins$2f$CoflCoinsPurchase$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].productGrid,
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$CoflCoins$2f$PurchaseElement$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                            coflCoinsToBuy: 1800,
                            loadingProductId: loadingId,
                            redirectLink: currentRedirectLink,
                            paypalPrice: 8.69,
                            stripePrice: 8.42,
                            lemonsqueezyPrice: 8.69,
                            disabledTooltip: disabledTooltip,
                            isDisabled: isDisabled,
                            onPayPalPay: onPayPaypal,
                            onStripePay: onPayStripe,
                            onLemonSqeezyPay: onPayLemonSqueezy,
                            paypalProductId: "p_cc_1800",
                            stripeProductId: "s_cc_1800",
                            lemonsqueezyProductId: "l_cc_1800",
                            countryCode: selectedCountry ? selectedCountry.value : undefined
                        }, void 0, false, {
                            fileName: "[project]/components/CoflCoins/CoflCoinsPurchase.tsx",
                            lineNumber: 119,
                            columnNumber: 21
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$CoflCoins$2f$PurchaseElement$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                            coflCoinsToBuy: 5400,
                            loadingProductId: loadingId,
                            redirectLink: currentRedirectLink,
                            paypalPrice: 22.99,
                            stripePrice: 22.69,
                            lemonsqueezyPrice: 22.69,
                            disabledTooltip: disabledTooltip,
                            isDisabled: isDisabled,
                            onPayPalPay: onPayPaypal,
                            onStripePay: onPayStripe,
                            onLemonSqeezyPay: onPayLemonSqueezy,
                            paypalProductId: "p_cc_5400",
                            stripeProductId: "s_cc_5400",
                            lemonsqueezyProductId: "l_cc_5400",
                            countryCode: selectedCountry ? selectedCountry.value : undefined
                        }, void 0, false, {
                            fileName: "[project]/components/CoflCoins/CoflCoinsPurchase.tsx",
                            lineNumber: 136,
                            columnNumber: 21
                        }, this),
                        !showAll ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Button$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__["Button"], {
                            style: {
                                width: '100%'
                            },
                            onClick: ()=>{
                                setShowAll(true);
                            },
                            children: "Show all CoflCoin Options"
                        }, void 0, false, {
                            fileName: "[project]/components/CoflCoins/CoflCoinsPurchase.tsx",
                            lineNumber: 154,
                            columnNumber: 25
                        }, this) : null,
                        showAll ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$CoflCoins$2f$PurchaseElement$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    coflCoinsToBuy: 10800,
                                    loadingProductId: loadingId,
                                    redirectLink: currentRedirectLink,
                                    paypalPrice: 39.69,
                                    stripePrice: 38.99,
                                    lemonsqueezyPrice: 39.69,
                                    disabledTooltip: disabledTooltip,
                                    isDisabled: isDisabled,
                                    onPayPalPay: onPayPaypal,
                                    onStripePay: onPayStripe,
                                    onLemonSqeezyPay: onPayLemonSqueezy,
                                    paypalProductId: "p_cc_10800",
                                    stripeProductId: "s_cc_10800",
                                    lemonsqueezyProductId: "l_cc_10800",
                                    countryCode: selectedCountry ? selectedCountry.value : undefined
                                }, void 0, false, {
                                    fileName: "[project]/components/CoflCoins/CoflCoinsPurchase.tsx",
                                    lineNumber: 165,
                                    columnNumber: 29
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$CoflCoins$2f$PurchaseElement$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    coflCoinsToBuy: 21600,
                                    loadingProductId: loadingId,
                                    redirectLink: currentRedirectLink,
                                    paypalPrice: 78.69,
                                    stripePrice: 74.99,
                                    lemonsqueezyPrice: 78.69,
                                    disabledTooltip: disabledTooltip,
                                    isDisabled: isDisabled,
                                    onPayPalPay: onPayPaypal,
                                    onStripePay: onPayStripe,
                                    onLemonSqeezyPay: onPayLemonSqueezy,
                                    paypalProductId: "p_cc_21600",
                                    stripeProductId: "s_cc_21600",
                                    lemonsqueezyProductId: "l_cc_21600",
                                    countryCode: selectedCountry ? selectedCountry.value : undefined
                                }, void 0, false, {
                                    fileName: "[project]/components/CoflCoins/CoflCoinsPurchase.tsx",
                                    lineNumber: 182,
                                    columnNumber: 29
                                }, this),
                                coflCoins % 1800 != 0 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$CoflCoins$2f$PurchaseElement$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    coflCoinsToBuy: 1800 + (1800 - coflCoins % 1800),
                                    loadingProductId: loadingId,
                                    redirectLink: currentRedirectLink,
                                    paypalPrice: 8.69 / 1800 * (1800 + (1800 - coflCoins % 1800)),
                                    stripePrice: 8.42 / 1800 * (1800 + (1800 - coflCoins % 1800)),
                                    lemonsqueezyPrice: 8.69 / 1800 * (1800 + (1800 - coflCoins % 1800)),
                                    disabledTooltip: disabledTooltip,
                                    isDisabled: isDisabled,
                                    onPayPalPay: onPayPaypal,
                                    onStripePay: onPayStripe,
                                    onLemonSqeezyPay: onPayLemonSqueezy,
                                    isSpecial1800CoinsMultiplier: true,
                                    paypalProductId: "p_cc_1800",
                                    stripeProductId: "s_cc_1800",
                                    lemonsqueezyProductId: "l_cc_1800",
                                    countryCode: selectedCountry ? selectedCountry.value : undefined
                                }, void 0, false, {
                                    fileName: "[project]/components/CoflCoins/CoflCoinsPurchase.tsx",
                                    lineNumber: 200,
                                    columnNumber: 33
                                }, this) : null
                            ]
                        }, void 0, true) : null
                    ]
                }, void 0, true, {
                    fileName: "[project]/components/CoflCoins/CoflCoinsPurchase.tsx",
                    lineNumber: 118,
                    columnNumber: 17
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/components/CoflCoins/CoflCoinsPurchase.tsx",
            lineNumber: 111,
            columnNumber: 13
        }, this)
    }, void 0, false, {
        fileName: "[project]/components/CoflCoins/CoflCoinsPurchase.tsx",
        lineNumber: 110,
        columnNumber: 9
    }, this);
}
_s(Payment, "3yhwZ1FUxp/3Bb+cP3dAfwqCjHw=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Hooks$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCoflCoins"]
    ];
});
_c = Payment;
const __TURBOPACK__default__export__ = Payment;
var _c;
__turbopack_context__.k.register(_c, "Payment");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/CoflCoins/CoflCoinsDisplay.module.css [app-client] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "border": "CoflCoinsDisplay-module__3S8iUW__border",
});
}}),
"[project]/components/CoflCoins/CoflCoinsDisplay.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "CoflCoinsDisplay": (()=>CoflCoinsDisplay)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/api/ApiTypes.d.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Hooks$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/Hooks.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$LoadingUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/LoadingUtils.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Number$2f$Number$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/Number/Number.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$CoflCoins$2f$CoflCoinsDisplay$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/components/CoflCoins/CoflCoinsDisplay.module.css [app-client] (css module)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-toastify/dist/index.mjs [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
;
function CoflCoinsDisplay() {
    _s();
    let coflCoins = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Hooks$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCoflCoins"])();
    let [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "CoflCoinsDisplay.useEffect": ()=>{
            loadCoflCoins();
            document.addEventListener(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CUSTOM_EVENTS"].FLIP_SETTINGS_CHANGE, loadCoflCoins);
            return ({
                "CoflCoinsDisplay.useEffect": ()=>{
                    document.removeEventListener(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CUSTOM_EVENTS"].FLIP_SETTINGS_CHANGE, loadCoflCoins);
                }
            })["CoflCoinsDisplay.useEffect"];
        }
    }["CoflCoinsDisplay.useEffect"], []);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "CoflCoinsDisplay.useEffect": ()=>{
            if (coflCoins !== -1) {
                setIsLoading(false);
            } else {
                setIsLoading(true);
            }
        }
    }["CoflCoinsDisplay.useEffect"], [
        coflCoins
    ]);
    function loadCoflCoins() {}
    if (isNaN(coflCoins) || coflCoins === undefined || coflCoins === null) {
        console.error('coflCoins is not a number');
        console.error(coflCoins);
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('Something went wrong while loading your CoflCoins. Please try again.');
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "cofl-coins-display",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("fieldset", {
            className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$CoflCoins$2f$CoflCoinsDisplay$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].border,
            style: {
                width: 'max-content',
                borderRadius: '15px',
                textAlign: 'center'
            },
            children: isLoading ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$LoadingUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getLoadingElement"])(/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {}, void 0, false, {
                fileName: "[project]/components/CoflCoins/CoflCoinsDisplay.tsx",
                lineNumber: 43,
                columnNumber: 39
            }, this)) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("b", {
                style: {
                    fontSize: 'x-large'
                },
                children: [
                    "Balance: ",
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Number$2f$Number$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                        number: coflCoins
                    }, void 0, false, {
                        fileName: "[project]/components/CoflCoins/CoflCoinsDisplay.tsx",
                        lineNumber: 46,
                        columnNumber: 34
                    }, this),
                    " CoflCoins"
                ]
            }, void 0, true, {
                fileName: "[project]/components/CoflCoins/CoflCoinsDisplay.tsx",
                lineNumber: 45,
                columnNumber: 21
            }, this)
        }, void 0, false, {
            fileName: "[project]/components/CoflCoins/CoflCoinsDisplay.tsx",
            lineNumber: 41,
            columnNumber: 13
        }, this)
    }, void 0, false, {
        fileName: "[project]/components/CoflCoins/CoflCoinsDisplay.tsx",
        lineNumber: 40,
        columnNumber: 9
    }, this);
}
_s(CoflCoinsDisplay, "MQDxuNPwHmitzudPYuqZIQOWpgM=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Hooks$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCoflCoins"]
    ];
});
_c = CoflCoinsDisplay;
var _c;
__turbopack_context__.k.register(_c, "CoflCoinsDisplay");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/Premium/BuyPremium/BuyPremium.module.css [app-client] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "coinBalance": "BuyPremium-module__gXX4Mq__coinBalance",
  "dropdown": "BuyPremium-module__gXX4Mq__dropdown",
  "label": "BuyPremium-module__gXX4Mq__label",
  "priceRangeButton": "BuyPremium-module__gXX4Mq__priceRangeButton",
  "purchaseCard": "BuyPremium-module__gXX4Mq__purchaseCard",
});
}}),
"[project]/components/Premium/BuyPremiumConfirmationDialog/BuyPremiumConfirmationDialog.module.css [app-client] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "label": "BuyPremiumConfirmationDialog-module__o90etG__label",
});
}}),
"[project]/components/Premium/BuyPremiumConfirmationDialog/BuyPremiumConfirmationDialog.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>BuyPremiumConfirmationDialog)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Button$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/Button.js [app-client] (ecmascript) <export default as Button>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Modal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Modal$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/Modal.js [app-client] (ecmascript) <export default as Modal>");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$BuyPremiumConfirmationDialog$2f$BuyPremiumConfirmationDialog$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/components/Premium/BuyPremiumConfirmationDialog/BuyPremiumConfirmationDialog.module.css [app-client] (css module)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$PremiumTypeUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/PremiumTypeUtils.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$oauth$2f$google$2f$dist$2f$index$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@react-oauth/google/dist/index.esm.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-toastify/dist/index.mjs [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
function BuyPremiumConfirmationDialog(props) {
    _s();
    let [hasConfirmedLogin, setHasConfirmedLogin] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    let [googleToken, setGoogleToken] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Modal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Modal$3e$__["Modal"], {
        show: props.show,
        onHide: ()=>{
            props.onHide();
        },
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Modal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Modal$3e$__["Modal"].Header, {
                closeButton: true,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Modal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Modal$3e$__["Modal"].Title, {
                    children: "Confirmation"
                }, void 0, false, {
                    fileName: "[project]/components/Premium/BuyPremiumConfirmationDialog/BuyPremiumConfirmationDialog.tsx",
                    lineNumber: 34,
                    columnNumber: 17
                }, this)
            }, void 0, false, {
                fileName: "[project]/components/Premium/BuyPremiumConfirmationDialog/BuyPremiumConfirmationDialog.tsx",
                lineNumber: 33,
                columnNumber: 13
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Modal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Modal$3e$__["Modal"].Body, {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$BuyPremiumConfirmationDialog$2f$BuyPremiumConfirmationDialog$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].label,
                                        children: "Type:"
                                    }, void 0, false, {
                                        fileName: "[project]/components/Premium/BuyPremiumConfirmationDialog/BuyPremiumConfirmationDialog.tsx",
                                        lineNumber: 39,
                                        columnNumber: 25
                                    }, this),
                                    props.purchasePremiumType.label
                                ]
                            }, void 0, true, {
                                fileName: "[project]/components/Premium/BuyPremiumConfirmationDialog/BuyPremiumConfirmationDialog.tsx",
                                lineNumber: 38,
                                columnNumber: 21
                            }, this),
                            props.durationString && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$BuyPremiumConfirmationDialog$2f$BuyPremiumConfirmationDialog$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].label,
                                        children: "Duration:"
                                    }, void 0, false, {
                                        fileName: "[project]/components/Premium/BuyPremiumConfirmationDialog/BuyPremiumConfirmationDialog.tsx",
                                        lineNumber: 44,
                                        columnNumber: 29
                                    }, this),
                                    props.purchasePremiumOption.label,
                                    " ",
                                    props.durationString
                                ]
                            }, void 0, true, {
                                fileName: "[project]/components/Premium/BuyPremiumConfirmationDialog/BuyPremiumConfirmationDialog.tsx",
                                lineNumber: 43,
                                columnNumber: 25
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$BuyPremiumConfirmationDialog$2f$BuyPremiumConfirmationDialog$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].label,
                                        children: "Price:"
                                    }, void 0, false, {
                                        fileName: "[project]/components/Premium/BuyPremiumConfirmationDialog/BuyPremiumConfirmationDialog.tsx",
                                        lineNumber: 49,
                                        columnNumber: 25
                                    }, this),
                                    props.purchasePrice
                                ]
                            }, void 0, true, {
                                fileName: "[project]/components/Premium/BuyPremiumConfirmationDialog/BuyPremiumConfirmationDialog.tsx",
                                lineNumber: 48,
                                columnNumber: 21
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/Premium/BuyPremiumConfirmationDialog/BuyPremiumConfirmationDialog.tsx",
                        lineNumber: 37,
                        columnNumber: 17
                    }, this),
                    props.type === 'prepaid' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: "The time will be added to account. After you confirmed the purchase, it can't be canceled/moved to another account"
                    }, void 0, false, {
                        fileName: "[project]/components/Premium/BuyPremiumConfirmationDialog/BuyPremiumConfirmationDialog.tsx",
                        lineNumber: 54,
                        columnNumber: 21
                    }, this),
                    props.type === 'subscription' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: "This subscription will be automatically renewed every month. It can be canceled at any time and will then run out."
                    }, void 0, false, {
                        fileName: "[project]/components/Premium/BuyPremiumConfirmationDialog/BuyPremiumConfirmationDialog.tsx",
                        lineNumber: 57,
                        columnNumber: 21
                    }, this),
                    props.activePremiumProduct && (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$PremiumTypeUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getPremiumType"])(props.activePremiumProduct)?.productId !== props.purchasePremiumType.productId ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("hr", {}, void 0, false, {
                                fileName: "[project]/components/Premium/BuyPremiumConfirmationDialog/BuyPremiumConfirmationDialog.tsx",
                                lineNumber: 61,
                                columnNumber: 25
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                style: {
                                    color: 'yellow'
                                },
                                children: "It seems you already have an active premium product. While the 'better' premium is active, the other will get paused."
                            }, void 0, false, {
                                fileName: "[project]/components/Premium/BuyPremiumConfirmationDialog/BuyPremiumConfirmationDialog.tsx",
                                lineNumber: 62,
                                columnNumber: 25
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/Premium/BuyPremiumConfirmationDialog/BuyPremiumConfirmationDialog.tsx",
                        lineNumber: 60,
                        columnNumber: 21
                    }, this) : null,
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("hr", {}, void 0, false, {
                        fileName: "[project]/components/Premium/BuyPremiumConfirmationDialog/BuyPremiumConfirmationDialog.tsx",
                        lineNumber: 67,
                        columnNumber: 17
                    }, this),
                    !hasConfirmedLogin ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                children: "Please login again to confirm your Identity:"
                            }, void 0, false, {
                                fileName: "[project]/components/Premium/BuyPremiumConfirmationDialog/BuyPremiumConfirmationDialog.tsx",
                                lineNumber: 70,
                                columnNumber: 25
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                style: {
                                    width: '250px',
                                    colorScheme: 'light',
                                    marginBottom: '15px'
                                },
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$oauth$2f$google$2f$dist$2f$index$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["GoogleLogin"], {
                                    onSuccess: (response)=>{
                                        setHasConfirmedLogin(true);
                                        setGoogleToken(response.credential);
                                    },
                                    onError: ()=>{
                                        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('Login failed');
                                    },
                                    theme: 'filled_blue',
                                    size: 'large'
                                }, void 0, false, {
                                    fileName: "[project]/components/Premium/BuyPremiumConfirmationDialog/BuyPremiumConfirmationDialog.tsx",
                                    lineNumber: 72,
                                    columnNumber: 29
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/components/Premium/BuyPremiumConfirmationDialog/BuyPremiumConfirmationDialog.tsx",
                                lineNumber: 71,
                                columnNumber: 25
                            }, this)
                        ]
                    }, void 0, true) : null,
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Button$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__["Button"], {
                        variant: "danger",
                        onClick: props.onHide,
                        children: "Cancel"
                    }, void 0, false, {
                        fileName: "[project]/components/Premium/BuyPremiumConfirmationDialog/BuyPremiumConfirmationDialog.tsx",
                        lineNumber: 86,
                        columnNumber: 17
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Button$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__["Button"], {
                        variant: "success",
                        style: {
                            float: 'right'
                        },
                        disabled: !hasConfirmedLogin,
                        onClick: ()=>props.onConfirm(googleToken),
                        children: "Confirm"
                    }, void 0, false, {
                        fileName: "[project]/components/Premium/BuyPremiumConfirmationDialog/BuyPremiumConfirmationDialog.tsx",
                        lineNumber: 89,
                        columnNumber: 17
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/components/Premium/BuyPremiumConfirmationDialog/BuyPremiumConfirmationDialog.tsx",
                lineNumber: 36,
                columnNumber: 13
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/components/Premium/BuyPremiumConfirmationDialog/BuyPremiumConfirmationDialog.tsx",
        lineNumber: 27,
        columnNumber: 9
    }, this);
}
_s(BuyPremiumConfirmationDialog, "tkLjGpbpyOvhYQ7w0qaRpJZai4k=");
_c = BuyPremiumConfirmationDialog;
var _c;
__turbopack_context__.k.register(_c, "BuyPremiumConfirmationDialog");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/Premium/BuyPremium/BuyPremium.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Button$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/Button.js [app-client] (ecmascript) <export default as Button>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Card$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/Card.js [app-client] (ecmascript) <export default as Card>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/Form.js [app-client] (ecmascript) <export default as Form>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$ToggleButton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ToggleButton$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/ToggleButton.js [app-client] (ecmascript) <export default as ToggleButton>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$ToggleButtonGroup$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ToggleButtonGroup$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/ToggleButtonGroup.js [app-client] (ecmascript) <export default as ToggleButtonGroup>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-toastify/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/api/ApiHelper.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/api/ApiTypes.d.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Hooks$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/Hooks.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$PremiumTypeUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/PremiumTypeUtils.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$CoflCoins$2f$CoflCoinsDisplay$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/CoflCoins/CoflCoinsDisplay.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Number$2f$Number$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/Number/Number.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$BuyPremium$2f$BuyPremium$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/components/Premium/BuyPremium/BuyPremium.module.css [app-client] (css module)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$BuyPremiumConfirmationDialog$2f$BuyPremiumConfirmationDialog$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/Premium/BuyPremiumConfirmationDialog/BuyPremiumConfirmationDialog.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
;
;
;
;
;
function BuyPremium(props) {
    _s();
    let [purchasePremiumType, setPurchasePremiumType] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(__TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$PremiumTypeUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PREMIUM_TYPES"][0]);
    let [purchaseSuccessfulOption, setPurchaseSuccessfulDuration] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])();
    let [isPurchasing, setIsPurchasing] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    let [purchasePremiumOption, setPurchasePremiumOption] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(__TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$PremiumTypeUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PREMIUM_TYPES"][0].options[0]);
    let [showPrepaidConfirmationDialog, setShowPrepaidConfirmationDialog] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    let coflCoins = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Hooks$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCoflCoins"])();
    function onDurationChange(event) {
        let option = JSON.parse(event.target.value);
        setPurchasePremiumOption(option);
    }
    function onPremiumTypeChange(productId) {
        let selectedType = __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$PremiumTypeUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PREMIUM_TYPES"].find((type)=>type.productId === productId);
        if (selectedType) {
            setPurchasePremiumType(selectedType);
            setPurchasePremiumOption(selectedType.options[0]);
        }
    }
    function onPremiumBuy(googleToken) {
        setShowPrepaidConfirmationDialog(false);
        setIsPurchasing(true);
        __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].purchaseWithCoflcoins(purchasePremiumOption.productId, googleToken, purchasePremiumOption.value).then(()=>{
            document.dispatchEvent(new CustomEvent(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiTypes$2e$d$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CUSTOM_EVENTS"].COFLCOIN_UPDATE, {
                detail: {
                    coflCoins: coflCoins - getPurchasePrice()
                }
            }));
            setPurchaseSuccessfulDuration(purchasePremiumOption);
            setIsPurchasing(false);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success('Purchase successful');
            props.onNewActivePremiumProduct();
        });
    }
    function onPremiumBuyCancel() {
        setShowPrepaidConfirmationDialog(false);
    }
    function getPurchasePrice() {
        return purchasePremiumOption.value * purchasePremiumOption.price;
    }
    function getDurationString() {
        let durationString = purchasePremiumType.durationString;
        let duration = +purchasePremiumOption.value;
        if (durationString && duration > 1) {
            durationString += 's';
        }
        return durationString;
    }
    function getPremiumToggleButtonStyle(premiumType) {
        switch(premiumType.productId){
            case 'premium':
                return {
                    color: '#32de84'
                };
            case 'premium_plus':
                return {
                    color: '#ffaa00'
                };
            default:
                return {};
        }
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Card$3e$__["Card"], {
                className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$BuyPremium$2f$BuyPremium$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].purchaseCard,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Card$3e$__["Card"].Header, {
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Card$3e$__["Card"].Title, {
                            children: "Buy premium for a certain duration with your CoflCoins. Your premium time starts shortly after your purchase."
                        }, void 0, false, {
                            fileName: "[project]/components/Premium/BuyPremium/BuyPremium.tsx",
                            lineNumber: 90,
                            columnNumber: 21
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/components/Premium/BuyPremium/BuyPremium.tsx",
                        lineNumber: 89,
                        columnNumber: 17
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        style: {
                            padding: '15px'
                        },
                        children: !purchaseSuccessfulOption ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    style: {
                                        marginBottom: '15px'
                                    },
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$BuyPremium$2f$BuyPremium$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].label,
                                            children: "Premium type:"
                                        }, void 0, false, {
                                            fileName: "[project]/components/Premium/BuyPremium/BuyPremium.tsx",
                                            lineNumber: 96,
                                            columnNumber: 33
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$ToggleButtonGroup$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ToggleButtonGroup$3e$__["ToggleButtonGroup"], {
                                            style: {
                                                width: '250px',
                                                display: 'inline'
                                            },
                                            type: "radio",
                                            name: "options",
                                            value: purchasePremiumType.productId,
                                            onChange: onPremiumTypeChange,
                                            children: __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$PremiumTypeUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PREMIUM_TYPES"].map((premiumType)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$ToggleButton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ToggleButton$3e$__["ToggleButton"], {
                                                    id: premiumType.productId,
                                                    value: premiumType.productId,
                                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$BuyPremium$2f$BuyPremium$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].priceRangeButton,
                                                    size: "lg",
                                                    variant: "primary",
                                                    style: getPremiumToggleButtonStyle(premiumType),
                                                    children: premiumType.label
                                                }, premiumType.productId, false, {
                                                    fileName: "[project]/components/Premium/BuyPremium/BuyPremium.tsx",
                                                    lineNumber: 105,
                                                    columnNumber: 41
                                                }, this))
                                        }, void 0, false, {
                                            fileName: "[project]/components/Premium/BuyPremium/BuyPremium.tsx",
                                            lineNumber: 97,
                                            columnNumber: 33
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$BuyPremium$2f$BuyPremium$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].coinBalance,
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$CoflCoins$2f$CoflCoinsDisplay$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CoflCoinsDisplay"], {}, void 0, false, {
                                                fileName: "[project]/components/Premium/BuyPremium/BuyPremium.tsx",
                                                lineNumber: 119,
                                                columnNumber: 37
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/components/Premium/BuyPremium/BuyPremium.tsx",
                                            lineNumber: 118,
                                            columnNumber: 33
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/components/Premium/BuyPremium/BuyPremium.tsx",
                                    lineNumber: 95,
                                    columnNumber: 29
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    style: {
                                        marginBottom: '15px'
                                    },
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$BuyPremium$2f$BuyPremium$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].label,
                                            children: "Purchase Duration:"
                                        }, void 0, false, {
                                            fileName: "[project]/components/Premium/BuyPremium/BuyPremium.tsx",
                                            lineNumber: 123,
                                            columnNumber: 33
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__["Form"].Select, {
                                            onChange: onDurationChange,
                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$BuyPremium$2f$BuyPremium$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].dropdown,
                                            defaultValue: purchasePremiumOption.value,
                                            children: purchasePremiumType.options.map((option)=>{
                                                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                                    value: JSON.stringify(option),
                                                    children: option.label
                                                }, option.label, false, {
                                                    fileName: "[project]/components/Premium/BuyPremium/BuyPremium.tsx",
                                                    lineNumber: 132,
                                                    columnNumber: 45
                                                }, this);
                                            })
                                        }, purchasePremiumType.productId, false, {
                                            fileName: "[project]/components/Premium/BuyPremium/BuyPremium.tsx",
                                            lineNumber: 124,
                                            columnNumber: 33
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            style: {
                                                marginLeft: '20px'
                                            },
                                            children: getDurationString()
                                        }, void 0, false, {
                                            fileName: "[project]/components/Premium/BuyPremium/BuyPremium.tsx",
                                            lineNumber: 138,
                                            columnNumber: 33
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/components/Premium/BuyPremium/BuyPremium.tsx",
                                    lineNumber: 122,
                                    columnNumber: 29
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$BuyPremium$2f$BuyPremium$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].label,
                                            children: "Price:"
                                        }, void 0, false, {
                                            fileName: "[project]/components/Premium/BuyPremium/BuyPremium.tsx",
                                            lineNumber: 141,
                                            columnNumber: 33
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            style: {
                                                fontWeight: 'bold'
                                            },
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Number$2f$Number$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                    number: getPurchasePrice()
                                                }, void 0, false, {
                                                    fileName: "[project]/components/Premium/BuyPremium/BuyPremium.tsx",
                                                    lineNumber: 143,
                                                    columnNumber: 37
                                                }, this),
                                                " Coins"
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/components/Premium/BuyPremium/BuyPremium.tsx",
                                            lineNumber: 142,
                                            columnNumber: 33
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/components/Premium/BuyPremium/BuyPremium.tsx",
                                    lineNumber: 140,
                                    columnNumber: 29
                                }, this),
                                coflCoins >= getPurchasePrice() ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$BuyPremium$2f$BuyPremium$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].label,
                                            children: "Remaining after Purchase:"
                                        }, void 0, false, {
                                            fileName: "[project]/components/Premium/BuyPremium/BuyPremium.tsx",
                                            lineNumber: 148,
                                            columnNumber: 37
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Number$2f$Number$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                    number: coflCoins - getPurchasePrice()
                                                }, void 0, false, {
                                                    fileName: "[project]/components/Premium/BuyPremium/BuyPremium.tsx",
                                                    lineNumber: 150,
                                                    columnNumber: 41
                                                }, this),
                                                " Coins"
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/components/Premium/BuyPremium/BuyPremium.tsx",
                                            lineNumber: 149,
                                            columnNumber: 37
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/components/Premium/BuyPremium/BuyPremium.tsx",
                                    lineNumber: 147,
                                    columnNumber: 33
                                }, this) : null,
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    style: {
                                        marginTop: '20px'
                                    },
                                    children: "This is a prepaid service. We won't automatically charge you after your premium time runs out!"
                                }, void 0, false, {
                                    fileName: "[project]/components/Premium/BuyPremium/BuyPremium.tsx",
                                    lineNumber: 154,
                                    columnNumber: 29
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("hr", {}, void 0, false, {
                                    fileName: "[project]/components/Premium/BuyPremium/BuyPremium.tsx",
                                    lineNumber: 155,
                                    columnNumber: 29
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Button$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__["Button"], {
                                    style: {
                                        marginTop: '10px'
                                    },
                                    variant: "success",
                                    onClick: ()=>{
                                        setShowPrepaidConfirmationDialog(true);
                                    },
                                    disabled: getPurchasePrice() > coflCoins || isPurchasing,
                                    children: "Purchase"
                                }, void 0, false, {
                                    fileName: "[project]/components/Premium/BuyPremium/BuyPremium.tsx",
                                    lineNumber: 156,
                                    columnNumber: 29
                                }, this),
                                getPurchasePrice() > coflCoins && !isPurchasing ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                style: {
                                                    color: 'red'
                                                },
                                                children: "You don't have enough CoflCoins to buy this."
                                            }, void 0, false, {
                                                fileName: "[project]/components/Premium/BuyPremium/BuyPremium.tsx",
                                                lineNumber: 169,
                                                columnNumber: 41
                                            }, this),
                                            ' '
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/components/Premium/BuyPremium/BuyPremium.tsx",
                                        lineNumber: 168,
                                        columnNumber: 37
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/components/Premium/BuyPremium/BuyPremium.tsx",
                                    lineNumber: 167,
                                    columnNumber: 33
                                }, this) : ''
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/Premium/BuyPremium/BuyPremium.tsx",
                            lineNumber: 94,
                            columnNumber: 25
                        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            style: {
                                color: 'lime'
                            },
                            children: [
                                "You successfully bought ",
                                purchaseSuccessfulOption.label,
                                " ",
                                getDurationString(),
                                " of ",
                                purchasePremiumType.label,
                                " for",
                                ' ',
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Number$2f$Number$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    number: getPurchasePrice()
                                }, void 0, false, {
                                    fileName: "[project]/components/Premium/BuyPremium/BuyPremium.tsx",
                                    lineNumber: 179,
                                    columnNumber: 29
                                }, this),
                                " CoflCoins!"
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/Premium/BuyPremium/BuyPremium.tsx",
                            lineNumber: 177,
                            columnNumber: 25
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/components/Premium/BuyPremium/BuyPremium.tsx",
                        lineNumber: 92,
                        columnNumber: 17
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/components/Premium/BuyPremium/BuyPremium.tsx",
                lineNumber: 88,
                columnNumber: 13
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$BuyPremiumConfirmationDialog$2f$BuyPremiumConfirmationDialog$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                type: "prepaid",
                show: showPrepaidConfirmationDialog,
                durationString: getDurationString(),
                purchasePremiumOption: purchasePremiumOption,
                purchasePrice: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Number$2f$Number$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                            number: getPurchasePrice()
                        }, void 0, false, {
                            fileName: "[project]/components/Premium/BuyPremium/BuyPremium.tsx",
                            lineNumber: 191,
                            columnNumber: 25
                        }, void 0),
                        " CoflCoins"
                    ]
                }, void 0, true),
                purchasePremiumType: purchasePremiumType,
                onHide: onPremiumBuyCancel,
                onConfirm: onPremiumBuy,
                activePremiumProduct: props.activePremiumProduct
            }, void 0, false, {
                fileName: "[project]/components/Premium/BuyPremium/BuyPremium.tsx",
                lineNumber: 184,
                columnNumber: 13
            }, this)
        ]
    }, void 0, true);
}
_s(BuyPremium, "TSGgyyKq97RFtiS7iqaeytv1F8Y=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Hooks$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCoflCoins"]
    ];
});
_c = BuyPremium;
const __TURBOPACK__default__export__ = BuyPremium;
var _c;
__turbopack_context__.k.register(_c, "BuyPremium");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/FilterElement/FilterElements/PlayerFilterElement.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "PlayerFilterElement": (()=>PlayerFilterElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2d$typeahead$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap-typeahead/es/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2d$typeahead$2f$es$2f$components$2f$AsyncTypeahead$2f$AsyncTypeahead$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__AsyncTypeahead$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap-typeahead/es/components/AsyncTypeahead/AsyncTypeahead.js [app-client] (ecmascript) <export default as AsyncTypeahead>");
var __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/api/ApiHelper.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__ = __turbopack_context__.i("[project]/node_modules/uuid/dist/esm-browser/v4.js [app-client] (ecmascript) <export default as v4>");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
let PlayerFilterElement = /*#__PURE__*/ _s((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(_c = _s((props, ref)=>{
    _s();
    // for player search
    let [players, setPlayers] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    let [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    function _onChange(selected) {
        props.onChange(selected[0] || '');
    }
    function handlePlayerSearch(query) {
        setIsLoading(true);
        __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].playerSearch(query).then((players)=>{
            setPlayers(players);
            setIsLoading(false);
        });
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2d$typeahead$2f$es$2f$components$2f$AsyncTypeahead$2f$AsyncTypeahead$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__AsyncTypeahead$3e$__["AsyncTypeahead"], {
        id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
        disabled: props.disabled,
        filterBy: ()=>true,
        isLoading: isLoading,
        labelKey: "name",
        minLength: 1,
        isInvalid: !props.isValid,
        onSearch: handlePlayerSearch,
        defaultInputValue: props.defaultValue,
        options: players,
        placeholder: props.placeholder || 'Search users...',
        onChange: (selected)=>_onChange(selected.map((s)=>{
                if (props.returnType === 'player') {
                    return s;
                }
                return s[props.returnType];
            })),
        ref: ref
    }, void 0, false, {
        fileName: "[project]/components/FilterElement/FilterElements/PlayerFilterElement.tsx",
        lineNumber: 36,
        columnNumber: 9
    }, this);
}, "kJwltaJVqKcvCQGdHvHVupnEGtM=")), "kJwltaJVqKcvCQGdHvHVupnEGtM=");
_c1 = PlayerFilterElement;
var _c, _c1;
__turbopack_context__.k.register(_c, "PlayerFilterElement$forwardRef");
__turbopack_context__.k.register(_c1, "PlayerFilterElement");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/TransferCoflCoins/TransferCoflCoinsSummary.module.css [app-client] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "label": "TransferCoflCoinsSummary-module__ZfhhcG__label",
  "returnButton": "TransferCoflCoinsSummary-module__ZfhhcG__returnButton",
  "sendButton": "TransferCoflCoinsSummary-module__ZfhhcG__sendButton",
});
}}),
"[project]/components/TransferCoflCoins/TransferCoflCoinsSummary.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Button$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/Button.js [app-client] (ecmascript) <export default as Button>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-toastify/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__ = __turbopack_context__.i("[project]/node_modules/uuid/dist/esm-browser/v4.js [app-client] (ecmascript) <export default as v4>");
var __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/api/ApiHelper.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$LoadingUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/LoadingUtils.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Number$2f$Number$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/Number/Number.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$TransferCoflCoins$2f$TransferCoflCoinsSummary$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/components/TransferCoflCoins/TransferCoflCoinsSummary.module.css [app-client] (css module)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
;
;
;
function TransferCoflCoinsSummary(props) {
    _s();
    let [reference] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$browser$2f$v4$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])());
    let [isSending, setIsSending] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    function onSend() {
        setIsSending(true);
        __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].transferCoflCoins(props.email, props.player?.uuid, props.coflCoins, reference).then(()=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success(/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                children: [
                    "Successfuly sent ",
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Number$2f$Number$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                        number: props.coflCoins
                    }, void 0, false, {
                        fileName: "[project]/components/TransferCoflCoins/TransferCoflCoinsSummary.tsx",
                        lineNumber: 31,
                        columnNumber: 42
                    }, this),
                    " CoflCoins to ",
                    props.email === '' ? props.player?.name : props.email
                ]
            }, void 0, true, {
                fileName: "[project]/components/TransferCoflCoins/TransferCoflCoinsSummary.tsx",
                lineNumber: 30,
                columnNumber: 21
            }, this));
            setIsSending(false);
            props.onFinish();
        }).catch(()=>{
            setIsSending(false);
            props.onFinish();
        });
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: !isSending ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$TransferCoflCoins$2f$TransferCoflCoinsSummary$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].label,
                            children: "Receiver:"
                        }, void 0, false, {
                            fileName: "[project]/components/TransferCoflCoins/TransferCoflCoinsSummary.tsx",
                            lineNumber: 48,
                            columnNumber: 25
                        }, this),
                        props.receiverType === 'email' ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            children: props.email
                        }, void 0, false, {
                            fileName: "[project]/components/TransferCoflCoins/TransferCoflCoinsSummary.tsx",
                            lineNumber: 50,
                            columnNumber: 29
                        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    crossOrigin: "anonymous",
                                    className: "playerHeadIcon",
                                    src: props.player?.iconUrl || '',
                                    height: "32",
                                    width: "32",
                                    alt: "",
                                    style: {
                                        marginRight: '10px'
                                    },
                                    loading: "lazy"
                                }, void 0, false, {
                                    fileName: "[project]/components/TransferCoflCoins/TransferCoflCoinsSummary.tsx",
                                    lineNumber: 53,
                                    columnNumber: 33
                                }, this),
                                props.player.name
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/TransferCoflCoins/TransferCoflCoinsSummary.tsx",
                            lineNumber: 52,
                            columnNumber: 29
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/components/TransferCoflCoins/TransferCoflCoinsSummary.tsx",
                    lineNumber: 47,
                    columnNumber: 21
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$TransferCoflCoins$2f$TransferCoflCoinsSummary$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].label,
                            children: "Amount: "
                        }, void 0, false, {
                            fileName: "[project]/components/TransferCoflCoins/TransferCoflCoinsSummary.tsx",
                            lineNumber: 68,
                            columnNumber: 25
                        }, this),
                        props.coflCoins,
                        " CoflCoins"
                    ]
                }, void 0, true, {
                    fileName: "[project]/components/TransferCoflCoins/TransferCoflCoinsSummary.tsx",
                    lineNumber: 67,
                    columnNumber: 21
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("hr", {}, void 0, false, {
                    fileName: "[project]/components/TransferCoflCoins/TransferCoflCoinsSummary.tsx",
                    lineNumber: 72,
                    columnNumber: 21
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            style: {
                                color: 'red'
                            },
                            children: "Warning: "
                        }, void 0, false, {
                            fileName: "[project]/components/TransferCoflCoins/TransferCoflCoinsSummary.tsx",
                            lineNumber: 74,
                            columnNumber: 25
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("br", {}, void 0, false, {
                            fileName: "[project]/components/TransferCoflCoins/TransferCoflCoinsSummary.tsx",
                            lineNumber: 75,
                            columnNumber: 25
                        }, this),
                        "Please make sure this is really the person you want to send CoflCoins to. You may not be able to get them back!"
                    ]
                }, void 0, true, {
                    fileName: "[project]/components/TransferCoflCoins/TransferCoflCoinsSummary.tsx",
                    lineNumber: 73,
                    columnNumber: 21
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Button$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__["Button"], {
                    className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$TransferCoflCoins$2f$TransferCoflCoinsSummary$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].returnButton,
                    onClick: props.onBack,
                    children: "Back"
                }, void 0, false, {
                    fileName: "[project]/components/TransferCoflCoins/TransferCoflCoinsSummary.tsx",
                    lineNumber: 79,
                    columnNumber: 21
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Button$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__["Button"], {
                    variant: "success",
                    className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$TransferCoflCoins$2f$TransferCoflCoinsSummary$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].sendButton,
                    onClick: onSend,
                    children: "Send"
                }, void 0, false, {
                    fileName: "[project]/components/TransferCoflCoins/TransferCoflCoinsSummary.tsx",
                    lineNumber: 82,
                    columnNumber: 21
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/components/TransferCoflCoins/TransferCoflCoinsSummary.tsx",
            lineNumber: 46,
            columnNumber: 17
        }, this) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$LoadingUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getLoadingElement"])(/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
            children: "Sending CoflCoins"
        }, void 0, false, {
            fileName: "[project]/components/TransferCoflCoins/TransferCoflCoinsSummary.tsx",
            lineNumber: 87,
            columnNumber: 35
        }, this))
    }, void 0, false);
}
_s(TransferCoflCoinsSummary, "9gmouvvFcvEBtW4p05Vyn5GiGhU=");
_c = TransferCoflCoinsSummary;
const __TURBOPACK__default__export__ = TransferCoflCoinsSummary;
var _c;
__turbopack_context__.k.register(_c, "TransferCoflCoinsSummary");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/TransferCoflCoins/TransferCoflCoins.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Button$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/Button.js [app-client] (ecmascript) <export default as Button>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/Form.js [app-client] (ecmascript) <export default as Form>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$number$2d$format$2f$dist$2f$react$2d$number$2d$format$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-number-format/dist/react-number-format.es.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Hooks$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/Hooks.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterElements$2f$PlayerFilterElement$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/FilterElement/FilterElements/PlayerFilterElement.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Number$2f$Number$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/Number/Number.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$TransferCoflCoins$2f$TransferCoflCoinsSummary$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/TransferCoflCoins/TransferCoflCoinsSummary.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
;
function TransferCoflCoins(props) {
    _s();
    let [minecraftPlayer, setMinecraftPlayer] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])();
    let [email, setEmail] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    let [coflCoinsToSend, setCoflCoinsToSend] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(0);
    let coflCoinsBalance = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Hooks$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCoflCoins"])();
    let [showSummary, setShowSummary] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    function onContinue() {
        setShowSummary(true);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                style: {
                    display: showSummary ? 'none' : 'initial'
                },
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: "There are 2 ways to send CoflCoins to another person:"
                    }, void 0, false, {
                        fileName: "[project]/components/TransferCoflCoins/TransferCoflCoins.tsx",
                        lineNumber: 28,
                        columnNumber: 17
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("b", {
                                        children: "By Email:"
                                    }, void 0, false, {
                                        fileName: "[project]/components/TransferCoflCoins/TransferCoflCoins.tsx",
                                        lineNumber: 31,
                                        columnNumber: 25
                                    }, this),
                                    " Enter the email of the Google account of the receiver"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/components/TransferCoflCoins/TransferCoflCoins.tsx",
                                lineNumber: 30,
                                columnNumber: 21
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("b", {
                                        children: "By Minecraft name:"
                                    }, void 0, false, {
                                        fileName: "[project]/components/TransferCoflCoins/TransferCoflCoins.tsx",
                                        lineNumber: 34,
                                        columnNumber: 25
                                    }, this),
                                    " Search the players Minecraft name (only works if they linked their Minecraft account on the website)"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/components/TransferCoflCoins/TransferCoflCoins.tsx",
                                lineNumber: 33,
                                columnNumber: 21
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/TransferCoflCoins/TransferCoflCoins.tsx",
                        lineNumber: 29,
                        columnNumber: 17
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("hr", {}, void 0, false, {
                        fileName: "[project]/components/TransferCoflCoins/TransferCoflCoins.tsx",
                        lineNumber: 37,
                        columnNumber: 17
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        style: {
                            padding: '0 50px 0 50px'
                        },
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    minecraftPlayer === undefined ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        style: {
                                            marginBottom: '20px'
                                        },
                                        children: [
                                            "By Email",
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__["Form"].Control, {
                                                placeholder: "Enter Email...",
                                                onChange: (e)=>{
                                                    setEmail(e.target.value);
                                                }
                                            }, void 0, false, {
                                                fileName: "[project]/components/TransferCoflCoins/TransferCoflCoins.tsx",
                                                lineNumber: 43,
                                                columnNumber: 33
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/components/TransferCoflCoins/TransferCoflCoins.tsx",
                                        lineNumber: 41,
                                        columnNumber: 29
                                    }, this) : null,
                                    email === '' ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        style: {
                                            marginBottom: '20px'
                                        },
                                        children: [
                                            "By Minecraft name",
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FilterElement$2f$FilterElements$2f$PlayerFilterElement$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PlayerFilterElement"], {
                                                defaultValue: "",
                                                onChange: (p)=>{
                                                    setMinecraftPlayer(p);
                                                },
                                                returnType: 'player',
                                                placeholder: "Enter Minecraft name..."
                                            }, void 0, false, {
                                                fileName: "[project]/components/TransferCoflCoins/TransferCoflCoins.tsx",
                                                lineNumber: 54,
                                                columnNumber: 33
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/components/TransferCoflCoins/TransferCoflCoins.tsx",
                                        lineNumber: 52,
                                        columnNumber: 29
                                    }, this) : null
                                ]
                            }, void 0, true, {
                                fileName: "[project]/components/TransferCoflCoins/TransferCoflCoins.tsx",
                                lineNumber: 39,
                                columnNumber: 21
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                style: {
                                    marginBottom: '20px'
                                },
                                children: [
                                    "Amount of CoflCoins",
                                    ' ',
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$number$2d$format$2f$dist$2f$react$2d$number$2d$format$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumericFormat"], {
                                        id: "coflcoins-to-send",
                                        onValueChange: (n)=>{
                                            if (n.floatValue) {
                                                setCoflCoinsToSend(n.floatValue);
                                            }
                                        },
                                        isAllowed: (value)=>{
                                            return value.floatValue ? value.floatValue <= coflCoinsBalance : false;
                                        },
                                        customInput: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__["Form"].Control,
                                        defaultValue: 0,
                                        thousandSeparator: ".",
                                        decimalSeparator: ",",
                                        allowNegative: false,
                                        decimalScale: 1
                                    }, void 0, false, {
                                        fileName: "[project]/components/TransferCoflCoins/TransferCoflCoins.tsx",
                                        lineNumber: 67,
                                        columnNumber: 25
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/components/TransferCoflCoins/TransferCoflCoins.tsx",
                                lineNumber: 65,
                                columnNumber: 21
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: [
                                    "Your current Balance: ",
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Number$2f$Number$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                        number: coflCoinsBalance
                                    }, void 0, false, {
                                        fileName: "[project]/components/TransferCoflCoins/TransferCoflCoins.tsx",
                                        lineNumber: 86,
                                        columnNumber: 47
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/components/TransferCoflCoins/TransferCoflCoins.tsx",
                                lineNumber: 85,
                                columnNumber: 21
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Button$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__["Button"], {
                                variant: "success",
                                style: {
                                    float: 'right'
                                },
                                onClick: onContinue,
                                disabled: coflCoinsToSend <= 0 || email === '' && minecraftPlayer === undefined,
                                children: "Continue"
                            }, void 0, false, {
                                fileName: "[project]/components/TransferCoflCoins/TransferCoflCoins.tsx",
                                lineNumber: 88,
                                columnNumber: 21
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/TransferCoflCoins/TransferCoflCoins.tsx",
                        lineNumber: 38,
                        columnNumber: 17
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/components/TransferCoflCoins/TransferCoflCoins.tsx",
                lineNumber: 27,
                columnNumber: 13
            }, this),
            showSummary ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$TransferCoflCoins$2f$TransferCoflCoinsSummary$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                receiverType: minecraftPlayer !== undefined ? 'mcId' : 'email',
                coflCoins: coflCoinsToSend,
                email: email,
                player: minecraftPlayer,
                onBack: ()=>{
                    setShowSummary(false);
                },
                onFinish: props.onFinish
            }, void 0, false, {
                fileName: "[project]/components/TransferCoflCoins/TransferCoflCoins.tsx",
                lineNumber: 99,
                columnNumber: 17
            }, this) : null
        ]
    }, void 0, true);
}
_s(TransferCoflCoins, "y8xceu9MP0Vk0uNw88F2vlKgtps=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Hooks$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCoflCoins"]
    ];
});
_c = TransferCoflCoins;
const __TURBOPACK__default__export__ = TransferCoflCoins;
var _c;
__turbopack_context__.k.register(_c, "TransferCoflCoins");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/Premium/PremiumStatus/PremiumStatus.module.css [app-client] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "premiumStatusLabel": "PremiumStatus-module__89CruW__premiumStatusLabel",
});
}}),
"[project]/components/Premium/CancelSubscriptionConfirmDialog/CancelSubscriptionConfirmDialog.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Modal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Modal$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/Modal.js [app-client] (ecmascript) <export default as Modal>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Button$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/Button.js [app-client] (ecmascript) <export default as Button>");
;
;
const CancelSubscriptionConfirmDialog = ({ show, onHide, onConfirm })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Modal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Modal$3e$__["Modal"], {
        show: show,
        onHide: onHide,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Modal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Modal$3e$__["Modal"].Header, {
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Modal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Modal$3e$__["Modal"].Title, {
                    children: "Confirmation"
                }, void 0, false, {
                    fileName: "[project]/components/Premium/CancelSubscriptionConfirmDialog/CancelSubscriptionConfirmDialog.tsx",
                    lineNumber: 14,
                    columnNumber: 17
                }, this)
            }, void 0, false, {
                fileName: "[project]/components/Premium/CancelSubscriptionConfirmDialog/CancelSubscriptionConfirmDialog.tsx",
                lineNumber: 13,
                columnNumber: 13
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Modal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Modal$3e$__["Modal"].Body, {
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            children: "Are you sure you want to cancel your subscription?"
                        }, void 0, false, {
                            fileName: "[project]/components/Premium/CancelSubscriptionConfirmDialog/CancelSubscriptionConfirmDialog.tsx",
                            lineNumber: 18,
                            columnNumber: 21
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            style: {
                                display: 'flex',
                                gap: 5,
                                justifyContent: 'space-between'
                            },
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Button$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__["Button"], {
                                    variant: "danger",
                                    onClick: onHide,
                                    children: "Cancel"
                                }, void 0, false, {
                                    fileName: "[project]/components/Premium/CancelSubscriptionConfirmDialog/CancelSubscriptionConfirmDialog.tsx",
                                    lineNumber: 20,
                                    columnNumber: 25
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Button$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__["Button"], {
                                    variant: "success",
                                    onClick: onConfirm,
                                    children: "Confirm"
                                }, void 0, false, {
                                    fileName: "[project]/components/Premium/CancelSubscriptionConfirmDialog/CancelSubscriptionConfirmDialog.tsx",
                                    lineNumber: 23,
                                    columnNumber: 25
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/Premium/CancelSubscriptionConfirmDialog/CancelSubscriptionConfirmDialog.tsx",
                            lineNumber: 19,
                            columnNumber: 21
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/components/Premium/CancelSubscriptionConfirmDialog/CancelSubscriptionConfirmDialog.tsx",
                    lineNumber: 17,
                    columnNumber: 17
                }, this)
            }, void 0, false, {
                fileName: "[project]/components/Premium/CancelSubscriptionConfirmDialog/CancelSubscriptionConfirmDialog.tsx",
                lineNumber: 16,
                columnNumber: 13
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/components/Premium/CancelSubscriptionConfirmDialog/CancelSubscriptionConfirmDialog.tsx",
        lineNumber: 12,
        columnNumber: 9
    }, this);
};
_c = CancelSubscriptionConfirmDialog;
const __TURBOPACK__default__export__ = CancelSubscriptionConfirmDialog;
var _c;
__turbopack_context__.k.register(_c, "CancelSubscriptionConfirmDialog");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/Premium/PremiumStatus/PremiumStatus.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$moment$2f$moment$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/moment/moment.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Formatter$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/Formatter.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$PremiumTypeUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/PremiumTypeUtils.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Tooltip$2f$Tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/Tooltip/Tooltip.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$PremiumStatus$2f$PremiumStatus$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/components/Premium/PremiumStatus/PremiumStatus.module.css [app-client] (css module)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$CancelOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/icons-material/esm/CancelOutlined.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$CancelSubscriptionConfirmDialog$2f$CancelSubscriptionConfirmDialog$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/Premium/CancelSubscriptionConfirmDialog/CancelSubscriptionConfirmDialog.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
;
;
function PremiumStatus(props) {
    _s();
    let [highestPriorityProduct, setHighestPriorityProduct] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])();
    let [productsToShow, setProductsToShow] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])();
    let [showCancelSubscriptionDialogSubscription, setShowCancelSubscriptionDialogSubscription] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "PremiumStatus.useEffect": ()=>{
            let products = props.products.map({
                "PremiumStatus.useEffect.products": (product)=>{
                    return {
                        ...product,
                        timeDifference: 0
                    };
                }
            }["PremiumStatus.useEffect.products"]).sort({
                "PremiumStatus.useEffect.products": (a, b)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$PremiumTypeUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getPremiumType"])(b)?.priority - (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$PremiumTypeUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getPremiumType"])(a)?.priority
            }["PremiumStatus.useEffect.products"]);
            // Hide lower tier products that are most likely bought automatically together (<1min time difference)
            if (products.length > 1) {
                for(let i = 1; i < products.length; i++){
                    const diff = Math.abs(products[i - 1].expires.getTime() - products[i].expires.getTime());
                    if (diff < 60000) {
                        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$PremiumTypeUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getPremiumType"])(products[i - 1])?.priority > (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$PremiumTypeUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getPremiumType"])(products[i])?.priority) {
                            products.splice(i, 1);
                        } else {
                            products.splice(i - 1, 1);
                        }
                        i = 0;
                    } else products[i].timeDifference = diff;
                }
            }
            products = products.filter({
                "PremiumStatus.useEffect": (product)=>product.expires > new Date()
            }["PremiumStatus.useEffect"]);
            setProductsToShow(products);
            setHighestPriorityProduct((0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$PremiumTypeUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getHighestPriorityPremiumProduct"])(props.products));
        }
    }["PremiumStatus.useEffect"], [
        props.products
    ]);
    function getProductListEntry(product) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                    children: (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$PremiumTypeUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getPremiumType"])(product)?.label
                }, void 0, false, {
                    fileName: "[project]/components/Premium/PremiumStatus/PremiumStatus.tsx",
                    lineNumber: 56,
                    columnNumber: 17
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Tooltip$2f$Tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    type: "hover",
                    content: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        children: [
                            " (ends ",
                            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$moment$2f$moment$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(product.expires).fromNow(),
                            product.timeDifference > 0 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                children: [
                                    ", ",
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$PremiumStatus$2f$PremiumStatus$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].timeDifference,
                                        children: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$moment$2f$moment$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].duration(product.timeDifference).humanize()
                                    }, void 0, false, {
                                        fileName: "[project]/components/Premium/PremiumStatus/PremiumStatus.tsx",
                                        lineNumber: 60,
                                        columnNumber: 59
                                    }, void 0),
                                    " after"
                                ]
                            }, void 0, true) : null,
                            ")"
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/Premium/PremiumStatus/PremiumStatus.tsx",
                        lineNumber: 59,
                        columnNumber: 30
                    }, void 0),
                    tooltipContent: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        children: [
                            "At ",
                            (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Formatter$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getLocalDateAndTime"])(product.expires)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/Premium/PremiumStatus/PremiumStatus.tsx",
                        lineNumber: 61,
                        columnNumber: 37
                    }, void 0)
                }, void 0, false, {
                    fileName: "[project]/components/Premium/PremiumStatus/PremiumStatus.tsx",
                    lineNumber: 57,
                    columnNumber: 17
                }, this)
            ]
        }, void 0, true);
    }
    let numberOfEntriesToShow = (productsToShow?.length || 0) + (props.subscriptions?.length || 0);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                children: numberOfEntriesToShow > 1 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    style: {
                        overflow: 'hidden'
                    },
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$PremiumStatus$2f$PremiumStatus$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].premiumStatusLabel,
                            style: props.labelStyle,
                            children: "Premium Status:"
                        }, void 0, false, {
                            fileName: "[project]/components/Premium/PremiumStatus/PremiumStatus.tsx",
                            lineNumber: 74,
                            columnNumber: 25
                        }, this),
                        props.hasLoadingError === true ? 'Premium Status could not be loaded' : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                            style: {
                                float: 'left'
                            },
                            children: [
                                props.subscriptions.map((subscription)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                        children: [
                                            ' ',
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Tooltip$2f$Tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                type: "hover",
                                                content: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    children: [
                                                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$PremiumTypeUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getPremiumLabelForSubscription"])(subscription),
                                                        " (Subscription)",
                                                        ' ',
                                                        subscription.endsAt && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            style: {
                                                                color: 'red',
                                                                marginLeft: 5
                                                            },
                                                            children: "Canceled"
                                                        }, void 0, false, {
                                                            fileName: "[project]/components/Premium/PremiumStatus/PremiumStatus.tsx",
                                                            lineNumber: 87,
                                                            columnNumber: 77
                                                        }, void 0)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/components/Premium/PremiumStatus/PremiumStatus.tsx",
                                                    lineNumber: 85,
                                                    columnNumber: 49
                                                }, void 0),
                                                tooltipContent: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    children: subscription.endsAt ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        children: [
                                                            "Ends at ",
                                                            (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Formatter$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getLocalDateAndTime"])(subscription.endsAt),
                                                            " "
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/components/Premium/PremiumStatus/PremiumStatus.tsx",
                                                        lineNumber: 93,
                                                        columnNumber: 57
                                                    }, void 0) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        children: [
                                                            "Renews at ",
                                                            (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$Formatter$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getLocalDateAndTime"])(subscription.renewsAt)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/components/Premium/PremiumStatus/PremiumStatus.tsx",
                                                        lineNumber: 95,
                                                        columnNumber: 57
                                                    }, void 0)
                                                }, void 0, false, {
                                                    fileName: "[project]/components/Premium/PremiumStatus/PremiumStatus.tsx",
                                                    lineNumber: 91,
                                                    columnNumber: 49
                                                }, void 0)
                                            }, void 0, false, {
                                                fileName: "[project]/components/Premium/PremiumStatus/PremiumStatus.tsx",
                                                lineNumber: 82,
                                                columnNumber: 41
                                            }, this),
                                            !subscription.endsAt && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Tooltip$2f$Tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                type: "hover",
                                                content: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    style: {
                                                        color: 'red'
                                                    },
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$CancelOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                        style: {
                                                            cursor: 'pointer',
                                                            color: 'red',
                                                            marginLeft: 5
                                                        },
                                                        onClick: ()=>{
                                                            setShowCancelSubscriptionDialogSubscription(subscription);
                                                        }
                                                    }, void 0, false, {
                                                        fileName: "[project]/components/Premium/PremiumStatus/PremiumStatus.tsx",
                                                        lineNumber: 105,
                                                        columnNumber: 57
                                                    }, void 0)
                                                }, void 0, false, {
                                                    fileName: "[project]/components/Premium/PremiumStatus/PremiumStatus.tsx",
                                                    lineNumber: 104,
                                                    columnNumber: 53
                                                }, void 0),
                                                tooltipContent: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    children: "Cancel subscription"
                                                }, void 0, false, {
                                                    fileName: "[project]/components/Premium/PremiumStatus/PremiumStatus.tsx",
                                                    lineNumber: 113,
                                                    columnNumber: 65
                                                }, void 0)
                                            }, void 0, false, {
                                                fileName: "[project]/components/Premium/PremiumStatus/PremiumStatus.tsx",
                                                lineNumber: 101,
                                                columnNumber: 45
                                            }, this)
                                        ]
                                    }, subscription.externalId, true, {
                                        fileName: "[project]/components/Premium/PremiumStatus/PremiumStatus.tsx",
                                        lineNumber: 80,
                                        columnNumber: 37
                                    }, this)),
                                productsToShow?.map((product)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                        children: getProductListEntry(product)
                                    }, product.productSlug, false, {
                                        fileName: "[project]/components/Premium/PremiumStatus/PremiumStatus.tsx",
                                        lineNumber: 119,
                                        columnNumber: 37
                                    }, this))
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/Premium/PremiumStatus/PremiumStatus.tsx",
                            lineNumber: 78,
                            columnNumber: 29
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/components/Premium/PremiumStatus/PremiumStatus.tsx",
                    lineNumber: 73,
                    columnNumber: 21
                }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    children: [
                        ' ',
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$PremiumStatus$2f$PremiumStatus$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].premiumStatusLabel,
                            style: props.labelStyle,
                            children: "Premium Status:"
                        }, void 0, false, {
                            fileName: "[project]/components/Premium/PremiumStatus/PremiumStatus.tsx",
                            lineNumber: 127,
                            columnNumber: 25
                        }, this),
                        props.hasLoadingError === true ? 'Premium Status could not be loaded' : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                            children: highestPriorityProduct ? getProductListEntry({
                                ...highestPriorityProduct
                            }) : 'No Premium'
                        }, void 0, false)
                    ]
                }, void 0, true, {
                    fileName: "[project]/components/Premium/PremiumStatus/PremiumStatus.tsx",
                    lineNumber: 125,
                    columnNumber: 21
                }, this)
            }, void 0, false, {
                fileName: "[project]/components/Premium/PremiumStatus/PremiumStatus.tsx",
                lineNumber: 71,
                columnNumber: 13
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$CancelSubscriptionConfirmDialog$2f$CancelSubscriptionConfirmDialog$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                show: !!showCancelSubscriptionDialogSubscription,
                onConfirm: ()=>{
                    if (showCancelSubscriptionDialogSubscription) {
                        props.onSubscriptionCancel(showCancelSubscriptionDialogSubscription);
                        setShowCancelSubscriptionDialogSubscription(undefined);
                    }
                },
                onHide: ()=>{
                    setShowCancelSubscriptionDialogSubscription(undefined);
                }
            }, void 0, false, {
                fileName: "[project]/components/Premium/PremiumStatus/PremiumStatus.tsx",
                lineNumber: 138,
                columnNumber: 13
            }, this)
        ]
    }, void 0, true);
}
_s(PremiumStatus, "DmM9k82xSFwU109vvv6/wvVLL2g=");
_c = PremiumStatus;
const __TURBOPACK__default__export__ = PremiumStatus;
var _c;
__turbopack_context__.k.register(_c, "PremiumStatus");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/Premium/BuySubscription/BuySubscription.module.css [app-client] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "purchaseButton": "BuySubscription-module__Rzk2-G__purchaseButton",
  "purchaseButtonContainer": "BuySubscription-module__Rzk2-G__purchaseButtonContainer",
});
}}),
"[project]/components/Premium/BuySubscription/BuySubscription.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$BuyPremiumConfirmationDialog$2f$BuyPremiumConfirmationDialog$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/Premium/BuyPremiumConfirmationDialog/BuyPremiumConfirmationDialog.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$PremiumTypeUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/PremiumTypeUtils.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/api/ApiHelper.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Button$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/Button.js [app-client] (ecmascript) <export default as Button>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Card$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/Card.js [app-client] (ecmascript) <export default as Card>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Col$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Col$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/Col.js [app-client] (ecmascript) <export default as Col>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Row$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Row$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/Row.js [app-client] (ecmascript) <export default as Row>");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$BuySubscription$2f$BuySubscription$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/components/Premium/BuySubscription/BuySubscription.module.css [app-client] (css module)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Number$2f$Number$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/Number/Number.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
;
;
;
;
;
;
;
function BuySubscription(props) {
    _s();
    const [selectedPremiumType, setSelectedPremiumType] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])();
    const [isYearOption, setIsYearOption] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])();
    function getSubscriptionPrice() {
        if (!selectedPremiumType) {
            return -1;
        }
        if (selectedPremiumType.productId === 'premium') {
            return isYearOption ? 96.69 : 8.69;
        }
        if (selectedPremiumType.productId === 'premium_plus') {
            return isYearOption ? 354.20 : 35.69;
        }
        return -1;
    }
    function onSubscriptionBuyCancel() {
        setSelectedPremiumType(undefined);
    }
    function onSubscriptionBuy(googleToken) {
        if (!selectedPremiumType) {
            return;
        }
        let productId = '';
        if (selectedPremiumType.productId === 'premium') {
            productId = 'l_premium';
        }
        if (selectedPremiumType.productId === 'premium_plus') {
            productId = 'l_prem_plus';
        }
        if (isYearOption) {
            productId += '-year';
        }
        __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].purchasePremiumSubscription(productId, googleToken).then((data)=>{
            window.open(data.directLink, '_self');
        });
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Row$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Row$3e$__["Row"], {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Col$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Col$3e$__["Col"], {
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Card$3e$__["Card"], {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Card$3e$__["Card"].Header, {
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Card$3e$__["Card"].Title, {
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("b", {
                                            children: "Premium+"
                                        }, void 0, false, {
                                            fileName: "[project]/components/Premium/BuySubscription/BuySubscription.tsx",
                                            lineNumber: 60,
                                            columnNumber: 41
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/components/Premium/BuySubscription/BuySubscription.tsx",
                                        lineNumber: 60,
                                        columnNumber: 29
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/components/Premium/BuySubscription/BuySubscription.tsx",
                                    lineNumber: 59,
                                    columnNumber: 25
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Card$3e$__["Card"].Body, {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                    children: "top flip receive time"
                                                }, void 0, false, {
                                                    fileName: "[project]/components/Premium/BuySubscription/BuySubscription.tsx",
                                                    lineNumber: 64,
                                                    columnNumber: 33
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                    children: "all tools for analysis"
                                                }, void 0, false, {
                                                    fileName: "[project]/components/Premium/BuySubscription/BuySubscription.tsx",
                                                    lineNumber: 65,
                                                    columnNumber: 33
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                    children: "full auction archive"
                                                }, void 0, false, {
                                                    fileName: "[project]/components/Premium/BuySubscription/BuySubscription.tsx",
                                                    lineNumber: 66,
                                                    columnNumber: 33
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/components/Premium/BuySubscription/BuySubscription.tsx",
                                            lineNumber: 63,
                                            columnNumber: 29
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$BuySubscription$2f$BuySubscription$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].purchaseButtonContainer,
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Button$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__["Button"], {
                                                    variant: "success",
                                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$BuySubscription$2f$BuySubscription$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].purchaseButton,
                                                    onClick: ()=>{
                                                        setIsYearOption(false);
                                                        setSelectedPremiumType(__TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$PremiumTypeUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PREMIUM_TYPES"].find((type)=>type.productId === 'premium_plus'));
                                                    },
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Number$2f$Number$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                            number: 35.69
                                                        }, void 0, false, {
                                                            fileName: "[project]/components/Premium/BuySubscription/BuySubscription.tsx",
                                                            lineNumber: 77,
                                                            columnNumber: 37
                                                        }, this),
                                                        " Euro (+VAT) / 4 weeks"
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/components/Premium/BuySubscription/BuySubscription.tsx",
                                                    lineNumber: 69,
                                                    columnNumber: 33
                                                }, this),
                                                !props.activePremiumProduct || props.activePremiumProduct.expires.getTime() < new Date().getTime() + 3600 * 24 * 3 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            children: [
                                                                "Use code ",
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("code", {
                                                                    children: "M2OTC1OQ"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/components/Premium/BuySubscription/BuySubscription.tsx",
                                                                    lineNumber: 80,
                                                                    columnNumber: 52
                                                                }, this),
                                                                " at checkout, to get an extra ",
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("b", {
                                                                    children: "20% discount"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/components/Premium/BuySubscription/BuySubscription.tsx",
                                                                    lineNumber: 80,
                                                                    columnNumber: 103
                                                                }, this),
                                                                " on the yearly options"
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/components/Premium/BuySubscription/BuySubscription.tsx",
                                                            lineNumber: 80,
                                                            columnNumber: 40
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Button$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__["Button"], {
                                                            variant: "success",
                                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$BuySubscription$2f$BuySubscription$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].purchaseButton,
                                                            onClick: ()=>{
                                                                setIsYearOption(true);
                                                                setSelectedPremiumType(__TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$PremiumTypeUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PREMIUM_TYPES"].find((type)=>type.productId === 'premium_plus'));
                                                            },
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Number$2f$Number$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                                    number: 354.20
                                                                }, void 0, false, {
                                                                    fileName: "[project]/components/Premium/BuySubscription/BuySubscription.tsx",
                                                                    lineNumber: 89,
                                                                    columnNumber: 45
                                                                }, this),
                                                                " Euro (+VAT) / 52 weeks (23% off)"
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/components/Premium/BuySubscription/BuySubscription.tsx",
                                                            lineNumber: 81,
                                                            columnNumber: 41
                                                        }, this)
                                                    ]
                                                }, void 0, true) : null
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/components/Premium/BuySubscription/BuySubscription.tsx",
                                            lineNumber: 68,
                                            columnNumber: 29
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/components/Premium/BuySubscription/BuySubscription.tsx",
                                    lineNumber: 62,
                                    columnNumber: 25
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/Premium/BuySubscription/BuySubscription.tsx",
                            lineNumber: 58,
                            columnNumber: 21
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/components/Premium/BuySubscription/BuySubscription.tsx",
                        lineNumber: 57,
                        columnNumber: 17
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Col$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Col$3e$__["Col"], {
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Card$3e$__["Card"], {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Card$3e$__["Card"].Header, {
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Card$3e$__["Card"].Title, {
                                        children: "Premium"
                                    }, void 0, false, {
                                        fileName: "[project]/components/Premium/BuySubscription/BuySubscription.tsx",
                                        lineNumber: 99,
                                        columnNumber: 29
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/components/Premium/BuySubscription/BuySubscription.tsx",
                                    lineNumber: 98,
                                    columnNumber: 25
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Card$3e$__["Card"].Body, {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                    children: "up to 1s slower than prem+"
                                                }, void 0, false, {
                                                    fileName: "[project]/components/Premium/BuySubscription/BuySubscription.tsx",
                                                    lineNumber: 103,
                                                    columnNumber: 33
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                    children: "a lot of tools"
                                                }, void 0, false, {
                                                    fileName: "[project]/components/Premium/BuySubscription/BuySubscription.tsx",
                                                    lineNumber: 104,
                                                    columnNumber: 33
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                    children: "extended history & filter access"
                                                }, void 0, false, {
                                                    fileName: "[project]/components/Premium/BuySubscription/BuySubscription.tsx",
                                                    lineNumber: 105,
                                                    columnNumber: 33
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/components/Premium/BuySubscription/BuySubscription.tsx",
                                            lineNumber: 102,
                                            columnNumber: 29
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$BuySubscription$2f$BuySubscription$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].purchaseButtonContainer,
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Button$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__["Button"], {
                                                    variant: "success",
                                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$BuySubscription$2f$BuySubscription$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].purchaseButton,
                                                    onClick: ()=>{
                                                        setSelectedPremiumType(__TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$PremiumTypeUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PREMIUM_TYPES"].find((type)=>type.productId === 'premium'));
                                                    },
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Number$2f$Number$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                            number: 8.69
                                                        }, void 0, false, {
                                                            fileName: "[project]/components/Premium/BuySubscription/BuySubscription.tsx",
                                                            lineNumber: 115,
                                                            columnNumber: 37
                                                        }, this),
                                                        " Euro (+VAT) / 4 weeks"
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/components/Premium/BuySubscription/BuySubscription.tsx",
                                                    lineNumber: 108,
                                                    columnNumber: 33
                                                }, this),
                                                !props.activePremiumProduct || props.activePremiumProduct.expires.getTime() < new Date().getTime() + 3600 * 24 * 3 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    children: [
                                                        "Use code ",
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("code", {
                                                            children: "M2OTC1OQ"
                                                        }, void 0, false, {
                                                            fileName: "[project]/components/Premium/BuySubscription/BuySubscription.tsx",
                                                            lineNumber: 118,
                                                            columnNumber: 50
                                                        }, this),
                                                        " at checkout, to get an extra ",
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("b", {
                                                            children: "20% discount"
                                                        }, void 0, false, {
                                                            fileName: "[project]/components/Premium/BuySubscription/BuySubscription.tsx",
                                                            lineNumber: 118,
                                                            columnNumber: 101
                                                        }, this),
                                                        " on the yearly options"
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/components/Premium/BuySubscription/BuySubscription.tsx",
                                                    lineNumber: 118,
                                                    columnNumber: 38
                                                }, this) : null,
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Button$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__["Button"], {
                                                    variant: "success",
                                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$BuySubscription$2f$BuySubscription$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].purchaseButton,
                                                    onClick: ()=>{
                                                        setIsYearOption(true);
                                                        setSelectedPremiumType(__TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$PremiumTypeUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PREMIUM_TYPES"].find((type)=>type.productId === 'premium'));
                                                    },
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Number$2f$Number$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                            number: 96.69
                                                        }, void 0, false, {
                                                            fileName: "[project]/components/Premium/BuySubscription/BuySubscription.tsx",
                                                            lineNumber: 127,
                                                            columnNumber: 37
                                                        }, this),
                                                        " Euro (+VAT) / 52 weeks (14% off)"
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/components/Premium/BuySubscription/BuySubscription.tsx",
                                                    lineNumber: 119,
                                                    columnNumber: 33
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/components/Premium/BuySubscription/BuySubscription.tsx",
                                            lineNumber: 107,
                                            columnNumber: 29
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/components/Premium/BuySubscription/BuySubscription.tsx",
                                    lineNumber: 101,
                                    columnNumber: 25
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/Premium/BuySubscription/BuySubscription.tsx",
                            lineNumber: 97,
                            columnNumber: 21
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/components/Premium/BuySubscription/BuySubscription.tsx",
                        lineNumber: 96,
                        columnNumber: 17
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/components/Premium/BuySubscription/BuySubscription.tsx",
                lineNumber: 56,
                columnNumber: 13
            }, this),
            selectedPremiumType && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$BuyPremiumConfirmationDialog$2f$BuyPremiumConfirmationDialog$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                type: "subscription",
                show: selectedPremiumType !== undefined,
                purchasePremiumOption: __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$PremiumTypeUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PREMIUM_TYPES"].find((type)=>type.productId === 'premium')?.options[0],
                purchasePrice: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                    children: [
                        getSubscriptionPrice(),
                        " ",
                        isYearOption ? 'per year' : 'per month'
                    ]
                }, void 0, true),
                purchasePremiumType: selectedPremiumType,
                onHide: onSubscriptionBuyCancel,
                onConfirm: onSubscriptionBuy,
                activePremiumProduct: props.activePremiumProduct
            }, void 0, false, {
                fileName: "[project]/components/Premium/BuySubscription/BuySubscription.tsx",
                lineNumber: 135,
                columnNumber: 17
            }, this)
        ]
    }, void 0, true);
}
_s(BuySubscription, "Pr0m+CQBApz9lVR6qxmJMUcKZC4=");
_c = BuySubscription;
const __TURBOPACK__default__export__ = BuySubscription;
var _c;
__turbopack_context__.k.register(_c, "BuySubscription");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/Premium/Premium.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$GoogleSignIn$2f$GoogleSignIn$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/GoogleSignIn/GoogleSignIn.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$LoadingUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/LoadingUtils.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Button$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/Button.js [app-client] (ecmascript) <export default as Button>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Card$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/Card.js [app-client] (ecmascript) <export default as Card>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/Form.js [app-client] (ecmascript) <export default as Form>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Modal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Modal$3e$__ = __turbopack_context__.i("[project]/node_modules/react-bootstrap/esm/Modal.js [app-client] (ecmascript) <export default as Modal>");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$NavBar$2f$NavBar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/NavBar/NavBar.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$PremiumFeatures$2f$PremiumFeatures$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/Premium/PremiumFeatures/PremiumFeatures.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/api/ApiHelper.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$Premium$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/components/Premium/Premium.module.css [app-client] (css module)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$CoflCoins$2f$CoflCoinsPurchase$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/CoflCoins/CoflCoinsPurchase.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$BuyPremium$2f$BuyPremium$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/Premium/BuyPremium/BuyPremium.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$TransferCoflCoins$2f$TransferCoflCoins$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/TransferCoflCoins/TransferCoflCoins.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SettingsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/SettingsUtils.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$PremiumTypeUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/PremiumTypeUtils.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$PremiumStatus$2f$PremiumStatus$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/Premium/PremiumStatus/PremiumStatus.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-toastify/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$BuySubscription$2f$BuySubscription$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/Premium/BuySubscription/BuySubscription.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
function Premium() {
    _s();
    let [isLoggedIn, setIsLoggedIn] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    let [hasPremium, setHasPremium] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])();
    let [activePremiumProduct, setActivePremiumProduct] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])();
    let [products, setProducts] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    let [premiumSubscriptions, setPremiumSubscriptions] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    let [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    let [showSendCoflCoins, setShowSendCoflCoins] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    let [cancellationRightLossConfirmed, setCancellationRightLossConfirmed] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    let [isSSR, setIsSSR] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "Premium.useEffect": ()=>{
            setIsSSR(false);
            setCancellationRightLossConfirmed(localStorage.getItem(__TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SettingsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CANCELLATION_RIGHT_CONFIRMED"]) === 'true');
        // eslint-disable-next-line react-hooks/exhaustive-deps
        }
    }["Premium.useEffect"], []);
    function loadPremiumProducts() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].refreshLoadPremiumProducts((products)=>{
            products = products.filter((product)=>product.expires.getTime() > new Date().getTime());
            setProducts(products);
            let activePremiumProduct = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$PremiumTypeUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getHighestPriorityPremiumProduct"])(products);
            if (!activePremiumProduct) {
                setHasPremium(false);
            } else {
                setHasPremium(true);
                setActivePremiumProduct(activePremiumProduct);
            }
        });
    }
    function loadPremiumSubscriptions() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getPremiumSubscriptions().then((subscriptions)=>{
            subscriptions = subscriptions.filter((subscription)=>!subscription.endsAt || subscription.endsAt.getTime() > new Date().getTime());
            setPremiumSubscriptions(subscriptions);
        });
    }
    function onSubscriptionCancel(subscription) {
        __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$ApiHelper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].cancelPremiumSubscription(subscription.externalId).then(()=>{
            loadPremiumSubscriptions();
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success('Subscription cancelled');
        });
    }
    function onLogin() {
        let googleId = sessionStorage.getItem('googleId');
        if (googleId) {
            setIsLoading(true);
            setIsLoggedIn(true);
            Promise.all([
                loadPremiumProducts(),
                loadPremiumSubscriptions()
            ]).then(()=>{
                setIsLoading(false);
            });
        }
    }
    function onLoginFail() {
        setIsLoggedIn(false);
        setHasPremium(false);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$NavBar$2f$NavBar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                        fileName: "[project]/components/Premium/Premium.tsx",
                        lineNumber: 84,
                        columnNumber: 17
                    }, this),
                    "Premium"
                ]
            }, void 0, true, {
                fileName: "[project]/components/Premium/Premium.tsx",
                lineNumber: 83,
                columnNumber: 13
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("hr", {}, void 0, false, {
                fileName: "[project]/components/Premium/Premium.tsx",
                lineNumber: 87,
                columnNumber: 13
            }, this),
            isLoading ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$LoadingUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getLoadingElement"])() : !isLoggedIn ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    style: {
                        color: 'yellow',
                        margin: 0
                    },
                    children: "To use Premium please login with Google."
                }, void 0, false, {
                    fileName: "[project]/components/Premium/Premium.tsx",
                    lineNumber: 92,
                    columnNumber: 21
                }, this)
            }, void 0, false, {
                fileName: "[project]/components/Premium/Premium.tsx",
                lineNumber: 91,
                columnNumber: 17
            }, this) : hasPremium ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                style: {
                    color: '#00bc8c'
                },
                children: "You have a Premium account. Thank you for your support."
            }, void 0, false, {
                fileName: "[project]/components/Premium/Premium.tsx",
                lineNumber: 95,
                columnNumber: 17
            }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    style: {
                        color: 'red',
                        margin: 0
                    },
                    children: "You do not have a Premium account."
                }, void 0, false, {
                    fileName: "[project]/components/Premium/Premium.tsx",
                    lineNumber: 98,
                    columnNumber: 21
                }, this)
            }, void 0, false, {
                fileName: "[project]/components/Premium/Premium.tsx",
                lineNumber: 97,
                columnNumber: 17
            }, this),
            isLoggedIn && !hasPremium ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                    href: "#buyPremium",
                    children: "I want Premium!"
                }, void 0, false, {
                    fileName: "[project]/components/Premium/Premium.tsx",
                    lineNumber: 103,
                    columnNumber: 21
                }, this)
            }, void 0, false, {
                fileName: "[project]/components/Premium/Premium.tsx",
                lineNumber: 102,
                columnNumber: 17
            }, this) : null,
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("hr", {}, void 0, false, {
                fileName: "[project]/components/Premium/Premium.tsx",
                lineNumber: 106,
                columnNumber: 13
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                style: {
                    marginBottom: '20px'
                },
                children: [
                    isLoggedIn ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$PremiumStatus$2f$PremiumStatus$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                        products: products,
                        subscriptions: premiumSubscriptions,
                        onSubscriptionCancel: onSubscriptionCancel
                    }, void 0, false, {
                        fileName: "[project]/components/Premium/Premium.tsx",
                        lineNumber: 108,
                        columnNumber: 31
                    }, this) : null,
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$GoogleSignIn$2f$GoogleSignIn$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                        onAfterLogin: onLogin,
                        onLoginFail: onLoginFail
                    }, void 0, false, {
                        fileName: "[project]/components/Premium/Premium.tsx",
                        lineNumber: 109,
                        columnNumber: 17
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: isLoading ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$LoadingUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getLoadingElement"])() : ''
                    }, void 0, false, {
                        fileName: "[project]/components/Premium/Premium.tsx",
                        lineNumber: 110,
                        columnNumber: 17
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/components/Premium/Premium.tsx",
                lineNumber: 107,
                columnNumber: 13
            }, this),
            isLoggedIn ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                style: {
                    marginBottom: '20px'
                },
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("hr", {}, void 0, false, {
                        fileName: "[project]/components/Premium/Premium.tsx",
                        lineNumber: 114,
                        columnNumber: 21
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                        children: "Subscriptions"
                    }, void 0, false, {
                        fileName: "[project]/components/Premium/Premium.tsx",
                        lineNumber: 115,
                        columnNumber: 21
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$BuySubscription$2f$BuySubscription$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                        activePremiumProduct: activePremiumProduct
                    }, void 0, false, {
                        fileName: "[project]/components/Premium/Premium.tsx",
                        lineNumber: 116,
                        columnNumber: 21
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/components/Premium/Premium.tsx",
                lineNumber: 113,
                columnNumber: 17
            }, this) : null,
            isLoggedIn ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                style: {
                    marginBottom: '20px'
                },
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("hr", {}, void 0, false, {
                        fileName: "[project]/components/Premium/Premium.tsx",
                        lineNumber: 121,
                        columnNumber: 21
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                        children: "Prepaid"
                    }, void 0, false, {
                        fileName: "[project]/components/Premium/Premium.tsx",
                        lineNumber: 122,
                        columnNumber: 21
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$BuyPremium$2f$BuyPremium$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                        activePremiumProduct: activePremiumProduct,
                        premiumSubscriptions: premiumSubscriptions,
                        onNewActivePremiumProduct: loadPremiumProducts
                    }, void 0, false, {
                        fileName: "[project]/components/Premium/Premium.tsx",
                        lineNumber: 123,
                        columnNumber: 21
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/components/Premium/Premium.tsx",
                lineNumber: 120,
                columnNumber: 17
            }, this) : null,
            isLoggedIn ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                style: {
                    marginBottom: '20px'
                },
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("hr", {}, void 0, false, {
                        fileName: "[project]/components/Premium/Premium.tsx",
                        lineNumber: 132,
                        columnNumber: 21
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                        children: [
                            "CoflCoins",
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Button$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__["Button"], {
                                className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$Premium$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].sendCoflCoinsButton,
                                onClick: ()=>{
                                    setShowSendCoflCoins(true);
                                },
                                children: "Send CoflCoins"
                            }, void 0, false, {
                                fileName: "[project]/components/Premium/Premium.tsx",
                                lineNumber: 135,
                                columnNumber: 25
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Modal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Modal$3e$__["Modal"], {
                                size: 'lg',
                                show: showSendCoflCoins,
                                onHide: ()=>{
                                    setShowSendCoflCoins(false);
                                },
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Modal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Modal$3e$__["Modal"].Header, {
                                        closeButton: true,
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Modal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Modal$3e$__["Modal"].Title, {
                                            children: "Send CoflCoins"
                                        }, void 0, false, {
                                            fileName: "[project]/components/Premium/Premium.tsx",
                                            lineNumber: 151,
                                            columnNumber: 33
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/components/Premium/Premium.tsx",
                                        lineNumber: 150,
                                        columnNumber: 29
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Modal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Modal$3e$__["Modal"].Body, {
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$TransferCoflCoins$2f$TransferCoflCoins$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            onFinish: ()=>{
                                                setShowSendCoflCoins(false);
                                            }
                                        }, void 0, false, {
                                            fileName: "[project]/components/Premium/Premium.tsx",
                                            lineNumber: 154,
                                            columnNumber: 33
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/components/Premium/Premium.tsx",
                                        lineNumber: 153,
                                        columnNumber: 29
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/components/Premium/Premium.tsx",
                                lineNumber: 143,
                                columnNumber: 25
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/Premium/Premium.tsx",
                        lineNumber: 133,
                        columnNumber: 21
                    }, this),
                    !cancellationRightLossConfirmed ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        style: {
                            paddingBottom: '15px'
                        },
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Form$3e$__["Form"].Check, {
                                id: 'cancellationRightCheckbox',
                                className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$Premium$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].cancellationRightCheckbox,
                                defaultChecked: isSSR ? false : localStorage.getItem(__TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SettingsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CANCELLATION_RIGHT_CONFIRMED"]) === 'true',
                                onChange: (e)=>{
                                    localStorage.setItem(__TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$SettingsUtils$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CANCELLATION_RIGHT_CONFIRMED"], e.target.checked.toString());
                                    setCancellationRightLossConfirmed(e.target.checked);
                                },
                                inline: true
                            }, void 0, false, {
                                fileName: "[project]/components/Premium/Premium.tsx",
                                lineNumber: 164,
                                columnNumber: 29
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                htmlFor: 'cancellationRightCheckbox',
                                children: "By buying one of the following products, you confirm the immediate execution of the contract, hereby losing your cancellation right."
                            }, void 0, false, {
                                fileName: "[project]/components/Premium/Premium.tsx",
                                lineNumber: 174,
                                columnNumber: 29
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/Premium/Premium.tsx",
                        lineNumber: 163,
                        columnNumber: 25
                    }, this) : null,
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$CoflCoins$2f$CoflCoinsPurchase$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                        cancellationRightLossConfirmed: cancellationRightLossConfirmed
                    }, void 0, false, {
                        fileName: "[project]/components/Premium/Premium.tsx",
                        lineNumber: 180,
                        columnNumber: 21
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/components/Premium/Premium.tsx",
                lineNumber: 131,
                columnNumber: 17
            }, this) : null,
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("hr", {}, void 0, false, {
                fileName: "[project]/components/Premium/Premium.tsx",
                lineNumber: 183,
                columnNumber: 13
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                children: "Features"
            }, void 0, false, {
                fileName: "[project]/components/Premium/Premium.tsx",
                lineNumber: 184,
                columnNumber: 13
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Card$3e$__["Card"], {
                className: __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$Premium$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].premiumCard,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Card$3e$__["Card"].Header, {
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$bootstrap$2f$esm$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Card$3e$__["Card"].Title, {
                            children: hasPremium ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                children: "Thank you for your support. You have a Premium account. By buying another Premium plan you can extend your time. You can use the following premium features:"
                            }, void 0, false, {
                                fileName: "[project]/components/Premium/Premium.tsx",
                                lineNumber: 189,
                                columnNumber: 29
                            }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                children: "Log in and buy Premium to support us and get access to these features"
                            }, void 0, false, {
                                fileName: "[project]/components/Premium/Premium.tsx",
                                lineNumber: 194,
                                columnNumber: 29
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/components/Premium/Premium.tsx",
                            lineNumber: 187,
                            columnNumber: 21
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/components/Premium/Premium.tsx",
                        lineNumber: 186,
                        columnNumber: 17
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        style: {
                            padding: '15px'
                        },
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$Premium$2f$PremiumFeatures$2f$PremiumFeatures$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                            fileName: "[project]/components/Premium/Premium.tsx",
                            lineNumber: 199,
                            columnNumber: 21
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/components/Premium/Premium.tsx",
                        lineNumber: 198,
                        columnNumber: 17
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/components/Premium/Premium.tsx",
                lineNumber: 185,
                columnNumber: 13
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/components/Premium/Premium.tsx",
        lineNumber: 82,
        columnNumber: 9
    }, this);
}
_s(Premium, "Sn0oKl3dsZ2/ejs384c93Ue3bPI=");
_c = Premium;
const __TURBOPACK__default__export__ = Premium;
var _c;
__turbopack_context__.k.register(_c, "Premium");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=_32c59b6a._.js.map