{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/components/Premium/BuyPremium/BuyPremium.module.css"], "sourcesContent": [".label {\n    width: 200px;\n    float: left;\n    margin: 0;\n}\n\n.coinBalance {\n    float: right;\n}\n\n.dropdown {\n    display: inline;\n    width: 110px;\n}\n\n.dropdown::-webkit-scrollbar {\n    background-color: #272b30;\n}\n\n.dropdown::-webkit-scrollbar {\n    width: 20px;\n}\n\n.dropdown::-webkit-scrollbar-track {\n    box-shadow: inset 0 0 5px grey;\n    border-radius: 10px;\n}\n\n.dropdown::-webkit-scrollbar-thumb {\n    background: #3a3f44;\n    border-radius: 10px;\n}\n\n.dropdown::-webkit-scrollbar-thumb:hover {\n    background: #7a8288;\n}\n\n@media all and (max-width: 768px) {\n    .label {\n        width: auto;\n        margin-right: 15px;\n    }\n    .coinBalance {\n        float: none;\n        margin-top: 20px;\n    }\n    .coinBalance :global(b) {\n        font-size: medium !important;\n    }\n}\n\n@media all and (max-width: 350px) {\n    .label {\n        width: 100%;\n    }\n}\n\n/** Show the not selected price options more clearly as not selected **/\n.purchaseCard :global(:not(.btn-check:checked)) + .priceRangeButton{\n    background-color: #2a3644;\n    border-color: #2a3644;\n    filter: grayscale(70%);\n    font-weight: bold;\n    color: lightgray;\n}\n\n/** Show the selected price option more clearly as selected **/\n.purchaseCard :global(.btn-check:checked) + .priceRangeButton{\n    border-color: white;\n    font-weight: bold;\n}"], "names": [], "mappings": "AAAA;;;;;;AAMA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;AAIA;EACI;;;;;EAIA;;;;;EAIA;;;;;AAKJ;EACI;;;;;AAMJ;;;;;;;;AASA"}}]}