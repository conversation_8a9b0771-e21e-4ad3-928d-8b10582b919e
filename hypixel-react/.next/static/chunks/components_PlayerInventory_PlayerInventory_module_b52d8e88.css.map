{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/components/PlayerInventory/PlayerInventory.module.css"], "sourcesContent": [".inventory {\n    overflow: hidden;\n}\n\n.card {\n    margin-bottom: 30px;\n}\n\n.inventory .grid {\n    width: 540px;\n    height: 100%;\n}\n\n.inventory .gridCell {\n    width: 60px;\n    height: 60px;\n    background-color: #8b8b8b;\n    border-top: thick double #373737;\n    border-left: thick double #373737;\n    border-right: double #ffffff;\n    border-bottom: double #ffffff;\n    float: left;\n}\n\n.inventory .image {\n    display: block;\n    margin: auto;\n    padding: 2px;\n}\n\n.hoverElement li {\n    text-align: left;\n}\n\n.hoverElement {\n    min-width: \"800px\" !important;\n}\n\n#tooltipHoverId :global(.tooltip-inner){\n    max-width: 100%;\n  }"], "names": [], "mappings": "AAAA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;;;;;;;AAWA;;;;;;AAMA;;;;AAIA;;;;AAIA"}}]}