{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/components/FlipTracking/FlipTracking.module.css"], "sourcesContent": [".list {\n    display: flex;\n    flex-wrap: wrap;\n    justify-content: space-between;\n    flex-direction: initial;\n    width: 100%;\n}\n\n.listGroupItem {\n    border-radius: 20px !important;\n    width: 100%;\n    margin-bottom: 15px;\n}\n\n.noAuctionFound {\n    margin-right: auto;\n    margin-left: auto;\n    width: max-content;\n    text-align: center;\n    margin-top: 5vh;\n}\n\n.profitNumberCard {\n    width: 45%;\n    text-align: center;\n    border-color: transparent !important;\n    cursor: pointer;\n}\n\n.profitNumberHeader {\n    border-radius: 20px !important;\n}\n\n.profitNumberHeader:hover {\n    background-color: #3a3a3a;\n}\n\n@media all and (min-width: 1200px) {\n    .listGroupItem {\n        width: 49%;\n    }\n}\n\n.datePicker {\n    margin-right: 15px\n}\n\n.filterContainer{\n    display: flex;\n    align-items: center;\n}\n\n.rangeFilter {\n    width: 200px;\n}\n\n.itemFilterContainer:has(:global(.show.dropdown-menu)) {\n    z-index: 12;\n    position: relative;\n    height: 38px;\n}\n\n.multiSearch {\n    flex: 1;\n}\n\n.noPremiumInfoText{\n    font-size: small;\n}\n\n.topContainer {\n    display: flex;\n    justify-content: space-between;\n    margin-bottom: 20px;\n}\n\n.filterLabel {\n    min-width: 100px;\n}\n\n.filterValueField {\n    max-width: 200px;\n}\n\n@media all and (max-width: 768px) {\n    .noPremiumInfoText {\n        float: none;\n        text-align: center;\n    }\n    .topContainer {\n        display: flex;\n        flex-direction: column-reverse;\n        margin-bottom: 20px;\n        gap: 10px;\n    }\n}"], "names": [], "mappings": "AAAA;;;;;;;;AAQA;;;;;;AAMA;;;;;;;;AAQA;;;;;;;AAOA;;;;AAIA;;;;AAIA;EACI;;;;;AAKJ;;;;AAIA;;;;;AAKA;;;;AAIA;;;;;;AAMA;;;;AAIA;;;;AAIA;;;;;;AAMA;;;;AAIA;;;;AAIA;EACI;;;;;EAIA"}}]}