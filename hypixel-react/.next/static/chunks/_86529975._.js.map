{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///app/utils/Hooks.tsx"], "sourcesContent": ["import { Dispatch, SetStateAction, useCallback, useEffect, useRef, useState } from 'react'\nimport api from '../api/ApiHelper'\nimport { CUSTOM_EVENTS } from '../api/ApiTypes.d'\nimport { getCurrentCoflCoins, subscribeToCoflcoinChange } from './CoflCoinsUtils'\nimport { isClientSideRendering } from './SSRUtils'\nimport { getURLSearchParam } from './Parser/URLParser'\nimport { useRouter } from 'next/navigation'\n\nexport function useForceUpdate() {\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    const [update, setUpdate] = useState(0)\n    return () => setUpdate(update => update + 1)\n}\n\nexport function useSwipe(onSwipeUp?: Function, onSwipeRight?: Function, onSwipeDown?: Function, onSwipeLeft?: Function) {\n    if (!isClientSideRendering()) {\n        return\n    }\n\n    document.addEventListener('touchstart', handleTouchStart, false)\n    document.addEventListener('touchmove', handleTouchMove, false)\n\n    var xDown = null\n    var yDown = null\n\n    function getTouches(evt) {\n        return (\n            evt.touches || // browser API\n            evt.originalEvent.touches\n        ) // jQuery\n    }\n\n    function handleTouchStart(evt) {\n        const firstTouch = getTouches(evt)[0]\n        xDown = firstTouch.clientX\n        yDown = firstTouch.clientY\n    }\n\n    function handleTouchMove(evt) {\n        if (xDown === null || yDown === null) {\n            return\n        }\n\n        var xUp = evt.touches[0].clientX\n        var yUp = evt.touches[0].clientY\n\n        var xDiff = xDown! - xUp\n        var yDiff = yDown! - yUp\n\n        if (Math.abs(xDiff) > Math.abs(yDiff)) {\n            /*most significant*/\n            if (xDiff > 0) {\n                if (onSwipeLeft) {\n                    onSwipeLeft()\n                }\n            } else {\n                if (onSwipeRight) {\n                    onSwipeRight()\n                }\n            }\n        } else {\n            if (yDiff > 0) {\n                if (onSwipeUp) {\n                    onSwipeUp()\n                }\n            } else {\n                if (onSwipeDown) {\n                    onSwipeDown()\n                }\n            }\n        }\n        /* reset values */\n        xDown = null\n        yDown = null\n    }\n\n    return () => {\n        document.removeEventListener('touchstart', handleTouchStart, false)\n        document.removeEventListener('touchmove', handleTouchMove, false)\n    }\n}\n\nexport function useCoflCoins() {\n    const [coflCoins, setCoflCoins] = useState(getCurrentCoflCoins())\n\n    useEffect(() => {\n        let unsubscribe = subscribeToCoflcoinChange(setCoflCoins)\n\n        return () => {\n            unsubscribe()\n        }\n    }, [])\n\n    return coflCoins\n}\n\nexport function useWasAlreadyLoggedIn() {\n    const [wasAlreadyLoggedIn, setWasAlreadyLoggedIn] = useState(false)\n    useEffect(() => {\n        setWasAlreadyLoggedIn(localStorage.getItem('googleId') !== null)\n    }, [])\n\n    return wasAlreadyLoggedIn\n}\n\nexport function useDebounce(value, delay) {\n    const [debouncedValue, setDebouncedValue] = useState(value)\n    useEffect(() => {\n        const handler = setTimeout(() => {\n            setDebouncedValue(value)\n        }, delay)\n        return () => {\n            clearTimeout(handler)\n        }\n    }, [value, delay])\n    return debouncedValue\n}\n\ntype ReadonlyRef<T> = {\n    readonly current: T\n}\n\nexport function useStateWithRef<T>(defaultValue: T): [T, Dispatch<SetStateAction<T>>, ReadonlyRef<T>] {\n    const [state, _setState] = useState(defaultValue)\n    let stateRef = useRef(state)\n\n    const setState: typeof _setState = useCallback((newState: T) => {\n        stateRef.current = newState\n        _setState(newState)\n    }, [])\n\n    return [state, setState, stateRef]\n}\n\nexport function useQueryParamState<T>(key: string, defaultValue: T): [T, Dispatch<SetStateAction<T>>] {\n    const [state, setState] = useState<T>(getDefaultValue() || defaultValue)\n    const router = useRouter()\n\n    function getDefaultValue(): T | undefined {\n        let param = getURLSearchParam(key)\n        if (!param) {\n            return undefined\n        }\n        return JSON.parse(decodeURIComponent(param)) as T\n    }\n\n    function _setState(newState: T) {\n        setState(newState)\n        let urlparams = new URLSearchParams(window.location.search)\n        if (!newState) {\n            urlparams.delete(key)\n        } else {\n            urlparams.set(key, encodeURIComponent(JSON.stringify(newState)))\n        }\n        router.replace(`${window.location.pathname}?${urlparams.toString()}`)\n    }\n\n    return [state, _setState]\n}\n\nexport function useIsMobile() {\n    let [isMobile, setIsMobile] = useState(false)\n\n    useEffect(() => {\n        setIsMobile(isMobileCheck())\n    }, [])\n\n    function isMobileCheck() {\n        let check = false\n        // eslint-disable-next-line no-useless-escape\n        ;(function (a) {\n            if (\n                /(android|bb\\d+|meego).+mobile|avantgo|bada\\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i.test(\n                    a\n                ) ||\n                /1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\\-(n|u)|c55\\/|capi|ccwa|cdm\\-|cell|chtm|cldc|cmd\\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\\-s|devi|dica|dmob|do(c|p)o|ds(12|\\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\\-|_)|g1 u|g560|gene|gf\\-5|g\\-mo|go(\\.w|od)|gr(ad|un)|haie|hcit|hd\\-(m|p|t)|hei\\-|hi(pt|ta)|hp( i|ip)|hs\\-c|ht(c(\\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\\-(20|go|ma)|i230|iac( |\\-|\\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\\/)|klon|kpt |kwc\\-|kyo(c|k)|le(no|xi)|lg( g|\\/(k|l|u)|50|54|\\-[a-w])|libw|lynx|m1\\-w|m3ga|m50\\/|ma(te|ui|xo)|mc(01|21|ca)|m\\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\\-2|po(ck|rt|se)|prox|psio|pt\\-g|qa\\-a|qc(07|12|21|32|60|\\-[2-7]|i\\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\\-|oo|p\\-)|sdk\\/|se(c(\\-|0|1)|47|mc|nd|ri)|sgh\\-|shar|sie(\\-|m)|sk\\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\\-|v\\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\\-|tdg\\-|tel(i|m)|tim\\-|t\\-mo|to(pl|sh)|ts(70|m\\-|m3|m5)|tx\\-9|up(\\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\\-|your|zeto|zte\\-/i.test(\n                    a.substr(0, 4)\n                )\n            )\n                check = true\n        })(navigator.userAgent || navigator.vendor || (window as any).opera)\n        return check\n    }\n\n    return isMobile\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AAGA;AACA;AACA;AACA;;;;;;;AAEO,SAAS;;IACZ,6DAA6D;IAC7D,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,OAAO,IAAM,UAAU,CAAA,SAAU,SAAS;AAC9C;GAJgB;AAMT,SAAS,SAAS,SAAoB,EAAE,YAAuB,EAAE,WAAsB,EAAE,WAAsB;IAClH,IAAI,CAAC,CAAA,GAAA,qHAAA,CAAA,wBAAqB,AAAD,KAAK;QAC1B;IACJ;IAEA,SAAS,gBAAgB,CAAC,cAAc,kBAAkB;IAC1D,SAAS,gBAAgB,CAAC,aAAa,iBAAiB;IAExD,IAAI,QAAQ;IACZ,IAAI,QAAQ;IAEZ,SAAS,WAAW,GAAG;QACnB,OACI,IAAI,OAAO,IAAI,cAAc;QAC7B,IAAI,aAAa,CAAC,OAAO,EAC3B,SAAS;IACf;IAEA,SAAS,iBAAiB,GAAG;QACzB,MAAM,aAAa,WAAW,IAAI,CAAC,EAAE;QACrC,QAAQ,WAAW,OAAO;QAC1B,QAAQ,WAAW,OAAO;IAC9B;IAEA,SAAS,gBAAgB,GAAG;QACxB,IAAI,UAAU,QAAQ,UAAU,MAAM;YAClC;QACJ;QAEA,IAAI,MAAM,IAAI,OAAO,CAAC,EAAE,CAAC,OAAO;QAChC,IAAI,MAAM,IAAI,OAAO,CAAC,EAAE,CAAC,OAAO;QAEhC,IAAI,QAAQ,QAAS;QACrB,IAAI,QAAQ,QAAS;QAErB,IAAI,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC,QAAQ;YACnC,kBAAkB,GAClB,IAAI,QAAQ,GAAG;gBACX,IAAI,aAAa;oBACb;gBACJ;YACJ,OAAO;gBACH,IAAI,cAAc;oBACd;gBACJ;YACJ;QACJ,OAAO;YACH,IAAI,QAAQ,GAAG;gBACX,IAAI,WAAW;oBACX;gBACJ;YACJ,OAAO;gBACH,IAAI,aAAa;oBACb;gBACJ;YACJ;QACJ;QACA,gBAAgB,GAChB,QAAQ;QACR,QAAQ;IACZ;IAEA,OAAO;QACH,SAAS,mBAAmB,CAAC,cAAc,kBAAkB;QAC7D,SAAS,mBAAmB,CAAC,aAAa,iBAAiB;IAC/D;AACJ;AAEO,SAAS;;IACZ,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,GAAA,2HAAA,CAAA,sBAAmB,AAAD;IAE7D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACN,IAAI,cAAc,CAAA,GAAA,2HAAA,CAAA,4BAAyB,AAAD,EAAE;YAE5C;0CAAO;oBACH;gBACJ;;QACJ;iCAAG,EAAE;IAEL,OAAO;AACX;IAZgB;AAcT,SAAS;;IACZ,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2CAAE;YACN,sBAAsB,aAAa,OAAO,CAAC,gBAAgB;QAC/D;0CAAG,EAAE;IAEL,OAAO;AACX;IAPgB;AAST,SAAS,YAAY,KAAK,EAAE,KAAK;;IACpC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACN,MAAM,UAAU;iDAAW;oBACvB,kBAAkB;gBACtB;gDAAG;YACH;yCAAO;oBACH,aAAa;gBACjB;;QACJ;gCAAG;QAAC;QAAO;KAAM;IACjB,OAAO;AACX;IAXgB;AAiBT,SAAS,gBAAmB,YAAe;;IAC9C,MAAM,CAAC,OAAO,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACpC,IAAI,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAEtB,MAAM,WAA6B,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iDAAE,CAAC;YAC5C,SAAS,OAAO,GAAG;YACnB,UAAU;QACd;gDAAG,EAAE;IAEL,OAAO;QAAC;QAAO;QAAU;KAAS;AACtC;IAVgB;AAYT,SAAS,mBAAsB,GAAW,EAAE,YAAe;;IAC9D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAK,qBAAqB;IAC3D,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,SAAS;QACL,IAAI,QAAQ,CAAA,GAAA,gIAAA,CAAA,oBAAiB,AAAD,EAAE;QAC9B,IAAI,CAAC,OAAO;YACR,OAAO;QACX;QACA,OAAO,KAAK,KAAK,CAAC,mBAAmB;IACzC;IAEA,SAAS,UAAU,QAAW;QAC1B,SAAS;QACT,IAAI,YAAY,IAAI,gBAAgB,OAAO,QAAQ,CAAC,MAAM;QAC1D,IAAI,CAAC,UAAU;YACX,UAAU,MAAM,CAAC;QACrB,OAAO;YACH,UAAU,GAAG,CAAC,KAAK,mBAAmB,KAAK,SAAS,CAAC;QACzD;QACA,OAAO,OAAO,CAAC,GAAG,OAAO,QAAQ,CAAC,QAAQ,CAAC,CAAC,EAAE,UAAU,QAAQ,IAAI;IACxE;IAEA,OAAO;QAAC;QAAO;KAAU;AAC7B;IAxBgB;;QAEG,qIAAA,CAAA,YAAS;;;AAwBrB,SAAS;;IACZ,IAAI,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACN,YAAY;QAChB;gCAAG,EAAE;IAEL,SAAS;QACL,IAAI,QAAQ;QAEX,CAAC,SAAU,CAAC;YACT,IACI,2TAA2T,IAAI,CAC3T,MAEJ,0kDAA0kD,IAAI,CAC1kD,EAAE,MAAM,CAAC,GAAG,KAGhB,QAAQ;QAChB,CAAC,EAAE,UAAU,SAAS,IAAI,UAAU,MAAM,IAAI,AAAC,OAAe,KAAK;QACnE,OAAO;IACX;IAEA,OAAO;AACX;IAzBgB", "debugId": null}}, {"offset": {"line": 214, "column": 0}, "map": {"version": 3, "sources": ["file:///app/utils/LoadingUtils.tsx"], "sourcesContent": ["import Image from 'next/image'\nimport React, { type JSX } from 'react';\nimport { Spinner } from 'react-bootstrap'\n\nexport function getLoadingElement(text?: JSX.Element): JSX.Element {\n    return (\n        <div style={{ textAlign: 'center' }}>\n            <span>\n                <Spinner animation=\"grow\" variant=\"primary\"></Spinner>\n                <Spinner animation=\"grow\" variant=\"primary\"></Spinner>\n                <Spinner animation=\"grow\" variant=\"primary\"></Spinner>\n            </span>\n            {text ? text : <p>Loading Data...</p>}\n        </div>\n    )\n}\n\nexport function getInitialLoadingElement(): JSX.Element {\n    return (\n        <div className=\"main-loading\" style={{ height: '500px' }}>\n            <div>\n                <Image src=\"/logo192.png\" height=\"192\" width=\"192\" alt=\"auction house logo\" />\n                <div className=\"main-loading\">\n                    <span>Loading App...</span>\n                </div>\n            </div>\n        </div>\n    )\n}\n"], "names": [], "mappings": ";;;;;AAAA;AAEA;;;;AAEO,SAAS,kBAAkB,IAAkB;IAChD,qBACI,6LAAC;QAAI,OAAO;YAAE,WAAW;QAAS;;0BAC9B,6LAAC;;kCACG,6LAAC,6LAAA,CAAA,UAAO;wBAAC,WAAU;wBAAO,SAAQ;;;;;;kCAClC,6LAAC,6LAAA,CAAA,UAAO;wBAAC,WAAU;wBAAO,SAAQ;;;;;;kCAClC,6LAAC,6LAAA,CAAA,UAAO;wBAAC,WAAU;wBAAO,SAAQ;;;;;;;;;;;;YAErC,OAAO,qBAAO,6LAAC;0BAAE;;;;;;;;;;;;AAG9B;AAEO,SAAS;IACZ,qBACI,6LAAC;QAAI,WAAU;QAAe,OAAO;YAAE,QAAQ;QAAQ;kBACnD,cAAA,6LAAC;;8BACG,6LAAC,gIAAA,CAAA,UAAK;oBAAC,KAAI;oBAAe,QAAO;oBAAM,OAAM;oBAAM,KAAI;;;;;;8BACvD,6LAAC;oBAAI,WAAU;8BACX,cAAA,6LAAC;kCAAK;;;;;;;;;;;;;;;;;;;;;;AAK1B", "debugId": null}}, {"offset": {"line": 328, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/components/GoogleSignIn/GoogleSignIn.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"googleButton\": \"GoogleSignIn-module__lTSYOa__googleButton\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA"}}, {"offset": {"line": 337, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/GoogleSignIn/GoogleSignIn.tsx"], "sourcesContent": ["'use client'\nimport React, { useEffect, useState } from 'react'\nimport { toast } from 'react-toastify'\nimport api from '../../api/ApiHelper'\nimport { useMatomo } from '@jonkoops/matomo-tracker-react'\nimport { useForceUpdate } from '../../utils/Hooks'\nimport { isClientSideRendering } from '../../utils/SSRUtils'\nimport { CUSTOM_EVENTS } from '../../api/ApiTypes.d'\nimport { GoogleLogin } from '@react-oauth/google'\nimport styles from './GoogleSignIn.module.css'\nimport { GOOGLE_EMAIL, GOOGLE_NAME, GOOGLE_PROFILE_PICTURE_URL, setSetting } from '../../utils/SettingsUtils'\nimport { atobUnicode } from '../../utils/Base64Utils'\nimport { Modal } from 'react-bootstrap'\n\ninterface Props {\n    onAfterLogin?(): void\n    onLoginFail?(): void\n    onManualLoginClick?(): void\n    rerenderFlip?: number\n}\n\nfunction GoogleSignIn(props: Props) {\n    let [wasAlreadyLoggedInThisSession, setWasAlreadyLoggedInThisSession] = useState(\n        isClientSideRendering() ? isValidTokenAvailable(localStorage.getItem('googleId')) : false\n    )\n\n    let [isLoggedIn, setIsLoggedIn] = useState(false)\n    let [isSSR, setIsSSR] = useState(true)\n    let [isLoginNotShowing, setIsLoginNotShowing] = useState(false)\n    let [showButtonNotRenderingModal, setShowButtonNotRenderingModal] = useState(false)\n    let { trackEvent } = useMatomo()\n    let forceUpdate = useForceUpdate()\n\n    useEffect(() => {\n        setIsSSR(false)\n        if (wasAlreadyLoggedInThisSession) {\n            let token = localStorage.getItem('googleId')!\n            let userObject = JSON.parse(atobUnicode(token.split('.')[1]))\n            setSetting(GOOGLE_EMAIL, userObject.email)\n            onLoginSucces(token)\n        } else {\n            setTimeout(() => {\n                let isShown = false\n                document.querySelectorAll('iframe').forEach(e => {\n                    if (e.src && e.src.includes('accounts.google.com')) {\n                        isShown = true\n                    }\n                })\n                if (!isShown) {\n                    setIsLoggedIn(false)\n                    setIsLoginNotShowing(true)\n                    sessionStorage.removeItem('googleId')\n                    localStorage.removeItem('googleId')\n                }\n            }, 5000)\n        }\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [])\n\n    useEffect(() => {\n        if (wasAlreadyLoggedInThisSession) {\n            setIsLoggedIn(true)\n        }\n    }, [wasAlreadyLoggedInThisSession])\n\n    useEffect(() => {\n        forceUpdate()\n        setIsLoggedIn(sessionStorage.getItem('googleId') !== null)\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [props.rerenderFlip])\n\n    function onLoginSucces(token: string) {\n        setIsLoggedIn(true)\n        api.loginWithToken(token)\n            .then(token => {\n                localStorage.setItem('googleId', token)\n                sessionStorage.setItem('googleId', token)\n                let refId = (window as any).refId\n                if (refId) {\n                    api.setRef(refId)\n                }\n                document.dispatchEvent(new CustomEvent(CUSTOM_EVENTS.GOOGLE_LOGIN))\n                if (props.onAfterLogin) {\n                    props.onAfterLogin()\n                }\n            })\n            .catch(error => {\n                // dont show the error message for the invalid token error\n                // the google sign component sometimes sends an outdated token, causing this error\n                if (error.slug !== 'invalid_token') {\n                    toast.error(`An error occoured while trying to sign in with Google. ${error ? error.slug || JSON.stringify(error) : null}`)\n                } else {\n                    console.warn('setGoogle: Invalid token error', error)\n                    sessionStorage.removeItem('googleId')\n                    localStorage.removeItem('googleId')\n                }\n                setIsLoggedIn(false)\n                setWasAlreadyLoggedInThisSession(false)\n                sessionStorage.removeItem('googleId')\n                localStorage.removeItem('googleId')\n            })\n    }\n\n    function onLoginFail() {\n        toast.error('Something went wrong, please try again.', { autoClose: 20000 })\n    }\n\n    function onLoginClick() {\n        if (props.onManualLoginClick) {\n            props.onManualLoginClick()\n        }\n        trackEvent({\n            category: 'login',\n            action: 'click'\n        })\n    }\n\n    let style: React.CSSProperties = isLoggedIn\n        ? {\n              visibility: 'collapse',\n              height: 0\n          }\n        : {}\n\n    if (isSSR) {\n        return null\n    }\n\n    let buttonNotRenderingModal = (\n        <Modal\n            show={showButtonNotRenderingModal}\n            onHide={() => {\n                setShowButtonNotRenderingModal(false)\n            }}\n        >\n            <Modal.Header>\n                <Modal.Title>Google Login button not showing up?</Modal.Title>\n            </Modal.Header>\n            <Modal.Body>\n                <p>This is most likely caused by either an external software like an anti virus or your browser/extension blocking it.</p>\n                <hr />\n                <p>Known issues:</p>\n                <ul>\n                    <li>Kaspersky's \"Secure Browse\" feature seems to block the Google login.</li>\n                    <li>Opera GX seems to sometimes blocks the login button. The specific setting or reason on when it blocks it is unknown.</li>\n                </ul>\n            </Modal.Body>\n        </Modal>\n    )\n\n    return (\n        <div style={style} onClickCapture={onLoginClick}>\n            {!wasAlreadyLoggedInThisSession ? (\n                <>\n                    <div className={styles.googleButton}>\n                        {!isSSR ? (\n                            <GoogleLogin\n                                onSuccess={response => {\n                                    try {\n                                        let userObject = JSON.parse(atobUnicode(response.credential!.split('.')[1]))\n                                        setSetting(GOOGLE_PROFILE_PICTURE_URL, userObject.picture)\n                                        setSetting(GOOGLE_EMAIL, userObject.email)\n                                        setSetting(GOOGLE_NAME, userObject.name)\n                                    } catch {\n                                        toast.warn('Parsing issue with the google token. There might be issues when displaying details on the account page!')\n                                    }\n                                    onLoginSucces(response.credential!)\n                                }}\n                                onError={onLoginFail}\n                                theme={'filled_blue'}\n                                size={'large'}\n                                useOneTap\n                                auto_select\n                            />\n                        ) : null}\n                    </div>\n                    <p>\n                        I have read and agree to the <a href=\"https://coflnet.com/privacy\">Privacy Policy</a>\n                    </p>\n                    {isLoginNotShowing ? (\n                        <p>\n                            Login button not showing? Click{' '}\n                            <span\n                                style={{ color: '#007bff', cursor: 'pointer' }}\n                                onClick={() => {\n                                    setShowButtonNotRenderingModal(true)\n                                }}\n                            >\n                                here\n                            </span>\n                            .\n                        </p>\n                    ) : null}\n                </>\n            ) : null}\n            {buttonNotRenderingModal}\n        </div>\n    )\n}\n\nexport default GoogleSignIn\n\nexport function isValidTokenAvailable(token?: string | null) {\n    if (!token || token === 'null') {\n        return\n    }\n    try {\n        let details = JSON.parse(atobUnicode(token.split('.')[1]))\n        let expirationDate = new Date(parseInt(details.exp) * 1000)\n        return expirationDate.getTime() - 10000 > new Date().getTime()\n    } catch (e) {\n        toast.warn(\"Parsing issue with the google token. Can't automatically login!\")\n        return false\n    }\n}\n"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAZA;;;;;;;;;;;;;AAqBA,SAAS,aAAa,KAAY;;IAC9B,IAAI,CAAC,+BAA+B,iCAAiC,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAC3E,CAAA,GAAA,qHAAA,CAAA,wBAAqB,AAAD,MAAM,sBAAsB,aAAa,OAAO,CAAC,eAAe;IAGxF,IAAI,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,IAAI,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,IAAI,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,IAAI,CAAC,6BAA6B,+BAA+B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7E,IAAI,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,sNAAA,CAAA,YAAS,AAAD;IAC7B,IAAI,cAAc,CAAA,GAAA,kHAAA,CAAA,iBAAc,AAAD;IAE/B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACN,SAAS;YACT,IAAI,+BAA+B;gBAC/B,IAAI,QAAQ,aAAa,OAAO,CAAC;gBACjC,IAAI,aAAa,KAAK,KAAK,CAAC,CAAA,GAAA,wHAAA,CAAA,cAAW,AAAD,EAAE,MAAM,KAAK,CAAC,IAAI,CAAC,EAAE;gBAC3D,CAAA,GAAA,0HAAA,CAAA,aAAU,AAAD,EAAE,0HAAA,CAAA,eAAY,EAAE,WAAW,KAAK;gBACzC,cAAc;YAClB,OAAO;gBACH;8CAAW;wBACP,IAAI,UAAU;wBACd,SAAS,gBAAgB,CAAC,UAAU,OAAO;sDAAC,CAAA;gCACxC,IAAI,EAAE,GAAG,IAAI,EAAE,GAAG,CAAC,QAAQ,CAAC,wBAAwB;oCAChD,UAAU;gCACd;4BACJ;;wBACA,IAAI,CAAC,SAAS;4BACV,cAAc;4BACd,qBAAqB;4BACrB,eAAe,UAAU,CAAC;4BAC1B,aAAa,UAAU,CAAC;wBAC5B;oBACJ;6CAAG;YACP;QACA,uDAAuD;QAC3D;iCAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACN,IAAI,+BAA+B;gBAC/B,cAAc;YAClB;QACJ;iCAAG;QAAC;KAA8B;IAElC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACN;YACA,cAAc,eAAe,OAAO,CAAC,gBAAgB;QACrD,uDAAuD;QAC3D;iCAAG;QAAC,MAAM,YAAY;KAAC;IAEvB,SAAS,cAAc,KAAa;QAChC,cAAc;QACd,oHAAA,CAAA,UAAG,CAAC,cAAc,CAAC,OACd,IAAI,CAAC,CAAA;YACF,aAAa,OAAO,CAAC,YAAY;YACjC,eAAe,OAAO,CAAC,YAAY;YACnC,IAAI,QAAQ,AAAC,OAAe,KAAK;YACjC,IAAI,OAAO;gBACP,oHAAA,CAAA,UAAG,CAAC,MAAM,CAAC;YACf;YACA,SAAS,aAAa,CAAC,IAAI,YAAY,wHAAA,CAAA,gBAAa,CAAC,YAAY;YACjE,IAAI,MAAM,YAAY,EAAE;gBACpB,MAAM,YAAY;YACtB;QACJ,GACC,KAAK,CAAC,CAAA;YACH,0DAA0D;YAC1D,kFAAkF;YAClF,IAAI,MAAM,IAAI,KAAK,iBAAiB;gBAChC,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,uDAAuD,EAAE,QAAQ,MAAM,IAAI,IAAI,KAAK,SAAS,CAAC,SAAS,MAAM;YAC9H,OAAO;gBACH,QAAQ,IAAI,CAAC,kCAAkC;gBAC/C,eAAe,UAAU,CAAC;gBAC1B,aAAa,UAAU,CAAC;YAC5B;YACA,cAAc;YACd,iCAAiC;YACjC,eAAe,UAAU,CAAC;YAC1B,aAAa,UAAU,CAAC;QAC5B;IACR;IAEA,SAAS;QACL,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,2CAA2C;YAAE,WAAW;QAAM;IAC9E;IAEA,SAAS;QACL,IAAI,MAAM,kBAAkB,EAAE;YAC1B,MAAM,kBAAkB;QAC5B;QACA,WAAW;YACP,UAAU;YACV,QAAQ;QACZ;IACJ;IAEA,IAAI,QAA6B,aAC3B;QACI,YAAY;QACZ,QAAQ;IACZ,IACA,CAAC;IAEP,IAAI,OAAO;QACP,OAAO;IACX;IAEA,IAAI,wCACA,6LAAC,yLAAA,CAAA,QAAK;QACF,MAAM;QACN,QAAQ;YACJ,+BAA+B;QACnC;;0BAEA,6LAAC,yLAAA,CAAA,QAAK,CAAC,MAAM;0BACT,cAAA,6LAAC,yLAAA,CAAA,QAAK,CAAC,KAAK;8BAAC;;;;;;;;;;;0BAEjB,6LAAC,yLAAA,CAAA,QAAK,CAAC,IAAI;;kCACP,6LAAC;kCAAE;;;;;;kCACH,6LAAC;;;;;kCACD,6LAAC;kCAAE;;;;;;kCACH,6LAAC;;0CACG,6LAAC;0CAAG;;;;;;0CACJ,6LAAC;0CAAG;;;;;;;;;;;;;;;;;;;;;;;;IAMpB,qBACI,6LAAC;QAAI,OAAO;QAAO,gBAAgB;;YAC9B,CAAC,8CACE;;kCACI,6LAAC;wBAAI,WAAW,yJAAA,CAAA,UAAM,CAAC,YAAY;kCAC9B,CAAC,sBACE,6LAAC,qKAAA,CAAA,cAAW;4BACR,WAAW,CAAA;gCACP,IAAI;oCACA,IAAI,aAAa,KAAK,KAAK,CAAC,CAAA,GAAA,wHAAA,CAAA,cAAW,AAAD,EAAE,SAAS,UAAU,CAAE,KAAK,CAAC,IAAI,CAAC,EAAE;oCAC1E,CAAA,GAAA,0HAAA,CAAA,aAAU,AAAD,EAAE,0HAAA,CAAA,6BAA0B,EAAE,WAAW,OAAO;oCACzD,CAAA,GAAA,0HAAA,CAAA,aAAU,AAAD,EAAE,0HAAA,CAAA,eAAY,EAAE,WAAW,KAAK;oCACzC,CAAA,GAAA,0HAAA,CAAA,aAAU,AAAD,EAAE,0HAAA,CAAA,cAAW,EAAE,WAAW,IAAI;gCAC3C,EAAE,OAAM;oCACJ,sJAAA,CAAA,QAAK,CAAC,IAAI,CAAC;gCACf;gCACA,cAAc,SAAS,UAAU;4BACrC;4BACA,SAAS;4BACT,OAAO;4BACP,MAAM;4BACN,SAAS;4BACT,WAAW;;;;;mCAEf;;;;;;kCAER,6LAAC;;4BAAE;0CAC8B,6LAAC;gCAAE,MAAK;0CAA8B;;;;;;;;;;;;oBAEtE,kCACG,6LAAC;;4BAAE;4BACiC;0CAChC,6LAAC;gCACG,OAAO;oCAAE,OAAO;oCAAW,QAAQ;gCAAU;gCAC7C,SAAS;oCACL,+BAA+B;gCACnC;0CACH;;;;;;4BAEM;;;;;;+BAGX;;+BAER;YACH;;;;;;;AAGb;GAjLS;;QASgB,sNAAA,CAAA,YAAS;QACZ,kHAAA,CAAA,iBAAc;;;KAV3B;uCAmLM;AAER,SAAS,sBAAsB,KAAqB;IACvD,IAAI,CAAC,SAAS,UAAU,QAAQ;QAC5B;IACJ;IACA,IAAI;QACA,IAAI,UAAU,KAAK,KAAK,CAAC,CAAA,GAAA,wHAAA,CAAA,cAAW,AAAD,EAAE,MAAM,KAAK,CAAC,IAAI,CAAC,EAAE;QACxD,IAAI,iBAAiB,IAAI,KAAK,SAAS,QAAQ,GAAG,IAAI;QACtD,OAAO,eAAe,OAAO,KAAK,QAAQ,IAAI,OAAO,OAAO;IAChE,EAAE,OAAO,GAAG;QACR,sJAAA,CAAA,QAAK,CAAC,IAAI,CAAC;QACX,OAAO;IACX;AACJ", "debugId": null}}, {"offset": {"line": 669, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/components/NavBar/NavBar.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"hamburgerIcon\": \"NavBar-module__yBvhsG__hamburgerIcon\",\n  \"logo\": \"NavBar-module__yBvhsG__logo\",\n  \"menuItem\": \"NavBar-module__yBvhsG__menuItem\",\n  \"navBar\": \"NavBar-module__yBvhsG__navBar\",\n  \"navClosing\": \"NavBar-module__yBvhsG__navClosing\",\n  \"navOpen\": \"NavBar-module__yBvhsG__navOpen\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 683, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/NavBar/NavBar.tsx"], "sourcesContent": ["'use client'\nimport AccountBalanceIcon from '@mui/icons-material/AccountBalance'\nimport AccountIcon from '@mui/icons-material/AccountCircle'\nimport BuildIcon from '@mui/icons-material/Build'\nimport ChatIcon from '@mui/icons-material/Chat'\nimport DownloadIcon from '@mui/icons-material/Download'\nimport HomeIcon from '@mui/icons-material/Home'\nimport MenuIcon from '@mui/icons-material/Menu'\nimport NotificationIcon from '@mui/icons-material/NotificationsOutlined'\nimport PetsIcon from '@mui/icons-material/PetsOutlined'\nimport PolicyIcon from '@mui/icons-material/Policy'\nimport ShareIcon from '@mui/icons-material/ShareOutlined'\nimport StorefrontIcon from '@mui/icons-material/Storefront'\nimport CurrencyExchangeIcon from '@mui/icons-material/CurrencyExchange'\nimport Image from 'next/image'\nimport Link from 'next/link'\nimport React, { useEffect, useState } from 'react'\nimport { Menu, MenuItem, Sidebar } from 'react-pro-sidebar'\nimport { useForceUpdate } from '../../utils/Hooks'\nimport styles from './NavBar.module.css'\n\nlet resizePromise: NodeJS.Timeout | null = null\n\ninterface Props {\n    hamburgerIconStyle?: React.CSSProperties\n}\n\nfunction NavBar(props: Props) {\n    let [isWideOpen, setIsWideOpen] = useState(false)\n    let [isHovering, setIsHovering] = useState(false)\n    let [isSmall, setIsSmall] = useState(true)\n    let [collapsed, setCollapsed] = useState(true)\n    let forceUpdate = useForceUpdate()\n\n    useEffect(() => {\n        setIsSmall(document.body.clientWidth < 1500)\n\n        window.addEventListener('resize', resizeHandler)\n\n        return () => {\n            window.removeEventListener('resize', resizeHandler)\n        }\n    }, [])\n\n    useEffect(() => {\n        if (isWideOpen) {\n            document.addEventListener('click', outsideClickHandler, true)\n        } else {\n            document.removeEventListener('click', outsideClickHandler, true)\n        }\n\n        return () => {\n            document.removeEventListener('click', outsideClickHandler, true)\n        }\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [isWideOpen])\n\n    useEffect(() => {\n        setCollapsed(isCollapsed())\n    }, [isSmall, isWideOpen, isHovering])\n\n    function isCollapsed() {\n        if (isSmall) {\n            return false\n        }\n        return !isWideOpen && !isHovering\n    }\n\n    function outsideClickHandler(evt) {\n        const flyoutEl = document.getElementById('navBar')\n        const hamburgerEl = document.getElementById('hamburgerIcon')\n        let targetEl = evt.target\n\n        do {\n            if (targetEl === flyoutEl || targetEl === hamburgerEl) {\n                return\n            }\n            targetEl = (targetEl as any).parentNode\n        } while (targetEl)\n\n        if (isWideOpen) {\n            if (isSmall) {\n                let el = document.getElementById('pro-sidebar')\n                el?.classList.add(styles.navClosing)\n                el?.classList.remove(styles.navOpen)\n                setTimeout(() => {\n                    setIsWideOpen(false)\n                    el?.classList.remove(styles.navClosing)\n                }, 500)\n            } else {\n                setIsWideOpen(false)\n            }\n        }\n    }\n\n    function onMouseMove() {\n        setIsHovering(true)\n    }\n\n    function onMouseOut() {\n        setIsHovering(false)\n    }\n\n    function resizeHandler() {\n        if (resizePromise) {\n            return\n        }\n        resizePromise = setTimeout(() => {\n            setIsWideOpen(false)\n            setIsSmall(document.body.clientWidth < 1500)\n            forceUpdate()\n            resizePromise = null\n            let el = document.getElementById('pro-sidebar')\n            if (el) {\n                el.style.left = '0px'\n            }\n        }, 500)\n    }\n\n    function onHamburgerClick() {\n        if (isSmall && !isWideOpen) {\n            let el = document.getElementById('pro-sidebar')\n            if (el) {\n                el.hidden = false\n                el.style.left = '-270px'\n                setTimeout(() => {\n                    if (el) {\n                        el.classList.add(styles.navOpen)\n                    }\n                })\n                setTimeout(() => {\n                    setIsWideOpen(true)\n                }, 500)\n            }\n        } else {\n            setIsWideOpen(!isWideOpen)\n        }\n    }\n\n    return (\n        <span>\n            <aside className={styles.navBar} id=\"navBar\" onMouseEnter={onMouseMove} onMouseLeave={onMouseOut}>\n                <Sidebar id=\"pro-sidebar\" hidden={isSmall && !isWideOpen} backgroundColor=\"#1d1d1d\" collapsed={collapsed}>\n                    <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>\n                        <div>\n                            <div className={styles.logo}>\n                                <Image src=\"/logo512.png\" alt=\"Logo\" width={40} height={40} style={{ translate: '-5px' }} /> {!isCollapsed() ? 'Coflnet' : ''}\n                            </div>\n                        </div>\n                        <hr />\n                        <Menu>\n                            <MenuItem className={styles.menuItem} component={<Link href={'/'} />} icon={<HomeIcon />}>\n                                Home\n                            </MenuItem>\n                            <MenuItem className={styles.menuItem} component={<Link href={'/flipper'} />} icon={<StorefrontIcon />}>\n                                Item Flipper\n                            </MenuItem>\n                            <MenuItem className={styles.menuItem} component={<Link href={'/account'} />} icon={<AccountIcon />}>\n                                Account\n                            </MenuItem>\n                            <MenuItem className={styles.menuItem} component={<Link href={'/subscriptions'} />} icon={<NotificationIcon />}>\n                                Notifier\n                            </MenuItem>\n                            <MenuItem className={styles.menuItem} component={<Link href={'/crafts'} />} icon={<BuildIcon />}>\n                                Profitable Crafts\n                            </MenuItem>\n                            <MenuItem className={styles.menuItem} component={<Link href={'/premium'} />} icon={<AccountBalanceIcon />}>\n                                Premium / Shop\n                            </MenuItem>\n                            <MenuItem className={styles.menuItem} component={<Link href={'/trade'} />} icon={<CurrencyExchangeIcon />}>\n                                Trading\n                            </MenuItem>\n                            <MenuItem className={styles.menuItem} component={<Link href={'/kat'} />} icon={<PetsIcon />}>\n                                Kat Flips\n                            </MenuItem>\n                            <MenuItem className={styles.menuItem} component={<Link href={'/mod'} />} icon={<DownloadIcon />}>\n                                Mod\n                            </MenuItem>\n                            <MenuItem className={styles.menuItem} component={<Link href={'/ref'} />} icon={<ShareIcon />}>\n                                Referral\n                            </MenuItem>\n                            <MenuItem className={styles.menuItem} component={<Link href={'/about'} />} icon={<PolicyIcon />}>\n                                Links / Legal\n                            </MenuItem>\n                            <MenuItem className={styles.menuItem} component={<Link href={'/feedback'} />} icon={<ChatIcon />}>\n                                Feedback\n                            </MenuItem>\n                            <MenuItem\n                                className={styles.menuItem}\n                                component={<Link href={'https://discord.gg/wvKXfTgCfb'} target=\"_blank\" />}\n                                rel=\"noreferrer\"\n                                icon={<Image src=\"/discord_icon.svg\" alt=\"Discord icon\" height={24} width={32} />}\n                            >\n                                Discord\n                            </MenuItem>\n                        </Menu>\n                    </div>\n                </Sidebar>\n            </aside>\n            {isSmall ? (\n                <span onClick={onHamburgerClick} className={styles.hamburgerIcon} id=\"hamburgerIcon\" style={props.hamburgerIconStyle}>\n                    <MenuIcon fontSize=\"large\" />\n                </span>\n            ) : (\n                ''\n            )}\n        </span>\n    )\n}\n\nexport default NavBar\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAnBA;;;;;;;;;;;;;;;;;;;;AAqBA,IAAI,gBAAuC;AAM3C,SAAS,OAAO,KAAY;;IACxB,IAAI,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,IAAI,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,IAAI,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,IAAI,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,IAAI,cAAc,CAAA,GAAA,kHAAA,CAAA,iBAAc,AAAD;IAE/B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACN,WAAW,SAAS,IAAI,CAAC,WAAW,GAAG;YAEvC,OAAO,gBAAgB,CAAC,UAAU;YAElC;oCAAO;oBACH,OAAO,mBAAmB,CAAC,UAAU;gBACzC;;QACJ;2BAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACN,IAAI,YAAY;gBACZ,SAAS,gBAAgB,CAAC,SAAS,qBAAqB;YAC5D,OAAO;gBACH,SAAS,mBAAmB,CAAC,SAAS,qBAAqB;YAC/D;YAEA;oCAAO;oBACH,SAAS,mBAAmB,CAAC,SAAS,qBAAqB;gBAC/D;;QACA,uDAAuD;QAC3D;2BAAG;QAAC;KAAW;IAEf,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACN,aAAa;QACjB;2BAAG;QAAC;QAAS;QAAY;KAAW;IAEpC,SAAS;QACL,IAAI,SAAS;YACT,OAAO;QACX;QACA,OAAO,CAAC,cAAc,CAAC;IAC3B;IAEA,SAAS,oBAAoB,GAAG;QAC5B,MAAM,WAAW,SAAS,cAAc,CAAC;QACzC,MAAM,cAAc,SAAS,cAAc,CAAC;QAC5C,IAAI,WAAW,IAAI,MAAM;QAEzB,GAAG;YACC,IAAI,aAAa,YAAY,aAAa,aAAa;gBACnD;YACJ;YACA,WAAW,AAAC,SAAiB,UAAU;QAC3C,QAAS,SAAS;QAElB,IAAI,YAAY;YACZ,IAAI,SAAS;gBACT,IAAI,KAAK,SAAS,cAAc,CAAC;gBACjC,IAAI,UAAU,IAAI,6IAAA,CAAA,UAAM,CAAC,UAAU;gBACnC,IAAI,UAAU,OAAO,6IAAA,CAAA,UAAM,CAAC,OAAO;gBACnC,WAAW;oBACP,cAAc;oBACd,IAAI,UAAU,OAAO,6IAAA,CAAA,UAAM,CAAC,UAAU;gBAC1C,GAAG;YACP,OAAO;gBACH,cAAc;YAClB;QACJ;IACJ;IAEA,SAAS;QACL,cAAc;IAClB;IAEA,SAAS;QACL,cAAc;IAClB;IAEA,SAAS;QACL,IAAI,eAAe;YACf;QACJ;QACA,gBAAgB,WAAW;YACvB,cAAc;YACd,WAAW,SAAS,IAAI,CAAC,WAAW,GAAG;YACvC;YACA,gBAAgB;YAChB,IAAI,KAAK,SAAS,cAAc,CAAC;YACjC,IAAI,IAAI;gBACJ,GAAG,KAAK,CAAC,IAAI,GAAG;YACpB;QACJ,GAAG;IACP;IAEA,SAAS;QACL,IAAI,WAAW,CAAC,YAAY;YACxB,IAAI,KAAK,SAAS,cAAc,CAAC;YACjC,IAAI,IAAI;gBACJ,GAAG,MAAM,GAAG;gBACZ,GAAG,KAAK,CAAC,IAAI,GAAG;gBAChB,WAAW;oBACP,IAAI,IAAI;wBACJ,GAAG,SAAS,CAAC,GAAG,CAAC,6IAAA,CAAA,UAAM,CAAC,OAAO;oBACnC;gBACJ;gBACA,WAAW;oBACP,cAAc;gBAClB,GAAG;YACP;QACJ,OAAO;YACH,cAAc,CAAC;QACnB;IACJ;IAEA,qBACI,6LAAC;;0BACG,6LAAC;gBAAM,WAAW,6IAAA,CAAA,UAAM,CAAC,MAAM;gBAAE,IAAG;gBAAS,cAAc;gBAAa,cAAc;0BAClF,cAAA,6LAAC,iKAAA,CAAA,UAAO;oBAAC,IAAG;oBAAc,QAAQ,WAAW,CAAC;oBAAY,iBAAgB;oBAAU,WAAW;8BAC3F,cAAA,6LAAC;wBAAI,OAAO;4BAAE,QAAQ;4BAAQ,SAAS;4BAAQ,eAAe;wBAAS;;0CACnE,6LAAC;0CACG,cAAA,6LAAC;oCAAI,WAAW,6IAAA,CAAA,UAAM,CAAC,IAAI;;sDACvB,6LAAC,gIAAA,CAAA,UAAK;4CAAC,KAAI;4CAAe,KAAI;4CAAO,OAAO;4CAAI,QAAQ;4CAAI,OAAO;gDAAE,WAAW;4CAAO;;;;;;wCAAK;wCAAE,CAAC,gBAAgB,YAAY;;;;;;;;;;;;0CAGnI,6LAAC;;;;;0CACD,6LAAC,iKAAA,CAAA,OAAI;;kDACD,6LAAC,iKAAA,CAAA,WAAQ;wCAAC,WAAW,6IAAA,CAAA,UAAM,CAAC,QAAQ;wCAAE,yBAAW,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAM;;;;;;wCAAS,oBAAM,6LAAC,4JAAA,CAAA,UAAQ;;;;;kDAAK;;;;;;kDAG1F,6LAAC,iKAAA,CAAA,WAAQ;wCAAC,WAAW,6IAAA,CAAA,UAAM,CAAC,QAAQ;wCAAE,yBAAW,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAM;;;;;;wCAAgB,oBAAM,6LAAC,kKAAA,CAAA,UAAc;;;;;kDAAK;;;;;;kDAGvG,6LAAC,iKAAA,CAAA,WAAQ;wCAAC,WAAW,6IAAA,CAAA,UAAM,CAAC,QAAQ;wCAAE,yBAAW,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAM;;;;;;wCAAgB,oBAAM,6LAAC,qKAAA,CAAA,UAAW;;;;;kDAAK;;;;;;kDAGpG,6LAAC,iKAAA,CAAA,WAAQ;wCAAC,WAAW,6IAAA,CAAA,UAAM,CAAC,QAAQ;wCAAE,yBAAW,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAM;;;;;;wCAAsB,oBAAM,6LAAC,6KAAA,CAAA,UAAgB;;;;;kDAAK;;;;;;kDAG/G,6LAAC,iKAAA,CAAA,WAAQ;wCAAC,WAAW,6IAAA,CAAA,UAAM,CAAC,QAAQ;wCAAE,yBAAW,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAM;;;;;;wCAAe,oBAAM,6LAAC,6JAAA,CAAA,UAAS;;;;;kDAAK;;;;;;kDAGjG,6LAAC,iKAAA,CAAA,WAAQ;wCAAC,WAAW,6IAAA,CAAA,UAAM,CAAC,QAAQ;wCAAE,yBAAW,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAM;;;;;;wCAAgB,oBAAM,6LAAC,sKAAA,CAAA,UAAkB;;;;;kDAAK;;;;;;kDAG3G,6LAAC,iKAAA,CAAA,WAAQ;wCAAC,WAAW,6IAAA,CAAA,UAAM,CAAC,QAAQ;wCAAE,yBAAW,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAM;;;;;;wCAAc,oBAAM,6LAAC,wKAAA,CAAA,UAAoB;;;;;kDAAK;;;;;;kDAG3G,6LAAC,iKAAA,CAAA,WAAQ;wCAAC,WAAW,6IAAA,CAAA,UAAM,CAAC,QAAQ;wCAAE,yBAAW,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAM;;;;;;wCAAY,oBAAM,6LAAC,oKAAA,CAAA,UAAQ;;;;;kDAAK;;;;;;kDAG7F,6LAAC,iKAAA,CAAA,WAAQ;wCAAC,WAAW,6IAAA,CAAA,UAAM,CAAC,QAAQ;wCAAE,yBAAW,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAM;;;;;;wCAAY,oBAAM,6LAAC,gKAAA,CAAA,UAAY;;;;;kDAAK;;;;;;kDAGjG,6LAAC,iKAAA,CAAA,WAAQ;wCAAC,WAAW,6IAAA,CAAA,UAAM,CAAC,QAAQ;wCAAE,yBAAW,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAM;;;;;;wCAAY,oBAAM,6LAAC,qKAAA,CAAA,UAAS;;;;;kDAAK;;;;;;kDAG9F,6LAAC,iKAAA,CAAA,WAAQ;wCAAC,WAAW,6IAAA,CAAA,UAAM,CAAC,QAAQ;wCAAE,yBAAW,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAM;;;;;;wCAAc,oBAAM,6LAAC,8JAAA,CAAA,UAAU;;;;;kDAAK;;;;;;kDAGjG,6LAAC,iKAAA,CAAA,WAAQ;wCAAC,WAAW,6IAAA,CAAA,UAAM,CAAC,QAAQ;wCAAE,yBAAW,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAM;;;;;;wCAAiB,oBAAM,6LAAC,4JAAA,CAAA,UAAQ;;;;;kDAAK;;;;;;kDAGlG,6LAAC,iKAAA,CAAA,WAAQ;wCACL,WAAW,6IAAA,CAAA,UAAM,CAAC,QAAQ;wCAC1B,yBAAW,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAM;4CAAiC,QAAO;;;;;;wCAC/D,KAAI;wCACJ,oBAAM,6LAAC,gIAAA,CAAA,UAAK;4CAAC,KAAI;4CAAoB,KAAI;4CAAe,QAAQ;4CAAI,OAAO;;;;;;kDAC9E;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAOhB,wBACG,6LAAC;gBAAK,SAAS;gBAAkB,WAAW,6IAAA,CAAA,UAAM,CAAC,aAAa;gBAAE,IAAG;gBAAgB,OAAO,MAAM,kBAAkB;0BAChH,cAAA,6LAAC,4JAAA,CAAA,UAAQ;oBAAC,UAAS;;;;;;;;;;uBAGvB;;;;;;;AAIhB;GArLS;;QAKa,kHAAA,CAAA,iBAAc;;;KAL3B;uCAuLM", "debugId": null}}, {"offset": {"line": 1230, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/Number/Number.tsx"], "sourcesContent": ["'use client'\nimport React, { useEffect, useState } from 'react'\nimport { numberWithThousandsSeparators } from '../../utils/Formatter'\n\ninterface Props {\n    number: number | string\n}\n\nexport default function NumberElement(props: Props) {\n    let [isSSR, setIsSSR] = useState(true)\n\n    let value = Number(props.number)\n\n    useEffect(() => {\n        setIsSSR(false)\n    }, [])\n\n    // Use consistent formatting to prevent hydration mismatches\n    // Always use comma as thousand separator and period as decimal separator\n    return <span suppressHydrationWarning>{numberWithThousandsSeparators(value, ',', '.')}</span>\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;;;AAFA;;;AAQe,SAAS,cAAc,KAAY;;IAC9C,IAAI,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjC,IAAI,QAAQ,OAAO,MAAM,MAAM;IAE/B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACN,SAAS;QACb;kCAAG,EAAE;IAEL,4DAA4D;IAC5D,yEAAyE;IACzE,qBAAO,6LAAC;QAAK,wBAAwB;kBAAE,CAAA,GAAA,sHAAA,CAAA,gCAA6B,AAAD,EAAE,OAAO,KAAK;;;;;;AACrF;GAZwB;KAAA", "debugId": null}}, {"offset": {"line": 1273, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/components/Premium/PremiumStatus/PremiumStatus.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"premiumStatusLabel\": \"PremiumStatus-module__89CruW__premiumStatusLabel\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA"}}, {"offset": {"line": 1282, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/Premium/CancelSubscriptionConfirmDialog/CancelSubscriptionConfirmDialog.tsx"], "sourcesContent": ["import React from 'react'\nimport { <PERSON><PERSON>, But<PERSON> } from 'react-bootstrap'\n\ninterface CancelSubscriptionConfirmDialogProps {\n    show: boolean\n    onHide: () => void\n    onConfirm: () => void\n}\n\nconst CancelSubscriptionConfirmDialog = ({ show, onHide, onConfirm }: CancelSubscriptionConfirmDialogProps) => {\n    return (\n        <Modal show={show} onHide={onHide}>\n            <Modal.Header>\n                <Modal.Title>Confirmation</Modal.Title>\n            </Modal.Header>\n            <Modal.Body>\n                <div>\n                    <p>Are you sure you want to cancel your subscription?</p>\n                    <div style={{ display: 'flex', gap: 5, justifyContent: 'space-between' }}>\n                        <Button variant=\"danger\" onClick={onHide}>\n                            Cancel\n                        </Button>\n                        <Button variant=\"success\" onClick={onConfirm}>\n                            Confirm\n                        </Button>\n                    </div>\n                </div>\n            </Modal.Body>\n        </Modal>\n    )\n}\n\nexport default CancelSubscriptionConfirmDialog\n"], "names": [], "mappings": ";;;;AACA;AAAA;;;AAQA,MAAM,kCAAkC,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,SAAS,EAAwC;IACtG,qBACI,6LAAC,yLAAA,CAAA,QAAK;QAAC,MAAM;QAAM,QAAQ;;0BACvB,6LAAC,yLAAA,CAAA,QAAK,CAAC,MAAM;0BACT,cAAA,6LAAC,yLAAA,CAAA,QAAK,CAAC,KAAK;8BAAC;;;;;;;;;;;0BAEjB,6LAAC,yLAAA,CAAA,QAAK,CAAC,IAAI;0BACP,cAAA,6LAAC;;sCACG,6LAAC;sCAAE;;;;;;sCACH,6LAAC;4BAAI,OAAO;gCAAE,SAAS;gCAAQ,KAAK;gCAAG,gBAAgB;4BAAgB;;8CACnE,6LAAC,2LAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAS,SAAS;8CAAQ;;;;;;8CAG1C,6LAAC,2LAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,SAAS;8CAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtE;KArBM;uCAuBS", "debugId": null}}, {"offset": {"line": 1380, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/Premium/PremiumStatus/PremiumStatus.tsx"], "sourcesContent": ["'use client'\nimport moment from 'moment'\nimport React, { useEffect, useState } from 'react'\nimport { getLocalDateAndTime } from '../../../utils/Formatter'\nimport { getHighestPriorityPremiumProduct, getPremiumLabelForSubscription, getPremiumType } from '../../../utils/PremiumTypeUtils'\nimport Tooltip from '../../Tooltip/Tooltip'\nimport styles from './PremiumStatus.module.css'\nimport { CancelOutlined } from '@mui/icons-material'\nimport CancelSubscriptionConfirmDialog from '../CancelSubscriptionConfirmDialog/CancelSubscriptionConfirmDialog'\n\ninterface Props {\n    products: PremiumProduct[]\n    subscriptions: PremiumSubscription[]\n    labelStyle?: React.CSSProperties\n    onSubscriptionCancel(subscription: PremiumSubscription): void\n    hasLoadingError?: boolean\n}\n\nfunction PremiumStatus(props: Props) {\n    let [highestPriorityProduct, setHighestPriorityProduct] = useState<PremiumProduct>()\n    let [productsToShow, setProductsToShow] = useState<PremiumProductWithtimeDifference[]>()\n    let [showCancelSubscriptionDialogSubscription, setShowCancelSubscriptionDialogSubscription] = useState<PremiumSubscription>()\n\n    useEffect(() => {\n        let products = props.products.map(product => {\n            return {\n                ...product,\n                timeDifference: 0\n            };\n        }).sort((a, b) => getPremiumType(b)?.priority - getPremiumType(a)?.priority);\n\n        // Hide lower tier products that are most likely bought automatically together (<1min time difference)\n        if (products.length > 1) {\n            for (let i = 1; i < products.length; i++) {\n                const diff = Math.abs(products[i - 1].expires.getTime() - products[i].expires.getTime());\n                if (diff < 60000) {\n                    if (getPremiumType(products[i - 1])?.priority > getPremiumType(products[i])?.priority) {\n                        products.splice(i, 1)\n                    } else {\n                        products.splice(i - 1, 1)\n                    }\n                    i = 0\n                } else\n                    products[i].timeDifference = diff\n            }\n        }\n\n        products = products.filter(product => product.expires > new Date())\n        setProductsToShow(products)\n        setHighestPriorityProduct(getHighestPriorityPremiumProduct(props.products))\n    }, [props.products])\n\n    function getProductListEntry(product: PremiumProductWithtimeDifference) {\n        return (\n            <>\n                <span>{getPremiumType(product)?.label}</span>\n                <Tooltip\n                    type=\"hover\"\n                    content={<span> (ends {moment(product.expires).fromNow()}{\n                        product.timeDifference > 0 ? (<>, <span className={styles.timeDifference}>{moment.duration(product.timeDifference).humanize()}</span> after</>) : null})</span>}\n                    tooltipContent={<span>At {getLocalDateAndTime(product.expires)}</span>}\n                />\n            </>\n        )\n    }\n\n    let numberOfEntriesToShow = (productsToShow?.length || 0) + (props.subscriptions?.length || 0)\n\n    return (\n        <>\n            <div>\n                {numberOfEntriesToShow > 1 ? (\n                    <div style={{ overflow: 'hidden' }}>\n                        <span className={styles.premiumStatusLabel} style={props.labelStyle}>\n                            Premium Status:\n                        </span>\n                        {props.hasLoadingError === true ? 'Premium Status could not be loaded' :\n                            <ul style={{ float: 'left' }}>\n                                {props.subscriptions.map(subscription => (\n                                    <li key={subscription.externalId}>\n                                        {' '}\n                                        <Tooltip\n                                            type=\"hover\"\n                                            content={\n                                                <span>\n                                                    {getPremiumLabelForSubscription(subscription)} (Subscription){' '}\n                                                    {subscription.endsAt && <span style={{ color: 'red', marginLeft: 5 }}>Canceled</span>}\n                                                </span>\n                                            }\n                                            tooltipContent={\n                                                <span>\n                                                    {subscription.endsAt ? (\n                                                        <span>Ends at {getLocalDateAndTime(subscription.endsAt)} </span>\n                                                    ) : (\n                                                        <span>Renews at {getLocalDateAndTime(subscription.renewsAt)}</span>\n                                                    )}\n                                                </span>\n                                            }\n                                        />\n                                        {!subscription.endsAt && (\n                                            <Tooltip\n                                                type=\"hover\"\n                                                content={\n                                                    <span style={{ color: 'red' }}>\n                                                        <CancelOutlined\n                                                            style={{ cursor: 'pointer', color: 'red', marginLeft: 5 }}\n                                                            onClick={() => {\n                                                                setShowCancelSubscriptionDialogSubscription(subscription)\n                                                            }}\n                                                        />\n                                                    </span>\n                                                }\n                                                tooltipContent={<span>Cancel subscription</span>}\n                                            />\n                                        )}\n                                    </li>\n                                ))}\n                                {productsToShow?.map(product => (\n                                    <li key={product.productSlug}>{getProductListEntry(product)}</li>\n                                ))}\n                            </ul>\n                        }\n                    </div>\n                ) : (\n                    <p>\n                        {' '}\n                        <span className={styles.premiumStatusLabel} style={props.labelStyle}>\n                            Premium Status:\n                        </span>\n                        {props.hasLoadingError === true ? 'Premium Status could not be loaded' :\n                            <>\n                                {highestPriorityProduct ? getProductListEntry({ ...highestPriorityProduct } as PremiumProductWithtimeDifference) : 'No Premium'}\n                            </>\n                        }\n                    </p>\n                )}\n            </div>\n            <CancelSubscriptionConfirmDialog\n                show={!!showCancelSubscriptionDialogSubscription}\n                onConfirm={() => {\n                    if (showCancelSubscriptionDialogSubscription) {\n                        props.onSubscriptionCancel(showCancelSubscriptionDialogSubscription)\n                        setShowCancelSubscriptionDialogSubscription(undefined)\n                    }\n                }}\n                onHide={() => {\n                    setShowCancelSubscriptionDialogSubscription(undefined)\n                }}\n            />\n        </>\n    )\n}\n\nexport default PremiumStatus\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AARA;;;;;;;;;AAkBA,SAAS,cAAc,KAAY;;IAC/B,IAAI,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;IACjE,IAAI,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;IACjD,IAAI,CAAC,0CAA0C,4CAA4C,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;IAErG,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACN,IAAI,WAAW,MAAM,QAAQ,CAAC,GAAG;oDAAC,CAAA;oBAC9B,OAAO;wBACH,GAAG,OAAO;wBACV,gBAAgB;oBACpB;gBACJ;mDAAG,IAAI;oDAAC,CAAC,GAAG,IAAM,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,IAAI,WAAW,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,IAAI;;YAEnE,sGAAsG;YACtG,IAAI,SAAS,MAAM,GAAG,GAAG;gBACrB,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,IAAK;oBACtC,MAAM,OAAO,KAAK,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,OAAO,CAAC,OAAO,KAAK,QAAQ,CAAC,EAAE,CAAC,OAAO,CAAC,OAAO;oBACrF,IAAI,OAAO,OAAO;wBACd,IAAI,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,CAAC,IAAI,EAAE,GAAG,WAAW,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,CAAC,EAAE,GAAG,UAAU;4BACnF,SAAS,MAAM,CAAC,GAAG;wBACvB,OAAO;4BACH,SAAS,MAAM,CAAC,IAAI,GAAG;wBAC3B;wBACA,IAAI;oBACR,OACI,QAAQ,CAAC,EAAE,CAAC,cAAc,GAAG;gBACrC;YACJ;YAEA,WAAW,SAAS,MAAM;2CAAC,CAAA,UAAW,QAAQ,OAAO,GAAG,IAAI;;YAC5D,kBAAkB;YAClB,0BAA0B,CAAA,GAAA,6HAAA,CAAA,mCAAgC,AAAD,EAAE,MAAM,QAAQ;QAC7E;kCAAG;QAAC,MAAM,QAAQ;KAAC;IAEnB,SAAS,oBAAoB,OAAyC;QAClE,qBACI;;8BACI,6LAAC;8BAAM,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,UAAU;;;;;;8BAChC,6LAAC,oIAAA,CAAA,UAAO;oBACJ,MAAK;oBACL,uBAAS,6LAAC;;4BAAK;4BAAQ,CAAA,GAAA,mIAAA,CAAA,UAAM,AAAD,EAAE,QAAQ,OAAO,EAAE,OAAO;4BAClD,QAAQ,cAAc,GAAG,kBAAK;;oCAAE;kDAAE,6LAAC;wCAAK,WAAW,sKAAA,CAAA,UAAM,CAAC,cAAc;kDAAG,mIAAA,CAAA,UAAM,CAAC,QAAQ,CAAC,QAAQ,cAAc,EAAE,QAAQ;;;;;;oCAAU;;+CAAa;4BAAK;;;;;;;oBAC3J,8BAAgB,6LAAC;;4BAAK;4BAAI,CAAA,GAAA,sHAAA,CAAA,sBAAmB,AAAD,EAAE,QAAQ,OAAO;;;;;;;;;;;;;;IAI7E;IAEA,IAAI,wBAAwB,CAAC,gBAAgB,UAAU,CAAC,IAAI,CAAC,MAAM,aAAa,EAAE,UAAU,CAAC;IAE7F,qBACI;;0BACI,6LAAC;0BACI,wBAAwB,kBACrB,6LAAC;oBAAI,OAAO;wBAAE,UAAU;oBAAS;;sCAC7B,6LAAC;4BAAK,WAAW,sKAAA,CAAA,UAAM,CAAC,kBAAkB;4BAAE,OAAO,MAAM,UAAU;sCAAE;;;;;;wBAGpE,MAAM,eAAe,KAAK,OAAO,qDAC9B,6LAAC;4BAAG,OAAO;gCAAE,OAAO;4BAAO;;gCACtB,MAAM,aAAa,CAAC,GAAG,CAAC,CAAA,6BACrB,6LAAC;;4CACI;0DACD,6LAAC,oIAAA,CAAA,UAAO;gDACJ,MAAK;gDACL,uBACI,6LAAC;;wDACI,CAAA,GAAA,6HAAA,CAAA,iCAA8B,AAAD,EAAE;wDAAc;wDAAgB;wDAC7D,aAAa,MAAM,kBAAI,6LAAC;4DAAK,OAAO;gEAAE,OAAO;gEAAO,YAAY;4DAAE;sEAAG;;;;;;;;;;;;gDAG9E,8BACI,6LAAC;8DACI,aAAa,MAAM,iBAChB,6LAAC;;4DAAK;4DAAS,CAAA,GAAA,sHAAA,CAAA,sBAAmB,AAAD,EAAE,aAAa,MAAM;4DAAE;;;;;;+EAExD,6LAAC;;4DAAK;4DAAW,CAAA,GAAA,sHAAA,CAAA,sBAAmB,AAAD,EAAE,aAAa,QAAQ;;;;;;;;;;;;;;;;;4CAKzE,CAAC,aAAa,MAAM,kBACjB,6LAAC,oIAAA,CAAA,UAAO;gDACJ,MAAK;gDACL,uBACI,6LAAC;oDAAK,OAAO;wDAAE,OAAO;oDAAM;8DACxB,cAAA,6LAAC,sKAAA,CAAA,UAAc;wDACX,OAAO;4DAAE,QAAQ;4DAAW,OAAO;4DAAO,YAAY;wDAAE;wDACxD,SAAS;4DACL,4CAA4C;wDAChD;;;;;;;;;;;gDAIZ,8BAAgB,6LAAC;8DAAK;;;;;;;;;;;;uCAjCzB,aAAa,UAAU;;;;;gCAsCnC,gBAAgB,IAAI,CAAA,wBACjB,6LAAC;kDAA8B,oBAAoB;uCAA1C,QAAQ,WAAW;;;;;;;;;;;;;;;;yCAM5C,6LAAC;;wBACI;sCACD,6LAAC;4BAAK,WAAW,sKAAA,CAAA,UAAM,CAAC,kBAAkB;4BAAE,OAAO,MAAM,UAAU;sCAAE;;;;;;wBAGpE,MAAM,eAAe,KAAK,OAAO,qDAC9B;sCACK,yBAAyB,oBAAoB;gCAAE,GAAG,sBAAsB;4BAAC,KAAyC;;;;;;;;;;;;;0BAMvI,6LAAC,+LAAA,CAAA,UAA+B;gBAC5B,MAAM,CAAC,CAAC;gBACR,WAAW;oBACP,IAAI,0CAA0C;wBAC1C,MAAM,oBAAoB,CAAC;wBAC3B,4CAA4C;oBAChD;gBACJ;gBACA,QAAQ;oBACJ,4CAA4C;gBAChD;;;;;;;;AAIhB;GArIS;KAAA;uCAuIM", "debugId": null}}, {"offset": {"line": 1701, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/FilterElement/FilterElements/PlayerFilterElement.tsx"], "sourcesContent": ["'use client'\nimport { forwardRef, Ref, useState } from 'react'\nimport { AsyncTypeahead } from 'react-bootstrap-typeahead'\nimport api from '../../../api/ApiHelper'\nimport { v4 as generateUUID } from 'uuid'\nimport Typeahead from 'react-bootstrap-typeahead/types/core/Typeahead'\ninterface Props {\n    onChange(n: string | Player)\n    disabled?: boolean\n    returnType: 'name' | 'uuid' | 'player'\n    defaultValue: string\n    ref?(ref)\n    placeholder?: string\n    isValid?: boolean\n}\n\nexport let PlayerFilterElement = forwardRef((props: Props, ref: Ref<Typeahead>) => {\n    // for player search\n    let [players, setPlayers] = useState<Player[]>([])\n    let [isLoading, setIsLoading] = useState(false)\n\n    function _onChange(selected) {\n        props.onChange(selected[0] || '')\n    }\n\n    function handlePlayerSearch(query) {\n        setIsLoading(true)\n\n        api.playerSearch(query).then(players => {\n            setPlayers(players)\n            setIsLoading(false)\n        })\n    }\n\n    return (\n        <AsyncTypeahead\n            id={generateUUID()}\n            disabled={props.disabled}\n            filterBy={() => true}\n            isLoading={isLoading}\n            labelKey=\"name\"\n            minLength={1}\n            isInvalid={!props.isValid}\n            onSearch={handlePlayerSearch}\n            defaultInputValue={props.defaultValue}\n            options={players}\n            placeholder={props.placeholder || 'Search users...'}\n            onChange={selected =>\n                _onChange(\n                    selected.map(s => {\n                        if (props.returnType === 'player') {\n                            return s\n                        }\n                        return s[props.returnType]\n                    })\n                )\n            }\n            ref={ref}\n        />\n    )\n})\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AACA;AACA;;;AAJA;;;;;AAgBO,IAAI,oCAAsB,GAAA,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,UAAE,CAAC,OAAc;;IACvD,oBAAoB;IACpB,IAAI,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACjD,IAAI,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,SAAS,UAAU,QAAQ;QACvB,MAAM,QAAQ,CAAC,QAAQ,CAAC,EAAE,IAAI;IAClC;IAEA,SAAS,mBAAmB,KAAK;QAC7B,aAAa;QAEb,oHAAA,CAAA,UAAG,CAAC,YAAY,CAAC,OAAO,IAAI,CAAC,CAAA;YACzB,WAAW;YACX,aAAa;QACjB;IACJ;IAEA,qBACI,6LAAC,uPAAA,CAAA,iBAAc;QACX,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAY,AAAD;QACf,UAAU,MAAM,QAAQ;QACxB,UAAU,IAAM;QAChB,WAAW;QACX,UAAS;QACT,WAAW;QACX,WAAW,CAAC,MAAM,OAAO;QACzB,UAAU;QACV,mBAAmB,MAAM,YAAY;QACrC,SAAS;QACT,aAAa,MAAM,WAAW,IAAI;QAClC,UAAU,CAAA,WACN,UACI,SAAS,GAAG,CAAC,CAAA;gBACT,IAAI,MAAM,UAAU,KAAK,UAAU;oBAC/B,OAAO;gBACX;gBACA,OAAO,CAAC,CAAC,MAAM,UAAU,CAAC;YAC9B;QAGR,KAAK;;;;;;AAGjB", "debugId": null}}, {"offset": {"line": 1769, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/components/TransferCoflCoins/TransferCoflCoinsSummary.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"label\": \"TransferCoflCoinsSummary-module__ZfhhcG__label\",\n  \"returnButton\": \"TransferCoflCoinsSummary-module__ZfhhcG__returnButton\",\n  \"sendButton\": \"TransferCoflCoinsSummary-module__ZfhhcG__sendButton\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 1780, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/TransferCoflCoins/TransferCoflCoinsSummary.tsx"], "sourcesContent": ["'use client'\nimport Image from 'next/image'\nimport { useState } from 'react'\nimport { Button } from 'react-bootstrap'\nimport { toast } from 'react-toastify'\nimport { v4 as generateUUID } from 'uuid'\nimport api from '../../api/ApiHelper'\nimport { getLoadingElement } from '../../utils/LoadingUtils'\nimport Number from '../Number/Number'\nimport styles from './TransferCoflCoinsSummary.module.css'\n\ninterface Props {\n    receiverType: 'email' | 'mcId'\n    email: string | undefined\n    player: Player | undefined\n    coflCoins: number\n    onBack()\n    onFinish()\n}\n\nfunction TransferCoflCoinsSummary(props: Props) {\n    let [reference] = useState(generateUUID())\n    let [isSending, setIsSending] = useState(false)\n\n    function onSend() {\n        setIsSending(true)\n        api.transferCoflCoins(props.email, props.player?.uuid, props.coflCoins, reference)\n            .then(() => {\n                toast.success(\n                    <span>\n                        Successfuly sent <Number number={props.coflCoins} /> CoflCoins to {props.email === '' ? props.player?.name : props.email}\n                    </span>\n                )\n                setIsSending(false)\n                props.onFinish()\n            })\n            .catch(() => {\n                setIsSending(false)\n                props.onFinish()\n            })\n    }\n\n    return (\n        <>\n            {!isSending ? (\n                <div>\n                    <p>\n                        <span className={styles.label}>Receiver:</span>\n                        {props.receiverType === 'email' ? (\n                            <span>{props.email}</span>\n                        ) : (\n                            <span>\n                                <Image\n                                    crossOrigin=\"anonymous\"\n                                    className=\"playerHeadIcon\"\n                                    src={props.player?.iconUrl || ''}\n                                    height=\"32\"\n                                    width=\"32\"\n                                    alt=\"\"\n                                    style={{ marginRight: '10px' }}\n                                    loading=\"lazy\"\n                                />\n                                {props.player!.name}\n                            </span>\n                        )}\n                    </p>\n                    <p>\n                        <span className={styles.label}>Amount: </span>\n                        {props.coflCoins} CoflCoins\n                    </p>\n\n                    <hr />\n                    <p>\n                        <span style={{ color: 'red' }}>Warning: </span>\n                        <br />\n                        Please make sure this is really the person you want to send CoflCoins to. You may not be able to get them back!\n                    </p>\n\n                    <Button className={styles.returnButton} onClick={props.onBack}>\n                        Back\n                    </Button>\n                    <Button variant=\"success\" className={styles.sendButton} onClick={onSend}>\n                        Send\n                    </Button>\n                </div>\n            ) : (\n                getLoadingElement(<p>Sending CoflCoins</p>)\n            )}\n        </>\n    )\n}\n\nexport default TransferCoflCoinsSummary\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AATA;;;;;;;;;;AAoBA,SAAS,yBAAyB,KAAY;;IAC1C,IAAI,CAAC,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,GAAA,wLAAA,CAAA,KAAY,AAAD;IACtC,IAAI,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,SAAS;QACL,aAAa;QACb,oHAAA,CAAA,UAAG,CAAC,iBAAiB,CAAC,MAAM,KAAK,EAAE,MAAM,MAAM,EAAE,MAAM,MAAM,SAAS,EAAE,WACnE,IAAI,CAAC;YACF,sJAAA,CAAA,QAAK,CAAC,OAAO,eACT,6LAAC;;oBAAK;kCACe,6LAAC,kIAAA,CAAA,UAAM;wBAAC,QAAQ,MAAM,SAAS;;;;;;oBAAI;oBAAe,MAAM,KAAK,KAAK,KAAK,MAAM,MAAM,EAAE,OAAO,MAAM,KAAK;;;;;;;YAGhI,aAAa;YACb,MAAM,QAAQ;QAClB,GACC,KAAK,CAAC;YACH,aAAa;YACb,MAAM,QAAQ;QAClB;IACR;IAEA,qBACI;kBACK,CAAC,0BACE,6LAAC;;8BACG,6LAAC;;sCACG,6LAAC;4BAAK,WAAW,0KAAA,CAAA,UAAM,CAAC,KAAK;sCAAE;;;;;;wBAC9B,MAAM,YAAY,KAAK,wBACpB,6LAAC;sCAAM,MAAM,KAAK;;;;;iDAElB,6LAAC;;8CACG,6LAAC,gIAAA,CAAA,UAAK;oCACF,aAAY;oCACZ,WAAU;oCACV,KAAK,MAAM,MAAM,EAAE,WAAW;oCAC9B,QAAO;oCACP,OAAM;oCACN,KAAI;oCACJ,OAAO;wCAAE,aAAa;oCAAO;oCAC7B,SAAQ;;;;;;gCAEX,MAAM,MAAM,CAAE,IAAI;;;;;;;;;;;;;8BAI/B,6LAAC;;sCACG,6LAAC;4BAAK,WAAW,0KAAA,CAAA,UAAM,CAAC,KAAK;sCAAE;;;;;;wBAC9B,MAAM,SAAS;wBAAC;;;;;;;8BAGrB,6LAAC;;;;;8BACD,6LAAC;;sCACG,6LAAC;4BAAK,OAAO;gCAAE,OAAO;4BAAM;sCAAG;;;;;;sCAC/B,6LAAC;;;;;wBAAK;;;;;;;8BAIV,6LAAC,2LAAA,CAAA,SAAM;oBAAC,WAAW,0KAAA,CAAA,UAAM,CAAC,YAAY;oBAAE,SAAS,MAAM,MAAM;8BAAE;;;;;;8BAG/D,6LAAC,2LAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAU,WAAW,0KAAA,CAAA,UAAM,CAAC,UAAU;oBAAE,SAAS;8BAAQ;;;;;;;;;;;mBAK7E,CAAA,GAAA,yHAAA,CAAA,oBAAiB,AAAD,gBAAE,6LAAC;sBAAE;;;;;;;AAIrC;GAtES;KAAA;uCAwEM", "debugId": null}}, {"offset": {"line": 1981, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/TransferCoflCoins/TransferCoflCoins.tsx"], "sourcesContent": ["'use client'\nimport { useState } from 'react'\nimport { Button, Form } from 'react-bootstrap'\nimport { NumericFormat } from 'react-number-format'\nimport { useCoflCoins } from '../../utils/Hooks'\nimport { PlayerFilterElement } from '../FilterElement/FilterElements/PlayerFilterElement'\nimport Number from '../Number/Number'\nimport TransferCoflCoinsSummary from './TransferCoflCoinsSummary'\n\ninterface Props {\n    onFinish()\n}\n\nfunction TransferCoflCoins(props: Props) {\n    let [minecraftPlayer, setMinecraftPlayer] = useState<Player>()\n    let [email, setEmail] = useState('')\n    let [coflCoinsToSend, setCoflCoinsToSend] = useState(0)\n    let coflCoinsBalance = useCoflCoins()\n    let [showSummary, setShowSummary] = useState(false)\n\n    function onContinue() {\n        setShowSummary(true)\n    }\n\n    return (\n        <>\n            <div style={{ display: showSummary ? 'none' : 'initial' }}>\n                <p>There are 2 ways to send Cofl<PERSON>oi<PERSON> to another person:</p>\n                <ul>\n                    <li>\n                        <b>By Email:</b> Enter the email of the Google account of the receiver\n                    </li>\n                    <li>\n                        <b>By Minecraft name:</b> Search the players Minecraft name (only works if they linked their Minecraft account on the website)\n                    </li>\n                </ul>\n                <hr />\n                <div style={{ padding: '0 50px 0 50px' }}>\n                    <div>\n                        {minecraftPlayer === undefined ? (\n                            <div style={{ marginBottom: '20px' }}>\n                                By Email\n                                <Form.Control\n                                    placeholder=\"Enter Email...\"\n                                    onChange={e => {\n                                        setEmail(e.target.value)\n                                    }}\n                                />\n                            </div>\n                        ) : null}\n                        {email === '' ? (\n                            <div style={{ marginBottom: '20px' }}>\n                                By Minecraft name\n                                <PlayerFilterElement\n                                    defaultValue=\"\"\n                                    onChange={p => {\n                                        setMinecraftPlayer(p as Player)\n                                    }}\n                                    returnType={'player'}\n                                    placeholder=\"Enter Minecraft name...\"\n                                />\n                            </div>\n                        ) : null}\n                    </div>\n                    <div style={{ marginBottom: '20px' }}>\n                        Amount of CoflCoins{' '}\n                        <NumericFormat\n                            id=\"coflcoins-to-send\"\n                            onValueChange={n => {\n                                if (n.floatValue) {\n                                    setCoflCoinsToSend(n.floatValue)\n                                }\n                            }}\n                            isAllowed={value => {\n                                return value.floatValue ? value.floatValue <= coflCoinsBalance : false\n                            }}\n                            customInput={Form.Control}\n                            defaultValue={0}\n                            thousandSeparator=\".\"\n                            decimalSeparator=\",\"\n                            allowNegative={false}\n                            decimalScale={1}\n                        />\n                    </div>\n                    <span>\n                        Your current Balance: <Number number={coflCoinsBalance} />\n                    </span>\n                    <Button\n                        variant=\"success\"\n                        style={{ float: 'right' }}\n                        onClick={onContinue}\n                        disabled={coflCoinsToSend <= 0 || (email === '' && minecraftPlayer === undefined)}\n                    >\n                        Continue\n                    </Button>\n                </div>\n            </div>\n            {showSummary ? (\n                <TransferCoflCoinsSummary\n                    receiverType={minecraftPlayer !== undefined ? 'mcId' : 'email'}\n                    coflCoins={coflCoinsToSend}\n                    email={email}\n                    player={minecraftPlayer}\n                    onBack={() => {\n                        setShowSummary(false)\n                    }}\n                    onFinish={props.onFinish}\n                />\n            ) : null}\n        </>\n    )\n}\n\nexport default TransferCoflCoins\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;;AAaA,SAAS,kBAAkB,KAAY;;IACnC,IAAI,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;IACnD,IAAI,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,IAAI,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,IAAI,mBAAmB,CAAA,GAAA,kHAAA,CAAA,eAAY,AAAD;IAClC,IAAI,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,SAAS;QACL,eAAe;IACnB;IAEA,qBACI;;0BACI,6LAAC;gBAAI,OAAO;oBAAE,SAAS,cAAc,SAAS;gBAAU;;kCACpD,6LAAC;kCAAE;;;;;;kCACH,6LAAC;;0CACG,6LAAC;;kDACG,6LAAC;kDAAE;;;;;;oCAAa;;;;;;;0CAEpB,6LAAC;;kDACG,6LAAC;kDAAE;;;;;;oCAAsB;;;;;;;;;;;;;kCAGjC,6LAAC;;;;;kCACD,6LAAC;wBAAI,OAAO;4BAAE,SAAS;wBAAgB;;0CACnC,6LAAC;;oCACI,oBAAoB,0BACjB,6LAAC;wCAAI,OAAO;4CAAE,cAAc;wCAAO;;4CAAG;0DAElC,6LAAC,uLAAA,CAAA,OAAI,CAAC,OAAO;gDACT,aAAY;gDACZ,UAAU,CAAA;oDACN,SAAS,EAAE,MAAM,CAAC,KAAK;gDAC3B;;;;;;;;;;;+CAGR;oCACH,UAAU,mBACP,6LAAC;wCAAI,OAAO;4CAAE,cAAc;wCAAO;;4CAAG;0DAElC,6LAAC,wKAAA,CAAA,sBAAmB;gDAChB,cAAa;gDACb,UAAU,CAAA;oDACN,mBAAmB;gDACvB;gDACA,YAAY;gDACZ,aAAY;;;;;;;;;;;+CAGpB;;;;;;;0CAER,6LAAC;gCAAI,OAAO;oCAAE,cAAc;gCAAO;;oCAAG;oCACd;kDACpB,6LAAC,uLAAA,CAAA,gBAAa;wCACV,IAAG;wCACH,eAAe,CAAA;4CACX,IAAI,EAAE,UAAU,EAAE;gDACd,mBAAmB,EAAE,UAAU;4CACnC;wCACJ;wCACA,WAAW,CAAA;4CACP,OAAO,MAAM,UAAU,GAAG,MAAM,UAAU,IAAI,mBAAmB;wCACrE;wCACA,aAAa,uLAAA,CAAA,OAAI,CAAC,OAAO;wCACzB,cAAc;wCACd,mBAAkB;wCAClB,kBAAiB;wCACjB,eAAe;wCACf,cAAc;;;;;;;;;;;;0CAGtB,6LAAC;;oCAAK;kDACoB,6LAAC,kIAAA,CAAA,UAAM;wCAAC,QAAQ;;;;;;;;;;;;0CAE1C,6LAAC,2LAAA,CAAA,SAAM;gCACH,SAAQ;gCACR,OAAO;oCAAE,OAAO;gCAAQ;gCACxB,SAAS;gCACT,UAAU,mBAAmB,KAAM,UAAU,MAAM,oBAAoB;0CAC1E;;;;;;;;;;;;;;;;;;YAKR,4BACG,6LAAC,+JAAA,CAAA,UAAwB;gBACrB,cAAc,oBAAoB,YAAY,SAAS;gBACvD,WAAW;gBACX,OAAO;gBACP,QAAQ;gBACR,QAAQ;oBACJ,eAAe;gBACnB;gBACA,UAAU,MAAM,QAAQ;;;;;uBAE5B;;;AAGhB;GAlGS;;QAIkB,kHAAA,CAAA,eAAY;;;KAJ9B;uCAoGM", "debugId": null}}, {"offset": {"line": 2240, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/components/AccountDetails/AccountDetails.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"label\": \"AccountDetails-module__I1X07G__label\",\n  \"link\": \"AccountDetails-module__I1X07G__link\",\n  \"sendCoflCoinsButton\": \"AccountDetails-module__I1X07G__sendCoflCoinsButton\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 2250, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/components/AccountDetails/PrivacySettings/PrivacySettings.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"label\": \"PrivacySettings-module__Y-Melq__label\",\n  \"settingsLine\": \"PrivacySettings-module__Y-Melq__settingsLine\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA"}}, {"offset": {"line": 2260, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/AccountDetails/PrivacySettings/PrivacySettings.tsx"], "sourcesContent": ["'use client'\nimport { useEffect, useState } from 'react'\nimport { Form } from 'react-bootstrap'\nimport api from '../../../api/ApiHelper'\nimport { getLoadingElement } from '../../../utils/LoadingUtils'\nimport styles from './PrivacySettings.module.css'\n\nfunction PrivacySettings() {\n    let [privacySettings, setPrivacySettings] = useState<PrivacySettings | null>(null)\n\n    useEffect(() => {\n        loadPrivacySettings()\n    }, [])\n\n    function loadPrivacySettings() {\n        api.getPrivacySettings().then(settings => {\n            setPrivacySettings(settings)\n        })\n    }\n\n    function onSettingChange(key: string, value: any) {\n        if (!privacySettings) {\n            return\n        }\n        privacySettings[key] = value\n        setPrivacySettings(privacySettings)\n\n        api.setPrivacySettings(privacySettings)\n    }\n\n    if (!privacySettings) {\n        return getLoadingElement(<p>Loading settings</p>)\n    }\n\n    return (\n        <>\n            <p className={styles.settingsLine}>\n                <span className={styles.label}>Allow Proxy: </span>\n                <Form.Check\n                    onChange={e => {\n                        onSettingChange('allowProxy', e.target.checked)\n                    }}\n                    defaultChecked={privacySettings.allowProxy}\n                    type=\"checkbox\"\n                />\n            </p>\n            <p className={styles.settingsLine}>\n                <span className={styles.label}>Autostart: </span>\n                <Form.Check\n                    onChange={e => {\n                        onSettingChange('autoStart', e.target.checked)\n                    }}\n                    defaultChecked={privacySettings.autoStart}\n                    type=\"checkbox\"\n                />\n            </p>\n            <p className={styles.settingsLine}>\n                <span className={styles.label}>Chat: </span>\n                <Form.Check\n                    onChange={e => {\n                        onSettingChange('collectChat', e.target.checked)\n                    }}\n                    defaultChecked={privacySettings.collectChat}\n                    type=\"checkbox\"\n                />\n            </p>\n            <p className={styles.settingsLine}>\n                <span className={styles.label}>Chat clicks: </span>\n                <Form.Check\n                    onChange={e => {\n                        onSettingChange('collectChatClicks', e.target.checked)\n                    }}\n                    defaultChecked={privacySettings.collectChatClicks}\n                    type=\"checkbox\"\n                />\n            </p>\n            <p className={styles.settingsLine}>\n                <span className={styles.label}>Entities: </span>\n                <Form.Check\n                    onChange={e => {\n                        onSettingChange('collectEntities', e.target.checked)\n                    }}\n                    defaultChecked={privacySettings.collectEntities}\n                    type=\"checkbox\"\n                />\n            </p>\n            <p className={styles.settingsLine}>\n                <span className={styles.label}>Inventory clicks: </span>\n                <Form.Check\n                    onChange={e => {\n                        onSettingChange('collectInvClick', e.target.checked)\n                    }}\n                    defaultChecked={privacySettings.collectInvClick}\n                    type=\"checkbox\"\n                />\n            </p>\n            <p className={styles.settingsLine}>\n                <span className={styles.label}>Inventory: </span>\n                <Form.Check\n                    onChange={e => {\n                        onSettingChange('collectInventory', e.target.checked)\n                    }}\n                    defaultChecked={privacySettings.collectInventory}\n                    type=\"checkbox\"\n                />\n            </p>\n            <p className={styles.settingsLine}>\n                <span className={styles.label}>Lobby changes: </span>\n                <Form.Check\n                    onChange={e => {\n                        onSettingChange('collectLobbyChanges', e.target.checked)\n                    }}\n                    defaultChecked={privacySettings.collectLobbyChanges}\n                    type=\"checkbox\"\n                />\n            </p>\n            <p className={styles.settingsLine}>\n                <span className={styles.label}>Scoreboard: </span>\n                <Form.Check\n                    onChange={e => {\n                        onSettingChange('collectScoreboard', e.target.checked)\n                    }}\n                    defaultChecked={privacySettings.collectScoreboard}\n                    type=\"checkbox\"\n                />\n            </p>\n            <p className={styles.settingsLine}>\n                <span className={styles.label}>Tab: </span>\n                <Form.Check\n                    onChange={e => {\n                        onSettingChange('collectTab', e.target.checked)\n                    }}\n                    defaultChecked={privacySettings.collectTab}\n                    type=\"checkbox\"\n                />\n            </p>\n            <p className={styles.settingsLine}>\n                <span className={styles.label}>Extend description: </span>\n                <Form.Check\n                    onChange={e => {\n                        onSettingChange('extendDescriptions', e.target.checked)\n                    }}\n                    defaultChecked={privacySettings.extendDescriptions}\n                    type=\"checkbox\"\n                />\n            </p>\n        </>\n    )\n}\n\nexport default PrivacySettings\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;;;AALA;;;;;;AAOA,SAAS;;IACL,IAAI,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B;IAE7E,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACN;QACJ;oCAAG,EAAE;IAEL,SAAS;QACL,oHAAA,CAAA,UAAG,CAAC,kBAAkB,GAAG,IAAI,CAAC,CAAA;YAC1B,mBAAmB;QACvB;IACJ;IAEA,SAAS,gBAAgB,GAAW,EAAE,KAAU;QAC5C,IAAI,CAAC,iBAAiB;YAClB;QACJ;QACA,eAAe,CAAC,IAAI,GAAG;QACvB,mBAAmB;QAEnB,oHAAA,CAAA,UAAG,CAAC,kBAAkB,CAAC;IAC3B;IAEA,IAAI,CAAC,iBAAiB;QAClB,OAAO,CAAA,GAAA,yHAAA,CAAA,oBAAiB,AAAD,gBAAE,6LAAC;sBAAE;;;;;;IAChC;IAEA,qBACI;;0BACI,6LAAC;gBAAE,WAAW,iLAAA,CAAA,UAAM,CAAC,YAAY;;kCAC7B,6LAAC;wBAAK,WAAW,iLAAA,CAAA,UAAM,CAAC,KAAK;kCAAE;;;;;;kCAC/B,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;wBACP,UAAU,CAAA;4BACN,gBAAgB,cAAc,EAAE,MAAM,CAAC,OAAO;wBAClD;wBACA,gBAAgB,gBAAgB,UAAU;wBAC1C,MAAK;;;;;;;;;;;;0BAGb,6LAAC;gBAAE,WAAW,iLAAA,CAAA,UAAM,CAAC,YAAY;;kCAC7B,6LAAC;wBAAK,WAAW,iLAAA,CAAA,UAAM,CAAC,KAAK;kCAAE;;;;;;kCAC/B,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;wBACP,UAAU,CAAA;4BACN,gBAAgB,aAAa,EAAE,MAAM,CAAC,OAAO;wBACjD;wBACA,gBAAgB,gBAAgB,SAAS;wBACzC,MAAK;;;;;;;;;;;;0BAGb,6LAAC;gBAAE,WAAW,iLAAA,CAAA,UAAM,CAAC,YAAY;;kCAC7B,6LAAC;wBAAK,WAAW,iLAAA,CAAA,UAAM,CAAC,KAAK;kCAAE;;;;;;kCAC/B,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;wBACP,UAAU,CAAA;4BACN,gBAAgB,eAAe,EAAE,MAAM,CAAC,OAAO;wBACnD;wBACA,gBAAgB,gBAAgB,WAAW;wBAC3C,MAAK;;;;;;;;;;;;0BAGb,6LAAC;gBAAE,WAAW,iLAAA,CAAA,UAAM,CAAC,YAAY;;kCAC7B,6LAAC;wBAAK,WAAW,iLAAA,CAAA,UAAM,CAAC,KAAK;kCAAE;;;;;;kCAC/B,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;wBACP,UAAU,CAAA;4BACN,gBAAgB,qBAAqB,EAAE,MAAM,CAAC,OAAO;wBACzD;wBACA,gBAAgB,gBAAgB,iBAAiB;wBACjD,MAAK;;;;;;;;;;;;0BAGb,6LAAC;gBAAE,WAAW,iLAAA,CAAA,UAAM,CAAC,YAAY;;kCAC7B,6LAAC;wBAAK,WAAW,iLAAA,CAAA,UAAM,CAAC,KAAK;kCAAE;;;;;;kCAC/B,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;wBACP,UAAU,CAAA;4BACN,gBAAgB,mBAAmB,EAAE,MAAM,CAAC,OAAO;wBACvD;wBACA,gBAAgB,gBAAgB,eAAe;wBAC/C,MAAK;;;;;;;;;;;;0BAGb,6LAAC;gBAAE,WAAW,iLAAA,CAAA,UAAM,CAAC,YAAY;;kCAC7B,6LAAC;wBAAK,WAAW,iLAAA,CAAA,UAAM,CAAC,KAAK;kCAAE;;;;;;kCAC/B,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;wBACP,UAAU,CAAA;4BACN,gBAAgB,mBAAmB,EAAE,MAAM,CAAC,OAAO;wBACvD;wBACA,gBAAgB,gBAAgB,eAAe;wBAC/C,MAAK;;;;;;;;;;;;0BAGb,6LAAC;gBAAE,WAAW,iLAAA,CAAA,UAAM,CAAC,YAAY;;kCAC7B,6LAAC;wBAAK,WAAW,iLAAA,CAAA,UAAM,CAAC,KAAK;kCAAE;;;;;;kCAC/B,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;wBACP,UAAU,CAAA;4BACN,gBAAgB,oBAAoB,EAAE,MAAM,CAAC,OAAO;wBACxD;wBACA,gBAAgB,gBAAgB,gBAAgB;wBAChD,MAAK;;;;;;;;;;;;0BAGb,6LAAC;gBAAE,WAAW,iLAAA,CAAA,UAAM,CAAC,YAAY;;kCAC7B,6LAAC;wBAAK,WAAW,iLAAA,CAAA,UAAM,CAAC,KAAK;kCAAE;;;;;;kCAC/B,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;wBACP,UAAU,CAAA;4BACN,gBAAgB,uBAAuB,EAAE,MAAM,CAAC,OAAO;wBAC3D;wBACA,gBAAgB,gBAAgB,mBAAmB;wBACnD,MAAK;;;;;;;;;;;;0BAGb,6LAAC;gBAAE,WAAW,iLAAA,CAAA,UAAM,CAAC,YAAY;;kCAC7B,6LAAC;wBAAK,WAAW,iLAAA,CAAA,UAAM,CAAC,KAAK;kCAAE;;;;;;kCAC/B,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;wBACP,UAAU,CAAA;4BACN,gBAAgB,qBAAqB,EAAE,MAAM,CAAC,OAAO;wBACzD;wBACA,gBAAgB,gBAAgB,iBAAiB;wBACjD,MAAK;;;;;;;;;;;;0BAGb,6LAAC;gBAAE,WAAW,iLAAA,CAAA,UAAM,CAAC,YAAY;;kCAC7B,6LAAC;wBAAK,WAAW,iLAAA,CAAA,UAAM,CAAC,KAAK;kCAAE;;;;;;kCAC/B,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;wBACP,UAAU,CAAA;4BACN,gBAAgB,cAAc,EAAE,MAAM,CAAC,OAAO;wBAClD;wBACA,gBAAgB,gBAAgB,UAAU;wBAC1C,MAAK;;;;;;;;;;;;0BAGb,6LAAC;gBAAE,WAAW,iLAAA,CAAA,UAAM,CAAC,YAAY;;kCAC7B,6LAAC;wBAAK,WAAW,iLAAA,CAAA,UAAM,CAAC,KAAK;kCAAE;;;;;;kCAC/B,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;wBACP,UAAU,CAAA;4BACN,gBAAgB,sBAAsB,EAAE,MAAM,CAAC,OAAO;wBAC1D;wBACA,gBAAgB,gBAAgB,kBAAkB;wBAClD,MAAK;;;;;;;;;;;;;;AAKzB;GA7IS;KAAA;uCA+IM", "debugId": null}}, {"offset": {"line": 2634, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/AccountDetails/TransactionHistory/TransactionHistory.tsx"], "sourcesContent": ["'use client'\nimport { useEffect, useState } from 'react'\nimport { ListGroup, ListGroupItem } from 'react-bootstrap'\nimport api from '../../../api/ApiHelper'\nimport { numberWithThousandsSeparators } from '../../../utils/Formatter'\nimport { getLoadingElement } from '../../../utils/LoadingUtils'\nimport { PREMIUM_TYPES } from '../../../utils/PremiumTypeUtils'\n\nfunction TransactionHistory() {\n    let [transactions, setTransactions] = useState<Transaction[]>([])\n    let [isLoading, setIsLoading] = useState(true)\n\n    useEffect(() => {\n        loadTransactions()\n    }, [])\n\n    function loadTransactions() {\n        api.getTransactions()\n            .then(newTransacitons => {\n                newTransacitons = newTransacitons.sort((a, b) => b.timeStamp.getTime() - a.timeStamp.getTime())\n                setTransactions(newTransacitons)\n            })\n            .finally(() => {\n                setIsLoading(false)\n            })\n    }\n\n    function getDescriptionText(transaction: Transaction) {\n        if (transaction.productId === 'compensation') {\n            return (\n                <span>\n                    <b>Compensation:</b> {transaction.reference}\n                </span>\n            )\n        }\n        if (transaction.reference.startsWith('license-')) {\n            const licenseparts = transaction.reference.split('-')\n            return `Transfer (License ${licenseparts[1]} for ${licenseparts[2]})`\n        }\n        if (transaction.productId === 'transfer') {\n            return 'CoflCoins-Transfer'\n        }\n        if (transaction.productId === 'verify_mc') {\n            return 'Verified Minecraft Account'\n        }\n        if (transaction.productId === 'test-premium') {\n            return 'Awarded Test-Premium'\n        }\n        if (transaction.productId.startsWith('p_cc')) {\n            return 'Bought CoflCoins via PayPal'\n        }\n        if (transaction.productId.startsWith('s_cc')) {\n            return 'Bought CoflCoins via Stripe'\n        }\n        if (transaction.productId.startsWith('l_cc')) {\n            return 'Bought CoflCoins via LemonSqueezy'\n        }\n        if (transaction.productId.startsWith('pre_api')) {\n            return 'Bought Pre-API'\n        }\n        if (transaction.productId.startsWith('l_premium')) {\n            return 'Bought Premium (Subscription)'\n        }\n        if (transaction.productId.startsWith('l_prem_plus')) {\n            return 'Bought Premium+ (Subscription)'\n        }\n\n        const parts = transaction.reference.split('.')\n        let suffix = ''\n        if (parts.length === 2 && /^[0-9a-fA-F]{32}$/.test(parts[0]) && /^[0-9a-fA-F\\-]{1,}$/.test(parts[1])) {\n            suffix = ` (License for ${parts[0]})`\n        }\n\n        let purchasedPremiumOption\n        PREMIUM_TYPES.forEach(premiumType => {\n            if (transaction.productId.startsWith(premiumType.productId)) {\n                purchasedPremiumOption = premiumType.label\n            }\n        })\n\n        if (purchasedPremiumOption) {\n            return `Bought ${purchasedPremiumOption}${suffix}`\n        }\n\n        return transaction.productId\n    }\n\n    if (isLoading) {\n        return getLoadingElement(<p>Loading transactions...</p>)\n    }\n\n    return (\n        <ListGroup>\n            {transactions.map(transaction => (\n                <ListGroupItem key={transaction.reference} style={{ display: 'flex', gap: 15, justifyContent: 'space-between', alignItems: 'center' }}>\n                    <span style={{ flex: 1 }}>{transaction.timeStamp.toLocaleString()}</span>\n                    <span style={{ flex: 4 }}>{getDescriptionText(transaction)}</span>\n                    <span style={{ flex: 2, fontWeight: 'bold', color: transaction.amount < 0 ? 'red' : 'lime', textAlign: 'right' }}>\n                        {transaction.amount > 0 ? '+' : ''}\n                        {numberWithThousandsSeparators(transaction.amount)} CoflCoins\n                    </span>\n                </ListGroupItem>\n            ))}\n        </ListGroup>\n    )\n}\n\nexport default TransactionHistory\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;;;AANA;;;;;;;AAQA,SAAS;;IACL,IAAI,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAChE,IAAI,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACN;QACJ;uCAAG,EAAE;IAEL,SAAS;QACL,oHAAA,CAAA,UAAG,CAAC,eAAe,GACd,IAAI,CAAC,CAAA;YACF,kBAAkB,gBAAgB,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,SAAS,CAAC,OAAO,KAAK,EAAE,SAAS,CAAC,OAAO;YAC5F,gBAAgB;QACpB,GACC,OAAO,CAAC;YACL,aAAa;QACjB;IACR;IAEA,SAAS,mBAAmB,WAAwB;QAChD,IAAI,YAAY,SAAS,KAAK,gBAAgB;YAC1C,qBACI,6LAAC;;kCACG,6LAAC;kCAAE;;;;;;oBAAiB;oBAAE,YAAY,SAAS;;;;;;;QAGvD;QACA,IAAI,YAAY,SAAS,CAAC,UAAU,CAAC,aAAa;YAC9C,MAAM,eAAe,YAAY,SAAS,CAAC,KAAK,CAAC;YACjD,OAAO,CAAC,kBAAkB,EAAE,YAAY,CAAC,EAAE,CAAC,KAAK,EAAE,YAAY,CAAC,EAAE,CAAC,CAAC,CAAC;QACzE;QACA,IAAI,YAAY,SAAS,KAAK,YAAY;YACtC,OAAO;QACX;QACA,IAAI,YAAY,SAAS,KAAK,aAAa;YACvC,OAAO;QACX;QACA,IAAI,YAAY,SAAS,KAAK,gBAAgB;YAC1C,OAAO;QACX;QACA,IAAI,YAAY,SAAS,CAAC,UAAU,CAAC,SAAS;YAC1C,OAAO;QACX;QACA,IAAI,YAAY,SAAS,CAAC,UAAU,CAAC,SAAS;YAC1C,OAAO;QACX;QACA,IAAI,YAAY,SAAS,CAAC,UAAU,CAAC,SAAS;YAC1C,OAAO;QACX;QACA,IAAI,YAAY,SAAS,CAAC,UAAU,CAAC,YAAY;YAC7C,OAAO;QACX;QACA,IAAI,YAAY,SAAS,CAAC,UAAU,CAAC,cAAc;YAC/C,OAAO;QACX;QACA,IAAI,YAAY,SAAS,CAAC,UAAU,CAAC,gBAAgB;YACjD,OAAO;QACX;QAEA,MAAM,QAAQ,YAAY,SAAS,CAAC,KAAK,CAAC;QAC1C,IAAI,SAAS;QACb,IAAI,MAAM,MAAM,KAAK,KAAK,oBAAoB,IAAI,CAAC,KAAK,CAAC,EAAE,KAAK,sBAAsB,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG;YAClG,SAAS,CAAC,cAAc,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC;QACzC;QAEA,IAAI;QACJ,6HAAA,CAAA,gBAAa,CAAC,OAAO,CAAC,CAAA;YAClB,IAAI,YAAY,SAAS,CAAC,UAAU,CAAC,YAAY,SAAS,GAAG;gBACzD,yBAAyB,YAAY,KAAK;YAC9C;QACJ;QAEA,IAAI,wBAAwB;YACxB,OAAO,CAAC,OAAO,EAAE,yBAAyB,QAAQ;QACtD;QAEA,OAAO,YAAY,SAAS;IAChC;IAEA,IAAI,WAAW;QACX,OAAO,CAAA,GAAA,yHAAA,CAAA,oBAAiB,AAAD,gBAAE,6LAAC;sBAAE;;;;;;IAChC;IAEA,qBACI,6LAAC,iMAAA,CAAA,YAAS;kBACL,aAAa,GAAG,CAAC,CAAA,4BACd,6LAAC,yMAAA,CAAA,gBAAa;gBAA6B,OAAO;oBAAE,SAAS;oBAAQ,KAAK;oBAAI,gBAAgB;oBAAiB,YAAY;gBAAS;;kCAChI,6LAAC;wBAAK,OAAO;4BAAE,MAAM;wBAAE;kCAAI,YAAY,SAAS,CAAC,cAAc;;;;;;kCAC/D,6LAAC;wBAAK,OAAO;4BAAE,MAAM;wBAAE;kCAAI,mBAAmB;;;;;;kCAC9C,6LAAC;wBAAK,OAAO;4BAAE,MAAM;4BAAG,YAAY;4BAAQ,OAAO,YAAY,MAAM,GAAG,IAAI,QAAQ;4BAAQ,WAAW;wBAAQ;;4BAC1G,YAAY,MAAM,GAAG,IAAI,MAAM;4BAC/B,CAAA,GAAA,sHAAA,CAAA,gCAA6B,AAAD,EAAE,YAAY,MAAM;4BAAE;;;;;;;;eALvC,YAAY,SAAS;;;;;;;;;;AAWzD;GAjGS;KAAA;uCAmGM", "debugId": null}}, {"offset": {"line": 2819, "column": 0}, "map": {"version": 3, "sources": ["file:///app/components/AccountDetails/AccountDetails.tsx"], "sourcesContent": ["'use client'\nimport { useMatomo } from '@jonkoops/matomo-tracker-react'\nimport { googleLogout } from '@react-oauth/google'\nimport Cookies from 'js-cookie'\nimport Image from 'next/image'\nimport { ChangeEvent, useEffect, useState, type JSX } from 'react';\nimport { Button, Form, Modal } from 'react-bootstrap'\nimport { toast } from 'react-toastify'\nimport api from '../../api/ApiHelper'\nimport cacheUtils from '../../utils/CacheUtils'\nimport { useCoflCoins } from '../../utils/Hooks'\nimport { getLoadingElement } from '../../utils/LoadingUtils'\nimport GoogleSignIn from '../GoogleSignIn/GoogleSignIn'\nimport NavBar from '../NavBar/NavBar'\nimport Number from '../Number/Number'\nimport PremiumStatus from '../Premium/PremiumStatus/PremiumStatus'\nimport Tooltip from '../Tooltip/Tooltip'\nimport TransferCoflCoins from '../TransferCoflCoins/TransferCoflCoins'\nimport styles from './AccountDetails.module.css'\nimport PrivacySettings from './PrivacySettings/PrivacySettings'\nimport { GOOGLE_EMAIL, GOOGLE_NAME, GOOGLE_PROFILE_PICTURE_URL, getSetting } from '../../utils/SettingsUtils'\nimport TransactionHistory from './TransactionHistory/TransactionHistory'\n\nfunction AccountDetails() {\n    let [isLoggedIn, setIsLoggedIn] = useState(false)\n    let [isLoading, setIsLoading] = useState(true)\n    let [rerenderGoogleSignIn, setRerenderGoogleSignIn] = useState(0)\n    let [products, setProducts] = useState<PremiumProduct[]>([])\n    let [premiumSubscriptions, setPremiumSubscriptions] = useState<PremiumSubscription[]>([])\n    let [showSendcoflcoins, setShowSendCoflcoins] = useState(false)\n    let [hasLoadingPremiumError, setHasLoadingPremiumError] = useState(false)\n    let coflCoins = useCoflCoins()\n    let { pushInstruction } = useMatomo()\n\n    useEffect(() => {\n        if (sessionStorage.getItem('googleId') === null) {\n            setIsLoading(false)\n        }\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [])\n\n    function getAccountElement(): JSX.Element {\n        let picture = getSetting(GOOGLE_PROFILE_PICTURE_URL)\n        let email = getSetting(GOOGLE_EMAIL)\n        let name = getSetting(GOOGLE_NAME)\n\n        let imageElement = picture ? <Image src={picture} height={24} width={24} alt=\"\" /> : <span />\n        return (\n            <span>\n                {imageElement} {`${name} (${email})`}\n            </span>\n        )\n    }\n\n    function loadPremiumProducts(): Promise<void> {\n        return new Promise((resolve, reject) => {\n            api.refreshLoadPremiumProducts(products => {\n                products = products.filter(product => product.expires.getTime() > new Date().getTime())\n                setProducts(products)\n                resolve()\n            }, () => {\n                reject();\n            })\n        });\n\n    }\n\n    function loadPremiumSubscriptions(): Promise<void> {\n        return new Promise((resolve, reject) => {\n            api.getPremiumSubscriptions().then(subscriptions => {\n                subscriptions = subscriptions.filter(subscription => !subscription.endsAt || subscription.endsAt.getTime() > new Date().getTime())\n                setPremiumSubscriptions([...subscriptions])\n                resolve()\n            }).catch(() => {\n                reject();\n            })\n        })\n    }\n\n    function onLogout() {\n        setIsLoggedIn(false)\n        setIsLoading(false)\n        googleLogout()\n        sessionStorage.removeItem('googleId')\n        localStorage.removeItem('googleId')\n        setRerenderGoogleSignIn(rerenderGoogleSignIn + 1)\n        toast.warn('Successfully logged out')\n        setTimeout(() => {\n            location.reload()\n        }, 1000)\n    }\n\n    function onLogin() {\n        let googleId = sessionStorage.getItem('googleId')\n        setIsLoading(true)\n        if (googleId) {\n            Promise.all([loadPremiumProducts(), loadPremiumSubscriptions()]).then(() => {\n                setIsLoading(false)\n            }).catch(() => {\n                setIsLoading(false)\n                setHasLoadingPremiumError(true)\n                toast.error('Error loading premium products or subscriptionss')\n            })\n            setIsLoggedIn(true)\n        }\n    }\n\n    function onLoginFail() {\n        setIsLoggedIn(false)\n        setRerenderGoogleSignIn(rerenderGoogleSignIn + 1)\n    }\n\n    function deleteCaches() {\n        cacheUtils.clearAll()\n        document.cookie = ''\n        localStorage.clear()\n        sessionStorage.clear()\n        window.location.reload()\n    }\n\n    function setTrackingAllowed(event: ChangeEvent<HTMLInputElement>) {\n        let val = event.target.checked\n        if (val) {\n            pushInstruction('rememberConsentGiven')\n            Cookies.set('nonEssentialCookiesAllowed', 'true')\n        } else {\n            pushInstruction('forgetConsentGiven')\n            Cookies.set('nonEssentialCookiesAllowed', false)\n        }\n    }\n\n    function isTrackingAllowed() {\n        let cookie = Cookies.get('nonEssentialCookiesAllowed')\n        return cookie === 'true'\n    }\n\n    function deleteGoogleToken() {\n        sessionStorage.removeItem('googleId')\n        localStorage.removeItem('googleId')\n        setIsLoggedIn(false)\n        setRerenderGoogleSignIn(rerenderGoogleSignIn + 1)\n    }\n\n    function onSubscriptionCancel(subscription: PremiumSubscription) {\n        api.cancelPremiumSubscription(subscription.externalId).then(() => {\n            toast.success('Subscription cancelled')\n            loadPremiumSubscriptions()\n        })\n    }\n\n    return (\n        <>\n            <h2 style={{ marginBottom: '30px' }}>\n                <NavBar />\n                Account details\n            </h2>\n            {!isLoading && isLoggedIn ? (\n                <div>\n                    <p>\n                        <span className={styles.label}>Account:</span> {getAccountElement()}\n                    </p>\n                    <PremiumStatus\n                        products={products}\n                        subscriptions={premiumSubscriptions}\n                        onSubscriptionCancel={onSubscriptionCancel}\n                        labelStyle={{ width: '300px', fontWeight: 'bold' }}\n                        hasLoadingError={hasLoadingPremiumError}\n                    />\n                    <p>\n                        <span className={styles.label}>CoflCoins:</span> <Number number={coflCoins} />\n                        <Button\n                            className={styles.sendCoflCoinsButton}\n                            onClick={() => {\n                                setShowSendCoflcoins(true)\n                            }}\n                        >\n                            Send CoflCoins\n                        </Button>\n                        <Modal\n                            size={'lg'}\n                            show={showSendcoflcoins}\n                            onHide={() => {\n                                setShowSendCoflcoins(false)\n                            }}\n                        >\n                            <Modal.Header closeButton>\n                                <Modal.Title>Send CoflCoins</Modal.Title>\n                            </Modal.Header>\n                            <Modal.Body>\n                                <TransferCoflCoins\n                                    onFinish={() => {\n                                        setShowSendCoflcoins(false)\n                                    }}\n                                />\n                            </Modal.Body>\n                        </Modal>\n                    </p>\n                    <p>\n                        <span className={styles.label}>Transaction history:</span>\n                        <Tooltip\n                            type=\"click\"\n                            content={<span className={styles.link}>View transactions</span>}\n                            tooltipContent={<TransactionHistory />}\n                            tooltipTitle={<span>Transaction History</span>}\n                        />\n                    </p>\n                </div>\n            ) : null}\n            {isLoading ? getLoadingElement() : null}\n            <GoogleSignIn onAfterLogin={onLogin} onLoginFail={onLoginFail} rerenderFlip={rerenderGoogleSignIn} />\n            {isLoggedIn ? (\n                <div style={{ marginTop: '20px' }}>\n                    <Button onClick={onLogout}>Logout</Button>\n                </div>\n            ) : null}\n            <hr />\n            <h2 style={{ marginBottom: '30px' }}>Settings</h2>\n            <div style={{ paddingBottom: '1rem' }}>\n                <div>\n                    <span className={styles.label}>Allow cookies for analytics: </span>\n                    <Form.Check onChange={setTrackingAllowed} defaultChecked={isTrackingAllowed()} type=\"checkbox\" />\n                </div>\n            </div>\n            <div style={{ paddingBottom: '1rem' }}>\n                <span className={styles.label}>Login problems?</span>\n                <div>\n                    <Tooltip\n                        type=\"hover\"\n                        content={\n                            <Button variant=\"danger\" onClick={deleteGoogleToken}>\n                                Reset Google login\n                            </Button>\n                        }\n                        tooltipContent={<span>Make sure your browser doesn't block popups. Otherwise use this button to reset your Google login</span>}\n                    />\n                </div>\n            </div>\n            {isLoggedIn ? (\n                <div style={{ paddingBottom: '1rem' }}>\n                    <div style={{ display: 'inline-block' }}>\n                        <span className={styles.label}>Mod data settings: </span>\n                        <Tooltip\n                            type=\"click\"\n                            content={<span className={styles.link}>Open settings</span>}\n                            tooltipContent={<PrivacySettings />}\n                            tooltipTitle={<span>Mod data settings</span>}\n                        />\n                    </div>\n                </div>\n            ) : null}\n            <div style={{ paddingBottom: '1rem' }}>\n                <span className={styles.label}>Delete Caches/Cookies and hard refresh:</span>\n                <Tooltip\n                    type=\"click\"\n                    content={<span className={styles.link}>Delete</span>}\n                    tooltipContent={\n                        <div>\n                            <p>Warning: Deleting your Caches/Cookies will delete all your settings and log you out.</p>\n                            <Button variant=\"danger\" onClick={deleteCaches}>\n                                Confirm deletion\n                            </Button>\n                        </div>\n                    }\n                    tooltipTitle={<span>Are you sure?</span>}\n                />\n            </div>\n        </>\n    )\n}\n\nexport default AccountDetails\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AArBA;;;;;;;;;;;;;;;;;;;;;;AAuBA,SAAS;;IACL,IAAI,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,IAAI,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,IAAI,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,IAAI,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB,EAAE;IAC3D,IAAI,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAyB,EAAE;IACxF,IAAI,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,IAAI,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnE,IAAI,YAAY,CAAA,GAAA,kHAAA,CAAA,eAAY,AAAD;IAC3B,IAAI,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,sNAAA,CAAA,YAAS,AAAD;IAElC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACN,IAAI,eAAe,OAAO,CAAC,gBAAgB,MAAM;gBAC7C,aAAa;YACjB;QACA,uDAAuD;QAC3D;mCAAG,EAAE;IAEL,SAAS;QACL,IAAI,UAAU,CAAA,GAAA,0HAAA,CAAA,aAAU,AAAD,EAAE,0HAAA,CAAA,6BAA0B;QACnD,IAAI,QAAQ,CAAA,GAAA,0HAAA,CAAA,aAAU,AAAD,EAAE,0HAAA,CAAA,eAAY;QACnC,IAAI,OAAO,CAAA,GAAA,0HAAA,CAAA,aAAU,AAAD,EAAE,0HAAA,CAAA,cAAW;QAEjC,IAAI,eAAe,wBAAU,6LAAC,gIAAA,CAAA,UAAK;YAAC,KAAK;YAAS,QAAQ;YAAI,OAAO;YAAI,KAAI;;;;;iCAAQ,6LAAC;;;;;QACtF,qBACI,6LAAC;;gBACI;gBAAa;gBAAE,GAAG,KAAK,EAAE,EAAE,MAAM,CAAC,CAAC;;;;;;;IAGhD;IAEA,SAAS;QACL,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,oHAAA,CAAA,UAAG,CAAC,0BAA0B,CAAC,CAAA;gBAC3B,WAAW,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,OAAO,CAAC,OAAO,KAAK,IAAI,OAAO,OAAO;gBACpF,YAAY;gBACZ;YACJ,GAAG;gBACC;YACJ;QACJ;IAEJ;IAEA,SAAS;QACL,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,oHAAA,CAAA,UAAG,CAAC,uBAAuB,GAAG,IAAI,CAAC,CAAA;gBAC/B,gBAAgB,cAAc,MAAM,CAAC,CAAA,eAAgB,CAAC,aAAa,MAAM,IAAI,aAAa,MAAM,CAAC,OAAO,KAAK,IAAI,OAAO,OAAO;gBAC/H,wBAAwB;uBAAI;iBAAc;gBAC1C;YACJ,GAAG,KAAK,CAAC;gBACL;YACJ;QACJ;IACJ;IAEA,SAAS;QACL,cAAc;QACd,aAAa;QACb,CAAA,GAAA,qKAAA,CAAA,eAAY,AAAD;QACX,eAAe,UAAU,CAAC;QAC1B,aAAa,UAAU,CAAC;QACxB,wBAAwB,uBAAuB;QAC/C,sJAAA,CAAA,QAAK,CAAC,IAAI,CAAC;QACX,WAAW;YACP,SAAS,MAAM;QACnB,GAAG;IACP;IAEA,SAAS;QACL,IAAI,WAAW,eAAe,OAAO,CAAC;QACtC,aAAa;QACb,IAAI,UAAU;YACV,QAAQ,GAAG,CAAC;gBAAC;gBAAuB;aAA2B,EAAE,IAAI,CAAC;gBAClE,aAAa;YACjB,GAAG,KAAK,CAAC;gBACL,aAAa;gBACb,0BAA0B;gBAC1B,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YAChB;YACA,cAAc;QAClB;IACJ;IAEA,SAAS;QACL,cAAc;QACd,wBAAwB,uBAAuB;IACnD;IAEA,SAAS;QACL,uHAAA,CAAA,UAAU,CAAC,QAAQ;QACnB,SAAS,MAAM,GAAG;QAClB,aAAa,KAAK;QAClB,eAAe,KAAK;QACpB,OAAO,QAAQ,CAAC,MAAM;IAC1B;IAEA,SAAS,mBAAmB,KAAoC;QAC5D,IAAI,MAAM,MAAM,MAAM,CAAC,OAAO;QAC9B,IAAI,KAAK;YACL,gBAAgB;YAChB,wJAAA,CAAA,UAAO,CAAC,GAAG,CAAC,8BAA8B;QAC9C,OAAO;YACH,gBAAgB;YAChB,wJAAA,CAAA,UAAO,CAAC,GAAG,CAAC,8BAA8B;QAC9C;IACJ;IAEA,SAAS;QACL,IAAI,SAAS,wJAAA,CAAA,UAAO,CAAC,GAAG,CAAC;QACzB,OAAO,WAAW;IACtB;IAEA,SAAS;QACL,eAAe,UAAU,CAAC;QAC1B,aAAa,UAAU,CAAC;QACxB,cAAc;QACd,wBAAwB,uBAAuB;IACnD;IAEA,SAAS,qBAAqB,YAAiC;QAC3D,oHAAA,CAAA,UAAG,CAAC,yBAAyB,CAAC,aAAa,UAAU,EAAE,IAAI,CAAC;YACxD,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd;QACJ;IACJ;IAEA,qBACI;;0BACI,6LAAC;gBAAG,OAAO;oBAAE,cAAc;gBAAO;;kCAC9B,6LAAC,kIAAA,CAAA,UAAM;;;;;oBAAG;;;;;;;YAGb,CAAC,aAAa,2BACX,6LAAC;;kCACG,6LAAC;;0CACG,6LAAC;gCAAK,WAAW,6JAAA,CAAA,UAAM,CAAC,KAAK;0CAAE;;;;;;4BAAe;4BAAE;;;;;;;kCAEpD,6LAAC,2JAAA,CAAA,UAAa;wBACV,UAAU;wBACV,eAAe;wBACf,sBAAsB;wBACtB,YAAY;4BAAE,OAAO;4BAAS,YAAY;wBAAO;wBACjD,iBAAiB;;;;;;kCAErB,6LAAC;;0CACG,6LAAC;gCAAK,WAAW,6JAAA,CAAA,UAAM,CAAC,KAAK;0CAAE;;;;;;4BAAiB;0CAAC,6LAAC,kIAAA,CAAA,UAAM;gCAAC,QAAQ;;;;;;0CACjE,6LAAC,2LAAA,CAAA,SAAM;gCACH,WAAW,6JAAA,CAAA,UAAM,CAAC,mBAAmB;gCACrC,SAAS;oCACL,qBAAqB;gCACzB;0CACH;;;;;;0CAGD,6LAAC,yLAAA,CAAA,QAAK;gCACF,MAAM;gCACN,MAAM;gCACN,QAAQ;oCACJ,qBAAqB;gCACzB;;kDAEA,6LAAC,yLAAA,CAAA,QAAK,CAAC,MAAM;wCAAC,WAAW;kDACrB,cAAA,6LAAC,yLAAA,CAAA,QAAK,CAAC,KAAK;sDAAC;;;;;;;;;;;kDAEjB,6LAAC,yLAAA,CAAA,QAAK,CAAC,IAAI;kDACP,cAAA,6LAAC,wJAAA,CAAA,UAAiB;4CACd,UAAU;gDACN,qBAAqB;4CACzB;;;;;;;;;;;;;;;;;;;;;;;kCAKhB,6LAAC;;0CACG,6LAAC;gCAAK,WAAW,6JAAA,CAAA,UAAM,CAAC,KAAK;0CAAE;;;;;;0CAC/B,6LAAC,oIAAA,CAAA,UAAO;gCACJ,MAAK;gCACL,uBAAS,6LAAC;oCAAK,WAAW,6JAAA,CAAA,UAAM,CAAC,IAAI;8CAAE;;;;;;gCACvC,8BAAgB,6LAAC,4KAAA,CAAA,UAAkB;;;;;gCACnC,4BAAc,6LAAC;8CAAK;;;;;;;;;;;;;;;;;;;;;;uBAIhC;YACH,YAAY,CAAA,GAAA,yHAAA,CAAA,oBAAiB,AAAD,MAAM;0BACnC,6LAAC,8IAAA,CAAA,UAAY;gBAAC,cAAc;gBAAS,aAAa;gBAAa,cAAc;;;;;;YAC5E,2BACG,6LAAC;gBAAI,OAAO;oBAAE,WAAW;gBAAO;0BAC5B,cAAA,6LAAC,2LAAA,CAAA,SAAM;oBAAC,SAAS;8BAAU;;;;;;;;;;uBAE/B;0BACJ,6LAAC;;;;;0BACD,6LAAC;gBAAG,OAAO;oBAAE,cAAc;gBAAO;0BAAG;;;;;;0BACrC,6LAAC;gBAAI,OAAO;oBAAE,eAAe;gBAAO;0BAChC,cAAA,6LAAC;;sCACG,6LAAC;4BAAK,WAAW,6JAAA,CAAA,UAAM,CAAC,KAAK;sCAAE;;;;;;sCAC/B,6LAAC,uLAAA,CAAA,OAAI,CAAC,KAAK;4BAAC,UAAU;4BAAoB,gBAAgB;4BAAqB,MAAK;;;;;;;;;;;;;;;;;0BAG5F,6LAAC;gBAAI,OAAO;oBAAE,eAAe;gBAAO;;kCAChC,6LAAC;wBAAK,WAAW,6JAAA,CAAA,UAAM,CAAC,KAAK;kCAAE;;;;;;kCAC/B,6LAAC;kCACG,cAAA,6LAAC,oIAAA,CAAA,UAAO;4BACJ,MAAK;4BACL,uBACI,6LAAC,2LAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAS,SAAS;0CAAmB;;;;;;4BAIzD,8BAAgB,6LAAC;0CAAK;;;;;;;;;;;;;;;;;;;;;;YAIjC,2BACG,6LAAC;gBAAI,OAAO;oBAAE,eAAe;gBAAO;0BAChC,cAAA,6LAAC;oBAAI,OAAO;wBAAE,SAAS;oBAAe;;sCAClC,6LAAC;4BAAK,WAAW,6JAAA,CAAA,UAAM,CAAC,KAAK;sCAAE;;;;;;sCAC/B,6LAAC,oIAAA,CAAA,UAAO;4BACJ,MAAK;4BACL,uBAAS,6LAAC;gCAAK,WAAW,6JAAA,CAAA,UAAM,CAAC,IAAI;0CAAE;;;;;;4BACvC,8BAAgB,6LAAC,sKAAA,CAAA,UAAe;;;;;4BAChC,4BAAc,6LAAC;0CAAK;;;;;;;;;;;;;;;;;;;;;uBAIhC;0BACJ,6LAAC;gBAAI,OAAO;oBAAE,eAAe;gBAAO;;kCAChC,6LAAC;wBAAK,WAAW,6JAAA,CAAA,UAAM,CAAC,KAAK;kCAAE;;;;;;kCAC/B,6LAAC,oIAAA,CAAA,UAAO;wBACJ,MAAK;wBACL,uBAAS,6LAAC;4BAAK,WAAW,6JAAA,CAAA,UAAM,CAAC,IAAI;sCAAE;;;;;;wBACvC,8BACI,6LAAC;;8CACG,6LAAC;8CAAE;;;;;;8CACH,6LAAC,2LAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAS,SAAS;8CAAc;;;;;;;;;;;;wBAKxD,4BAAc,6LAAC;sCAAK;;;;;;;;;;;;;;;;;;;AAKxC;GArPS;;QAQW,kHAAA,CAAA,eAAY;QACF,sNAAA,CAAA,YAAS;;;KAT9B;uCAuPM", "debugId": null}}]}