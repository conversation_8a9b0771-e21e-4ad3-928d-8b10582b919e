{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/components/PlayerDetailsList/PlayerDetailsList.module.css"], "sourcesContent": [".bidItemImage {\n    margin-right: 15px;\n}\n\n.bidList {\n    width: 96%;\n    margin-left: auto;\n    margin-right: auto;\n}\n\n.loadingBanner {\n    width: 50%;\n    margin-left: auto;\n    margin-right: auto;\n    margin-top: 5vh;\n}\n\n.noElementFound {\n    margin-right: auto;\n    margin-left: auto;\n    width: max-content;\n    text-align: center;\n    margin-top: 5vh;\n}\n\n.subscribeButton {\n    position: fixed;\n    bottom: 20px;\n    right: calc(20px + 45px + 20px);\n}\n\n.fixedBottom {\n    z-index: 10;\n    position: fixed;\n    bottom: 20px;\n    right: 20px;\n    width: 100%;\n    display: flex;\n    justify-content: flex-end;\n    pointer-events: none;\n}\n\n.btnBottom {\n    margin-left: 10px;\n    width: max-content;\n    pointer-events: all;\n}\n\n.playerDetailsList .list {\n    display: flex;\n    flex-wrap: wrap;\n    justify-content: space-between;\n    flex-direction: initial;\n}\n\n.playerDetailsList .list :global(.list-group-item) {\n    border-radius: 20px;\n    width: 100%;\n    margin-bottom: 15px;\n}\n\n@media all and (min-width: 1200px) {\n    .playerDetailsList .list :global(.list-group-item) {\n        width: 49%;\n    }\n}\n"], "names": [], "mappings": "AAAA;;;;AAIA;;;;;;AAMA;;;;;;;AAOA;;;;;;;;AAQA;;;;;;AAMA;;;;;;;;;;;AAWA;;;;;;AAMA;;;;;;;AAOA;;;;;;AAMA;EACI"}}]}