/* [project]/components/PlayerDetailsList/PlayerDetailsList.module.css [app-client] (css) */
.PlayerDetailsList-module__MrHFHW__bidItemImage {
  margin-right: 15px;
}

.PlayerDetailsList-module__MrHFHW__bidList {
  width: 96%;
  margin-left: auto;
  margin-right: auto;
}

.PlayerDetailsList-module__MrHFHW__loadingBanner {
  width: 50%;
  margin-left: auto;
  margin-right: auto;
  margin-top: 5vh;
}

.PlayerDetailsList-module__MrHFHW__noElementFound {
  margin-right: auto;
  margin-left: auto;
  width: max-content;
  text-align: center;
  margin-top: 5vh;
}

.PlayerDetailsList-module__MrHFHW__subscribeButton {
  position: fixed;
  bottom: 20px;
  right: 85px;
}

.PlayerDetailsList-module__MrHFHW__fixedBottom {
  z-index: 10;
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 100%;
  display: flex;
  justify-content: flex-end;
  pointer-events: none;
}

.PlayerDetailsList-module__MrHFHW__btnBottom {
  margin-left: 10px;
  width: max-content;
  pointer-events: all;
}

.PlayerDetailsList-module__MrHFHW__playerDetailsList .PlayerDetailsList-module__MrHFHW__list {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  flex-direction: initial;
}

.PlayerDetailsList-module__MrHFHW__playerDetailsList .PlayerDetailsList-module__MrHFHW__list .list-group-item {
  border-radius: 20px;
  width: 100%;
  margin-bottom: 15px;
}

@media (width >= 1200px) {
  .PlayerDetailsList-module__MrHFHW__playerDetailsList .PlayerDetailsList-module__MrHFHW__list .list-group-item {
    width: 49%;
  }
}

/*# sourceMappingURL=components_PlayerDetailsList_PlayerDetailsList_module_b52d8e88.css.map*/