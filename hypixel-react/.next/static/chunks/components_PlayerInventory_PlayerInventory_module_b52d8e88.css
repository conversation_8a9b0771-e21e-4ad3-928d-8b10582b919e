/* [project]/components/PlayerInventory/PlayerInventory.module.css [app-client] (css) */
.PlayerInventory-module__VgUqnq__inventory {
  overflow: hidden;
}

.PlayerInventory-module__VgUqnq__card {
  margin-bottom: 30px;
}

.PlayerInventory-module__VgUqnq__inventory .PlayerInventory-module__VgUqnq__grid {
  width: 540px;
  height: 100%;
}

.PlayerInventory-module__VgUqnq__inventory .PlayerInventory-module__VgUqnq__gridCell {
  width: 60px;
  height: 60px;
  background-color: #8b8b8b;
  border-top: thick double #373737;
  border-left: thick double #373737;
  border-right: double #fff;
  border-bottom: double #fff;
  float: left;
}

.PlayerInventory-module__VgUqnq__inventory .PlayerInventory-module__VgUqnq__image {
  display: block;
  margin: auto;
  padding: 2px;
}

.PlayerInventory-module__VgUqnq__hoverElement li {
  text-align: left;
}

.PlayerInventory-module__VgUqnq__hoverElement {
  min-width: "800px" !important;
}

#PlayerInventory-module__VgUqnq__tooltipHoverId .tooltip-inner {
  max-width: 100%;
}

/*# sourceMappingURL=components_PlayerInventory_PlayerInventory_module_b52d8e88.css.map*/