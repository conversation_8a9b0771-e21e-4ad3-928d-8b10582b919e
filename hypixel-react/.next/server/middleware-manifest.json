{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/[root of the server]__3738de99._.js", "server/edge/chunks/_f53cf861._.js", "server/edge/chunks/edge-wrapper_3ca763d6.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "pZ2E7nj8k6+mUoTHeAy2Q4jqKcyGHweQgkt4p3jT89s=", "__NEXT_PREVIEW_MODE_ID": "9c591625abdf77a0d1e15d2789d1b4ac", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "a44cc3a9cb03bb28a303fb8624b3fe44f60cd5eb5d31a477d0068479962faafc", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "6f4ebd25d18558df9bc6af3100655fd837631ea45e48c829c34eac2e8254c51d"}}}, "sortedMiddleware": ["/"], "functions": {}}