{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/[root of the server]__3738de99._.js", "server/edge/chunks/_f53cf861._.js", "server/edge/chunks/edge-wrapper_3ca763d6.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "L4WdlG4F4gAal3QUHe+NCm3vWWmH8wM0x/5fB+Nv6IA=", "__NEXT_PREVIEW_MODE_ID": "391207b8fc778cc6ea189a702c65c168", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "e46d4b0e5a42d162ffaebf8da2c2723f3624bc770cba6d1abd76bc7cbd418cc5", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "943278ebf6e303c528a00a0aafff95e6d0039b45ea987676fa40db0cc87bdb09"}}}, "sortedMiddleware": ["/"], "functions": {}}