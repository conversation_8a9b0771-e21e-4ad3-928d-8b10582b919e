'use client'
import React, { useEffect, useState } from 'react'
import { numberWithThousandsSeparators } from '../../utils/Formatter'

interface Props {
    number: number | string
}

export default function NumberElement(props: Props) {
    let [isSSR, setIsSSR] = useState(true)

    let value = Number(props.number)

    useEffect(() => {
        setIsSSR(false)
    }, [])

    // Use consistent formatting to prevent hydration mismatches
    // Always use comma as thousand separator and period as decimal separator
    return <span suppressHydrationWarning>{numberWithThousandsSeparators(value, ',', '.')}</span>
}
